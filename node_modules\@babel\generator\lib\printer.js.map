{"version": 3, "names": ["SCIENTIFIC_NOTATION", "ZERO_DECIMAL_INTEGER", "NON_DECIMAL_LITERAL", "PURE_ANNOTATION_RE", "needsParens", "n", "Printer", "constructor", "format", "map", "inForStatementInitCounter", "_printStack", "_indent", "_indentChar", "_indentRepeat", "_insideAux", "_parenPushNewlineState", "_noLineTerminator", "_printAuxAfterOnNextUserNode", "_printedComments", "Set", "_endsWithInteger", "_endsWithWord", "_lastCommentLine", "_buf", "<PERSON><PERSON><PERSON>", "indent", "style", "charCodeAt", "length", "generate", "ast", "print", "_maybe<PERSON>dd<PERSON>uxComment", "get", "compact", "concise", "dedent", "semicolon", "force", "_appendChar", "_queue", "rightBrace", "minified", "removeLastSemicolon", "token", "space", "_space", "<PERSON><PERSON><PERSON><PERSON>", "lastCp", "getLastChar", "word", "str", "endsWith", "_append", "number", "Number", "isInteger", "test", "maybeNewline", "lastChar", "str<PERSON><PERSON><PERSON>", "tokenChar", "char", "newline", "i", "retainLines", "getNewlineCount", "j", "_newline", "endsWithCharAndNewline", "removeTrailingNewline", "exactSource", "loc", "cb", "_catchUp", "source", "prop", "sourceWithOffset", "lineOffset", "columnOffset", "withSource", "_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_maybeIndent", "append", "_maybeAddParenChar", "appendChar", "queue", "firstChar", "queueIndentation", "_getIndent", "_shouldIndent", "parenPushNewlineState", "printed", "len", "cha", "chaPost", "slice", "catchUp", "line", "count", "getCurrentLine", "pos", "ensureNoLineTerminator", "fn", "printTerminatorless", "node", "parent", "isLabel", "terminatorState", "noLineTerminator", "trailingCommentsLineOffset", "nodeType", "type", "oldConcise", "_compact", "printMethod", "undefined", "ReferenceError", "JSON", "stringify", "name", "push", "oldInAux", "shouldPrintParens", "retainFunctionParens", "extra", "parenthesized", "_printLeadingComments", "bind", "_printTrailingComments", "pop", "enteredPositionlessNode", "_printAuxBeforeComment", "_printAuxAfterComment", "comment", "auxiliaryCommentBefore", "_printComment", "value", "auxiliaryCommentAfter", "getPossibleRaw", "raw", "rawValue", "printJoin", "nodes", "opts", "newlineOpts", "addNewlines", "nextNodeStartLine", "separator", "statement", "_printNewline", "iterator", "nextNode", "start", "printAndIndentOnComments", "leadingComments", "printBlock", "body", "comments", "_getComments", "_printComments", "printInnerComments", "innerComments", "printSequence", "printList", "items", "commaSeparator", "newLine", "startLine", "lastCommentLine", "offset", "leading", "trailingComments", "skipNewLines", "ignore", "has", "shouldPrintComment", "add", "isBlockComment", "printNewLines", "lastCharCode", "val", "adjustMultilineComment", "column", "newlineRegex", "RegExp", "replace", "indentSize", "getCurrentColumn", "repeat", "nodeLoc", "hasLoc", "nodeStartLine", "nodeEndLine", "end", "lastLine", "leadingCommentNewline", "commentStartLine", "commentEndLine", "Math", "max", "min", "properties", "printAssertions", "assertions", "Object", "assign", "prototype", "generatorFunctions", "Noop"], "sources": ["../src/printer.ts"], "sourcesContent": ["import Buffer from \"./buffer\";\nimport type { Loc } from \"./buffer\";\nimport * as n from \"./node\";\nimport type * as t from \"@babel/types\";\nimport type {\n  RecordAndTuplePluginOptions,\n  PipelineOperatorPluginOptions,\n} from \"@babel/parser\";\nimport type { Opts as jsescOptions } from \"jsesc\";\n\nimport * as generatorFunctions from \"./generators\";\nimport type SourceMap from \"./source-map\";\nimport * as charCodes from \"charcodes\";\n\nconst SCIENTIFIC_NOTATION = /e/i;\nconst ZERO_DECIMAL_INTEGER = /\\.0+$/;\nconst NON_DECIMAL_LITERAL = /^0[box]/;\nconst PURE_ANNOTATION_RE = /^\\s*[@#]__PURE__\\s*$/;\n\nconst { needsParens } = n;\n\nconst enum COMMENT_TYPE {\n  LEADING,\n  INNER,\n  TRAILING,\n}\n\nconst enum COMMENT_SKIP_NEWLINE {\n  DEFAULT,\n  SKIP_ALL,\n  SKIP_LEADING,\n  SKIP_TRAILING,\n}\n\nexport type Format = {\n  shouldPrintComment: (comment: string) => boolean;\n  retainLines: boolean;\n  retainFunctionParens: boolean;\n  comments: boolean;\n  auxiliaryCommentBefore: string;\n  auxiliaryCommentAfter: string;\n  compact: boolean | \"auto\";\n  minified: boolean;\n  concise: boolean;\n  indent: {\n    adjustMultilineComment: boolean;\n    style: string;\n  };\n  recordAndTupleSyntaxType: RecordAndTuplePluginOptions[\"syntaxType\"];\n  jsescOption: jsescOptions;\n  jsonCompatibleStrings?: boolean;\n  /**\n   * For use with the Hack-style pipe operator.\n   * Changes what token is used for pipe bodies’ topic references.\n   */\n  topicToken?: PipelineOperatorPluginOptions[\"topicToken\"];\n  /**\n   * @deprecated Removed in Babel 8\n   */\n  decoratorsBeforeExport?: boolean;\n};\n\ninterface AddNewlinesOptions {\n  addNewlines(leading: boolean, node: t.Node): number;\n  nextNodeStartLine: number;\n}\n\ninterface PrintSequenceOptions extends Partial<AddNewlinesOptions> {\n  statement?: boolean;\n  indent?: boolean;\n  trailingCommentsLineOffset?: number;\n}\n\ninterface PrintListOptions {\n  separator?: (this: Printer) => void;\n  statement?: boolean;\n  indent?: boolean;\n}\n\ntype PrintJoinOptions = PrintListOptions &\n  PrintSequenceOptions & {\n    iterator?: (node: t.Node, index: number) => void;\n  };\nclass Printer {\n  constructor(format: Format, map: SourceMap) {\n    this.format = format;\n    this._buf = new Buffer(map);\n\n    this._indentChar = format.indent.style.charCodeAt(0);\n    this._indentRepeat = format.indent.style.length;\n  }\n\n  declare format: Format;\n  inForStatementInitCounter: number = 0;\n\n  declare _buf: Buffer;\n  _printStack: Array<t.Node> = [];\n  _indent: number = 0;\n  _indentChar: number = 0;\n  _indentRepeat: number = 0;\n  _insideAux: boolean = false;\n  _parenPushNewlineState: { printed: boolean } | null = null;\n  _noLineTerminator: boolean = false;\n  _printAuxAfterOnNextUserNode: boolean = false;\n  _printedComments = new Set<t.Comment>();\n  _endsWithInteger = false;\n  _endsWithWord = false;\n  _lastCommentLine = 0;\n\n  generate(ast: t.Node) {\n    this.print(ast);\n    this._maybeAddAuxComment();\n\n    return this._buf.get();\n  }\n\n  /**\n   * Increment indent size.\n   */\n\n  indent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent++;\n  }\n\n  /**\n   * Decrement indent size.\n   */\n\n  dedent(): void {\n    if (this.format.compact || this.format.concise) return;\n\n    this._indent--;\n  }\n\n  /**\n   * Add a semicolon to the buffer.\n   */\n\n  semicolon(force: boolean = false): void {\n    this._maybeAddAuxComment();\n    if (force) {\n      this._appendChar(charCodes.semicolon);\n    } else {\n      this._queue(charCodes.semicolon);\n    }\n  }\n\n  /**\n   * Add a right brace to the buffer.\n   */\n\n  rightBrace(): void {\n    if (this.format.minified) {\n      this._buf.removeLastSemicolon();\n    }\n    this.token(\"}\");\n  }\n\n  /**\n   * Add a space to the buffer unless it is compact.\n   */\n\n  space(force: boolean = false): void {\n    if (this.format.compact) return;\n\n    if (force) {\n      this._space();\n    } else if (this._buf.hasContent()) {\n      const lastCp = this.getLastChar();\n      if (lastCp !== charCodes.space && lastCp !== charCodes.lineFeed) {\n        this._space();\n      }\n    }\n  }\n\n  /**\n   * Writes a token that can't be safely parsed without taking whitespace into account.\n   */\n\n  word(str: string): void {\n    // prevent concatenating words and creating // comment out of division and regex\n    if (\n      this._endsWithWord ||\n      (str.charCodeAt(0) === charCodes.slash && this.endsWith(charCodes.slash))\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, false);\n\n    this._endsWithWord = true;\n  }\n\n  /**\n   * Writes a number token so that we can validate if it is an integer.\n   */\n\n  number(str: string): void {\n    this.word(str);\n\n    // Integer tokens need special handling because they cannot have '.'s inserted\n    // immediately after them.\n    this._endsWithInteger =\n      Number.isInteger(+str) &&\n      !NON_DECIMAL_LITERAL.test(str) &&\n      !SCIENTIFIC_NOTATION.test(str) &&\n      !ZERO_DECIMAL_INTEGER.test(str) &&\n      str.charCodeAt(str.length - 1) !== charCodes.dot;\n  }\n\n  /**\n   * Writes a simple token.\n   */\n\n  token(str: string, maybeNewline = false): void {\n    // space is mandatory to avoid outputting <!--\n    // http://javascript.spec.whatwg.org/#comment-syntax\n    const lastChar = this.getLastChar();\n    const strFirst = str.charCodeAt(0);\n    if (\n      (lastChar === charCodes.exclamationMark && str === \"--\") ||\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (strFirst === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (strFirst === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (strFirst === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._append(str, maybeNewline);\n  }\n\n  tokenChar(char: number): void {\n    // space is mandatory to avoid outputting <!--\n    // http://javascript.spec.whatwg.org/#comment-syntax\n    const lastChar = this.getLastChar();\n    if (\n      // Need spaces for operators of the same kind to avoid: `a+++b`\n      (char === charCodes.plusSign && lastChar === charCodes.plusSign) ||\n      (char === charCodes.dash && lastChar === charCodes.dash) ||\n      // Needs spaces to avoid changing '34' to '34.', which would still be a valid number.\n      (char === charCodes.dot && this._endsWithInteger)\n    ) {\n      this._space();\n    }\n\n    this._maybeAddAuxComment();\n    this._appendChar(char);\n  }\n\n  /**\n   * Add a newline (or many newlines), maintaining formatting.\n   * This function checks the number of newlines in the queue and subtracts them.\n   * It currently has some limitations.\n   * @see {Buffer#getNewlineCount}\n   */\n  newline(i: number = 1, force?: boolean): void {\n    if (i <= 0) return;\n\n    if (!force) {\n      if (this.format.retainLines || this.format.compact) return;\n\n      if (this.format.concise) {\n        this.space();\n        return;\n      }\n    }\n\n    if (i > 2) i = 2; // Max two lines\n\n    i -= this._buf.getNewlineCount();\n\n    for (let j = 0; j < i; j++) {\n      this._newline();\n    }\n\n    return;\n  }\n\n  endsWith(char: number): boolean {\n    return this.getLastChar() === char;\n  }\n\n  getLastChar(): number {\n    return this._buf.getLastChar();\n  }\n\n  endsWithCharAndNewline(): number {\n    return this._buf.endsWithCharAndNewline();\n  }\n\n  removeTrailingNewline(): void {\n    this._buf.removeTrailingNewline();\n  }\n\n  exactSource(loc: Loc | undefined, cb: () => void) {\n    if (!loc) return cb();\n\n    this._catchUp(\"start\", loc);\n\n    this._buf.exactSource(loc, cb);\n  }\n\n  source(prop: \"start\" | \"end\", loc: Loc | undefined): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.source(prop, loc);\n  }\n\n  sourceWithOffset(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    lineOffset: number,\n    columnOffset: number,\n  ): void {\n    if (!loc) return;\n\n    this._catchUp(prop, loc);\n\n    this._buf.sourceWithOffset(prop, loc, lineOffset, columnOffset);\n  }\n\n  withSource(\n    prop: \"start\" | \"end\",\n    loc: Loc | undefined,\n    cb: () => void,\n  ): void {\n    if (!loc) return cb();\n\n    this._catchUp(prop, loc);\n\n    this._buf.withSource(prop, loc, cb);\n  }\n\n  _space(): void {\n    this._queue(charCodes.space);\n  }\n\n  _newline(): void {\n    this._queue(charCodes.lineFeed);\n  }\n\n  _append(str: string, maybeNewline: boolean): void {\n    this._maybeAddParen(str);\n    this._maybeIndent(str.charCodeAt(0));\n\n    this._buf.append(str, maybeNewline);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _appendChar(char: number): void {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.appendChar(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _queue(char: number) {\n    this._maybeAddParenChar(char);\n    this._maybeIndent(char);\n\n    this._buf.queue(char);\n\n    this._endsWithWord = false;\n    this._endsWithInteger = false;\n  }\n\n  _maybeIndent(firstChar: number): void {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      this._buf.queueIndentation(this._indentChar, this._getIndent());\n    }\n  }\n\n  _shouldIndent(firstChar: number) {\n    // we've got a newline before us so prepend on the indentation\n    if (\n      this._indent &&\n      firstChar !== charCodes.lineFeed &&\n      this.endsWith(charCodes.lineFeed)\n    ) {\n      return true;\n    }\n  }\n\n  _maybeAddParenChar(char: number): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    if (char === charCodes.space) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    if (char !== charCodes.lineFeed) {\n      this._parenPushNewlineState = null;\n      return;\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  _maybeAddParen(str: string): void {\n    // see startTerminatorless() instance method\n    const parenPushNewlineState = this._parenPushNewlineState;\n    if (!parenPushNewlineState) return;\n\n    // This function does two things:\n    // - If needed, prints a parenthesis\n    // - If the currently printed string removes the need for the paren,\n    //   it resets the _parenPushNewlineState field.\n    //   Almost everything removes the need for a paren, except for\n    //   comments and whitespaces.\n\n    const len = str.length;\n\n    let i;\n    for (i = 0; i < len && str.charCodeAt(i) === charCodes.space; i++) continue;\n    if (i === len) {\n      // Whitespaces only, the parentheses might still be needed.\n      return;\n    }\n\n    // Check for newline or comment.\n    const cha = str.charCodeAt(i);\n    if (cha !== charCodes.lineFeed) {\n      if (\n        // This is not a comment (it doesn't start with /)\n        cha !== charCodes.slash ||\n        // This is not a comment (it's a / operator)\n        i + 1 === len\n      ) {\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n\n      const chaPost = str.charCodeAt(i + 1);\n\n      if (chaPost === charCodes.asterisk) {\n        // This is a block comment\n\n        if (PURE_ANNOTATION_RE.test(str.slice(i + 2, len - 2))) {\n          // We avoid printing newlines after #__PURE__ comments (we treat\n          // then as unary operators), but we must keep the old\n          // parenPushNewlineState because, if a newline was forbidden, it is\n          // still forbidden after the comment.\n          return;\n        }\n\n        // NOTE: code flow continues from here to after these if/elses\n      } else if (chaPost !== charCodes.slash) {\n        // This is neither a block comment, nor a line comment.\n        // After a normal token, the parentheses aren't needed anymore\n        this._parenPushNewlineState = null;\n        return;\n      }\n    }\n\n    this.token(\"(\");\n    this.indent();\n    parenPushNewlineState.printed = true;\n  }\n\n  catchUp(line: number) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const count = line - this._buf.getCurrentLine();\n\n    for (let i = 0; i < count; i++) {\n      this._newline();\n    }\n  }\n\n  _catchUp(prop: \"start\" | \"end\", loc?: Loc) {\n    if (!this.format.retainLines) return;\n\n    // catch up to this nodes newline if we're behind\n    const pos = loc ? loc[prop] : null;\n    if (pos?.line != null) {\n      const count = pos.line - this._buf.getCurrentLine();\n\n      for (let i = 0; i < count; i++) {\n        this._newline();\n      }\n    }\n  }\n\n  /**\n   * Get the current indent.\n   */\n\n  _getIndent(): number {\n    return this._indentRepeat * this._indent;\n  }\n\n  ensureNoLineTerminator(fn: () => void) {\n    const { _noLineTerminator } = this;\n    this._noLineTerminator = true;\n    fn();\n    this._noLineTerminator = _noLineTerminator;\n  }\n\n  printTerminatorless(node: t.Node, parent: t.Node, isLabel: boolean) {\n    /**\n     * Set some state that will be modified if a newline has been inserted before any\n     * non-space characters.\n     *\n     * This is to prevent breaking semantics for terminatorless separator nodes. eg:\n     *\n     *   return foo;\n     *\n     * returns `foo`. But if we do:\n     *\n     *   return\n     *   foo;\n     *\n     *  `undefined` will be returned and not `foo` due to the terminator.\n     */\n    if (isLabel) {\n      this.ensureNoLineTerminator(() => {\n        this.print(node, parent);\n      });\n    } else {\n      const terminatorState = {\n        printed: false,\n      };\n      this._parenPushNewlineState = terminatorState;\n      this.print(node, parent);\n      /**\n       * Print an ending parentheses if a starting one has been printed.\n       */\n      if (terminatorState.printed) {\n        this.dedent();\n        this.newline();\n        this.token(\")\");\n      }\n    }\n  }\n\n  print(\n    node: t.Node | null,\n    parent?: t.Node,\n    noLineTerminator?: boolean,\n    // trailingCommentsLineOffset also used to check if called from printJoin\n    // it will be ignored if `noLineTerminator||this._noLineTerminator`\n    trailingCommentsLineOffset?: number,\n  ) {\n    if (!node) return;\n\n    const nodeType = node.type;\n    const format = this.format;\n\n    const oldConcise = format.concise;\n    if (\n      // @ts-expect-error document _compact AST properties\n      node._compact\n    ) {\n      format.concise = true;\n    }\n\n    const printMethod =\n      this[\n        nodeType as Exclude<\n          t.Node[\"type\"],\n          // removed\n          | \"Noop\"\n          // renamed\n          | t.DeprecatedAliases[\"type\"]\n        >\n      ];\n    if (printMethod === undefined) {\n      throw new ReferenceError(\n        `unknown node of type ${JSON.stringify(\n          nodeType,\n        )} with constructor ${JSON.stringify(node.constructor.name)}`,\n      );\n    }\n\n    this._printStack.push(node);\n\n    const oldInAux = this._insideAux;\n    this._insideAux = node.loc == undefined;\n    this._maybeAddAuxComment(this._insideAux && !oldInAux);\n\n    let shouldPrintParens = false;\n    if (\n      format.retainFunctionParens &&\n      nodeType === \"FunctionExpression\" &&\n      node.extra &&\n      node.extra.parenthesized\n    ) {\n      shouldPrintParens = true;\n    } else {\n      shouldPrintParens = needsParens(node, parent, this._printStack);\n    }\n    if (shouldPrintParens) this.token(\"(\");\n\n    this._lastCommentLine = 0;\n\n    this._printLeadingComments(node);\n\n    const loc = nodeType === \"Program\" || nodeType === \"File\" ? null : node.loc;\n\n    this.exactSource(loc, printMethod.bind(this, node, parent));\n\n    if (noLineTerminator && !this._noLineTerminator) {\n      this._noLineTerminator = true;\n      this._printTrailingComments(node);\n      this._noLineTerminator = false;\n    } else {\n      this._printTrailingComments(node, trailingCommentsLineOffset);\n    }\n\n    if (shouldPrintParens) this.token(\")\");\n\n    // end\n    this._printStack.pop();\n\n    format.concise = oldConcise;\n    this._insideAux = oldInAux;\n  }\n\n  _maybeAddAuxComment(enteredPositionlessNode?: boolean) {\n    if (enteredPositionlessNode) this._printAuxBeforeComment();\n    if (!this._insideAux) this._printAuxAfterComment();\n  }\n\n  _printAuxBeforeComment() {\n    if (this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = true;\n\n    const comment = this.format.auxiliaryCommentBefore;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  _printAuxAfterComment() {\n    if (!this._printAuxAfterOnNextUserNode) return;\n    this._printAuxAfterOnNextUserNode = false;\n\n    const comment = this.format.auxiliaryCommentAfter;\n    if (comment) {\n      this._printComment(\n        {\n          type: \"CommentBlock\",\n          value: comment,\n        },\n        COMMENT_SKIP_NEWLINE.DEFAULT,\n      );\n    }\n  }\n\n  getPossibleRaw(\n    node:\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral\n      | t.DecimalLiteral\n      | t.DirectiveLiteral\n      | t.JSXText,\n  ): string | undefined {\n    const extra = node.extra;\n    if (\n      extra &&\n      extra.raw != null &&\n      extra.rawValue != null &&\n      node.value === extra.rawValue\n    ) {\n      // @ts-expect-error: The extra.raw of these AST node types must be a string\n      return extra.raw;\n    }\n  }\n\n  printJoin(\n    nodes: Array<t.Node> | undefined | null,\n    parent: t.Node,\n    opts: PrintJoinOptions = {},\n  ) {\n    if (!nodes?.length) return;\n\n    if (opts.indent) this.indent();\n\n    const newlineOpts: AddNewlinesOptions = {\n      addNewlines: opts.addNewlines,\n      nextNodeStartLine: 0,\n    };\n\n    const separator = opts.separator ? opts.separator.bind(this) : null;\n\n    const len = nodes.length;\n    for (let i = 0; i < len; i++) {\n      const node = nodes[i];\n      if (!node) continue;\n\n      if (opts.statement) this._printNewline(i === 0, newlineOpts);\n\n      this.print(node, parent, undefined, opts.trailingCommentsLineOffset || 0);\n\n      opts.iterator?.(node, i);\n\n      if (i < len - 1) separator?.();\n\n      if (opts.statement) {\n        if (i + 1 === len) {\n          this.newline(1);\n        } else {\n          const nextNode = nodes[i + 1];\n          newlineOpts.nextNodeStartLine = nextNode.loc?.start.line || 0;\n\n          this._printNewline(true, newlineOpts);\n        }\n      }\n    }\n\n    if (opts.indent) this.dedent();\n  }\n\n  printAndIndentOnComments(node: t.Node, parent: t.Node) {\n    const indent = node.leadingComments && node.leadingComments.length > 0;\n    if (indent) this.indent();\n    this.print(node, parent);\n    if (indent) this.dedent();\n  }\n\n  printBlock(parent: Extract<t.Node, { body: t.Statement }>) {\n    const node = parent.body;\n\n    if (node.type !== \"EmptyStatement\") {\n      this.space();\n    }\n\n    this.print(node, parent);\n  }\n\n  _printTrailingComments(node: t.Node, lineOffset?: number) {\n    const comments = this._getComments(false, node);\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.TRAILING, comments, node, lineOffset);\n  }\n\n  _printLeadingComments(node: t.Node) {\n    const comments = this._getComments(true, node);\n    if (!comments?.length) return;\n    this._printComments(COMMENT_TYPE.LEADING, comments, node);\n  }\n\n  printInnerComments(node: t.Node, indent = true) {\n    if (!node.innerComments?.length) return;\n    if (indent) this.indent();\n    this._printComments(COMMENT_TYPE.INNER, node.innerComments, node);\n    if (indent) this.dedent();\n  }\n\n  printSequence(\n    nodes: t.Node[],\n    parent: t.Node,\n    opts: PrintSequenceOptions = {},\n  ) {\n    opts.statement = true;\n    return this.printJoin(nodes, parent, opts);\n  }\n\n  printList(items: t.Node[], parent: t.Node, opts: PrintListOptions = {}) {\n    if (opts.separator == null) {\n      opts.separator = commaSeparator;\n    }\n\n    return this.printJoin(items, parent, opts);\n  }\n\n  _printNewline(newLine: boolean, opts: AddNewlinesOptions) {\n    // Fast path since 'this.newline' does nothing when not tracking lines.\n    if (this.format.retainLines || this.format.compact) return;\n\n    // Fast path for concise since 'this.newline' just inserts a space when\n    // concise formatting is in use.\n    if (this.format.concise) {\n      this.space();\n      return;\n    }\n\n    if (!newLine) {\n      return;\n    }\n\n    const startLine = opts.nextNodeStartLine;\n    const lastCommentLine = this._lastCommentLine;\n    if (startLine > 0 && lastCommentLine > 0) {\n      const offset = startLine - lastCommentLine;\n      if (offset >= 0) {\n        this.newline(offset || 1);\n        return;\n      }\n    }\n\n    // don't add newlines at the beginning of the file\n    if (this._buf.hasContent()) {\n      // Here is the logic of the original line wrapping according to the node layout, we are not using it now.\n      // We currently add at most one newline to each node in the list, ignoring `opts.addNewlines`.\n\n      // let lines = 0;\n      // if (!leading) lines++; // always include at least a single line after\n      // if (opts.addNewlines) lines += opts.addNewlines(leading, node) || 0;\n\n      // const needs = leading ? needsWhitespaceBefore : needsWhitespaceAfter;\n      // if (needs(node, parent)) lines++;\n\n      // this.newline(Math.min(2, lines));\n\n      this.newline(1);\n    }\n  }\n\n  _getComments(leading: boolean, node: t.Node) {\n    // Note, we use a boolean flag here instead of passing in the attribute name as it is faster\n    // because this is called extremely frequently.\n    return (\n      (node && (leading ? node.leadingComments : node.trailingComments)) || null\n    );\n  }\n\n  _printComment(comment: t.Comment, skipNewLines: COMMENT_SKIP_NEWLINE) {\n    // Some plugins (such as flow-strip-types) use this to mark comments as removed using the AST-root 'comments' property,\n    // where they can't manually mutate the AST node comment lists.\n    if (comment.ignore) return;\n\n    if (this._printedComments.has(comment)) return;\n\n    if (!this.format.shouldPrintComment(comment.value)) return;\n\n    this._printedComments.add(comment);\n\n    const isBlockComment = comment.type === \"CommentBlock\";\n\n    // Add a newline before and after a block comment, unless explicitly\n    // disallowed\n    const printNewLines =\n      isBlockComment &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.SKIP_ALL &&\n      !this._noLineTerminator;\n\n    if (\n      printNewLines &&\n      this._buf.hasContent() &&\n      skipNewLines !== COMMENT_SKIP_NEWLINE.SKIP_LEADING\n    ) {\n      this.newline(1);\n    }\n\n    const lastCharCode = this.getLastChar();\n    if (\n      lastCharCode !== charCodes.leftSquareBracket &&\n      lastCharCode !== charCodes.leftCurlyBrace\n    ) {\n      this.space();\n    }\n\n    let val;\n    if (isBlockComment) {\n      val = `/*${comment.value}*/`;\n      if (this.format.indent.adjustMultilineComment) {\n        const offset = comment.loc?.start.column;\n        if (offset) {\n          const newlineRegex = new RegExp(\"\\\\n\\\\s{1,\" + offset + \"}\", \"g\");\n          val = val.replace(newlineRegex, \"\\n\");\n        }\n\n        let indentSize = this.format.retainLines\n          ? 0\n          : this._buf.getCurrentColumn();\n\n        if (this._shouldIndent(charCodes.slash) || this.format.retainLines) {\n          indentSize += this._getIndent();\n        }\n\n        val = val.replace(/\\n(?!$)/g, `\\n${\" \".repeat(indentSize)}`);\n      }\n    } else if (!this._noLineTerminator) {\n      val = `//${comment.value}`;\n    } else {\n      val = `/*${comment.value}*/`;\n    }\n\n    // Avoid creating //* comments\n    if (this.endsWith(charCodes.slash)) this._space();\n\n    this.source(\"start\", comment.loc);\n    this._append(val, isBlockComment);\n\n    if (!isBlockComment && !this._noLineTerminator) {\n      this.newline(1, true);\n    }\n\n    if (printNewLines && skipNewLines !== COMMENT_SKIP_NEWLINE.SKIP_TRAILING) {\n      this.newline(1);\n    }\n  }\n\n  _printComments(\n    type: COMMENT_TYPE,\n    comments: readonly t.Comment[],\n    node: t.Node,\n    lineOffset: number = 0,\n  ) {\n    {\n      const nodeLoc = node.loc;\n      const len = comments.length;\n      let hasLoc = !!nodeLoc;\n      const nodeStartLine = hasLoc ? nodeLoc.start.line : 0;\n      const nodeEndLine = hasLoc ? nodeLoc.end.line : 0;\n      let lastLine = 0;\n      let leadingCommentNewline = 0;\n\n      for (let i = 0; i < len; i++) {\n        const comment = comments[i];\n\n        if (hasLoc && comment.loc && !this._printedComments.has(comment)) {\n          const commentStartLine = comment.loc.start.line;\n          const commentEndLine = comment.loc.end.line;\n          if (type === COMMENT_TYPE.LEADING) {\n            let offset = 0;\n            if (i === 0) {\n              // Because currently we cannot handle blank lines before leading comments,\n              // we always wrap before and after multi-line comments.\n              if (\n                this._buf.hasContent() &&\n                (comment.type === \"CommentLine\" ||\n                  commentStartLine != commentEndLine)\n              ) {\n                offset = leadingCommentNewline = 1;\n              }\n            } else {\n              offset = commentStartLine - lastLine;\n            }\n            lastLine = commentEndLine;\n\n            this.newline(offset);\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.SKIP_ALL);\n\n            if (i + 1 === len) {\n              this.newline(\n                Math.max(nodeStartLine - lastLine, leadingCommentNewline),\n              );\n              lastLine = nodeStartLine;\n            }\n          } else if (type === COMMENT_TYPE.INNER) {\n            const offset =\n              commentStartLine - (i === 0 ? nodeStartLine : lastLine);\n            lastLine = commentEndLine;\n\n            this.newline(offset);\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.SKIP_ALL);\n\n            if (i + 1 === len) {\n              this.newline(Math.min(1, nodeEndLine - lastLine)); // TODO: Improve here when inner comments processing is stronger\n              lastLine = nodeEndLine;\n            }\n          } else {\n            const offset =\n              commentStartLine -\n              (i === 0 ? nodeEndLine - lineOffset : lastLine);\n            lastLine = commentEndLine;\n\n            this.newline(offset);\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.SKIP_ALL);\n          }\n        } else {\n          hasLoc = false;\n\n          if (len === 1) {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.SKIP_ALL);\n          } else if (\n            type === COMMENT_TYPE.INNER &&\n            !(node.type === \"ObjectExpression\" && node.properties.length > 1) &&\n            node.type !== \"ClassBody\"\n          ) {\n            // class X {\n            //   /*:: a: number*/\n            //   /*:: b: ?string*/\n            // }\n\n            this._printComment(\n              comment,\n              i === 0\n                ? COMMENT_SKIP_NEWLINE.SKIP_LEADING\n                : i === len - 1\n                ? COMMENT_SKIP_NEWLINE.SKIP_TRAILING\n                : COMMENT_SKIP_NEWLINE.DEFAULT,\n            );\n          } else {\n            this._printComment(comment, COMMENT_SKIP_NEWLINE.DEFAULT);\n          }\n        }\n      }\n\n      if (type === COMMENT_TYPE.TRAILING && hasLoc && lastLine) {\n        this._lastCommentLine = lastLine;\n      }\n    }\n  }\n  // todo(flow->ts): was Node\n  printAssertions(node: Extract<t.Node, { assertions?: t.ImportAttribute[] }>) {\n    if (node.assertions?.length) {\n      this.space();\n      this.word(\"assert\");\n      this.space();\n      this.token(\"{\");\n      this.space();\n      this.printList(node.assertions, node);\n      this.space();\n      this.token(\"}\");\n    }\n  }\n}\n\n// Expose the node type functions and helpers on the prototype for easy usage.\nObject.assign(Printer.prototype, generatorFunctions);\n\nif (!process.env.BABEL_8_BREAKING) {\n  // @ts-ignore(Babel 7 vs Babel 8) Babel 7 has Noop print method\n  Printer.prototype.Noop = function Noop(this: Printer) {};\n}\n\ntype GeneratorFunctions = typeof generatorFunctions;\ninterface Printer extends GeneratorFunctions {}\nexport default Printer;\n\nfunction commaSeparator(this: Printer) {\n  this.token(\",\");\n  this.space();\n}\n"], "mappings": ";;;;;;;AAAA;;AAEA;;AAQA;;AAIA,MAAMA,mBAAmB,GAAG,IAA5B;AACA,MAAMC,oBAAoB,GAAG,OAA7B;AACA,MAAMC,mBAAmB,GAAG,SAA5B;AACA,MAAMC,kBAAkB,GAAG,sBAA3B;AAEA,MAAM;EAAEC;AAAF,IAAkBC,CAAxB;;AAgEA,MAAMC,OAAN,CAAc;EACZC,WAAW,CAACC,MAAD,EAAiBC,GAAjB,EAAiC;IAAA,KAS5CC,yBAT4C,GASR,CATQ;IAAA,KAY5CC,WAZ4C,GAYf,EAZe;IAAA,KAa5CC,OAb4C,GAa1B,CAb0B;IAAA,KAc5CC,WAd4C,GActB,CAdsB;IAAA,KAe5CC,aAf4C,GAepB,CAfoB;IAAA,KAgB5CC,UAhB4C,GAgBtB,KAhBsB;IAAA,KAiB5CC,sBAjB4C,GAiBU,IAjBV;IAAA,KAkB5CC,iBAlB4C,GAkBf,KAlBe;IAAA,KAmB5CC,4BAnB4C,GAmBJ,KAnBI;IAAA,KAoB5CC,gBApB4C,GAoBzB,IAAIC,GAAJ,EApByB;IAAA,KAqB5CC,gBArB4C,GAqBzB,KArByB;IAAA,KAsB5CC,aAtB4C,GAsB5B,KAtB4B;IAAA,KAuB5CC,gBAvB4C,GAuBzB,CAvByB;IAC1C,KAAKf,MAAL,GAAcA,MAAd;IACA,KAAKgB,IAAL,GAAY,IAAIC,eAAJ,CAAWhB,GAAX,CAAZ;IAEA,KAAKI,WAAL,GAAmBL,MAAM,CAACkB,MAAP,CAAcC,KAAd,CAAoBC,UAApB,CAA+B,CAA/B,CAAnB;IACA,KAAKd,aAAL,GAAqBN,MAAM,CAACkB,MAAP,CAAcC,KAAd,CAAoBE,MAAzC;EACD;;EAmBDC,QAAQ,CAACC,GAAD,EAAc;IACpB,KAAKC,KAAL,CAAWD,GAAX;;IACA,KAAKE,mBAAL;;IAEA,OAAO,KAAKT,IAAL,CAAUU,GAAV,EAAP;EACD;;EAMDR,MAAM,GAAS;IACb,IAAI,KAAKlB,MAAL,CAAY2B,OAAZ,IAAuB,KAAK3B,MAAL,CAAY4B,OAAvC,EAAgD;IAEhD,KAAKxB,OAAL;EACD;;EAMDyB,MAAM,GAAS;IACb,IAAI,KAAK7B,MAAL,CAAY2B,OAAZ,IAAuB,KAAK3B,MAAL,CAAY4B,OAAvC,EAAgD;IAEhD,KAAKxB,OAAL;EACD;;EAMD0B,SAAS,CAACC,KAAc,GAAG,KAAlB,EAA+B;IACtC,KAAKN,mBAAL;;IACA,IAAIM,KAAJ,EAAW;MACT,KAAKC,WAAL;IACD,CAFD,MAEO;MACL,KAAKC,MAAL;IACD;EACF;;EAMDC,UAAU,GAAS;IACjB,IAAI,KAAKlC,MAAL,CAAYmC,QAAhB,EAA0B;MACxB,KAAKnB,IAAL,CAAUoB,mBAAV;IACD;;IACD,KAAKC,SAAL;EACD;;EAMDC,KAAK,CAACP,KAAc,GAAG,KAAlB,EAA+B;IAClC,IAAI,KAAK/B,MAAL,CAAY2B,OAAhB,EAAyB;;IAEzB,IAAII,KAAJ,EAAW;MACT,KAAKQ,MAAL;IACD,CAFD,MAEO,IAAI,KAAKvB,IAAL,CAAUwB,UAAV,EAAJ,EAA4B;MACjC,MAAMC,MAAM,GAAG,KAAKC,WAAL,EAAf;;MACA,IAAID,MAAM,OAAN,IAA8BA,MAAM,OAAxC,EAAiE;QAC/D,KAAKF,MAAL;MACD;IACF;EACF;;EAMDI,IAAI,CAACC,GAAD,EAAoB;IAEtB,IACE,KAAK9B,aAAL,IACC8B,GAAG,CAACxB,UAAJ,CAAe,CAAf,YAAyC,KAAKyB,QAAL,IAF5C,EAGE;MACA,KAAKN,MAAL;IACD;;IAED,KAAKd,mBAAL;;IACA,KAAKqB,OAAL,CAAaF,GAAb,EAAkB,KAAlB;;IAEA,KAAK9B,aAAL,GAAqB,IAArB;EACD;;EAMDiC,MAAM,CAACH,GAAD,EAAoB;IACxB,KAAKD,IAAL,CAAUC,GAAV;IAIA,KAAK/B,gBAAL,GACEmC,MAAM,CAACC,SAAP,CAAiB,CAACL,GAAlB,KACA,CAAClD,mBAAmB,CAACwD,IAApB,CAAyBN,GAAzB,CADD,IAEA,CAACpD,mBAAmB,CAAC0D,IAApB,CAAyBN,GAAzB,CAFD,IAGA,CAACnD,oBAAoB,CAACyD,IAArB,CAA0BN,GAA1B,CAHD,IAIAA,GAAG,CAACxB,UAAJ,CAAewB,GAAG,CAACvB,MAAJ,GAAa,CAA5B,QALF;EAMD;;EAMDgB,KAAK,CAACO,GAAD,EAAcO,YAAY,GAAG,KAA7B,EAA0C;IAG7C,MAAMC,QAAQ,GAAG,KAAKV,WAAL,EAAjB;IACA,MAAMW,QAAQ,GAAGT,GAAG,CAACxB,UAAJ,CAAe,CAAf,CAAjB;;IACA,IACGgC,QAAQ,OAAR,IAA0CR,GAAG,KAAK,IAAnD,IAECS,QAAQ,OAAR,IAAmCD,QAAQ,OAF5C,IAGCC,QAAQ,OAAR,IAA+BD,QAAQ,OAHxC,IAKCC,QAAQ,OAAR,IAA8B,KAAKxC,gBANtC,EAOE;MACA,KAAK0B,MAAL;IACD;;IAED,KAAKd,mBAAL;;IACA,KAAKqB,OAAL,CAAaF,GAAb,EAAkBO,YAAlB;EACD;;EAEDG,SAAS,CAACC,IAAD,EAAqB;IAG5B,MAAMH,QAAQ,GAAG,KAAKV,WAAL,EAAjB;;IACA,IAEGa,IAAI,OAAJ,IAA+BH,QAAQ,OAAxC,IACCG,IAAI,OAAJ,IAA2BH,QAAQ,OADpC,IAGCG,IAAI,OAAJ,IAA0B,KAAK1C,gBALlC,EAME;MACA,KAAK0B,MAAL;IACD;;IAED,KAAKd,mBAAL;;IACA,KAAKO,WAAL,CAAiBuB,IAAjB;EACD;;EAQDC,OAAO,CAACC,CAAS,GAAG,CAAb,EAAgB1B,KAAhB,EAAuC;IAC5C,IAAI0B,CAAC,IAAI,CAAT,EAAY;;IAEZ,IAAI,CAAC1B,KAAL,EAAY;MACV,IAAI,KAAK/B,MAAL,CAAY0D,WAAZ,IAA2B,KAAK1D,MAAL,CAAY2B,OAA3C,EAAoD;;MAEpD,IAAI,KAAK3B,MAAL,CAAY4B,OAAhB,EAAyB;QACvB,KAAKU,KAAL;QACA;MACD;IACF;;IAED,IAAImB,CAAC,GAAG,CAAR,EAAWA,CAAC,GAAG,CAAJ;IAEXA,CAAC,IAAI,KAAKzC,IAAL,CAAU2C,eAAV,EAAL;;IAEA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,EAAxB,EAA4B;MAC1B,KAAKC,QAAL;IACD;;IAED;EACD;;EAEDhB,QAAQ,CAACU,IAAD,EAAwB;IAC9B,OAAO,KAAKb,WAAL,OAAuBa,IAA9B;EACD;;EAEDb,WAAW,GAAW;IACpB,OAAO,KAAK1B,IAAL,CAAU0B,WAAV,EAAP;EACD;;EAEDoB,sBAAsB,GAAW;IAC/B,OAAO,KAAK9C,IAAL,CAAU8C,sBAAV,EAAP;EACD;;EAEDC,qBAAqB,GAAS;IAC5B,KAAK/C,IAAL,CAAU+C,qBAAV;EACD;;EAEDC,WAAW,CAACC,GAAD,EAAuBC,EAAvB,EAAuC;IAChD,IAAI,CAACD,GAAL,EAAU,OAAOC,EAAE,EAAT;;IAEV,KAAKC,QAAL,CAAc,OAAd,EAAuBF,GAAvB;;IAEA,KAAKjD,IAAL,CAAUgD,WAAV,CAAsBC,GAAtB,EAA2BC,EAA3B;EACD;;EAEDE,MAAM,CAACC,IAAD,EAAwBJ,GAAxB,EAAoD;IACxD,IAAI,CAACA,GAAL,EAAU;;IAEV,KAAKE,QAAL,CAAcE,IAAd,EAAoBJ,GAApB;;IAEA,KAAKjD,IAAL,CAAUoD,MAAV,CAAiBC,IAAjB,EAAuBJ,GAAvB;EACD;;EAEDK,gBAAgB,CACdD,IADc,EAEdJ,GAFc,EAGdM,UAHc,EAIdC,YAJc,EAKR;IACN,IAAI,CAACP,GAAL,EAAU;;IAEV,KAAKE,QAAL,CAAcE,IAAd,EAAoBJ,GAApB;;IAEA,KAAKjD,IAAL,CAAUsD,gBAAV,CAA2BD,IAA3B,EAAiCJ,GAAjC,EAAsCM,UAAtC,EAAkDC,YAAlD;EACD;;EAEDC,UAAU,CACRJ,IADQ,EAERJ,GAFQ,EAGRC,EAHQ,EAIF;IACN,IAAI,CAACD,GAAL,EAAU,OAAOC,EAAE,EAAT;;IAEV,KAAKC,QAAL,CAAcE,IAAd,EAAoBJ,GAApB;;IAEA,KAAKjD,IAAL,CAAUyD,UAAV,CAAqBJ,IAArB,EAA2BJ,GAA3B,EAAgCC,EAAhC;EACD;;EAED3B,MAAM,GAAS;IACb,KAAKN,MAAL;EACD;;EAED4B,QAAQ,GAAS;IACf,KAAK5B,MAAL;EACD;;EAEDa,OAAO,CAACF,GAAD,EAAcO,YAAd,EAA2C;IAChD,KAAKuB,cAAL,CAAoB9B,GAApB;;IACA,KAAK+B,YAAL,CAAkB/B,GAAG,CAACxB,UAAJ,CAAe,CAAf,CAAlB;;IAEA,KAAKJ,IAAL,CAAU4D,MAAV,CAAiBhC,GAAjB,EAAsBO,YAAtB;;IAEA,KAAKrC,aAAL,GAAqB,KAArB;IACA,KAAKD,gBAAL,GAAwB,KAAxB;EACD;;EAEDmB,WAAW,CAACuB,IAAD,EAAqB;IAC9B,KAAKsB,kBAAL,CAAwBtB,IAAxB;;IACA,KAAKoB,YAAL,CAAkBpB,IAAlB;;IAEA,KAAKvC,IAAL,CAAU8D,UAAV,CAAqBvB,IAArB;;IAEA,KAAKzC,aAAL,GAAqB,KAArB;IACA,KAAKD,gBAAL,GAAwB,KAAxB;EACD;;EAEDoB,MAAM,CAACsB,IAAD,EAAe;IACnB,KAAKsB,kBAAL,CAAwBtB,IAAxB;;IACA,KAAKoB,YAAL,CAAkBpB,IAAlB;;IAEA,KAAKvC,IAAL,CAAU+D,KAAV,CAAgBxB,IAAhB;;IAEA,KAAKzC,aAAL,GAAqB,KAArB;IACA,KAAKD,gBAAL,GAAwB,KAAxB;EACD;;EAED8D,YAAY,CAACK,SAAD,EAA0B;IAEpC,IACE,KAAK5E,OAAL,IACA4E,SAAS,OADT,IAEA,KAAKnC,QAAL,IAHF,EAIE;MACA,KAAK7B,IAAL,CAAUiE,gBAAV,CAA2B,KAAK5E,WAAhC,EAA6C,KAAK6E,UAAL,EAA7C;IACD;EACF;;EAEDC,aAAa,CAACH,SAAD,EAAoB;IAE/B,IACE,KAAK5E,OAAL,IACA4E,SAAS,OADT,IAEA,KAAKnC,QAAL,IAHF,EAIE;MACA,OAAO,IAAP;IACD;EACF;;EAEDgC,kBAAkB,CAACtB,IAAD,EAAqB;IAErC,MAAM6B,qBAAqB,GAAG,KAAK5E,sBAAnC;IACA,IAAI,CAAC4E,qBAAL,EAA4B;;IAS5B,IAAI7B,IAAI,OAAR,EAA8B;MAE5B;IACD;;IAGD,IAAIA,IAAI,OAAR,EAAiC;MAC/B,KAAK/C,sBAAL,GAA8B,IAA9B;MACA;IACD;;IAED,KAAK6B,SAAL;IACA,KAAKnB,MAAL;IACAkE,qBAAqB,CAACC,OAAtB,GAAgC,IAAhC;EACD;;EAEDX,cAAc,CAAC9B,GAAD,EAAoB;IAEhC,MAAMwC,qBAAqB,GAAG,KAAK5E,sBAAnC;IACA,IAAI,CAAC4E,qBAAL,EAA4B;IAS5B,MAAME,GAAG,GAAG1C,GAAG,CAACvB,MAAhB;IAEA,IAAIoC,CAAJ;;IACA,KAAKA,CAAC,GAAG,CAAT,EAAYA,CAAC,GAAG6B,GAAJ,IAAW1C,GAAG,CAACxB,UAAJ,CAAeqC,CAAf,QAAvB,EAA8DA,CAAC,EAA/D,EAAmE;;IACnE,IAAIA,CAAC,KAAK6B,GAAV,EAAe;MAEb;IACD;;IAGD,MAAMC,GAAG,GAAG3C,GAAG,CAACxB,UAAJ,CAAeqC,CAAf,CAAZ;;IACA,IAAI8B,GAAG,OAAP,EAAgC;MAC9B,IAEEA,GAAG,OAAH,IAEA9B,CAAC,GAAG,CAAJ,KAAU6B,GAJZ,EAKE;QAEA,KAAK9E,sBAAL,GAA8B,IAA9B;QACA;MACD;;MAED,MAAMgF,OAAO,GAAG5C,GAAG,CAACxB,UAAJ,CAAeqC,CAAC,GAAG,CAAnB,CAAhB;;MAEA,IAAI+B,OAAO,OAAX,EAAoC;QAGlC,IAAI7F,kBAAkB,CAACuD,IAAnB,CAAwBN,GAAG,CAAC6C,KAAJ,CAAUhC,CAAC,GAAG,CAAd,EAAiB6B,GAAG,GAAG,CAAvB,CAAxB,CAAJ,EAAwD;UAKtD;QACD;MAGF,CAZD,MAYO,IAAIE,OAAO,OAAX,EAAiC;QAGtC,KAAKhF,sBAAL,GAA8B,IAA9B;QACA;MACD;IACF;;IAED,KAAK6B,SAAL;IACA,KAAKnB,MAAL;IACAkE,qBAAqB,CAACC,OAAtB,GAAgC,IAAhC;EACD;;EAEDK,OAAO,CAACC,IAAD,EAAe;IACpB,IAAI,CAAC,KAAK3F,MAAL,CAAY0D,WAAjB,EAA8B;;IAG9B,MAAMkC,KAAK,GAAGD,IAAI,GAAG,KAAK3E,IAAL,CAAU6E,cAAV,EAArB;;IAEA,KAAK,IAAIpC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmC,KAApB,EAA2BnC,CAAC,EAA5B,EAAgC;MAC9B,KAAKI,QAAL;IACD;EACF;;EAEDM,QAAQ,CAACE,IAAD,EAAwBJ,GAAxB,EAAmC;IACzC,IAAI,CAAC,KAAKjE,MAAL,CAAY0D,WAAjB,EAA8B;IAG9B,MAAMoC,GAAG,GAAG7B,GAAG,GAAGA,GAAG,CAACI,IAAD,CAAN,GAAe,IAA9B;;IACA,IAAI,CAAAyB,GAAG,QAAH,YAAAA,GAAG,CAAEH,IAAL,KAAa,IAAjB,EAAuB;MACrB,MAAMC,KAAK,GAAGE,GAAG,CAACH,IAAJ,GAAW,KAAK3E,IAAL,CAAU6E,cAAV,EAAzB;;MAEA,KAAK,IAAIpC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmC,KAApB,EAA2BnC,CAAC,EAA5B,EAAgC;QAC9B,KAAKI,QAAL;MACD;IACF;EACF;;EAMDqB,UAAU,GAAW;IACnB,OAAO,KAAK5E,aAAL,GAAqB,KAAKF,OAAjC;EACD;;EAED2F,sBAAsB,CAACC,EAAD,EAAiB;IACrC,MAAM;MAAEvF;IAAF,IAAwB,IAA9B;IACA,KAAKA,iBAAL,GAAyB,IAAzB;IACAuF,EAAE;IACF,KAAKvF,iBAAL,GAAyBA,iBAAzB;EACD;;EAEDwF,mBAAmB,CAACC,IAAD,EAAeC,MAAf,EAA+BC,OAA/B,EAAiD;IAgBlE,IAAIA,OAAJ,EAAa;MACX,KAAKL,sBAAL,CAA4B,MAAM;QAChC,KAAKvE,KAAL,CAAW0E,IAAX,EAAiBC,MAAjB;MACD,CAFD;IAGD,CAJD,MAIO;MACL,MAAME,eAAe,GAAG;QACtBhB,OAAO,EAAE;MADa,CAAxB;MAGA,KAAK7E,sBAAL,GAA8B6F,eAA9B;MACA,KAAK7E,KAAL,CAAW0E,IAAX,EAAiBC,MAAjB;;MAIA,IAAIE,eAAe,CAAChB,OAApB,EAA6B;QAC3B,KAAKxD,MAAL;QACA,KAAK2B,OAAL;QACA,KAAKnB,SAAL;MACD;IACF;EACF;;EAEDb,KAAK,CACH0E,IADG,EAEHC,MAFG,EAGHG,gBAHG,EAMHC,0BANG,EAOH;IACA,IAAI,CAACL,IAAL,EAAW;IAEX,MAAMM,QAAQ,GAAGN,IAAI,CAACO,IAAtB;IACA,MAAMzG,MAAM,GAAG,KAAKA,MAApB;IAEA,MAAM0G,UAAU,GAAG1G,MAAM,CAAC4B,OAA1B;;IACA,IAEEsE,IAAI,CAACS,QAFP,EAGE;MACA3G,MAAM,CAAC4B,OAAP,GAAiB,IAAjB;IACD;;IAED,MAAMgF,WAAW,GACf,KACEJ,QADF,CADF;;IAUA,IAAII,WAAW,KAAKC,SAApB,EAA+B;MAC7B,MAAM,IAAIC,cAAJ,CACH,wBAAuBC,IAAI,CAACC,SAAL,CACtBR,QADsB,CAEtB,qBAAoBO,IAAI,CAACC,SAAL,CAAed,IAAI,CAACnG,WAAL,CAAiBkH,IAAhC,CAAsC,EAHxD,CAAN;IAKD;;IAED,KAAK9G,WAAL,CAAiB+G,IAAjB,CAAsBhB,IAAtB;;IAEA,MAAMiB,QAAQ,GAAG,KAAK5G,UAAtB;IACA,KAAKA,UAAL,GAAkB2F,IAAI,CAACjC,GAAL,IAAY4C,SAA9B;;IACA,KAAKpF,mBAAL,CAAyB,KAAKlB,UAAL,IAAmB,CAAC4G,QAA7C;;IAEA,IAAIC,iBAAiB,GAAG,KAAxB;;IACA,IACEpH,MAAM,CAACqH,oBAAP,IACAb,QAAQ,KAAK,oBADb,IAEAN,IAAI,CAACoB,KAFL,IAGApB,IAAI,CAACoB,KAAL,CAAWC,aAJb,EAKE;MACAH,iBAAiB,GAAG,IAApB;IACD,CAPD,MAOO;MACLA,iBAAiB,GAAGxH,WAAW,CAACsG,IAAD,EAAOC,MAAP,EAAe,KAAKhG,WAApB,CAA/B;IACD;;IACD,IAAIiH,iBAAJ,EAAuB,KAAK/E,SAAL;IAEvB,KAAKtB,gBAAL,GAAwB,CAAxB;;IAEA,KAAKyG,qBAAL,CAA2BtB,IAA3B;;IAEA,MAAMjC,GAAG,GAAGuC,QAAQ,KAAK,SAAb,IAA0BA,QAAQ,KAAK,MAAvC,GAAgD,IAAhD,GAAuDN,IAAI,CAACjC,GAAxE;IAEA,KAAKD,WAAL,CAAiBC,GAAjB,EAAsB2C,WAAW,CAACa,IAAZ,CAAiB,IAAjB,EAAuBvB,IAAvB,EAA6BC,MAA7B,CAAtB;;IAEA,IAAIG,gBAAgB,IAAI,CAAC,KAAK7F,iBAA9B,EAAiD;MAC/C,KAAKA,iBAAL,GAAyB,IAAzB;;MACA,KAAKiH,sBAAL,CAA4BxB,IAA5B;;MACA,KAAKzF,iBAAL,GAAyB,KAAzB;IACD,CAJD,MAIO;MACL,KAAKiH,sBAAL,CAA4BxB,IAA5B,EAAkCK,0BAAlC;IACD;;IAED,IAAIa,iBAAJ,EAAuB,KAAK/E,SAAL;;IAGvB,KAAKlC,WAAL,CAAiBwH,GAAjB;;IAEA3H,MAAM,CAAC4B,OAAP,GAAiB8E,UAAjB;IACA,KAAKnG,UAAL,GAAkB4G,QAAlB;EACD;;EAED1F,mBAAmB,CAACmG,uBAAD,EAAoC;IACrD,IAAIA,uBAAJ,EAA6B,KAAKC,sBAAL;IAC7B,IAAI,CAAC,KAAKtH,UAAV,EAAsB,KAAKuH,qBAAL;EACvB;;EAEDD,sBAAsB,GAAG;IACvB,IAAI,KAAKnH,4BAAT,EAAuC;IACvC,KAAKA,4BAAL,GAAoC,IAApC;IAEA,MAAMqH,OAAO,GAAG,KAAK/H,MAAL,CAAYgI,sBAA5B;;IACA,IAAID,OAAJ,EAAa;MACX,KAAKE,aAAL,CACE;QACExB,IAAI,EAAE,cADR;QAEEyB,KAAK,EAAEH;MAFT,CADF;IAOD;EACF;;EAEDD,qBAAqB,GAAG;IACtB,IAAI,CAAC,KAAKpH,4BAAV,EAAwC;IACxC,KAAKA,4BAAL,GAAoC,KAApC;IAEA,MAAMqH,OAAO,GAAG,KAAK/H,MAAL,CAAYmI,qBAA5B;;IACA,IAAIJ,OAAJ,EAAa;MACX,KAAKE,aAAL,CACE;QACExB,IAAI,EAAE,cADR;QAEEyB,KAAK,EAAEH;MAFT,CADF;IAOD;EACF;;EAEDK,cAAc,CACZlC,IADY,EAQQ;IACpB,MAAMoB,KAAK,GAAGpB,IAAI,CAACoB,KAAnB;;IACA,IACEA,KAAK,IACLA,KAAK,CAACe,GAAN,IAAa,IADb,IAEAf,KAAK,CAACgB,QAAN,IAAkB,IAFlB,IAGApC,IAAI,CAACgC,KAAL,KAAeZ,KAAK,CAACgB,QAJvB,EAKE;MAEA,OAAOhB,KAAK,CAACe,GAAb;IACD;EACF;;EAEDE,SAAS,CACPC,KADO,EAEPrC,MAFO,EAGPsC,IAAsB,GAAG,EAHlB,EAIP;IACA,IAAI,EAACD,KAAD,YAACA,KAAK,CAAEnH,MAAR,CAAJ,EAAoB;IAEpB,IAAIoH,IAAI,CAACvH,MAAT,EAAiB,KAAKA,MAAL;IAEjB,MAAMwH,WAA+B,GAAG;MACtCC,WAAW,EAAEF,IAAI,CAACE,WADoB;MAEtCC,iBAAiB,EAAE;IAFmB,CAAxC;IAKA,MAAMC,SAAS,GAAGJ,IAAI,CAACI,SAAL,GAAiBJ,IAAI,CAACI,SAAL,CAAepB,IAAf,CAAoB,IAApB,CAAjB,GAA6C,IAA/D;IAEA,MAAMnC,GAAG,GAAGkD,KAAK,CAACnH,MAAlB;;IACA,KAAK,IAAIoC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6B,GAApB,EAAyB7B,CAAC,EAA1B,EAA8B;MAC5B,MAAMyC,IAAI,GAAGsC,KAAK,CAAC/E,CAAD,CAAlB;MACA,IAAI,CAACyC,IAAL,EAAW;MAEX,IAAIuC,IAAI,CAACK,SAAT,EAAoB,KAAKC,aAAL,CAAmBtF,CAAC,KAAK,CAAzB,EAA4BiF,WAA5B;MAEpB,KAAKlH,KAAL,CAAW0E,IAAX,EAAiBC,MAAjB,EAAyBU,SAAzB,EAAoC4B,IAAI,CAAClC,0BAAL,IAAmC,CAAvE;MAEAkC,IAAI,CAACO,QAAL,oBAAAP,IAAI,CAACO,QAAL,CAAgB9C,IAAhB,EAAsBzC,CAAtB;MAEA,IAAIA,CAAC,GAAG6B,GAAG,GAAG,CAAd,EAAiBuD,SAAS,QAAT,YAAAA,SAAS;;MAE1B,IAAIJ,IAAI,CAACK,SAAT,EAAoB;QAClB,IAAIrF,CAAC,GAAG,CAAJ,KAAU6B,GAAd,EAAmB;UACjB,KAAK9B,OAAL,CAAa,CAAb;QACD,CAFD,MAEO;UAAA;;UACL,MAAMyF,QAAQ,GAAGT,KAAK,CAAC/E,CAAC,GAAG,CAAL,CAAtB;UACAiF,WAAW,CAACE,iBAAZ,GAAgC,kBAAAK,QAAQ,CAAChF,GAAT,mCAAciF,KAAd,CAAoBvD,IAApB,KAA4B,CAA5D;;UAEA,KAAKoD,aAAL,CAAmB,IAAnB,EAAyBL,WAAzB;QACD;MACF;IACF;;IAED,IAAID,IAAI,CAACvH,MAAT,EAAiB,KAAKW,MAAL;EAClB;;EAEDsH,wBAAwB,CAACjD,IAAD,EAAeC,MAAf,EAA+B;IACrD,MAAMjF,MAAM,GAAGgF,IAAI,CAACkD,eAAL,IAAwBlD,IAAI,CAACkD,eAAL,CAAqB/H,MAArB,GAA8B,CAArE;IACA,IAAIH,MAAJ,EAAY,KAAKA,MAAL;IACZ,KAAKM,KAAL,CAAW0E,IAAX,EAAiBC,MAAjB;IACA,IAAIjF,MAAJ,EAAY,KAAKW,MAAL;EACb;;EAEDwH,UAAU,CAAClD,MAAD,EAAiD;IACzD,MAAMD,IAAI,GAAGC,MAAM,CAACmD,IAApB;;IAEA,IAAIpD,IAAI,CAACO,IAAL,KAAc,gBAAlB,EAAoC;MAClC,KAAKnE,KAAL;IACD;;IAED,KAAKd,KAAL,CAAW0E,IAAX,EAAiBC,MAAjB;EACD;;EAEDuB,sBAAsB,CAACxB,IAAD,EAAe3B,UAAf,EAAoC;IACxD,MAAMgF,QAAQ,GAAG,KAAKC,YAAL,CAAkB,KAAlB,EAAyBtD,IAAzB,CAAjB;;IACA,IAAI,EAACqD,QAAD,YAACA,QAAQ,CAAElI,MAAX,CAAJ,EAAuB;;IACvB,KAAKoI,cAAL,IAA2CF,QAA3C,EAAqDrD,IAArD,EAA2D3B,UAA3D;EACD;;EAEDiD,qBAAqB,CAACtB,IAAD,EAAe;IAClC,MAAMqD,QAAQ,GAAG,KAAKC,YAAL,CAAkB,IAAlB,EAAwBtD,IAAxB,CAAjB;;IACA,IAAI,EAACqD,QAAD,YAACA,QAAQ,CAAElI,MAAX,CAAJ,EAAuB;;IACvB,KAAKoI,cAAL,IAA0CF,QAA1C,EAAoDrD,IAApD;EACD;;EAEDwD,kBAAkB,CAACxD,IAAD,EAAehF,MAAM,GAAG,IAAxB,EAA8B;IAAA;;IAC9C,IAAI,yBAACgF,IAAI,CAACyD,aAAN,aAAC,oBAAoBtI,MAArB,CAAJ,EAAiC;IACjC,IAAIH,MAAJ,EAAY,KAAKA,MAAL;;IACZ,KAAKuI,cAAL,IAAwCvD,IAAI,CAACyD,aAA7C,EAA4DzD,IAA5D;;IACA,IAAIhF,MAAJ,EAAY,KAAKW,MAAL;EACb;;EAED+H,aAAa,CACXpB,KADW,EAEXrC,MAFW,EAGXsC,IAA0B,GAAG,EAHlB,EAIX;IACAA,IAAI,CAACK,SAAL,GAAiB,IAAjB;IACA,OAAO,KAAKP,SAAL,CAAeC,KAAf,EAAsBrC,MAAtB,EAA8BsC,IAA9B,CAAP;EACD;;EAEDoB,SAAS,CAACC,KAAD,EAAkB3D,MAAlB,EAAkCsC,IAAsB,GAAG,EAA3D,EAA+D;IACtE,IAAIA,IAAI,CAACI,SAAL,IAAkB,IAAtB,EAA4B;MAC1BJ,IAAI,CAACI,SAAL,GAAiBkB,cAAjB;IACD;;IAED,OAAO,KAAKxB,SAAL,CAAeuB,KAAf,EAAsB3D,MAAtB,EAA8BsC,IAA9B,CAAP;EACD;;EAEDM,aAAa,CAACiB,OAAD,EAAmBvB,IAAnB,EAA6C;IAExD,IAAI,KAAKzI,MAAL,CAAY0D,WAAZ,IAA2B,KAAK1D,MAAL,CAAY2B,OAA3C,EAAoD;;IAIpD,IAAI,KAAK3B,MAAL,CAAY4B,OAAhB,EAAyB;MACvB,KAAKU,KAAL;MACA;IACD;;IAED,IAAI,CAAC0H,OAAL,EAAc;MACZ;IACD;;IAED,MAAMC,SAAS,GAAGxB,IAAI,CAACG,iBAAvB;IACA,MAAMsB,eAAe,GAAG,KAAKnJ,gBAA7B;;IACA,IAAIkJ,SAAS,GAAG,CAAZ,IAAiBC,eAAe,GAAG,CAAvC,EAA0C;MACxC,MAAMC,MAAM,GAAGF,SAAS,GAAGC,eAA3B;;MACA,IAAIC,MAAM,IAAI,CAAd,EAAiB;QACf,KAAK3G,OAAL,CAAa2G,MAAM,IAAI,CAAvB;QACA;MACD;IACF;;IAGD,IAAI,KAAKnJ,IAAL,CAAUwB,UAAV,EAAJ,EAA4B;MAa1B,KAAKgB,OAAL,CAAa,CAAb;IACD;EACF;;EAEDgG,YAAY,CAACY,OAAD,EAAmBlE,IAAnB,EAAiC;IAG3C,OACGA,IAAI,KAAKkE,OAAO,GAAGlE,IAAI,CAACkD,eAAR,GAA0BlD,IAAI,CAACmE,gBAA3C,CAAL,IAAsE,IADxE;EAGD;;EAEDpC,aAAa,CAACF,OAAD,EAAqBuC,YAArB,EAAyD;IAGpE,IAAIvC,OAAO,CAACwC,MAAZ,EAAoB;IAEpB,IAAI,KAAK5J,gBAAL,CAAsB6J,GAAtB,CAA0BzC,OAA1B,CAAJ,EAAwC;IAExC,IAAI,CAAC,KAAK/H,MAAL,CAAYyK,kBAAZ,CAA+B1C,OAAO,CAACG,KAAvC,CAAL,EAAoD;;IAEpD,KAAKvH,gBAAL,CAAsB+J,GAAtB,CAA0B3C,OAA1B;;IAEA,MAAM4C,cAAc,GAAG5C,OAAO,CAACtB,IAAR,KAAiB,cAAxC;IAIA,MAAMmE,aAAa,GACjBD,cAAc,IACdL,YAAY,MADZ,IAEA,CAAC,KAAK7J,iBAHR;;IAKA,IACEmK,aAAa,IACb,KAAK5J,IAAL,CAAUwB,UAAV,EADA,IAEA8H,YAAY,MAHd,EAIE;MACA,KAAK9G,OAAL,CAAa,CAAb;IACD;;IAED,MAAMqH,YAAY,GAAG,KAAKnI,WAAL,EAArB;;IACA,IACEmI,YAAY,OAAZ,IACAA,YAAY,QAFd,EAGE;MACA,KAAKvI,KAAL;IACD;;IAED,IAAIwI,GAAJ;;IACA,IAAIH,cAAJ,EAAoB;MAClBG,GAAG,GAAI,KAAI/C,OAAO,CAACG,KAAM,IAAzB;;MACA,IAAI,KAAKlI,MAAL,CAAYkB,MAAZ,CAAmB6J,sBAAvB,EAA+C;QAAA;;QAC7C,MAAMZ,MAAM,mBAAGpC,OAAO,CAAC9D,GAAX,qBAAG,aAAaiF,KAAb,CAAmB8B,MAAlC;;QACA,IAAIb,MAAJ,EAAY;UACV,MAAMc,YAAY,GAAG,IAAIC,MAAJ,CAAW,cAAcf,MAAd,GAAuB,GAAlC,EAAuC,GAAvC,CAArB;UACAW,GAAG,GAAGA,GAAG,CAACK,OAAJ,CAAYF,YAAZ,EAA0B,IAA1B,CAAN;QACD;;QAED,IAAIG,UAAU,GAAG,KAAKpL,MAAL,CAAY0D,WAAZ,GACb,CADa,GAEb,KAAK1C,IAAL,CAAUqK,gBAAV,EAFJ;;QAIA,IAAI,KAAKlG,aAAL,QAAuC,KAAKnF,MAAL,CAAY0D,WAAvD,EAAoE;UAClE0H,UAAU,IAAI,KAAKlG,UAAL,EAAd;QACD;;QAED4F,GAAG,GAAGA,GAAG,CAACK,OAAJ,CAAY,UAAZ,EAAyB,KAAI,IAAIG,MAAJ,CAAWF,UAAX,CAAuB,EAApD,CAAN;MACD;IACF,CAnBD,MAmBO,IAAI,CAAC,KAAK3K,iBAAV,EAA6B;MAClCqK,GAAG,GAAI,KAAI/C,OAAO,CAACG,KAAM,EAAzB;IACD,CAFM,MAEA;MACL4C,GAAG,GAAI,KAAI/C,OAAO,CAACG,KAAM,IAAzB;IACD;;IAGD,IAAI,KAAKrF,QAAL,IAAJ,EAAoC,KAAKN,MAAL;IAEpC,KAAK6B,MAAL,CAAY,OAAZ,EAAqB2D,OAAO,CAAC9D,GAA7B;;IACA,KAAKnB,OAAL,CAAagI,GAAb,EAAkBH,cAAlB;;IAEA,IAAI,CAACA,cAAD,IAAmB,CAAC,KAAKlK,iBAA7B,EAAgD;MAC9C,KAAK+C,OAAL,CAAa,CAAb,EAAgB,IAAhB;IACD;;IAED,IAAIoH,aAAa,IAAIN,YAAY,MAAjC,EAA0E;MACxE,KAAK9G,OAAL,CAAa,CAAb;IACD;EACF;;EAEDiG,cAAc,CACZhD,IADY,EAEZ8C,QAFY,EAGZrD,IAHY,EAIZ3B,UAAkB,GAAG,CAJT,EAKZ;IACA;MACE,MAAMgH,OAAO,GAAGrF,IAAI,CAACjC,GAArB;MACA,MAAMqB,GAAG,GAAGiE,QAAQ,CAAClI,MAArB;MACA,IAAImK,MAAM,GAAG,CAAC,CAACD,OAAf;MACA,MAAME,aAAa,GAAGD,MAAM,GAAGD,OAAO,CAACrC,KAAR,CAAcvD,IAAjB,GAAwB,CAApD;MACA,MAAM+F,WAAW,GAAGF,MAAM,GAAGD,OAAO,CAACI,GAAR,CAAYhG,IAAf,GAAsB,CAAhD;MACA,IAAIiG,QAAQ,GAAG,CAAf;MACA,IAAIC,qBAAqB,GAAG,CAA5B;;MAEA,KAAK,IAAIpI,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG6B,GAApB,EAAyB7B,CAAC,EAA1B,EAA8B;QAC5B,MAAMsE,OAAO,GAAGwB,QAAQ,CAAC9F,CAAD,CAAxB;;QAEA,IAAI+H,MAAM,IAAIzD,OAAO,CAAC9D,GAAlB,IAAyB,CAAC,KAAKtD,gBAAL,CAAsB6J,GAAtB,CAA0BzC,OAA1B,CAA9B,EAAkE;UAChE,MAAM+D,gBAAgB,GAAG/D,OAAO,CAAC9D,GAAR,CAAYiF,KAAZ,CAAkBvD,IAA3C;UACA,MAAMoG,cAAc,GAAGhE,OAAO,CAAC9D,GAAR,CAAY0H,GAAZ,CAAgBhG,IAAvC;;UACA,IAAIc,IAAI,MAAR,EAAmC;YACjC,IAAI0D,MAAM,GAAG,CAAb;;YACA,IAAI1G,CAAC,KAAK,CAAV,EAAa;cAGX,IACE,KAAKzC,IAAL,CAAUwB,UAAV,OACCuF,OAAO,CAACtB,IAAR,KAAiB,aAAjB,IACCqF,gBAAgB,IAAIC,cAFtB,CADF,EAIE;gBACA5B,MAAM,GAAG0B,qBAAqB,GAAG,CAAjC;cACD;YACF,CAVD,MAUO;cACL1B,MAAM,GAAG2B,gBAAgB,GAAGF,QAA5B;YACD;;YACDA,QAAQ,GAAGG,cAAX;YAEA,KAAKvI,OAAL,CAAa2G,MAAb;;YACA,KAAKlC,aAAL,CAAmBF,OAAnB;;YAEA,IAAItE,CAAC,GAAG,CAAJ,KAAU6B,GAAd,EAAmB;cACjB,KAAK9B,OAAL,CACEwI,IAAI,CAACC,GAAL,CAASR,aAAa,GAAGG,QAAzB,EAAmCC,qBAAnC,CADF;cAGAD,QAAQ,GAAGH,aAAX;YACD;UACF,CA1BD,MA0BO,IAAIhF,IAAI,MAAR,EAAiC;YACtC,MAAM0D,MAAM,GACV2B,gBAAgB,IAAIrI,CAAC,KAAK,CAAN,GAAUgI,aAAV,GAA0BG,QAA9B,CADlB;YAEAA,QAAQ,GAAGG,cAAX;YAEA,KAAKvI,OAAL,CAAa2G,MAAb;;YACA,KAAKlC,aAAL,CAAmBF,OAAnB;;YAEA,IAAItE,CAAC,GAAG,CAAJ,KAAU6B,GAAd,EAAmB;cACjB,KAAK9B,OAAL,CAAawI,IAAI,CAACE,GAAL,CAAS,CAAT,EAAYR,WAAW,GAAGE,QAA1B,CAAb;cACAA,QAAQ,GAAGF,WAAX;YACD;UACF,CAZM,MAYA;YACL,MAAMvB,MAAM,GACV2B,gBAAgB,IACfrI,CAAC,KAAK,CAAN,GAAUiI,WAAW,GAAGnH,UAAxB,GAAqCqH,QADtB,CADlB;YAGAA,QAAQ,GAAGG,cAAX;YAEA,KAAKvI,OAAL,CAAa2G,MAAb;;YACA,KAAKlC,aAAL,CAAmBF,OAAnB;UACD;QACF,CAlDD,MAkDO;UACLyD,MAAM,GAAG,KAAT;;UAEA,IAAIlG,GAAG,KAAK,CAAZ,EAAe;YACb,KAAK2C,aAAL,CAAmBF,OAAnB;UACD,CAFD,MAEO,IACLtB,IAAI,MAAJ,IACA,EAAEP,IAAI,CAACO,IAAL,KAAc,kBAAd,IAAoCP,IAAI,CAACiG,UAAL,CAAgB9K,MAAhB,GAAyB,CAA/D,CADA,IAEA6E,IAAI,CAACO,IAAL,KAAc,WAHT,EAIL;YAMA,KAAKwB,aAAL,CACEF,OADF,EAEEtE,CAAC,KAAK,CAAN,OAEIA,CAAC,KAAK6B,GAAG,GAAG,CAAZ,QAJN;UAQD,CAlBM,MAkBA;YACL,KAAK2C,aAAL,CAAmBF,OAAnB;UACD;QACF;MACF;;MAED,IAAItB,IAAI,MAAJ,IAAkC+E,MAAlC,IAA4CI,QAAhD,EAA0D;QACxD,KAAK7K,gBAAL,GAAwB6K,QAAxB;MACD;IACF;EACF;;EAEDQ,eAAe,CAAClG,IAAD,EAA8D;IAAA;;IAC3E,wBAAIA,IAAI,CAACmG,UAAT,aAAI,iBAAiBhL,MAArB,EAA6B;MAC3B,KAAKiB,KAAL;MACA,KAAKK,IAAL,CAAU,QAAV;MACA,KAAKL,KAAL;MACA,KAAKD,SAAL;MACA,KAAKC,KAAL;MACA,KAAKuH,SAAL,CAAe3D,IAAI,CAACmG,UAApB,EAAgCnG,IAAhC;MACA,KAAK5D,KAAL;MACA,KAAKD,SAAL;IACD;EACF;;AAr8BW;;AAy8BdiK,MAAM,CAACC,MAAP,CAAczM,OAAO,CAAC0M,SAAtB,EAAiCC,kBAAjC;AAEmC;EAEjC3M,OAAO,CAAC0M,SAAR,CAAkBE,IAAlB,GAAyB,SAASA,IAAT,GAA6B,CAAE,CAAxD;AACD;eAIc5M,O;;;AAEf,SAASiK,cAAT,GAAuC;EACrC,KAAK1H,SAAL;EACA,KAAKC,KAAL;AACD"}