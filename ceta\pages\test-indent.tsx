/**
 * Test page for custom indent functionality
 * This page allows testing the new indent system
 */

import { useState } from 'react';
import LexicalEditor from 'components/editor/lexical-editor';
// import { TEST_INDENT_MARKDOWN, validateIndentPreservation, debugIndentStructure } from 'components/editor/utils/indent-test';

export default function TestIndentPage() {
    const [content, setContent] = useState(`# 测试标题

这是一个普通段落。

这是另一个段落，可以用来测试缩进。

- 列表项 1
- 列表项 2
- 列表项 3

1. 编号列表 1
2. 编号列表 2
3. 编号列表 3`);
    const [testResults, setTestResults] = useState<string[]>([]);

    const handleContentChange = (getValue: () => string) => {
        const newContent = getValue();
        setContent(newContent);

        // 简单的测试：检查是否包含缩进标记
        const timestamp = new Date().toLocaleTimeString();
        const hasIndentMarkers = newContent.includes('<!-- indent:');

        if (hasIndentMarkers) {
            setTestResults(prev => [...prev, `${timestamp}: ✅ 检测到缩进标记`]);
        } else {
            setTestResults(prev => [...prev, `${timestamp}: ℹ️ 内容更新，无缩进标记`]);
        }

        // Debug output
        console.log('=== Content Updated ===');
        console.log(newContent);
    };

    const resetContent = () => {
        setContent(`# 测试标题

这是一个普通段落。

这是另一个段落，可以用来测试缩进。

- 列表项 1
- 列表项 2
- 列表项 3

1. 编号列表 1
2. 编号列表 2
3. 编号列表 3`);
        setTestResults([]);
    };

    const clearResults = () => {
        setTestResults([]);
    };

    return (
        <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
            <h1>Lexical Editor Indent Test</h1>
            
            <div style={{ marginBottom: '20px' }}>
                <button 
                    onClick={resetContent}
                    style={{ 
                        marginRight: '10px', 
                        padding: '8px 16px', 
                        backgroundColor: '#007bff', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }}
                >
                    Reset Test Content
                </button>
                <button 
                    onClick={clearResults}
                    style={{ 
                        padding: '8px 16px', 
                        backgroundColor: '#6c757d', 
                        color: 'white', 
                        border: 'none', 
                        borderRadius: '4px',
                        cursor: 'pointer'
                    }}
                >
                    Clear Results
                </button>
            </div>

            <div style={{ display: 'flex', gap: '20px' }}>
                {/* Editor */}
                <div style={{ flex: 1 }}>
                    <h2>Editor</h2>
                    <div style={{ 
                        border: '1px solid #ccc', 
                        borderRadius: '4px', 
                        minHeight: '400px',
                        backgroundColor: 'white'
                    }}>
                        <LexicalEditor
                            value={content}
                            onChange={handleContentChange}
                            readOnly={false}
                        />
                    </div>
                </div>

                {/* Test Results */}
                <div style={{ flex: 1 }}>
                    <h2>Test Results</h2>
                    <div style={{ 
                        border: '1px solid #ccc', 
                        borderRadius: '4px', 
                        padding: '10px',
                        minHeight: '400px',
                        backgroundColor: '#f8f9fa',
                        fontFamily: 'monospace',
                        fontSize: '12px',
                        overflow: 'auto'
                    }}>
                        {testResults.length === 0 ? (
                            <p style={{ color: '#6c757d' }}>No test results yet. Make changes to the editor to see results.</p>
                        ) : (
                            testResults.map((result, index) => (
                                <div key={index} style={{ marginBottom: '5px' }}>
                                    {result}
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>

            {/* Instructions */}
            <div style={{ marginTop: '20px', padding: '15px', backgroundColor: '#e9ecef', borderRadius: '4px' }}>
                <h3>Test Instructions</h3>
                <ol>
                    <li>Use <kbd>Tab</kbd> to indent content</li>
                    <li>Use <kbd>Shift+Tab</kbd> to outdent content</li>
                    <li>Use the bubble menu indent/outdent buttons when text is selected</li>
                    <li>Try indenting paragraphs, headings, and list items</li>
                    <li>Save and reload to test persistence</li>
                    <li>Check the test results panel for validation feedback</li>
                </ol>
                
                <h4>Expected Behavior:</h4>
                <ul>
                    <li>Indentation should be preserved when saving/loading</li>
                    <li>Both paragraph and list indentation should work</li>
                    <li>Visual indentation should match saved markdown</li>
                    <li>Test results should show ✅ PASSED for proper indent preservation</li>
                </ul>
            </div>

            {/* Raw Content Display */}
            <div style={{ marginTop: '20px' }}>
                <h3>Raw Markdown Content</h3>
                <pre style={{ 
                    backgroundColor: '#f8f9fa', 
                    padding: '10px', 
                    borderRadius: '4px',
                    fontSize: '12px',
                    overflow: 'auto',
                    maxHeight: '300px'
                }}>
                    {content}
                </pre>
            </div>
        </div>
    );
}
