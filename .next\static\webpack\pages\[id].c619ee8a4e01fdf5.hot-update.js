"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/note.ts":
/*!********************************!*\
  !*** ./libs/web/state/note.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var libs_web_state_tree__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/web/state/tree */ \"./libs/web/state/tree.ts\");\n/* harmony import */ var libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/meta */ \"./libs/shared/meta.ts\");\n/* harmony import */ var _api_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../api/note */ \"./libs/web/api/note.ts\");\n/* harmony import */ var _cache_note__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar useNote = function(initData) {\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initData), note = ref[0], setNote = ref[1];\n    var ref1 = (0,_api_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), find = ref1.find, abortFindNote = ref1.abort;\n    var ref2 = (0,_api_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), create = ref2.create, createError = ref2.error;\n    var ref3 = (0,_api_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), mutate = ref3.mutate, loading = ref3.loading, abort = ref3.abort;\n    var ref4 = libs_web_state_tree__WEBPACK_IMPORTED_MODULE_1__[\"default\"].useContainer(), addItem = ref4.addItem, removeItem = ref4.removeItem, mutateItem = ref4.mutateItem, genNewId = ref4.genNewId;\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    var fetchNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id) {\n            var cache, result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getItem(id)\n                        ];\n                    case 1:\n                        cache = _state.sent();\n                        if (cache) {\n                            setNote(cache);\n                        }\n                        return [\n                            4,\n                            find(id)\n                        ];\n                    case 2:\n                        result = _state.sent();\n                        if (!result) {\n                            return [\n                                2\n                            ];\n                        }\n                        // 🔧 修复：对于 JSON 编辑器，空内容应该是空字符串，不是 '\\n'\n                        result.content = result.content || \"\";\n                        setNote(result);\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setItem(id, result)\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            2,\n                            result\n                        ];\n                }\n            });\n        });\n        return function(id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        find\n    ]);\n    var removeNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id) {\n            var payload;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        payload = {\n                            deleted: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_DELETED.DELETED\n                        };\n                        setNote(function(prev) {\n                            if ((prev === null || prev === void 0 ? void 0 : prev.id) === id) {\n                                return (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, prev, payload);\n                            }\n                            return prev;\n                        });\n                        return [\n                            4,\n                            mutate(id, payload)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].mutateItem(id, payload)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            4,\n                            removeItem(id)\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        mutate,\n        removeItem\n    ]);\n    var mutateNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id, payload) {\n            var note, diff;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getItem(id)\n                        ];\n                    case 1:\n                        note = _state.sent();\n                        if (!note) {\n                            // todo\n                            console.error(\"mutate note error\");\n                            return [\n                                2\n                            ];\n                        }\n                        diff = {};\n                        (0,lodash__WEBPACK_IMPORTED_MODULE_6__.map)(payload, function(value, key) {\n                            if (note[key] !== value) {\n                                diff[key] = value;\n                            }\n                        });\n                        if ((0,lodash__WEBPACK_IMPORTED_MODULE_6__.isEmpty)(diff)) {\n                            return [\n                                2\n                            ];\n                        }\n                        setNote(function(prev) {\n                            if ((prev === null || prev === void 0 ? void 0 : prev.id) === id) {\n                                return (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, prev, payload);\n                            }\n                            return prev;\n                        });\n                        return [\n                            4,\n                            mutate(id, payload)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].mutateItem(id, payload)\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            4,\n                            mutateItem(id, {\n                                data: (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, note, payload)\n                            })\n                        ];\n                    case 4:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(id, payload) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        mutate,\n        mutateItem\n    ]);\n    var createNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(body) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            create(body)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (!result) {\n                            toast(createError, \"error\");\n                            return [\n                                2\n                            ];\n                        }\n                        // 🔧 修复：对于 JSON 编辑器，空内容应该是空字符串，不是 '\\n'\n                        result.content = result.content || \"\";\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setItem(result.id, result)\n                        ];\n                    case 2:\n                        _state.sent();\n                        setNote(result);\n                        addItem(result);\n                        return [\n                            2,\n                            result\n                        ];\n                }\n            });\n        });\n        return function(body) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        create,\n        addItem,\n        toast,\n        createError\n    ]);\n    var createNoteWithTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var id, result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        id = genNewId();\n                        return [\n                            4,\n                            create({\n                                id: id,\n                                title: title\n                            })\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (!result) {\n                            return [\n                                2\n                            ];\n                        }\n                        result.content = result.content || \"\\n\";\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setItem(result.id, result)\n                        ];\n                    case 2:\n                        _state.sent();\n                        addItem(result);\n                        return [\n                            2,\n                            {\n                                id: id\n                            }\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        addItem,\n        create,\n        genNewId\n    ]);\n    var updateNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var localNote, noteToUpdate, updateData, newNote, result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        abort();\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                            toast(\"Not found id\", \"error\");\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        localNote = _state.sent();\n                        noteToUpdate = localNote || note;\n                        updateData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, data), {\n                            content: data.content || noteToUpdate.content\n                        });\n                        newNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToUpdate, data);\n                        setNote(newNote);\n                        return [\n                            4,\n                            mutateItem(newNote.id, {\n                                data: newNote\n                            })\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            4,\n                            mutate(note.id, updateData)\n                        ];\n                    case 3:\n                        result = _state.sent();\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].mutateItem(note.id, updateData)\n                        ];\n                    case 4:\n                        _state.sent();\n                        return [\n                            2,\n                            result\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        abort,\n        toast,\n        note,\n        mutate,\n        mutateItem\n    ]);\n    var initNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(note) {\n        setNote((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n            deleted: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_DELETED.NORMAL,\n            shared: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_SHARED.PRIVATE,\n            pinned: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_PINNED.UNPINNED,\n            editorsize: null,\n            id: \"-1\",\n            title: \"\"\n        }, note));\n    }, []);\n    var findOrCreateNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id, note) {\n            var data, e;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            2,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            fetchNote(id)\n                        ];\n                    case 1:\n                        data = _state.sent();\n                        if (!data) {\n                            throw data;\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        e = _state.sent();\n                        return [\n                            4,\n                            createNote((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n                                id: id\n                            }, note))\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(id, note) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNote,\n        fetchNote\n    ]);\n    return {\n        note: note,\n        fetchNote: fetchNote,\n        abortFindNote: abortFindNote,\n        createNote: createNote,\n        findOrCreateNote: findOrCreateNote,\n        createNoteWithTitle: createNoteWithTitle,\n        updateNote: updateNote,\n        removeNote: removeNote,\n        mutateNote: mutateNote,\n        initNote: initNote,\n        loading: loading\n    };\n};\nvar NoteState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useNote);\n/* harmony default export */ __webpack_exports__[\"default\"] = (NoteState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/note.ts\n"));

/***/ })

});