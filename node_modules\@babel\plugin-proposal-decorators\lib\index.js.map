{"version": 3, "names": ["declare", "api", "options", "assertVersion", "legacy", "version", "name", "inherits", "syntaxDecorators", "visitor", "legacyVisitor", "transformer2022_03", "createClassFeaturePlugin", "feature", "FEATURES", "decorators"], "sources": ["../src/index.ts"], "sourcesContent": ["/* eslint-disable @babel/development/plugin-name */\n\nimport { declare } from \"@babel/helper-plugin-utils\";\nimport syntaxDecorators from \"@babel/plugin-syntax-decorators\";\nimport {\n  createClassFeaturePlugin,\n  FEATURES,\n} from \"@babel/helper-create-class-features-plugin\";\nimport legacyVisitor from \"./transformer-legacy\";\nimport transformer2022_03 from \"./transformer-2022-03\";\nimport type { Options as SyntaxOptions } from \"@babel/plugin-syntax-decorators\";\n\ninterface Options extends SyntaxOptions {\n  /** @depreated use `constantSuper` assumption instead. Only supported in 2021-12 version. */\n  loose?: boolean;\n}\n\nexport type { Options };\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(7);\n\n  // Options are validated in @babel/plugin-syntax-decorators\n  if (!process.env.BABEL_8_BREAKING) {\n    // eslint-disable-next-line no-var\n    var { legacy } = options;\n  }\n  const { version } = options;\n\n  if (\n    process.env.BABEL_8_BREAKING\n      ? version === \"legacy\"\n      : legacy || version === \"legacy\"\n  ) {\n    return {\n      name: \"proposal-decorators\",\n      inherits: syntaxDecorators,\n      visitor: legacyVisitor,\n    };\n  } else if (version === \"2021-12\" || version === \"2022-03\") {\n    return transformer2022_03(api, options, version);\n  } else if (!process.env.BABEL_8_BREAKING) {\n    return createClassFeaturePlugin({\n      name: \"proposal-decorators\",\n\n      api,\n      feature: FEATURES.decorators,\n      inherits: syntaxDecorators,\n      // loose: options.loose, Not supported\n    });\n  } else {\n    throw new Error(\n      \"The '.version' option must be one of 'legacy', '2021-12' or '2022-03'.\",\n    );\n  }\n});\n"], "mappings": ";;;;;;;AAEA;;AACA;;AACA;;AAIA;;AACA;;eAUe,IAAAA,0BAAA,EAAQ,CAACC,GAAD,EAAMC,OAAN,KAA2B;EAChDD,GAAG,CAACE,aAAJ,CAAkB,CAAlB;EAGmC;IAEjC,IAAI;MAAEC;IAAF,IAAaF,OAAjB;EACD;EACD,MAAM;IAAEG;EAAF,IAAcH,OAApB;;EAEA,IAGME,MAAM,IAAIC,OAAO,KAAK,QAH5B,EAIE;IACA,OAAO;MACLC,IAAI,EAAE,qBADD;MAELC,QAAQ,EAAEC,+BAFL;MAGLC,OAAO,EAAEC;IAHJ,CAAP;EAKD,CAVD,MAUO,IAAIL,OAAO,KAAK,SAAZ,IAAyBA,OAAO,KAAK,SAAzC,EAAoD;IACzD,OAAO,IAAAM,oBAAA,EAAmBV,GAAnB,EAAwBC,OAAxB,EAAiCG,OAAjC,CAAP;EACD,CAFM,MAEmC;IACxC,OAAO,IAAAO,yDAAA,EAAyB;MAC9BN,IAAI,EAAE,qBADwB;MAG9BL,GAH8B;MAI9BY,OAAO,EAAEC,yCAAA,CAASC,UAJY;MAK9BR,QAAQ,EAAEC;IALoB,CAAzB,CAAP;EAQD;AAKF,CApCc,C"}