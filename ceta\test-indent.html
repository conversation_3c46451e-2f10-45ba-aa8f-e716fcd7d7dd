<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lexical Indent Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .instructions {
            background-color: #f0f8ff;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .test-result {
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>Lexical 缩进功能测试</h1>
    
    <div class="instructions">
        <h3>测试说明：</h3>
        <ol>
            <li>在编辑器中输入一些文本</li>
            <li>使用 <kbd>Tab</kbd> 键进行缩进</li>
            <li>使用 <kbd>Shift+Tab</kbd> 键进行反缩进</li>
            <li>测试段落、标题、列表的缩进功能</li>
            <li>保存并重新加载，检查缩进是否保持</li>
        </ol>
    </div>

    <div class="test-section">
        <h3>测试内容示例：</h3>
        <div class="test-result">这是一个普通段落
    这是缩进1级的段落
        这是缩进2级的段落

# 这是标题
    # 这是缩进的标题

- 这是列表项
    - 这是缩进的列表项
        - 这是缩进2级的列表项

1. 有序列表项
    1. 缩进的有序列表项
        1. 缩进2级的有序列表项</div>
    </div>

    <div class="test-section">
        <h3>预期行为：</h3>
        <ul>
            <li>Tab键应该增加缩进级别</li>
            <li>Shift+Tab键应该减少缩进级别</li>
            <li>缩进应该在保存/加载后保持</li>
            <li>最大缩进级别为10级</li>
            <li>不同类型的内容（段落、标题、列表）都应该支持缩进</li>
        </ul>
    </div>

    <div class="test-section">
        <h3>升级后的改进：</h3>
        <ul>
            <li>✅ 使用Lexical 0.32.1原生缩进功能</li>
            <li>✅ 移除了自定义缩进插件</li>
            <li>✅ 使用TabIndentationPlugin</li>
            <li>✅ 支持原生INDENT_CONTENT_COMMAND和OUTDENT_CONTENT_COMMAND</li>
            <li>✅ 更好的VSCode风格缩进行为</li>
        </ul>
    </div>

    <script>
        // 简单的键盘快捷键提示
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Tab') {
                console.log('Tab键被按下，应该触发缩进功能');
            }
            if (e.key === 'Tab' && e.shiftKey) {
                console.log('Shift+Tab键被按下，应该触发反缩进功能');
            }
        });
    </script>
</body>
</html>
