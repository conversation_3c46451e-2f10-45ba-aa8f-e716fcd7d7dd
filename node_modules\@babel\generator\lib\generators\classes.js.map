{"version": 3, "names": ["isExportDefaultDeclaration", "isExportNamedDeclaration", "ClassDeclaration", "node", "parent", "format", "decoratorsBeforeExport", "printJoin", "decorators", "declare", "word", "space", "abstract", "printInnerComments", "id", "print", "typeParameters", "superClass", "superTypeParameters", "implements", "printList", "body", "ClassBody", "token", "length", "newline", "indent", "printSequence", "dedent", "endsWith", "sourceWithOffset", "loc", "rightBrace", "ClassProperty", "endLine", "key", "end", "line", "catchUp", "tsPrintClassMemberModifiers", "computed", "_variance", "optional", "definite", "typeAnnotation", "value", "semicolon", "ClassAccessorProperty", "ClassPrivateProperty", "static", "ClassMethod", "_classMethodHead", "ClassPrivateMethod", "_methodHead", "StaticBlock"], "sources": ["../../src/generators/classes.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport {\n  isExportDefaultDeclaration,\n  isExportNamedDeclaration,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nexport function ClassDeclaration(\n  this: Printer,\n  node: t.ClassDeclaration,\n  parent: t.Node,\n) {\n  if (process.env.BABEL_8_BREAKING) {\n    this.printJoin(node.decorators, node);\n  } else {\n    if (\n      !this.format.decoratorsBeforeExport ||\n      (!isExportDefaultDeclaration(parent) && !isExportNamedDeclaration(parent))\n    ) {\n      this.printJoin(node.decorators, node);\n    }\n  }\n\n  if (node.declare) {\n    // TS\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (node.abstract) {\n    // TS\n    this.word(\"abstract\");\n    this.space();\n  }\n\n  this.word(\"class\");\n  this.printInnerComments(node);\n\n  if (node.id) {\n    this.space();\n    this.print(node.id, node);\n  }\n\n  this.print(node.typeParameters, node);\n\n  if (node.superClass) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.superClass, node);\n    this.print(node.superTypeParameters, node);\n  }\n\n  if (node.implements) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements, node);\n  }\n\n  this.space();\n  this.print(node.body, node);\n}\n\nexport { ClassDeclaration as ClassExpression };\n\nexport function ClassBody(this: Printer, node: t.ClassBody) {\n  this.token(\"{\");\n  this.printInnerComments(node);\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n\n    this.indent();\n    this.printSequence(node.body, node);\n    this.dedent();\n\n    if (!this.endsWith(charCodes.lineFeed)) this.newline();\n\n    this.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n    this.rightBrace();\n  }\n}\n\nexport function ClassProperty(this: Printer, node: t.ClassProperty) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    this._variance(node);\n    this.print(node.key, node);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassAccessorProperty(\n  this: Printer,\n  node: t.ClassAccessorProperty,\n) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to property key, avoid line break\n  // between member modifiers and the property key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  // TS does not support class accessor property yet\n  this.tsPrintClassMemberModifiers(node);\n\n  this.word(\"accessor\");\n  this.printInnerComments(node);\n  this.space();\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(node.key, node);\n    this.token(\"]\");\n  } else {\n    // Todo: Flow does not support class accessor property yet.\n    this._variance(node);\n    this.print(node.key, node);\n  }\n\n  // TS\n  if (node.optional) {\n    this.token(\"?\");\n  }\n  if (node.definite) {\n    this.token(\"!\");\n  }\n\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassPrivateProperty(\n  this: Printer,\n  node: t.ClassPrivateProperty,\n) {\n  this.printJoin(node.decorators, node);\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.key, node);\n  this.print(node.typeAnnotation, node);\n  if (node.value) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.value, node);\n  }\n  this.semicolon();\n}\n\nexport function ClassMethod(this: Printer, node: t.ClassMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function ClassPrivateMethod(this: Printer, node: t.ClassPrivateMethod) {\n  this._classMethodHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function _classMethodHead(\n  this: Printer,\n  node: t.ClassMethod | t.ClassPrivateMethod | t.TSDeclareMethod,\n) {\n  this.printJoin(node.decorators, node);\n\n  // catch up to method key, avoid line break\n  // between member modifiers/method heads and the method key.\n  const endLine = node.key.loc?.end?.line;\n  if (endLine) this.catchUp(endLine);\n\n  this.tsPrintClassMemberModifiers(node);\n  this._methodHead(node);\n}\n\nexport function StaticBlock(this: Printer, node: t.StaticBlock) {\n  this.word(\"static\");\n  this.space();\n  this.token(\"{\");\n  if (node.body.length === 0) {\n    this.token(\"}\");\n  } else {\n    this.newline();\n    this.printSequence(node.body, node, {\n      indent: true,\n    });\n\n    this.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n    this.rightBrace();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;AACA;;;EACEA,0B;EACAC;;;AAKK,SAASC,gBAAT,CAELC,IAFK,EAGLC,MAHK,EAIL;EAGO;IACL,IACE,CAAC,KAAKC,MAAL,CAAYC,sBAAb,IACC,CAACN,0BAA0B,CAACI,MAAD,CAA3B,IAAuC,CAACH,wBAAwB,CAACG,MAAD,CAFnE,EAGE;MACA,KAAKG,SAAL,CAAeJ,IAAI,CAACK,UAApB,EAAgCL,IAAhC;IACD;EACF;;EAED,IAAIA,IAAI,CAACM,OAAT,EAAkB;IAEhB,KAAKC,IAAL,CAAU,SAAV;IACA,KAAKC,KAAL;EACD;;EAED,IAAIR,IAAI,CAACS,QAAT,EAAmB;IAEjB,KAAKF,IAAL,CAAU,UAAV;IACA,KAAKC,KAAL;EACD;;EAED,KAAKD,IAAL,CAAU,OAAV;EACA,KAAKG,kBAAL,CAAwBV,IAAxB;;EAEA,IAAIA,IAAI,CAACW,EAAT,EAAa;IACX,KAAKH,KAAL;IACA,KAAKI,KAAL,CAAWZ,IAAI,CAACW,EAAhB,EAAoBX,IAApB;EACD;;EAED,KAAKY,KAAL,CAAWZ,IAAI,CAACa,cAAhB,EAAgCb,IAAhC;;EAEA,IAAIA,IAAI,CAACc,UAAT,EAAqB;IACnB,KAAKN,KAAL;IACA,KAAKD,IAAL,CAAU,SAAV;IACA,KAAKC,KAAL;IACA,KAAKI,KAAL,CAAWZ,IAAI,CAACc,UAAhB,EAA4Bd,IAA5B;IACA,KAAKY,KAAL,CAAWZ,IAAI,CAACe,mBAAhB,EAAqCf,IAArC;EACD;;EAED,IAAIA,IAAI,CAACgB,UAAT,EAAqB;IACnB,KAAKR,KAAL;IACA,KAAKD,IAAL,CAAU,YAAV;IACA,KAAKC,KAAL;IACA,KAAKS,SAAL,CAAejB,IAAI,CAACgB,UAApB,EAAgChB,IAAhC;EACD;;EAED,KAAKQ,KAAL;EACA,KAAKI,KAAL,CAAWZ,IAAI,CAACkB,IAAhB,EAAsBlB,IAAtB;AACD;;AAIM,SAASmB,SAAT,CAAkCnB,IAAlC,EAAqD;EAC1D,KAAKoB,SAAL;EACA,KAAKV,kBAAL,CAAwBV,IAAxB;;EACA,IAAIA,IAAI,CAACkB,IAAL,CAAUG,MAAV,KAAqB,CAAzB,EAA4B;IAC1B,KAAKD,SAAL;EACD,CAFD,MAEO;IACL,KAAKE,OAAL;IAEA,KAAKC,MAAL;IACA,KAAKC,aAAL,CAAmBxB,IAAI,CAACkB,IAAxB,EAA8BlB,IAA9B;IACA,KAAKyB,MAAL;IAEA,IAAI,CAAC,KAAKC,QAAL,IAAL,EAAwC,KAAKJ,OAAL;IAExC,KAAKK,gBAAL,CAAsB,KAAtB,EAA6B3B,IAAI,CAAC4B,GAAlC,EAAuC,CAAvC,EAA0C,CAAC,CAA3C;IAEA,KAAKC,UAAL;EACD;AACF;;AAEM,SAASC,aAAT,CAAsC9B,IAAtC,EAA6D;EAAA;;EAClE,KAAKI,SAAL,CAAeJ,IAAI,CAACK,UAApB,EAAgCL,IAAhC;EAIA,MAAM+B,OAAO,oBAAG/B,IAAI,CAACgC,GAAL,CAASJ,GAAZ,0CAAG,cAAcK,GAAjB,qBAAG,kBAAmBC,IAAnC;EACA,IAAIH,OAAJ,EAAa,KAAKI,OAAL,CAAaJ,OAAb;EAEb,KAAKK,2BAAL,CAAiCpC,IAAjC;;EAEA,IAAIA,IAAI,CAACqC,QAAT,EAAmB;IACjB,KAAKjB,SAAL;IACA,KAAKR,KAAL,CAAWZ,IAAI,CAACgC,GAAhB,EAAqBhC,IAArB;IACA,KAAKoB,SAAL;EACD,CAJD,MAIO;IACL,KAAKkB,SAAL,CAAetC,IAAf;;IACA,KAAKY,KAAL,CAAWZ,IAAI,CAACgC,GAAhB,EAAqBhC,IAArB;EACD;;EAGD,IAAIA,IAAI,CAACuC,QAAT,EAAmB;IACjB,KAAKnB,SAAL;EACD;;EACD,IAAIpB,IAAI,CAACwC,QAAT,EAAmB;IACjB,KAAKpB,SAAL;EACD;;EAED,KAAKR,KAAL,CAAWZ,IAAI,CAACyC,cAAhB,EAAgCzC,IAAhC;;EACA,IAAIA,IAAI,CAAC0C,KAAT,EAAgB;IACd,KAAKlC,KAAL;IACA,KAAKY,SAAL;IACA,KAAKZ,KAAL;IACA,KAAKI,KAAL,CAAWZ,IAAI,CAAC0C,KAAhB,EAAuB1C,IAAvB;EACD;;EACD,KAAK2C,SAAL;AACD;;AAEM,SAASC,qBAAT,CAEL5C,IAFK,EAGL;EAAA;;EACA,KAAKI,SAAL,CAAeJ,IAAI,CAACK,UAApB,EAAgCL,IAAhC;EAIA,MAAM+B,OAAO,qBAAG/B,IAAI,CAACgC,GAAL,CAASJ,GAAZ,2CAAG,eAAcK,GAAjB,qBAAG,mBAAmBC,IAAnC;EACA,IAAIH,OAAJ,EAAa,KAAKI,OAAL,CAAaJ,OAAb;EAGb,KAAKK,2BAAL,CAAiCpC,IAAjC;EAEA,KAAKO,IAAL,CAAU,UAAV;EACA,KAAKG,kBAAL,CAAwBV,IAAxB;EACA,KAAKQ,KAAL;;EAEA,IAAIR,IAAI,CAACqC,QAAT,EAAmB;IACjB,KAAKjB,SAAL;IACA,KAAKR,KAAL,CAAWZ,IAAI,CAACgC,GAAhB,EAAqBhC,IAArB;IACA,KAAKoB,SAAL;EACD,CAJD,MAIO;IAEL,KAAKkB,SAAL,CAAetC,IAAf;;IACA,KAAKY,KAAL,CAAWZ,IAAI,CAACgC,GAAhB,EAAqBhC,IAArB;EACD;;EAGD,IAAIA,IAAI,CAACuC,QAAT,EAAmB;IACjB,KAAKnB,SAAL;EACD;;EACD,IAAIpB,IAAI,CAACwC,QAAT,EAAmB;IACjB,KAAKpB,SAAL;EACD;;EAED,KAAKR,KAAL,CAAWZ,IAAI,CAACyC,cAAhB,EAAgCzC,IAAhC;;EACA,IAAIA,IAAI,CAAC0C,KAAT,EAAgB;IACd,KAAKlC,KAAL;IACA,KAAKY,SAAL;IACA,KAAKZ,KAAL;IACA,KAAKI,KAAL,CAAWZ,IAAI,CAAC0C,KAAhB,EAAuB1C,IAAvB;EACD;;EACD,KAAK2C,SAAL;AACD;;AAEM,SAASE,oBAAT,CAEL7C,IAFK,EAGL;EACA,KAAKI,SAAL,CAAeJ,IAAI,CAACK,UAApB,EAAgCL,IAAhC;;EACA,IAAIA,IAAI,CAAC8C,MAAT,EAAiB;IACf,KAAKvC,IAAL,CAAU,QAAV;IACA,KAAKC,KAAL;EACD;;EACD,KAAKI,KAAL,CAAWZ,IAAI,CAACgC,GAAhB,EAAqBhC,IAArB;EACA,KAAKY,KAAL,CAAWZ,IAAI,CAACyC,cAAhB,EAAgCzC,IAAhC;;EACA,IAAIA,IAAI,CAAC0C,KAAT,EAAgB;IACd,KAAKlC,KAAL;IACA,KAAKY,SAAL;IACA,KAAKZ,KAAL;IACA,KAAKI,KAAL,CAAWZ,IAAI,CAAC0C,KAAhB,EAAuB1C,IAAvB;EACD;;EACD,KAAK2C,SAAL;AACD;;AAEM,SAASI,WAAT,CAAoC/C,IAApC,EAAyD;EAC9D,KAAKgD,gBAAL,CAAsBhD,IAAtB;;EACA,KAAKQ,KAAL;EACA,KAAKI,KAAL,CAAWZ,IAAI,CAACkB,IAAhB,EAAsBlB,IAAtB;AACD;;AAEM,SAASiD,kBAAT,CAA2CjD,IAA3C,EAAuE;EAC5E,KAAKgD,gBAAL,CAAsBhD,IAAtB;;EACA,KAAKQ,KAAL;EACA,KAAKI,KAAL,CAAWZ,IAAI,CAACkB,IAAhB,EAAsBlB,IAAtB;AACD;;AAEM,SAASgD,gBAAT,CAELhD,IAFK,EAGL;EAAA;;EACA,KAAKI,SAAL,CAAeJ,IAAI,CAACK,UAApB,EAAgCL,IAAhC;EAIA,MAAM+B,OAAO,qBAAG/B,IAAI,CAACgC,GAAL,CAASJ,GAAZ,2CAAG,eAAcK,GAAjB,qBAAG,mBAAmBC,IAAnC;EACA,IAAIH,OAAJ,EAAa,KAAKI,OAAL,CAAaJ,OAAb;EAEb,KAAKK,2BAAL,CAAiCpC,IAAjC;;EACA,KAAKkD,WAAL,CAAiBlD,IAAjB;AACD;;AAEM,SAASmD,WAAT,CAAoCnD,IAApC,EAAyD;EAC9D,KAAKO,IAAL,CAAU,QAAV;EACA,KAAKC,KAAL;EACA,KAAKY,SAAL;;EACA,IAAIpB,IAAI,CAACkB,IAAL,CAAUG,MAAV,KAAqB,CAAzB,EAA4B;IAC1B,KAAKD,SAAL;EACD,CAFD,MAEO;IACL,KAAKE,OAAL;IACA,KAAKE,aAAL,CAAmBxB,IAAI,CAACkB,IAAxB,EAA8BlB,IAA9B,EAAoC;MAClCuB,MAAM,EAAE;IAD0B,CAApC;IAIA,KAAKI,gBAAL,CAAsB,KAAtB,EAA6B3B,IAAI,CAAC4B,GAAlC,EAAuC,CAAvC,EAA0C,CAAC,CAA3C;IAEA,KAAKC,UAAL;EACD;AACF"}