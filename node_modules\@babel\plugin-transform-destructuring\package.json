{"name": "@babel/plugin-transform-destructuring", "version": "7.19.4", "description": "Compile ES2015 destructuring to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-destructuring"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-destructuring", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.3", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/traverse": "^7.19.4"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}