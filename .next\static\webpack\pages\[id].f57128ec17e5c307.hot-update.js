"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_type_of.mjs */ \"./node_modules/@swc/helpers/src/_type_of.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var ref, ref1, ref2, ref3, snapshotJsonContent;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                noteId: note === null || note === void 0 ? void 0 : note.id,\n                noteContent: (note === null || note === void 0 ? void 0 : (ref = note.content) === null || ref === void 0 ? void 0 : ref.substring(0, 100)) + \"...\",\n                noteContentLength: (note === null || note === void 0 ? void 0 : (ref1 = note.content) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n            });\n            if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                // 新建笔记：快照为空值\n                console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n                return [\n                    2\n                ];\n            }\n            try {\n                ;\n                snapshotJsonContent = \"\";\n                console.log(\"\\uD83D\\uDD27 快照内容来源分析:\", {\n                    noteContent: ((ref2 = note.content) === null || ref2 === void 0 ? void 0 : ref2.substring(0, 100)) + \"...\",\n                    noteContentLength: ((ref3 = note.content) === null || ref3 === void 0 ? void 0 : ref3.length) || 0,\n                    noteContentType: (0,_swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(note.content)\n                });\n                // 直接使用 note 对象的内容作为快照\n                if (note.content !== undefined && note.content !== null) {\n                    snapshotJsonContent = note.content;\n                    console.log(\"\\uD83D\\uDD27 使用 note 对象内容作为快照\");\n                } else {\n                    snapshotJsonContent = \"\";\n                    console.log(\"\\uD83D\\uDD27 note 内容为空，设置空快照\");\n                }\n                console.log(\"\\uD83D\\uDD27 快照初始化完成:\", {\n                    noteId: note.id,\n                    hasContent: !!snapshotJsonContent,\n                    contentLength: snapshotJsonContent.length,\n                    isJson: snapshotJsonContent.startsWith(\"{\"),\n                    source: (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) ? \"cache\" : note.content ? \"note\" : \"empty\",\n                    contentPreview: snapshotJsonContent.substring(0, 100) + \"...\"\n                });\n                // 🔧 关键：设置快照和当前编辑器内容\n                setNoteSnapshot(snapshotJsonContent);\n                setCurrentEditorContent(snapshotJsonContent);\n            } catch (error) {\n                console.error(\"JSON快照初始化失败:\", error);\n                // 失败时设置为空快照\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n            }\n            return [\n                2\n            ];\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 当笔记ID变化或内容加载完成时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        // 🔧 修复：只有当 note 存在、有 ID 且内容已加载时才初始化\n        // 避免在内容未完全加载时就初始化快照\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            var ref;\n            console.log(\"\\uD83D\\uDD27 触发快照初始化条件:\", {\n                noteId: note.id,\n                hasContent: note.content !== undefined,\n                contentLength: ((ref = note.content) === null || ref === void 0 ? void 0 : ref.length) || 0\n            });\n            initializeSnapshot();\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        console.log(\"\\uD83D\\uDD27 清空所有快照\");\n        setNoteSnapshot(null);\n        setCurrentEditorContent(\"\");\n        // 🔧 修复：清空后重新初始化快照，确保编辑器显示正确内容\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            setTimeout(function() {\n                initializeSnapshot();\n            }, 0);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 关键修复：保存成功后更新快照为当前内容，而不是清空\n                    // 这样可以保持编辑器状态一致，避免内容丢失\n                    setNoteSnapshot(currentEditorContent);\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB,\n        clearAllSnapshots\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot,\n        clearAllSnapshots: clearAllSnapshots,\n        // 🔧 关键修复：返回当前编辑器内容作为编辑器的 value\n        editorNote: (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, note), {\n            content: currentEditorContent\n        })\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_12__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});