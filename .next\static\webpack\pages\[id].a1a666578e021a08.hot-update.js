"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/save-button.tsx":
/*!************************************!*\
  !*** ./components/save-button.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-ui/core */ \"./node_modules/@material-ui/core/esm/index.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/**\n * SaveButton Component\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nvar useStyles = (0,_material_ui_core__WEBPACK_IMPORTED_MODULE_3__.makeStyles)({\n    saveButton: {\n        minWidth: \"80px\",\n        fontWeight: \"bold\",\n        textTransform: \"none\",\n        borderRadius: \"8px\",\n        boxShadow: \"none !important\",\n        \"&:hover\": {\n            opacity: 0.8,\n            boxShadow: \"none !important\"\n        },\n        \"&:focus\": {\n            boxShadow: \"none !important\"\n        },\n        \"&:active\": {\n            boxShadow: \"none !important\"\n        }\n    },\n    viewButton: {\n        backgroundColor: \"#6B7280 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#4B5563 !important\"\n        }\n    },\n    saveStateButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    },\n    syncingButton: {\n        backgroundColor: \"#3185eb !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#2563EB !important\"\n        }\n    },\n    syncedButton: {\n        backgroundColor: \"#FBBF24 !important\",\n        color: \"#000000 !important\",\n        \"&:hover\": {\n            backgroundColor: \"#F59E0B !important\"\n        }\n    },\n    failedButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    }\n});\nvar SaveButton = function(param) {\n    var className = param.className;\n    _s();\n    var classes = useStyles();\n    var ref = libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer(), syncToServer = ref.syncToServer, note = ref.note, getEditorState = ref.getEditorState, saveCurrentContent = ref.saveCurrentContent;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"), syncStatus = ref1[0], setSyncStatus = ref1[1];\n    var syncedTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var syncTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 重构：基于快照对比的状态检测机制 - 负责监听快照机制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (!(note === null || note === void 0 ? void 0 : note.id)) {\n            setSyncStatus(\"view\");\n            return;\n        }\n        var checkSnapshotChanges = function() {\n            try {\n                var editorState = getEditorState();\n                // 🔧 关键：快照对比机制，看看新的JSON记录和缓存快照是否一致\n                if (editorState.hasChanges) {\n                    // 🔧 若不一致，为save状态\n                    if (syncStatus !== \"save\" && syncStatus !== \"syncing\") {\n                        setSyncStatus(\"save\");\n                    }\n                } else {\n                    // 🔧 若一致，为view状态，不保存以及不要调用useAutoSaveOnLeave\n                    if (syncStatus === \"save\") {\n                        setSyncStatus(\"view\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"快照对比检查失败:\", error);\n            // 出错时保持当前状态，避免状态混乱\n            }\n        };\n        // 立即检查一次\n        checkSnapshotChanges();\n        // 🔧 定期检查快照变化 - 兼顾savebutton中的状态变化，这个需要一直监听的\n        var interval = setInterval(checkSnapshotChanges, 500);\n        return function() {\n            clearInterval(interval);\n            if (syncedTimeoutRef.current) {\n                clearTimeout(syncedTimeoutRef.current);\n            }\n            if (syncTimeoutRef.current) {\n                clearTimeout(syncTimeoutRef.current);\n            }\n        };\n    }, [\n        note,\n        getEditorState,\n        syncStatus\n    ]);\n    // 🔧 手动保存流程 - 用户选择手动保存，则进入正常的手动保存操作\n    var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function() {\n        var saveSuccess, syncSuccess, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    setSyncStatus(\"syncing\");\n                    if (syncedTimeoutRef.current) {\n                        clearTimeout(syncedTimeoutRef.current);\n                    }\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                    }\n                    // 设置超时保护\n                    syncTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"fail\");\n                        setTimeout(function() {\n                            setSyncStatus(\"view\");\n                        }, 2000);\n                    }, 30000);\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        4,\n                        ,\n                        5\n                    ]);\n                    return [\n                        4,\n                        saveCurrentContent()\n                    ];\n                case 2:\n                    saveSuccess = _state.sent();\n                    if (!saveSuccess) {\n                        throw new Error(\"保存到IndexedDB失败\");\n                    }\n                    return [\n                        4,\n                        syncToServer()\n                    ];\n                case 3:\n                    syncSuccess = _state.sent();\n                    if (!syncSuccess) {\n                        throw new Error(\"同步到服务器失败\");\n                    }\n                    // 清除超时\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"synced\");\n                    // 🔧 保存成功后，save状态会转为view状态（3秒后）\n                    syncedTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 3000);\n                    return [\n                        3,\n                        5\n                    ];\n                case 4:\n                    error = _state.sent();\n                    console.error(\"手动保存失败:\", error);\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"fail\");\n                    setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 2000);\n                    return [\n                        3,\n                        5\n                    ];\n                case 5:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        syncToServer,\n        saveCurrentContent\n    ]);\n    // 🔧 暴露状态给 useAutoSaveOnLeave - 让 SaveButton 来处理快照对比机制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (true) {\n            // 🔧 暴露当前同步状态，供 useAutoSaveOnLeave 判断是否需要自动保存\n            window.saveButtonStatus = syncStatus;\n            // 🔧 暴露自动保存函数，供 useAutoSaveOnLeave 调用\n            window.saveButtonAutoSave = handleSave;\n        }\n        return function() {\n            if (true) {\n                delete window.saveButtonStatus;\n                delete window.saveButtonAutoSave;\n            }\n        };\n    }, [\n        syncStatus,\n        handleSave\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var handleKeyDown = function(e) {\n            if ((e.ctrlKey || e.metaKey) && e.key === \"s\") {\n                var target = e.target;\n                var isInEditor = target.closest(\".ProseMirror\") || target.closest(\"[contenteditable]\") || target.closest(\"textarea\") || target.closest(\"input\");\n                if (isInEditor) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                    handleSave();\n                }\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown, true);\n        return function() {\n            return document.removeEventListener(\"keydown\", handleKeyDown, true);\n        };\n    }, [\n        handleSave\n    ]);\n    var getButtonIcon = function() {\n        switch(syncStatus){\n            case \"view\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 242,\n                    columnNumber: 24\n                }, _this);\n            case \"save\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.DocumentIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 24\n                }, _this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.UploadIcon, {\n                    className: \"w-4 h-4 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 24\n                }, _this);\n            case \"synced\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.CheckIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 24\n                }, _this);\n            case \"fail\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.XIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 24\n                }, _this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 252,\n                    columnNumber: 24\n                }, _this);\n        }\n    };\n    var getButtonText = function() {\n        switch(syncStatus){\n            case \"view\":\n                return \"View\";\n            case \"save\":\n                return \"Save\";\n            case \"syncing\":\n                return \"Syncing...\";\n            case \"synced\":\n                return \"Synced\";\n            case \"fail\":\n                return \"Failed\";\n            default:\n                return \"View\";\n        }\n    };\n    var getButtonClassName = function() {\n        var baseClass = \"\".concat(classes.saveButton);\n        switch(syncStatus){\n            case \"view\":\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n            case \"save\":\n                return \"\".concat(baseClass, \" \").concat(classes.saveStateButton);\n            case \"syncing\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncingButton);\n            case \"synced\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncedButton);\n            case \"fail\":\n                return \"\".concat(baseClass, \" \").concat(classes.failedButton);\n            default:\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n        }\n    };\n    var isButtonDisabled = function() {\n        return syncStatus === \"syncing\" || syncStatus === \"view\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_ui_core__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"contained\",\n        startIcon: getButtonIcon(),\n        onClick: handleSave,\n        disabled: isButtonDisabled(),\n        className: \"\".concat(getButtonClassName(), \" \").concat(className || \"\"),\n        size: \"small\",\n        \"data-save-button\": \"true\",\n        children: getButtonText()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n        lineNumber: 296,\n        columnNumber: 9\n    }, _this);\n};\n_s(SaveButton, \"oq9mrT7bNrn9J3gl+mSvXr01r08=\", false, function() {\n    return [\n        useStyles,\n        libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer\n    ];\n});\n_c = SaveButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SaveButton);\nvar _c;\n$RefreshReg$(_c, \"SaveButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/save-button.tsx\n"));

/***/ })

});