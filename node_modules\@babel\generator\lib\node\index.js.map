{"version": 3, "names": ["FLIPPED_ALIAS_KEYS", "isCallExpression", "isExpressionStatement", "isMemberExpression", "isNewExpression", "expandAliases", "obj", "newObj", "add", "type", "func", "fn", "node", "parent", "stack", "result", "Object", "keys", "aliases", "alias", "expandedParens", "parens", "expandedWhitespaceNodes", "whitespace", "nodes", "find", "printStack", "isOrHasCallExpression", "object", "needsWhitespace", "expression", "flag", "needsWhitespaceBefore", "needsWhitespaceAfter", "needsParens", "callee"], "sources": ["../../src/node/index.ts"], "sourcesContent": ["import * as whitespace from \"./whitespace\";\nimport * as parens from \"./parentheses\";\nimport {\n  FLIPPED_ALIAS_KEYS,\n  isCallExpression,\n  isExpressionStatement,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport type { WhitespaceFlag } from \"./whitespace\";\n\nexport type NodeHandlers<R> = {\n  [K in string]?: (\n    node: K extends t.Node[\"type\"] ? Extract<t.Node, { type: K }> : t.Node,\n    // todo:\n    // node: K extends keyof typeof t\n    //   ? Extract<typeof t[K], { type: \"string\" }>\n    //   : t.Node,\n    parent: t.Node,\n    stack: t.Node[],\n  ) => R;\n};\n\nfunction expandAliases<R>(obj: NodeHandlers<R>) {\n  const newObj: NodeHandlers<R> = {};\n\n  function add(\n    type: string,\n    func: (node: t.Node, parent: t.Node, stack: t.Node[]) => R,\n  ) {\n    const fn = newObj[type];\n    newObj[type] = fn\n      ? function (node, parent, stack) {\n          const result = fn(node, parent, stack);\n\n          return result == null ? func(node, parent, stack) : result;\n        }\n      : func;\n  }\n\n  for (const type of Object.keys(obj)) {\n    const aliases = FLIPPED_ALIAS_KEYS[type];\n    if (aliases) {\n      for (const alias of aliases) {\n        add(alias, obj[type]);\n      }\n    } else {\n      add(type, obj[type]);\n    }\n  }\n\n  return newObj;\n}\n\n// Rather than using `t.is` on each object property, we pre-expand any type aliases\n// into concrete types so that the 'find' call below can be as fast as possible.\nconst expandedParens = expandAliases(parens);\nconst expandedWhitespaceNodes = expandAliases(whitespace.nodes);\n\nfunction find<R>(\n  obj: NodeHandlers<R>,\n  node: t.Node,\n  parent: t.Node,\n  printStack?: t.Node[],\n): R | null {\n  const fn = obj[node.type];\n  return fn ? fn(node, parent, printStack) : null;\n}\n\nfunction isOrHasCallExpression(node: t.Node): boolean {\n  if (isCallExpression(node)) {\n    return true;\n  }\n\n  return isMemberExpression(node) && isOrHasCallExpression(node.object);\n}\n\nexport function needsWhitespace(\n  node: t.Node,\n  parent: t.Node,\n  type: WhitespaceFlag,\n): boolean {\n  if (!node) return false;\n\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  const flag = find(expandedWhitespaceNodes, node, parent);\n\n  if (typeof flag === \"number\") {\n    return (flag & type) !== 0;\n  }\n\n  return false;\n}\n\nexport function needsWhitespaceBefore(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 1);\n}\n\nexport function needsWhitespaceAfter(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 2);\n}\n\nexport function needsParens(\n  node: t.Node,\n  parent: t.Node,\n  printStack?: t.Node[],\n) {\n  if (!parent) return false;\n\n  if (isNewExpression(parent) && parent.callee === node) {\n    if (isOrHasCallExpression(node)) return true;\n  }\n\n  return find(expandedParens, node, parent, printStack);\n}\n"], "mappings": ";;;;;;;;;;AAAA;;AACA;;AACA;;;EACEA,kB;EACAC,gB;EACAC,qB;EACAC,kB;EACAC;;;AAkBF,SAASC,aAAT,CAA0BC,GAA1B,EAAgD;EAC9C,MAAMC,MAAuB,GAAG,EAAhC;;EAEA,SAASC,GAAT,CACEC,IADF,EAEEC,IAFF,EAGE;IACA,MAAMC,EAAE,GAAGJ,MAAM,CAACE,IAAD,CAAjB;IACAF,MAAM,CAACE,IAAD,CAAN,GAAeE,EAAE,GACb,UAAUC,IAAV,EAAgBC,MAAhB,EAAwBC,KAAxB,EAA+B;MAC7B,MAAMC,MAAM,GAAGJ,EAAE,CAACC,IAAD,EAAOC,MAAP,EAAeC,KAAf,CAAjB;MAEA,OAAOC,MAAM,IAAI,IAAV,GAAiBL,IAAI,CAACE,IAAD,EAAOC,MAAP,EAAeC,KAAf,CAArB,GAA6CC,MAApD;IACD,CALY,GAMbL,IANJ;EAOD;;EAED,KAAK,MAAMD,IAAX,IAAmBO,MAAM,CAACC,IAAP,CAAYX,GAAZ,CAAnB,EAAqC;IACnC,MAAMY,OAAO,GAAGlB,kBAAkB,CAACS,IAAD,CAAlC;;IACA,IAAIS,OAAJ,EAAa;MACX,KAAK,MAAMC,KAAX,IAAoBD,OAApB,EAA6B;QAC3BV,GAAG,CAACW,KAAD,EAAQb,GAAG,CAACG,IAAD,CAAX,CAAH;MACD;IACF,CAJD,MAIO;MACLD,GAAG,CAACC,IAAD,EAAOH,GAAG,CAACG,IAAD,CAAV,CAAH;IACD;EACF;;EAED,OAAOF,MAAP;AACD;;AAID,MAAMa,cAAc,GAAGf,aAAa,CAACgB,MAAD,CAApC;AACA,MAAMC,uBAAuB,GAAGjB,aAAa,CAACkB,UAAU,CAACC,KAAZ,CAA7C;;AAEA,SAASC,IAAT,CACEnB,GADF,EAEEM,IAFF,EAGEC,MAHF,EAIEa,UAJF,EAKY;EACV,MAAMf,EAAE,GAAGL,GAAG,CAACM,IAAI,CAACH,IAAN,CAAd;EACA,OAAOE,EAAE,GAAGA,EAAE,CAACC,IAAD,EAAOC,MAAP,EAAea,UAAf,CAAL,GAAkC,IAA3C;AACD;;AAED,SAASC,qBAAT,CAA+Bf,IAA/B,EAAsD;EACpD,IAAIX,gBAAgB,CAACW,IAAD,CAApB,EAA4B;IAC1B,OAAO,IAAP;EACD;;EAED,OAAOT,kBAAkB,CAACS,IAAD,CAAlB,IAA4Be,qBAAqB,CAACf,IAAI,CAACgB,MAAN,CAAxD;AACD;;AAEM,SAASC,eAAT,CACLjB,IADK,EAELC,MAFK,EAGLJ,IAHK,EAII;EACT,IAAI,CAACG,IAAL,EAAW,OAAO,KAAP;;EAEX,IAAIV,qBAAqB,CAACU,IAAD,CAAzB,EAAiC;IAC/BA,IAAI,GAAGA,IAAI,CAACkB,UAAZ;EACD;;EAED,MAAMC,IAAI,GAAGN,IAAI,CAACH,uBAAD,EAA0BV,IAA1B,EAAgCC,MAAhC,CAAjB;;EAEA,IAAI,OAAOkB,IAAP,KAAgB,QAApB,EAA8B;IAC5B,OAAO,CAACA,IAAI,GAAGtB,IAAR,MAAkB,CAAzB;EACD;;EAED,OAAO,KAAP;AACD;;AAEM,SAASuB,qBAAT,CAA+BpB,IAA/B,EAA6CC,MAA7C,EAA6D;EAClE,OAAOgB,eAAe,CAACjB,IAAD,EAAOC,MAAP,EAAe,CAAf,CAAtB;AACD;;AAEM,SAASoB,oBAAT,CAA8BrB,IAA9B,EAA4CC,MAA5C,EAA4D;EACjE,OAAOgB,eAAe,CAACjB,IAAD,EAAOC,MAAP,EAAe,CAAf,CAAtB;AACD;;AAEM,SAASqB,WAAT,CACLtB,IADK,EAELC,MAFK,EAGLa,UAHK,EAIL;EACA,IAAI,CAACb,MAAL,EAAa,OAAO,KAAP;;EAEb,IAAIT,eAAe,CAACS,MAAD,CAAf,IAA2BA,MAAM,CAACsB,MAAP,KAAkBvB,IAAjD,EAAuD;IACrD,IAAIe,qBAAqB,CAACf,IAAD,CAAzB,EAAiC,OAAO,IAAP;EAClC;;EAED,OAAOa,IAAI,CAACL,cAAD,EAAiBR,IAAjB,EAAuBC,MAAvB,EAA+Ba,UAA/B,CAAX;AACD"}