{"version": 3, "names": ["FLIPPED_ALIAS_KEYS", "isArrayExpression", "isAssignmentExpression", "isBinary", "isBlockStatement", "isCallExpression", "isFunction", "isIdentifier", "isLiteral", "isMemberExpression", "isObjectExpression", "isOptionalCallExpression", "isOptionalMemberExpression", "isStringLiteral", "crawlInternal", "node", "state", "object", "computed", "property", "left", "right", "hasCall", "callee", "hasFunction", "<PERSON><PERSON><PERSON><PERSON>", "is<PERSON><PERSON>per", "crawl", "name", "charCodeAt", "isType", "nodes", "AssignmentExpression", "SwitchCase", "parent", "consequent", "length", "cases", "LogicalExpression", "Literal", "value", "CallExpression", "OptionalCallExpression", "VariableDeclaration", "i", "declarations", "declar", "enabled", "id", "init", "IfStatement", "ObjectProperty", "ObjectTypeProperty", "ObjectMethod", "properties", "ObjectTypeCallProperty", "callProperties", "ObjectTypeIndexer", "indexers", "ObjectTypeInternalSlot", "internalSlots", "for<PERSON>ach", "type", "amounts", "concat", "ret"], "sources": ["../../src/node/whitespace.ts"], "sourcesContent": ["import {\n  FLIPPED_ALIAS_KEYS,\n  isArrayExpression,\n  isAssignmentExpression,\n  isBinary,\n  isBlockStatement,\n  isCallExpression,\n  isFunction,\n  isIdentifier,\n  isLiteral,\n  isMemberExpression,\n  isObjectExpression,\n  isOptionalCallExpression,\n  isOptionalMemberExpression,\n  isStringLiteral,\n} from \"@babel/types\";\nimport * as charCodes from \"charcodes\";\n\nimport type { NodeHandlers } from \"./index\";\n\nimport type * as t from \"@babel/types\";\n\nconst enum WhitespaceFlag {\n  before = 1 << 0,\n  after = 1 << 1,\n}\n\nexport type { WhitespaceFlag };\n\nfunction crawlInternal(\n  node: t.Node,\n  state: { hasCall: boolean; hasFunction: boolean; hasHelper: boolean },\n) {\n  if (!node) return state;\n\n  if (isMemberExpression(node) || isOptionalMemberExpression(node)) {\n    crawlInternal(node.object, state);\n    if (node.computed) crawlInternal(node.property, state);\n  } else if (isBinary(node) || isAssignmentExpression(node)) {\n    crawlInternal(node.left, state);\n    crawlInternal(node.right, state);\n  } else if (isCallExpression(node) || isOptionalCallExpression(node)) {\n    state.hasCall = true;\n    crawlInternal(node.callee, state);\n  } else if (isFunction(node)) {\n    state.hasFunction = true;\n  } else if (isIdentifier(node)) {\n    state.hasHelper =\n      // @ts-expect-error todo(flow->ts): node.callee is not really expected here…\n      state.hasHelper || (node.callee && isHelper(node.callee));\n  }\n\n  return state;\n}\n\n/**\n * Crawl a node to test if it contains a CallExpression, a Function, or a Helper.\n *\n * @example\n * crawl(node)\n * // { hasCall: false, hasFunction: true, hasHelper: false }\n */\n\nfunction crawl(node: t.Node) {\n  return crawlInternal(node, {\n    hasCall: false,\n    hasFunction: false,\n    hasHelper: false,\n  });\n}\n\n/**\n * Test if a node is or has a helper.\n */\n\nfunction isHelper(node: t.Node): boolean {\n  if (!node) return false;\n\n  if (isMemberExpression(node)) {\n    return isHelper(node.object) || isHelper(node.property);\n  } else if (isIdentifier(node)) {\n    return (\n      node.name === \"require\" ||\n      node.name.charCodeAt(0) === charCodes.underscore\n    );\n  } else if (isCallExpression(node)) {\n    return isHelper(node.callee);\n  } else if (isBinary(node) || isAssignmentExpression(node)) {\n    return (\n      (isIdentifier(node.left) && isHelper(node.left)) || isHelper(node.right)\n    );\n  } else {\n    return false;\n  }\n}\n\nfunction isType(node: t.Node) {\n  return (\n    isLiteral(node) ||\n    isObjectExpression(node) ||\n    isArrayExpression(node) ||\n    isIdentifier(node) ||\n    isMemberExpression(node)\n  );\n}\n\n/**\n * Tests for node types that need whitespace.\n */\n\nexport const nodes: NodeHandlers<WhitespaceFlag> = {\n  /**\n   * Test if AssignmentExpression needs whitespace.\n   */\n\n  AssignmentExpression(node: t.AssignmentExpression): WhitespaceFlag {\n    const state = crawl(node.right);\n    if ((state.hasCall && state.hasHelper) || state.hasFunction) {\n      return state.hasFunction\n        ? WhitespaceFlag.before | WhitespaceFlag.after\n        : WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if SwitchCase needs whitespace.\n   */\n\n  SwitchCase(node: t.SwitchCase, parent: t.SwitchStatement): WhitespaceFlag {\n    return (\n      (!!node.consequent.length || parent.cases[0] === node\n        ? WhitespaceFlag.before\n        : 0) |\n      (!node.consequent.length && parent.cases[parent.cases.length - 1] === node\n        ? WhitespaceFlag.after\n        : 0)\n    );\n  },\n\n  /**\n   * Test if LogicalExpression needs whitespace.\n   */\n\n  LogicalExpression(node: t.LogicalExpression): WhitespaceFlag {\n    if (isFunction(node.left) || isFunction(node.right)) {\n      return WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if Literal needs whitespace.\n   */\n\n  Literal(node: t.Literal): WhitespaceFlag {\n    if (isStringLiteral(node) && node.value === \"use strict\") {\n      return WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if CallExpressionish needs whitespace.\n   */\n\n  CallExpression(node: t.CallExpression): WhitespaceFlag {\n    if (isFunction(node.callee) || isHelper(node)) {\n      return WhitespaceFlag.before | WhitespaceFlag.after;\n    }\n  },\n\n  OptionalCallExpression(node: t.OptionalCallExpression): WhitespaceFlag {\n    if (isFunction(node.callee)) {\n      return WhitespaceFlag.before | WhitespaceFlag.after;\n    }\n  },\n\n  /**\n   * Test if VariableDeclaration needs whitespace.\n   */\n\n  VariableDeclaration(node: t.VariableDeclaration): WhitespaceFlag {\n    for (let i = 0; i < node.declarations.length; i++) {\n      const declar = node.declarations[i];\n\n      let enabled = isHelper(declar.id) && !isType(declar.init);\n      if (!enabled && declar.init) {\n        const state = crawl(declar.init);\n        enabled = (isHelper(declar.init) && state.hasCall) || state.hasFunction;\n      }\n\n      if (enabled) {\n        return WhitespaceFlag.before | WhitespaceFlag.after;\n      }\n    }\n  },\n\n  /**\n   * Test if IfStatement needs whitespace.\n   */\n\n  IfStatement(node: t.IfStatement): WhitespaceFlag {\n    if (isBlockStatement(node.consequent)) {\n      return WhitespaceFlag.before | WhitespaceFlag.after;\n    }\n  },\n};\n\n/**\n * Test if Property needs whitespace.\n */\n\nnodes.ObjectProperty =\n  nodes.ObjectTypeProperty =\n  nodes.ObjectMethod =\n    function (\n      node: t.ObjectProperty | t.ObjectTypeProperty | t.ObjectMethod,\n      parent: t.ObjectExpression,\n    ): WhitespaceFlag {\n      if (parent.properties[0] === node) {\n        return WhitespaceFlag.before;\n      }\n    };\n\nnodes.ObjectTypeCallProperty = function (\n  node: t.ObjectTypeCallProperty,\n  parent: t.ObjectTypeAnnotation,\n): WhitespaceFlag {\n  if (parent.callProperties[0] === node && !parent.properties?.length) {\n    return WhitespaceFlag.before;\n  }\n};\n\nnodes.ObjectTypeIndexer = function (\n  node: t.ObjectTypeIndexer,\n  parent: t.ObjectTypeAnnotation,\n): WhitespaceFlag {\n  if (\n    parent.indexers[0] === node &&\n    !parent.properties?.length &&\n    !parent.callProperties?.length\n  ) {\n    return WhitespaceFlag.before;\n  }\n};\n\nnodes.ObjectTypeInternalSlot = function (\n  node: t.ObjectTypeInternalSlot,\n  parent: t.ObjectTypeAnnotation,\n): WhitespaceFlag {\n  if (\n    parent.internalSlots[0] === node &&\n    !parent.properties?.length &&\n    !parent.callProperties?.length &&\n    !parent.indexers?.length\n  ) {\n    return WhitespaceFlag.before;\n  }\n};\n\n/**\n * Add whitespace tests for nodes and their aliases.\n */\n\n(\n  [\n    [\"Function\", true],\n    [\"Class\", true],\n    [\"Loop\", true],\n    [\"LabeledStatement\", true],\n    [\"SwitchStatement\", true],\n    [\"TryStatement\", true],\n  ] as const\n).forEach(function ([type, amounts]) {\n  [type as string]\n    .concat(FLIPPED_ALIAS_KEYS[type] || [])\n    .forEach(function (type) {\n      const ret = amounts ? WhitespaceFlag.before | WhitespaceFlag.after : 0;\n      nodes[type] = () => ret;\n    });\n});\n"], "mappings": ";;;;;;;AAAA;;;EACEA,kB;EACAC,iB;EACAC,sB;EACAC,Q;EACAC,gB;EACAC,gB;EACAC,U;EACAC,Y;EACAC,S;EACAC,kB;EACAC,kB;EACAC,wB;EACAC,0B;EACAC;;;AAeF,SAASC,aAAT,CACEC,IADF,EAEEC,KAFF,EAGE;EACA,IAAI,CAACD,IAAL,EAAW,OAAOC,KAAP;;EAEX,IAAIP,kBAAkB,CAACM,IAAD,CAAlB,IAA4BH,0BAA0B,CAACG,IAAD,CAA1D,EAAkE;IAChED,aAAa,CAACC,IAAI,CAACE,MAAN,EAAcD,KAAd,CAAb;IACA,IAAID,IAAI,CAACG,QAAT,EAAmBJ,aAAa,CAACC,IAAI,CAACI,QAAN,EAAgBH,KAAhB,CAAb;EACpB,CAHD,MAGO,IAAIb,QAAQ,CAACY,IAAD,CAAR,IAAkBb,sBAAsB,CAACa,IAAD,CAA5C,EAAoD;IACzDD,aAAa,CAACC,IAAI,CAACK,IAAN,EAAYJ,KAAZ,CAAb;IACAF,aAAa,CAACC,IAAI,CAACM,KAAN,EAAaL,KAAb,CAAb;EACD,CAHM,MAGA,IAAIX,gBAAgB,CAACU,IAAD,CAAhB,IAA0BJ,wBAAwB,CAACI,IAAD,CAAtD,EAA8D;IACnEC,KAAK,CAACM,OAAN,GAAgB,IAAhB;IACAR,aAAa,CAACC,IAAI,CAACQ,MAAN,EAAcP,KAAd,CAAb;EACD,CAHM,MAGA,IAAIV,UAAU,CAACS,IAAD,CAAd,EAAsB;IAC3BC,KAAK,CAACQ,WAAN,GAAoB,IAApB;EACD,CAFM,MAEA,IAAIjB,YAAY,CAACQ,IAAD,CAAhB,EAAwB;IAC7BC,KAAK,CAACS,SAAN,GAEET,KAAK,CAACS,SAAN,IAAoBV,IAAI,CAACQ,MAAL,IAAeG,QAAQ,CAACX,IAAI,CAACQ,MAAN,CAF7C;EAGD;;EAED,OAAOP,KAAP;AACD;;AAUD,SAASW,KAAT,CAAeZ,IAAf,EAA6B;EAC3B,OAAOD,aAAa,CAACC,IAAD,EAAO;IACzBO,OAAO,EAAE,KADgB;IAEzBE,WAAW,EAAE,KAFY;IAGzBC,SAAS,EAAE;EAHc,CAAP,CAApB;AAKD;;AAMD,SAASC,QAAT,CAAkBX,IAAlB,EAAyC;EACvC,IAAI,CAACA,IAAL,EAAW,OAAO,KAAP;;EAEX,IAAIN,kBAAkB,CAACM,IAAD,CAAtB,EAA8B;IAC5B,OAAOW,QAAQ,CAACX,IAAI,CAACE,MAAN,CAAR,IAAyBS,QAAQ,CAACX,IAAI,CAACI,QAAN,CAAxC;EACD,CAFD,MAEO,IAAIZ,YAAY,CAACQ,IAAD,CAAhB,EAAwB;IAC7B,OACEA,IAAI,CAACa,IAAL,KAAc,SAAd,IACAb,IAAI,CAACa,IAAL,CAAUC,UAAV,CAAqB,CAArB,QAFF;EAID,CALM,MAKA,IAAIxB,gBAAgB,CAACU,IAAD,CAApB,EAA4B;IACjC,OAAOW,QAAQ,CAACX,IAAI,CAACQ,MAAN,CAAf;EACD,CAFM,MAEA,IAAIpB,QAAQ,CAACY,IAAD,CAAR,IAAkBb,sBAAsB,CAACa,IAAD,CAA5C,EAAoD;IACzD,OACGR,YAAY,CAACQ,IAAI,CAACK,IAAN,CAAZ,IAA2BM,QAAQ,CAACX,IAAI,CAACK,IAAN,CAApC,IAAoDM,QAAQ,CAACX,IAAI,CAACM,KAAN,CAD9D;EAGD,CAJM,MAIA;IACL,OAAO,KAAP;EACD;AACF;;AAED,SAASS,MAAT,CAAgBf,IAAhB,EAA8B;EAC5B,OACEP,SAAS,CAACO,IAAD,CAAT,IACAL,kBAAkB,CAACK,IAAD,CADlB,IAEAd,iBAAiB,CAACc,IAAD,CAFjB,IAGAR,YAAY,CAACQ,IAAD,CAHZ,IAIAN,kBAAkB,CAACM,IAAD,CALpB;AAOD;;AAMM,MAAMgB,KAAmC,GAAG;EAKjDC,oBAAoB,CAACjB,IAAD,EAA+C;IACjE,MAAMC,KAAK,GAAGW,KAAK,CAACZ,IAAI,CAACM,KAAN,CAAnB;;IACA,IAAKL,KAAK,CAACM,OAAN,IAAiBN,KAAK,CAACS,SAAxB,IAAsCT,KAAK,CAACQ,WAAhD,EAA6D;MAC3D,OAAOR,KAAK,CAACQ,WAAN,GACH,KADG,IAAP;IAGD;EACF,CAZgD;;EAkBjDS,UAAU,CAAClB,IAAD,EAAqBmB,MAArB,EAAgE;IACxE,OACE,CAAC,CAAC,CAACnB,IAAI,CAACoB,UAAL,CAAgBC,MAAlB,IAA4BF,MAAM,CAACG,KAAP,CAAa,CAAb,MAAoBtB,IAAhD,OAEG,CAFJ,KAGC,CAACA,IAAI,CAACoB,UAAL,CAAgBC,MAAjB,IAA2BF,MAAM,CAACG,KAAP,CAAaH,MAAM,CAACG,KAAP,CAAaD,MAAb,GAAsB,CAAnC,MAA0CrB,IAArE,OAEG,CALJ,CADF;EAQD,CA3BgD;;EAiCjDuB,iBAAiB,CAACvB,IAAD,EAA4C;IAC3D,IAAIT,UAAU,CAACS,IAAI,CAACK,IAAN,CAAV,IAAyBd,UAAU,CAACS,IAAI,CAACM,KAAN,CAAvC,EAAqD;MACnD;IACD;EACF,CArCgD;;EA2CjDkB,OAAO,CAACxB,IAAD,EAAkC;IACvC,IAAIF,eAAe,CAACE,IAAD,CAAf,IAAyBA,IAAI,CAACyB,KAAL,KAAe,YAA5C,EAA0D;MACxD;IACD;EACF,CA/CgD;;EAqDjDC,cAAc,CAAC1B,IAAD,EAAyC;IACrD,IAAIT,UAAU,CAACS,IAAI,CAACQ,MAAN,CAAV,IAA2BG,QAAQ,CAACX,IAAD,CAAvC,EAA+C;MAC7C,OAAO,KAAP;IACD;EACF,CAzDgD;;EA2DjD2B,sBAAsB,CAAC3B,IAAD,EAAiD;IACrE,IAAIT,UAAU,CAACS,IAAI,CAACQ,MAAN,CAAd,EAA6B;MAC3B,OAAO,KAAP;IACD;EACF,CA/DgD;;EAqEjDoB,mBAAmB,CAAC5B,IAAD,EAA8C;IAC/D,KAAK,IAAI6B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG7B,IAAI,CAAC8B,YAAL,CAAkBT,MAAtC,EAA8CQ,CAAC,EAA/C,EAAmD;MACjD,MAAME,MAAM,GAAG/B,IAAI,CAAC8B,YAAL,CAAkBD,CAAlB,CAAf;MAEA,IAAIG,OAAO,GAAGrB,QAAQ,CAACoB,MAAM,CAACE,EAAR,CAAR,IAAuB,CAAClB,MAAM,CAACgB,MAAM,CAACG,IAAR,CAA5C;;MACA,IAAI,CAACF,OAAD,IAAYD,MAAM,CAACG,IAAvB,EAA6B;QAC3B,MAAMjC,KAAK,GAAGW,KAAK,CAACmB,MAAM,CAACG,IAAR,CAAnB;QACAF,OAAO,GAAIrB,QAAQ,CAACoB,MAAM,CAACG,IAAR,CAAR,IAAyBjC,KAAK,CAACM,OAAhC,IAA4CN,KAAK,CAACQ,WAA5D;MACD;;MAED,IAAIuB,OAAJ,EAAa;QACX,OAAO,KAAP;MACD;IACF;EACF,CAnFgD;;EAyFjDG,WAAW,CAACnC,IAAD,EAAsC;IAC/C,IAAIX,gBAAgB,CAACW,IAAI,CAACoB,UAAN,CAApB,EAAuC;MACrC,OAAO,KAAP;IACD;EACF;;AA7FgD,CAA5C;;;AAoGPJ,KAAK,CAACoB,cAAN,GACEpB,KAAK,CAACqB,kBAAN,GACArB,KAAK,CAACsB,YAAN,GACE,UACEtC,IADF,EAEEmB,MAFF,EAGkB;EAChB,IAAIA,MAAM,CAACoB,UAAP,CAAkB,CAAlB,MAAyBvC,IAA7B,EAAmC;IACjC;EACD;AACF,CAVL;;AAYAgB,KAAK,CAACwB,sBAAN,GAA+B,UAC7BxC,IAD6B,EAE7BmB,MAF6B,EAGb;EAAA;;EAChB,IAAIA,MAAM,CAACsB,cAAP,CAAsB,CAAtB,MAA6BzC,IAA7B,IAAqC,wBAACmB,MAAM,CAACoB,UAAR,aAAC,mBAAmBlB,MAApB,CAAzC,EAAqE;IACnE;EACD;AACF,CAPD;;AASAL,KAAK,CAAC0B,iBAAN,GAA0B,UACxB1C,IADwB,EAExBmB,MAFwB,EAGR;EAAA;;EAChB,IACEA,MAAM,CAACwB,QAAP,CAAgB,CAAhB,MAAuB3C,IAAvB,IACA,yBAACmB,MAAM,CAACoB,UAAR,aAAC,oBAAmBlB,MAApB,CADA,IAEA,2BAACF,MAAM,CAACsB,cAAR,aAAC,sBAAuBpB,MAAxB,CAHF,EAIE;IACA;EACD;AACF,CAXD;;AAaAL,KAAK,CAAC4B,sBAAN,GAA+B,UAC7B5C,IAD6B,EAE7BmB,MAF6B,EAGb;EAAA;;EAChB,IACEA,MAAM,CAAC0B,aAAP,CAAqB,CAArB,MAA4B7C,IAA5B,IACA,yBAACmB,MAAM,CAACoB,UAAR,aAAC,oBAAmBlB,MAApB,CADA,IAEA,4BAACF,MAAM,CAACsB,cAAR,aAAC,uBAAuBpB,MAAxB,CAFA,IAGA,sBAACF,MAAM,CAACwB,QAAR,aAAC,iBAAiBtB,MAAlB,CAJF,EAKE;IACA;EACD;AACF,CAZD;;AAmBE,CACE,CAAC,UAAD,EAAa,IAAb,CADF,EAEE,CAAC,OAAD,EAAU,IAAV,CAFF,EAGE,CAAC,MAAD,EAAS,IAAT,CAHF,EAIE,CAAC,kBAAD,EAAqB,IAArB,CAJF,EAKE,CAAC,iBAAD,EAAoB,IAApB,CALF,EAME,CAAC,cAAD,EAAiB,IAAjB,CANF,CADF,CASEyB,OATF,CASU,UAAU,CAACC,IAAD,EAAOC,OAAP,CAAV,EAA2B;EACnC,CAACD,IAAD,EACGE,MADH,CACUhE,kBAAkB,CAAC8D,IAAD,CAAlB,IAA4B,EADtC,EAEGD,OAFH,CAEW,UAAUC,IAAV,EAAgB;IACvB,MAAMG,GAAG,GAAGF,OAAO,GAAG,KAAH,GAAkD,CAArE;;IACAhC,KAAK,CAAC+B,IAAD,CAAL,GAAc,MAAMG,GAApB;EACD,CALH;AAMD,CAhBD"}