{"version": 3, "names": ["isIdentifier", "_params", "node", "print", "typeParameters", "token", "_parameters", "params", "returnType", "type", "parameters", "parent", "i", "length", "_param", "space", "parameter", "printJoin", "decorators", "optional", "typeAnnotation", "_methodHead", "kind", "key", "word", "async", "_catchUp", "loc", "generator", "computed", "_predicate", "predicate", "_functionHead", "printInnerComments", "id", "FunctionExpression", "body", "ArrowFunctionExpression", "firstParam", "format", "retainLines", "auxiliaryCommentBefore", "auxiliaryCommentAfter", "hasTypesOrComments", "param", "leadingComments", "trailingComments"], "sources": ["../../src/generators/methods.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport { isIdentifier } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nexport function _params(\n  this: Printer,\n  node: t.Function | t.TSDeclareMethod | t.TSDeclareFunction,\n) {\n  this.print(node.typeParameters, node);\n  this.token(\"(\");\n  this._parameters(node.params, node);\n  this.token(\")\");\n\n  this.print(node.returnType, node, node.type === \"ArrowFunctionExpression\");\n}\n\nexport function _parameters(\n  this: Printer,\n  parameters: t.Function[\"params\"],\n  parent:\n    | t.Function\n    | t.TSIndexSignature\n    | t.TSDeclareMethod\n    | t.TSDeclareFunction\n    | t.TSFunctionType\n    | t.TSConstructorType,\n) {\n  for (let i = 0; i < parameters.length; i++) {\n    this._param(parameters[i], parent);\n\n    if (i < parameters.length - 1) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n}\n\nexport function _param(\n  this: Printer,\n  parameter: t.Identifier | t.RestElement | t.Pattern | t.TSParameterProperty,\n  parent?:\n    | t.Function\n    | t.TSIndexSignature\n    | t.TSDeclareMethod\n    | t.TSDeclareFunction\n    | t.TSFunctionType\n    | t.TSConstructorType,\n) {\n  this.printJoin(parameter.decorators, parameter);\n  this.print(parameter, parent);\n  if (\n    // @ts-expect-error optional is not in TSParameterProperty\n    parameter.optional\n  ) {\n    this.token(\"?\"); // TS / flow\n  }\n\n  this.print(\n    // @ts-expect-error typeAnnotation is not in TSParameterProperty\n    parameter.typeAnnotation,\n    parameter,\n  ); // TS / flow\n}\n\nexport function _methodHead(this: Printer, node: t.Method | t.TSDeclareMethod) {\n  const kind = node.kind;\n  const key = node.key;\n\n  if (kind === \"get\" || kind === \"set\") {\n    this.word(kind);\n    this.space();\n  }\n\n  if (node.async) {\n    // ensure `async` is in the same line with property name\n    this._catchUp(\"start\", key.loc);\n    this.word(\"async\");\n    this.space();\n  }\n\n  if (\n    kind === \"method\" ||\n    // @ts-expect-error Fixme: kind: \"init\" is not defined\n    kind === \"init\"\n  ) {\n    if (node.generator) {\n      this.token(\"*\");\n    }\n  }\n\n  if (node.computed) {\n    this.token(\"[\");\n    this.print(key, node);\n    this.token(\"]\");\n  } else {\n    this.print(key, node);\n  }\n\n  if (\n    // @ts-expect-error optional is not in ObjectMethod\n    node.optional\n  ) {\n    // TS\n    this.token(\"?\");\n  }\n\n  this._params(node);\n}\n\nexport function _predicate(\n  this: Printer,\n  node:\n    | t.FunctionDeclaration\n    | t.FunctionExpression\n    | t.ArrowFunctionExpression,\n) {\n  if (node.predicate) {\n    if (!node.returnType) {\n      this.token(\":\");\n    }\n    this.space();\n    this.print(node.predicate, node);\n  }\n}\n\nexport function _functionHead(\n  this: Printer,\n  node: t.FunctionDeclaration | t.FunctionExpression | t.TSDeclareFunction,\n) {\n  if (node.async) {\n    this.word(\"async\");\n    this.space();\n  }\n  this.word(\"function\");\n  if (node.generator) this.token(\"*\");\n  this.printInnerComments(node);\n\n  this.space();\n  if (node.id) {\n    this.print(node.id, node);\n  }\n\n  this._params(node);\n  if (node.type !== \"TSDeclareFunction\") {\n    this._predicate(node);\n  }\n}\n\nexport function FunctionExpression(this: Printer, node: t.FunctionExpression) {\n  this._functionHead(node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport { FunctionExpression as FunctionDeclaration };\n\nexport function ArrowFunctionExpression(\n  this: Printer,\n  node: t.ArrowFunctionExpression,\n) {\n  if (node.async) {\n    this.word(\"async\");\n    this.space();\n  }\n\n  const firstParam = node.params[0];\n\n  // Try to avoid printing parens in simple cases, but only if we're pretty\n  // sure that they aren't needed by type annotations or potential newlines.\n  if (\n    !this.format.retainLines &&\n    // Auxiliary comments can introduce unexpected newlines\n    !this.format.auxiliaryCommentBefore &&\n    !this.format.auxiliaryCommentAfter &&\n    node.params.length === 1 &&\n    isIdentifier(firstParam) &&\n    !hasTypesOrComments(node, firstParam)\n  ) {\n    this.print(firstParam, node);\n  } else {\n    this._params(node);\n  }\n\n  this._predicate(node);\n\n  this.space();\n  this.token(\"=>\");\n  this.space();\n\n  this.print(node.body, node);\n}\n\nfunction hasTypesOrComments(\n  node: t.ArrowFunctionExpression,\n  param: t.Identifier,\n): boolean {\n  return !!(\n    node.typeParameters ||\n    node.returnType ||\n    node.predicate ||\n    param.typeAnnotation ||\n    param.optional ||\n    param.leadingComments?.length ||\n    param.trailingComments?.length\n  );\n}\n"], "mappings": ";;;;;;;;;;;;;;AACA;;;EAASA;;;AAGF,SAASC,OAAT,CAELC,IAFK,EAGL;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACE,cAAhB,EAAgCF,IAAhC;EACA,KAAKG,SAAL;;EACA,KAAKC,WAAL,CAAiBJ,IAAI,CAACK,MAAtB,EAA8BL,IAA9B;;EACA,KAAKG,SAAL;EAEA,KAAKF,KAAL,CAAWD,IAAI,CAACM,UAAhB,EAA4BN,IAA5B,EAAkCA,IAAI,CAACO,IAAL,KAAc,yBAAhD;AACD;;AAEM,SAASH,WAAT,CAELI,UAFK,EAGLC,MAHK,EAUL;EACA,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGF,UAAU,CAACG,MAA/B,EAAuCD,CAAC,EAAxC,EAA4C;IAC1C,KAAKE,MAAL,CAAYJ,UAAU,CAACE,CAAD,CAAtB,EAA2BD,MAA3B;;IAEA,IAAIC,CAAC,GAAGF,UAAU,CAACG,MAAX,GAAoB,CAA5B,EAA+B;MAC7B,KAAKR,SAAL;MACA,KAAKU,KAAL;IACD;EACF;AACF;;AAEM,SAASD,MAAT,CAELE,SAFK,EAGLL,MAHK,EAUL;EACA,KAAKM,SAAL,CAAeD,SAAS,CAACE,UAAzB,EAAqCF,SAArC;EACA,KAAKb,KAAL,CAAWa,SAAX,EAAsBL,MAAtB;;EACA,IAEEK,SAAS,CAACG,QAFZ,EAGE;IACA,KAAKd,SAAL;EACD;;EAED,KAAKF,KAAL,CAEEa,SAAS,CAACI,cAFZ,EAGEJ,SAHF;AAKD;;AAEM,SAASK,WAAT,CAAoCnB,IAApC,EAAwE;EAC7E,MAAMoB,IAAI,GAAGpB,IAAI,CAACoB,IAAlB;EACA,MAAMC,GAAG,GAAGrB,IAAI,CAACqB,GAAjB;;EAEA,IAAID,IAAI,KAAK,KAAT,IAAkBA,IAAI,KAAK,KAA/B,EAAsC;IACpC,KAAKE,IAAL,CAAUF,IAAV;IACA,KAAKP,KAAL;EACD;;EAED,IAAIb,IAAI,CAACuB,KAAT,EAAgB;IAEd,KAAKC,QAAL,CAAc,OAAd,EAAuBH,GAAG,CAACI,GAA3B;;IACA,KAAKH,IAAL,CAAU,OAAV;IACA,KAAKT,KAAL;EACD;;EAED,IACEO,IAAI,KAAK,QAAT,IAEAA,IAAI,KAAK,MAHX,EAIE;IACA,IAAIpB,IAAI,CAAC0B,SAAT,EAAoB;MAClB,KAAKvB,SAAL;IACD;EACF;;EAED,IAAIH,IAAI,CAAC2B,QAAT,EAAmB;IACjB,KAAKxB,SAAL;IACA,KAAKF,KAAL,CAAWoB,GAAX,EAAgBrB,IAAhB;IACA,KAAKG,SAAL;EACD,CAJD,MAIO;IACL,KAAKF,KAAL,CAAWoB,GAAX,EAAgBrB,IAAhB;EACD;;EAED,IAEEA,IAAI,CAACiB,QAFP,EAGE;IAEA,KAAKd,SAAL;EACD;;EAED,KAAKJ,OAAL,CAAaC,IAAb;AACD;;AAEM,SAAS4B,UAAT,CAEL5B,IAFK,EAML;EACA,IAAIA,IAAI,CAAC6B,SAAT,EAAoB;IAClB,IAAI,CAAC7B,IAAI,CAACM,UAAV,EAAsB;MACpB,KAAKH,SAAL;IACD;;IACD,KAAKU,KAAL;IACA,KAAKZ,KAAL,CAAWD,IAAI,CAAC6B,SAAhB,EAA2B7B,IAA3B;EACD;AACF;;AAEM,SAAS8B,aAAT,CAEL9B,IAFK,EAGL;EACA,IAAIA,IAAI,CAACuB,KAAT,EAAgB;IACd,KAAKD,IAAL,CAAU,OAAV;IACA,KAAKT,KAAL;EACD;;EACD,KAAKS,IAAL,CAAU,UAAV;EACA,IAAItB,IAAI,CAAC0B,SAAT,EAAoB,KAAKvB,SAAL;EACpB,KAAK4B,kBAAL,CAAwB/B,IAAxB;EAEA,KAAKa,KAAL;;EACA,IAAIb,IAAI,CAACgC,EAAT,EAAa;IACX,KAAK/B,KAAL,CAAWD,IAAI,CAACgC,EAAhB,EAAoBhC,IAApB;EACD;;EAED,KAAKD,OAAL,CAAaC,IAAb;;EACA,IAAIA,IAAI,CAACO,IAAL,KAAc,mBAAlB,EAAuC;IACrC,KAAKqB,UAAL,CAAgB5B,IAAhB;EACD;AACF;;AAEM,SAASiC,kBAAT,CAA2CjC,IAA3C,EAAuE;EAC5E,KAAK8B,aAAL,CAAmB9B,IAAnB;;EACA,KAAKa,KAAL;EACA,KAAKZ,KAAL,CAAWD,IAAI,CAACkC,IAAhB,EAAsBlC,IAAtB;AACD;;AAIM,SAASmC,uBAAT,CAELnC,IAFK,EAGL;EACA,IAAIA,IAAI,CAACuB,KAAT,EAAgB;IACd,KAAKD,IAAL,CAAU,OAAV;IACA,KAAKT,KAAL;EACD;;EAED,MAAMuB,UAAU,GAAGpC,IAAI,CAACK,MAAL,CAAY,CAAZ,CAAnB;;EAIA,IACE,CAAC,KAAKgC,MAAL,CAAYC,WAAb,IAEA,CAAC,KAAKD,MAAL,CAAYE,sBAFb,IAGA,CAAC,KAAKF,MAAL,CAAYG,qBAHb,IAIAxC,IAAI,CAACK,MAAL,CAAYM,MAAZ,KAAuB,CAJvB,IAKAb,YAAY,CAACsC,UAAD,CALZ,IAMA,CAACK,kBAAkB,CAACzC,IAAD,EAAOoC,UAAP,CAPrB,EAQE;IACA,KAAKnC,KAAL,CAAWmC,UAAX,EAAuBpC,IAAvB;EACD,CAVD,MAUO;IACL,KAAKD,OAAL,CAAaC,IAAb;EACD;;EAED,KAAK4B,UAAL,CAAgB5B,IAAhB;;EAEA,KAAKa,KAAL;EACA,KAAKV,KAAL,CAAW,IAAX;EACA,KAAKU,KAAL;EAEA,KAAKZ,KAAL,CAAWD,IAAI,CAACkC,IAAhB,EAAsBlC,IAAtB;AACD;;AAED,SAASyC,kBAAT,CACEzC,IADF,EAEE0C,KAFF,EAGW;EAAA;;EACT,OAAO,CAAC,EACN1C,IAAI,CAACE,cAAL,IACAF,IAAI,CAACM,UADL,IAEAN,IAAI,CAAC6B,SAFL,IAGAa,KAAK,CAACxB,cAHN,IAIAwB,KAAK,CAACzB,QAJN,6BAKAyB,KAAK,CAACC,eALN,aAKA,sBAAuBhC,MALvB,6BAMA+B,KAAK,CAACE,gBANN,aAMA,sBAAwBjC,MAPlB,CAAR;AASD"}