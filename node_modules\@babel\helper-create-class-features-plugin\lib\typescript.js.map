{"version": 3, "names": ["assertFieldTransformed", "path", "node", "declare", "buildCodeFrameError"], "sources": ["../src/typescript.ts"], "sourcesContent": ["import type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nexport function assertFieldTransformed(\n  path: NodePath<t.ClassProperty | t.ClassDeclaration>,\n) {\n  // TODO (Babel 8): Also check path.node.definite\n\n  if (path.node.declare) {\n    throw path.buildCodeFrameError(\n      `TypeScript 'declare' fields must first be transformed by ` +\n        `@babel/plugin-transform-typescript.\\n` +\n        `If you have already enabled that plugin (or '@babel/preset-typescript'), make sure ` +\n        `that it runs before any plugin related to additional class features:\\n` +\n        ` - @babel/plugin-proposal-class-properties\\n` +\n        ` - @babel/plugin-proposal-private-methods\\n` +\n        ` - @babel/plugin-proposal-decorators`,\n    );\n  }\n}\n"], "mappings": ";;;;;;;AAGO,SAASA,sBAAT,CACLC,IADK,EAEL;EAGA,IAAIA,IAAI,CAACC,IAAL,CAAUC,OAAd,EAAuB;IACrB,MAAMF,IAAI,CAACG,mBAAL,CACH,2DAAD,GACG,uCADH,GAEG,qFAFH,GAGG,wEAHH,GAIG,8CAJH,GAKG,6CALH,GAMG,sCAPC,CAAN;EASD;AACF"}