{"version": 3, "names": ["JSXAttribute", "node", "print", "name", "value", "token", "JSXIdentifier", "word", "JSXNamespacedName", "namespace", "JSXMemberExpression", "object", "property", "JSXSpreadAttribute", "argument", "JSXExpressionContainer", "expression", "JSXSpreadChild", "JSXText", "raw", "getPossibleRaw", "undefined", "JSXElement", "open", "openingElement", "selfClosing", "indent", "child", "children", "dedent", "closingElement", "spaceSeparator", "space", "JSXOpeningElement", "typeParameters", "attributes", "length", "printJoin", "separator", "JSXClosingElement", "JSXEmptyExpression", "printInnerComments", "JSXFragment", "openingFragment", "closingFragment", "JSXOpeningFragment", "JSXClosingFragment"], "sources": ["../../src/generators/jsx.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function JSXAttribute(this: Printer, node: t.JSXAttribute) {\n  this.print(node.name, node);\n  if (node.value) {\n    this.token(\"=\");\n    this.print(node.value, node);\n  }\n}\n\nexport function JSXIdentifier(this: Printer, node: t.JSXIdentifier) {\n  this.word(node.name);\n}\n\nexport function JSXNamespacedName(this: Printer, node: t.JSXNamespacedName) {\n  this.print(node.namespace, node);\n  this.token(\":\");\n  this.print(node.name, node);\n}\n\nexport function JSXMemberExpression(\n  this: Printer,\n  node: t.JSXMemberExpression,\n) {\n  this.print(node.object, node);\n  this.token(\".\");\n  this.print(node.property, node);\n}\n\nexport function JSXSpreadAttribute(this: Printer, node: t.JSXSpreadAttribute) {\n  this.token(\"{\");\n  this.token(\"...\");\n  this.print(node.argument, node);\n  this.token(\"}\");\n}\n\nexport function JSXExpressionContainer(\n  this: Printer,\n  node: t.JSXExpressionContainer,\n) {\n  this.token(\"{\");\n  this.print(node.expression, node);\n  this.token(\"}\");\n}\n\nexport function JSXSpreadChild(this: Printer, node: t.JSXSpreadChild) {\n  this.token(\"{\");\n  this.token(\"...\");\n  this.print(node.expression, node);\n  this.token(\"}\");\n}\n\nexport function JSXText(this: Printer, node: t.JSXText) {\n  const raw = this.getPossibleRaw(node);\n\n  if (raw !== undefined) {\n    this.token(raw, true);\n  } else {\n    this.token(node.value, true);\n  }\n}\n\nexport function JSXElement(this: Printer, node: t.JSXElement) {\n  const open = node.openingElement;\n  this.print(open, node);\n  if (open.selfClosing) return;\n\n  this.indent();\n  for (const child of node.children) {\n    this.print(child, node);\n  }\n  this.dedent();\n\n  this.print(node.closingElement, node);\n}\n\nfunction spaceSeparator(this: Printer) {\n  this.space();\n}\n\nexport function JSXOpeningElement(this: Printer, node: t.JSXOpeningElement) {\n  this.token(\"<\");\n  this.print(node.name, node);\n  this.print(node.typeParameters, node); // TS\n  if (node.attributes.length > 0) {\n    this.space();\n    this.printJoin(node.attributes, node, { separator: spaceSeparator });\n  }\n  if (node.selfClosing) {\n    this.space();\n    this.token(\"/>\");\n  } else {\n    this.token(\">\");\n  }\n}\n\nexport function JSXClosingElement(this: Printer, node: t.JSXClosingElement) {\n  this.token(\"</\");\n  this.print(node.name, node);\n  this.token(\">\");\n}\n\nexport function JSXEmptyExpression(this: Printer, node: t.JSXEmptyExpression) {\n  this.printInnerComments(node);\n}\n\nexport function JSXFragment(this: Printer, node: t.JSXFragment) {\n  this.print(node.openingFragment, node);\n\n  this.indent();\n  for (const child of node.children) {\n    this.print(child, node);\n  }\n  this.dedent();\n\n  this.print(node.closingFragment, node);\n}\n\nexport function JSXOpeningFragment(this: Printer) {\n  this.token(\"<\");\n  this.token(\">\");\n}\n\nexport function JSXClosingFragment(this: Printer) {\n  this.token(\"</\");\n  this.token(\">\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAGO,SAASA,YAAT,CAAqCC,IAArC,EAA2D;EAChE,KAAKC,KAAL,CAAWD,IAAI,CAACE,IAAhB,EAAsBF,IAAtB;;EACA,IAAIA,IAAI,CAACG,KAAT,EAAgB;IACd,KAAKC,SAAL;IACA,KAAKH,KAAL,CAAWD,IAAI,CAACG,KAAhB,EAAuBH,IAAvB;EACD;AACF;;AAEM,SAASK,aAAT,CAAsCL,IAAtC,EAA6D;EAClE,KAAKM,IAAL,CAAUN,IAAI,CAACE,IAAf;AACD;;AAEM,SAASK,iBAAT,CAA0CP,IAA1C,EAAqE;EAC1E,KAAKC,KAAL,CAAWD,IAAI,CAACQ,SAAhB,EAA2BR,IAA3B;EACA,KAAKI,SAAL;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACE,IAAhB,EAAsBF,IAAtB;AACD;;AAEM,SAASS,mBAAT,CAELT,IAFK,EAGL;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACU,MAAhB,EAAwBV,IAAxB;EACA,KAAKI,SAAL;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACW,QAAhB,EAA0BX,IAA1B;AACD;;AAEM,SAASY,kBAAT,CAA2CZ,IAA3C,EAAuE;EAC5E,KAAKI,SAAL;EACA,KAAKA,KAAL,CAAW,KAAX;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACa,QAAhB,EAA0Bb,IAA1B;EACA,KAAKI,SAAL;AACD;;AAEM,SAASU,sBAAT,CAELd,IAFK,EAGL;EACA,KAAKI,SAAL;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACe,UAAhB,EAA4Bf,IAA5B;EACA,KAAKI,SAAL;AACD;;AAEM,SAASY,cAAT,CAAuChB,IAAvC,EAA+D;EACpE,KAAKI,SAAL;EACA,KAAKA,KAAL,CAAW,KAAX;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACe,UAAhB,EAA4Bf,IAA5B;EACA,KAAKI,SAAL;AACD;;AAEM,SAASa,OAAT,CAAgCjB,IAAhC,EAAiD;EACtD,MAAMkB,GAAG,GAAG,KAAKC,cAAL,CAAoBnB,IAApB,CAAZ;;EAEA,IAAIkB,GAAG,KAAKE,SAAZ,EAAuB;IACrB,KAAKhB,KAAL,CAAWc,GAAX,EAAgB,IAAhB;EACD,CAFD,MAEO;IACL,KAAKd,KAAL,CAAWJ,IAAI,CAACG,KAAhB,EAAuB,IAAvB;EACD;AACF;;AAEM,SAASkB,UAAT,CAAmCrB,IAAnC,EAAuD;EAC5D,MAAMsB,IAAI,GAAGtB,IAAI,CAACuB,cAAlB;EACA,KAAKtB,KAAL,CAAWqB,IAAX,EAAiBtB,IAAjB;EACA,IAAIsB,IAAI,CAACE,WAAT,EAAsB;EAEtB,KAAKC,MAAL;;EACA,KAAK,MAAMC,KAAX,IAAoB1B,IAAI,CAAC2B,QAAzB,EAAmC;IACjC,KAAK1B,KAAL,CAAWyB,KAAX,EAAkB1B,IAAlB;EACD;;EACD,KAAK4B,MAAL;EAEA,KAAK3B,KAAL,CAAWD,IAAI,CAAC6B,cAAhB,EAAgC7B,IAAhC;AACD;;AAED,SAAS8B,cAAT,GAAuC;EACrC,KAAKC,KAAL;AACD;;AAEM,SAASC,iBAAT,CAA0ChC,IAA1C,EAAqE;EAC1E,KAAKI,SAAL;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACE,IAAhB,EAAsBF,IAAtB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACiC,cAAhB,EAAgCjC,IAAhC;;EACA,IAAIA,IAAI,CAACkC,UAAL,CAAgBC,MAAhB,GAAyB,CAA7B,EAAgC;IAC9B,KAAKJ,KAAL;IACA,KAAKK,SAAL,CAAepC,IAAI,CAACkC,UAApB,EAAgClC,IAAhC,EAAsC;MAAEqC,SAAS,EAAEP;IAAb,CAAtC;EACD;;EACD,IAAI9B,IAAI,CAACwB,WAAT,EAAsB;IACpB,KAAKO,KAAL;IACA,KAAK3B,KAAL,CAAW,IAAX;EACD,CAHD,MAGO;IACL,KAAKA,SAAL;EACD;AACF;;AAEM,SAASkC,iBAAT,CAA0CtC,IAA1C,EAAqE;EAC1E,KAAKI,KAAL,CAAW,IAAX;EACA,KAAKH,KAAL,CAAWD,IAAI,CAACE,IAAhB,EAAsBF,IAAtB;EACA,KAAKI,SAAL;AACD;;AAEM,SAASmC,kBAAT,CAA2CvC,IAA3C,EAAuE;EAC5E,KAAKwC,kBAAL,CAAwBxC,IAAxB;AACD;;AAEM,SAASyC,WAAT,CAAoCzC,IAApC,EAAyD;EAC9D,KAAKC,KAAL,CAAWD,IAAI,CAAC0C,eAAhB,EAAiC1C,IAAjC;EAEA,KAAKyB,MAAL;;EACA,KAAK,MAAMC,KAAX,IAAoB1B,IAAI,CAAC2B,QAAzB,EAAmC;IACjC,KAAK1B,KAAL,CAAWyB,KAAX,EAAkB1B,IAAlB;EACD;;EACD,KAAK4B,MAAL;EAEA,KAAK3B,KAAL,CAAWD,IAAI,CAAC2C,eAAhB,EAAiC3C,IAAjC;AACD;;AAEM,SAAS4C,kBAAT,GAA2C;EAChD,KAAKxC,SAAL;EACA,KAAKA,SAAL;AACD;;AAEM,SAASyC,kBAAT,GAA2C;EAChD,KAAKzC,KAAL,CAAW,IAAX;EACA,KAAKA,SAAL;AACD"}