{"version": 3, "names": ["version", "split", "reduce", "v", "x", "version<PERSON>ey", "createClassFeaturePlugin", "name", "feature", "loose", "manipulateOptions", "api", "assumption", "inherits", "setPublicClassFields", "privateFieldsAsProperties", "constant<PERSON>uper", "noDocumentAll", "explicit", "undefined", "push", "length", "console", "warn", "join", "pre", "file", "enableFeature", "get", "set", "visitor", "Class", "path", "shouldTransform", "isClassDeclaration", "assertFieldTransformed", "isLoose", "constructor", "isDecorated", "hasDecorators", "node", "props", "elements", "computedPaths", "privateNames", "Set", "body", "isClassProperty", "isClassMethod", "computed", "isPrivate", "key", "id", "getName", "setName", "isClassPrivateMethod", "kind", "has", "buildCodeFrameError", "add", "isProperty", "isStaticBlock", "innerBinding", "ref", "isClassExpression", "nameFunction", "scope", "generateUidIdentifier", "t", "cloneNode", "privateNamesMap", "buildPrivateNamesMap", "privateNamesNodes", "buildPrivateNamesNodes", "transformPrivateNamesUsage", "keysNodes", "staticNodes", "instanceNodes", "pureStaticNodes", "wrapClass", "buildDecoratedClass", "extractComputedKeys", "buildFieldsInitNodes", "superClass", "injectInitialization", "referenceVisitor", "state", "prop", "static", "traverse", "<PERSON><PERSON><PERSON>", "insertBefore", "insertAfter", "find", "parent", "isStatement", "isDeclaration", "ExportDefaultDeclaration", "decl", "splitExportDeclaration", "type"], "sources": ["../src/index.ts"], "sourcesContent": ["import { types as t } from \"@babel/core\";\nimport type { PluginAPI, PluginObject } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport nameFunction from \"@babel/helper-function-name\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport {\n  buildPrivateNamesNodes,\n  buildPrivateNamesMap,\n  transformPrivateNamesUsage,\n  buildFieldsInitNodes,\n} from \"./fields\";\nimport type { PropPath } from \"./fields\";\nimport { buildDecoratedClass, hasDecorators } from \"./decorators\";\nimport { injectInitialization, extractComputedKeys } from \"./misc\";\nimport { enableFeature, FEATURES, isLoose, shouldTransform } from \"./features\";\nimport { assertFieldTransformed } from \"./typescript\";\n\nexport { FEATURES, enableFeature, injectInitialization };\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\n\n// Note: Versions are represented as an integer. e.g. 7.1.5 is represented\n//       as 70000100005. This method is easier than using a semver-parsing\n//       package, but it breaks if we release x.y.z where x, y or z are\n//       greater than 99_999.\nconst version = PACKAGE_JSON.version\n  .split(\".\")\n  .reduce((v, x) => v * 1e5 + +x, 0);\nconst versionKey = \"@babel/plugin-class-features/version\";\n\ninterface Options {\n  name: string;\n  feature: number;\n  loose?: boolean;\n  inherits?: PluginObject[\"inherits\"];\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n  api?: PluginAPI;\n}\n\nexport function createClassFeaturePlugin({\n  name,\n  feature,\n  loose,\n  manipulateOptions,\n  // @ts-ignore(Babel 7 vs Babel 8) TODO(Babel 8): Remove the default value\n  api = { assumption: () => void 0 },\n  inherits,\n}: Options): PluginObject {\n  const setPublicClassFields = api.assumption(\"setPublicClassFields\");\n  const privateFieldsAsProperties = api.assumption(\"privateFieldsAsProperties\");\n  const constantSuper = api.assumption(\"constantSuper\");\n  const noDocumentAll = api.assumption(\"noDocumentAll\");\n\n  if (loose === true) {\n    const explicit = [];\n\n    if (setPublicClassFields !== undefined) {\n      explicit.push(`\"setPublicClassFields\"`);\n    }\n    if (privateFieldsAsProperties !== undefined) {\n      explicit.push(`\"privateFieldsAsProperties\"`);\n    }\n    if (explicit.length !== 0) {\n      console.warn(\n        `[${name}]: You are using the \"loose: true\" option and you are` +\n          ` explicitly setting a value for the ${explicit.join(\" and \")}` +\n          ` assumption${explicit.length > 1 ? \"s\" : \"\"}. The \"loose\" option` +\n          ` can cause incompatibilities with the other class features` +\n          ` plugins, so it's recommended that you replace it with the` +\n          ` following top-level option:\\n` +\n          `\\t\"assumptions\": {\\n` +\n          `\\t\\t\"setPublicClassFields\": true,\\n` +\n          `\\t\\t\"privateFieldsAsProperties\": true\\n` +\n          `\\t}`,\n      );\n    }\n  }\n\n  return {\n    name,\n    manipulateOptions,\n    inherits,\n\n    pre(file) {\n      enableFeature(file, feature, loose);\n\n      if (!file.get(versionKey) || file.get(versionKey) < version) {\n        file.set(versionKey, version);\n      }\n    },\n\n    visitor: {\n      Class(path, { file }) {\n        if (file.get(versionKey) !== version) return;\n\n        if (!shouldTransform(path, file)) return;\n\n        if (path.isClassDeclaration()) assertFieldTransformed(path);\n\n        const loose = isLoose(file, feature);\n\n        let constructor: NodePath<t.ClassMethod>;\n        const isDecorated = hasDecorators(path.node);\n        const props: PropPath[] = [];\n        const elements = [];\n        const computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[] = [];\n        const privateNames = new Set<string>();\n        const body = path.get(\"body\");\n\n        for (const path of body.get(\"body\")) {\n          if (\n            // check path.node.computed is enough, but ts will complain\n            (path.isClassProperty() || path.isClassMethod()) &&\n            path.node.computed\n          ) {\n            computedPaths.push(path);\n          }\n\n          if (path.isPrivate()) {\n            const { name } = path.node.key.id;\n            const getName = `get ${name}`;\n            const setName = `set ${name}`;\n\n            if (path.isClassPrivateMethod()) {\n              if (path.node.kind === \"get\") {\n                if (\n                  privateNames.has(getName) ||\n                  (privateNames.has(name) && !privateNames.has(setName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(getName).add(name);\n              } else if (path.node.kind === \"set\") {\n                if (\n                  privateNames.has(setName) ||\n                  (privateNames.has(name) && !privateNames.has(getName))\n                ) {\n                  throw path.buildCodeFrameError(\"Duplicate private field\");\n                }\n                privateNames.add(setName).add(name);\n              }\n            } else {\n              if (\n                (privateNames.has(name) &&\n                  !privateNames.has(getName) &&\n                  !privateNames.has(setName)) ||\n                (privateNames.has(name) &&\n                  (privateNames.has(getName) || privateNames.has(setName)))\n              ) {\n                throw path.buildCodeFrameError(\"Duplicate private field\");\n              }\n\n              privateNames.add(name);\n            }\n          }\n\n          if (path.isClassMethod({ kind: \"constructor\" })) {\n            constructor = path;\n          } else {\n            elements.push(path);\n            if (\n              path.isProperty() ||\n              path.isPrivate() ||\n              path.isStaticBlock?.()\n            ) {\n              props.push(path as PropPath);\n            }\n          }\n        }\n\n        if (process.env.BABEL_8_BREAKING) {\n          if (!props.length) return;\n        } else {\n          if (!props.length && !isDecorated) return;\n        }\n\n        const innerBinding = path.node.id;\n        let ref: t.Identifier;\n        if (!innerBinding || path.isClassExpression()) {\n          nameFunction(path);\n          ref = path.scope.generateUidIdentifier(\"class\");\n        } else {\n          ref = t.cloneNode(path.node.id);\n        }\n\n        // NODE: These three functions don't support decorators yet,\n        //       but verifyUsedFeatures throws if there are both\n        //       decorators and private fields.\n        const privateNamesMap = buildPrivateNamesMap(props);\n        const privateNamesNodes = buildPrivateNamesNodes(\n          privateNamesMap,\n          (privateFieldsAsProperties ?? loose) as boolean,\n          file,\n        );\n\n        transformPrivateNamesUsage(\n          ref,\n          path,\n          privateNamesMap,\n          {\n            privateFieldsAsProperties: privateFieldsAsProperties ?? loose,\n            noDocumentAll,\n            innerBinding,\n          },\n          file,\n        );\n\n        let keysNodes: t.Statement[],\n          staticNodes: t.Statement[],\n          instanceNodes: t.Statement[],\n          pureStaticNodes: t.FunctionDeclaration[],\n          wrapClass: (path: NodePath<t.Class>) => NodePath;\n\n        if (!process.env.BABEL_8_BREAKING) {\n          if (isDecorated) {\n            staticNodes = pureStaticNodes = keysNodes = [];\n            ({ instanceNodes, wrapClass } = buildDecoratedClass(\n              ref,\n              path,\n              elements,\n              file,\n            ));\n          } else {\n            keysNodes = extractComputedKeys(path, computedPaths, file);\n            ({ staticNodes, pureStaticNodes, instanceNodes, wrapClass } =\n              buildFieldsInitNodes(\n                ref,\n                path.node.superClass,\n                props,\n                privateNamesMap,\n                file,\n                (setPublicClassFields ?? loose) as boolean,\n                (privateFieldsAsProperties ?? loose) as boolean,\n                (constantSuper ?? loose) as boolean,\n                innerBinding,\n              ));\n          }\n        } else {\n          keysNodes = extractComputedKeys(path, computedPaths, file);\n          ({ staticNodes, pureStaticNodes, instanceNodes, wrapClass } =\n            buildFieldsInitNodes(\n              ref,\n              path.node.superClass,\n              props,\n              privateNamesMap,\n              file,\n              (setPublicClassFields ?? loose) as boolean,\n              (privateFieldsAsProperties ?? loose) as boolean,\n              (constantSuper ?? loose) as boolean,\n              innerBinding,\n            ));\n        }\n\n        if (instanceNodes.length > 0) {\n          injectInitialization(\n            path,\n            constructor,\n            instanceNodes,\n            (referenceVisitor, state) => {\n              if (!process.env.BABEL_8_BREAKING) {\n                if (isDecorated) return;\n              }\n              for (const prop of props) {\n                // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n                if (t.isStaticBlock?.(prop.node) || prop.node.static) continue;\n                prop.traverse(referenceVisitor, state);\n              }\n            },\n          );\n        }\n\n        // rename to make ts happy\n        const wrappedPath = wrapClass(path);\n        wrappedPath.insertBefore([...privateNamesNodes, ...keysNodes]);\n        if (staticNodes.length > 0) {\n          wrappedPath.insertAfter(staticNodes);\n        }\n        if (pureStaticNodes.length > 0) {\n          wrappedPath\n            .find(parent => parent.isStatement() || parent.isDeclaration())\n            .insertAfter(pureStaticNodes);\n        }\n      },\n\n      ExportDefaultDeclaration(path, { file }) {\n        if (!process.env.BABEL_8_BREAKING) {\n          if (file.get(versionKey) !== version) return;\n\n          const decl = path.get(\"declaration\");\n\n          if (decl.isClassDeclaration() && hasDecorators(decl.node)) {\n            if (decl.node.id) {\n              // export default class Foo {}\n              //   -->\n              // class Foo {} export { Foo as default }\n              splitExportDeclaration(path);\n            } else {\n              // @ts-expect-error Anonymous class declarations can be\n              // transformed as if they were expressions\n              decl.node.type = \"ClassExpression\";\n            }\n          }\n        }\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGA;;AACA;;AACA;;AAOA;;AACA;;AACA;;AACA;;AAUA,MAAMA,OAAO,GAAG,SACbC,KADa,CACP,GADO,EAEbC,MAFa,CAEN,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAG,GAAJ,GAAU,CAACC,CAFf,EAEkB,CAFlB,CAAhB;AAGA,MAAMC,UAAU,GAAG,sCAAnB;;AAWO,SAASC,wBAAT,CAAkC;EACvCC,IADuC;EAEvCC,OAFuC;EAGvCC,KAHuC;EAIvCC,iBAJuC;EAMvCC,GAAG,GAAG;IAAEC,UAAU,EAAE,MAAM,KAAK;EAAzB,CANiC;EAOvCC;AAPuC,CAAlC,EAQmB;EACxB,MAAMC,oBAAoB,GAAGH,GAAG,CAACC,UAAJ,CAAe,sBAAf,CAA7B;EACA,MAAMG,yBAAyB,GAAGJ,GAAG,CAACC,UAAJ,CAAe,2BAAf,CAAlC;EACA,MAAMI,aAAa,GAAGL,GAAG,CAACC,UAAJ,CAAe,eAAf,CAAtB;EACA,MAAMK,aAAa,GAAGN,GAAG,CAACC,UAAJ,CAAe,eAAf,CAAtB;;EAEA,IAAIH,KAAK,KAAK,IAAd,EAAoB;IAClB,MAAMS,QAAQ,GAAG,EAAjB;;IAEA,IAAIJ,oBAAoB,KAAKK,SAA7B,EAAwC;MACtCD,QAAQ,CAACE,IAAT,CAAe,wBAAf;IACD;;IACD,IAAIL,yBAAyB,KAAKI,SAAlC,EAA6C;MAC3CD,QAAQ,CAACE,IAAT,CAAe,6BAAf;IACD;;IACD,IAAIF,QAAQ,CAACG,MAAT,KAAoB,CAAxB,EAA2B;MACzBC,OAAO,CAACC,IAAR,CACG,IAAGhB,IAAK,uDAAT,GACG,uCAAsCW,QAAQ,CAACM,IAAT,CAAc,OAAd,CAAuB,EADhE,GAEG,cAAaN,QAAQ,CAACG,MAAT,GAAkB,CAAlB,GAAsB,GAAtB,GAA4B,EAAG,sBAF/C,GAGG,4DAHH,GAIG,4DAJH,GAKG,gCALH,GAMG,sBANH,GAOG,qCAPH,GAQG,yCARH,GASG,KAVL;IAYD;EACF;;EAED,OAAO;IACLd,IADK;IAELG,iBAFK;IAGLG,QAHK;;IAKLY,GAAG,CAACC,IAAD,EAAO;MACR,IAAAC,uBAAA,EAAcD,IAAd,EAAoBlB,OAApB,EAA6BC,KAA7B;;MAEA,IAAI,CAACiB,IAAI,CAACE,GAAL,CAASvB,UAAT,CAAD,IAAyBqB,IAAI,CAACE,GAAL,CAASvB,UAAT,IAAuBL,OAApD,EAA6D;QAC3D0B,IAAI,CAACG,GAAL,CAASxB,UAAT,EAAqBL,OAArB;MACD;IACF,CAXI;;IAaL8B,OAAO,EAAE;MACPC,KAAK,CAACC,IAAD,EAAO;QAAEN;MAAF,CAAP,EAAiB;QACpB,IAAIA,IAAI,CAACE,GAAL,CAASvB,UAAT,MAAyBL,OAA7B,EAAsC;QAEtC,IAAI,CAAC,IAAAiC,yBAAA,EAAgBD,IAAhB,EAAsBN,IAAtB,CAAL,EAAkC;QAElC,IAAIM,IAAI,CAACE,kBAAL,EAAJ,EAA+B,IAAAC,kCAAA,EAAuBH,IAAvB;QAE/B,MAAMvB,KAAK,GAAG,IAAA2B,iBAAA,EAAQV,IAAR,EAAclB,OAAd,CAAd;QAEA,IAAI6B,WAAJ;QACA,MAAMC,WAAW,GAAG,IAAAC,yBAAA,EAAcP,IAAI,CAACQ,IAAnB,CAApB;QACA,MAAMC,KAAiB,GAAG,EAA1B;QACA,MAAMC,QAAQ,GAAG,EAAjB;QACA,MAAMC,aAA0D,GAAG,EAAnE;QACA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,EAArB;QACA,MAAMC,IAAI,GAAGd,IAAI,CAACJ,GAAL,CAAS,MAAT,CAAb;;QAEA,KAAK,MAAMI,IAAX,IAAmBc,IAAI,CAAClB,GAAL,CAAS,MAAT,CAAnB,EAAqC;UACnC,IAEE,CAACI,IAAI,CAACe,eAAL,MAA0Bf,IAAI,CAACgB,aAAL,EAA3B,KACAhB,IAAI,CAACQ,IAAL,CAAUS,QAHZ,EAIE;YACAN,aAAa,CAACvB,IAAd,CAAmBY,IAAnB;UACD;;UAED,IAAIA,IAAI,CAACkB,SAAL,EAAJ,EAAsB;YACpB,MAAM;cAAE3C;YAAF,IAAWyB,IAAI,CAACQ,IAAL,CAAUW,GAAV,CAAcC,EAA/B;YACA,MAAMC,OAAO,GAAI,OAAM9C,IAAK,EAA5B;YACA,MAAM+C,OAAO,GAAI,OAAM/C,IAAK,EAA5B;;YAEA,IAAIyB,IAAI,CAACuB,oBAAL,EAAJ,EAAiC;cAC/B,IAAIvB,IAAI,CAACQ,IAAL,CAAUgB,IAAV,KAAmB,KAAvB,EAA8B;gBAC5B,IACEZ,YAAY,CAACa,GAAb,CAAiBJ,OAAjB,KACCT,YAAY,CAACa,GAAb,CAAiBlD,IAAjB,KAA0B,CAACqC,YAAY,CAACa,GAAb,CAAiBH,OAAjB,CAF9B,EAGE;kBACA,MAAMtB,IAAI,CAAC0B,mBAAL,CAAyB,yBAAzB,CAAN;gBACD;;gBACDd,YAAY,CAACe,GAAb,CAAiBN,OAAjB,EAA0BM,GAA1B,CAA8BpD,IAA9B;cACD,CARD,MAQO,IAAIyB,IAAI,CAACQ,IAAL,CAAUgB,IAAV,KAAmB,KAAvB,EAA8B;gBACnC,IACEZ,YAAY,CAACa,GAAb,CAAiBH,OAAjB,KACCV,YAAY,CAACa,GAAb,CAAiBlD,IAAjB,KAA0B,CAACqC,YAAY,CAACa,GAAb,CAAiBJ,OAAjB,CAF9B,EAGE;kBACA,MAAMrB,IAAI,CAAC0B,mBAAL,CAAyB,yBAAzB,CAAN;gBACD;;gBACDd,YAAY,CAACe,GAAb,CAAiBL,OAAjB,EAA0BK,GAA1B,CAA8BpD,IAA9B;cACD;YACF,CAlBD,MAkBO;cACL,IACGqC,YAAY,CAACa,GAAb,CAAiBlD,IAAjB,KACC,CAACqC,YAAY,CAACa,GAAb,CAAiBJ,OAAjB,CADF,IAEC,CAACT,YAAY,CAACa,GAAb,CAAiBH,OAAjB,CAFH,IAGCV,YAAY,CAACa,GAAb,CAAiBlD,IAAjB,MACEqC,YAAY,CAACa,GAAb,CAAiBJ,OAAjB,KAA6BT,YAAY,CAACa,GAAb,CAAiBH,OAAjB,CAD/B,CAJH,EAME;gBACA,MAAMtB,IAAI,CAAC0B,mBAAL,CAAyB,yBAAzB,CAAN;cACD;;cAEDd,YAAY,CAACe,GAAb,CAAiBpD,IAAjB;YACD;UACF;;UAED,IAAIyB,IAAI,CAACgB,aAAL,CAAmB;YAAEQ,IAAI,EAAE;UAAR,CAAnB,CAAJ,EAAiD;YAC/CnB,WAAW,GAAGL,IAAd;UACD,CAFD,MAEO;YACLU,QAAQ,CAACtB,IAAT,CAAcY,IAAd;;YACA,IACEA,IAAI,CAAC4B,UAAL,MACA5B,IAAI,CAACkB,SAAL,EADA,IAEAlB,IAAI,CAAC6B,aAFL,YAEA7B,IAAI,CAAC6B,aAAL,EAHF,EAIE;cACApB,KAAK,CAACrB,IAAN,CAAWY,IAAX;YACD;UACF;QACF;;QAIM;UACL,IAAI,CAACS,KAAK,CAACpB,MAAP,IAAiB,CAACiB,WAAtB,EAAmC;QACpC;QAED,MAAMwB,YAAY,GAAG9B,IAAI,CAACQ,IAAL,CAAUY,EAA/B;QACA,IAAIW,GAAJ;;QACA,IAAI,CAACD,YAAD,IAAiB9B,IAAI,CAACgC,iBAAL,EAArB,EAA+C;UAC7C,IAAAC,2BAAA,EAAajC,IAAb;UACA+B,GAAG,GAAG/B,IAAI,CAACkC,KAAL,CAAWC,qBAAX,CAAiC,OAAjC,CAAN;QACD,CAHD,MAGO;UACLJ,GAAG,GAAGK,WAAA,CAAEC,SAAF,CAAYrC,IAAI,CAACQ,IAAL,CAAUY,EAAtB,CAAN;QACD;;QAKD,MAAMkB,eAAe,GAAG,IAAAC,4BAAA,EAAqB9B,KAArB,CAAxB;QACA,MAAM+B,iBAAiB,GAAG,IAAAC,8BAAA,EACxBH,eADwB,EAEvBvD,yBAFuB,WAEvBA,yBAFuB,GAEMN,KAFN,EAGxBiB,IAHwB,CAA1B;QAMA,IAAAgD,kCAAA,EACEX,GADF,EAEE/B,IAFF,EAGEsC,eAHF,EAIE;UACEvD,yBAAyB,EAAEA,yBAAF,WAAEA,yBAAF,GAA+BN,KAD1D;UAEEQ,aAFF;UAGE6C;QAHF,CAJF,EASEpC,IATF;QAYA,IAAIiD,SAAJ,EACEC,WADF,EAEEC,aAFF,EAGEC,eAHF,EAIEC,SAJF;QAMmC;UACjC,IAAIzC,WAAJ,EAAiB;YACfsC,WAAW,GAAGE,eAAe,GAAGH,SAAS,GAAG,EAA5C;YACA,CAAC;cAAEE,aAAF;cAAiBE;YAAjB,IAA+B,IAAAC,+BAAA,EAC9BjB,GAD8B,EAE9B/B,IAF8B,EAG9BU,QAH8B,EAI9BhB,IAJ8B,CAAhC;UAMD,CARD,MAQO;YACLiD,SAAS,GAAG,IAAAM,yBAAA,EAAoBjD,IAApB,EAA0BW,aAA1B,EAAyCjB,IAAzC,CAAZ;YACA,CAAC;cAAEkD,WAAF;cAAeE,eAAf;cAAgCD,aAAhC;cAA+CE;YAA/C,IACC,IAAAG,4BAAA,EACEnB,GADF,EAEE/B,IAAI,CAACQ,IAAL,CAAU2C,UAFZ,EAGE1C,KAHF,EAIE6B,eAJF,EAKE5C,IALF,EAMGZ,oBANH,WAMGA,oBANH,GAM2BL,KAN3B,EAOGM,yBAPH,WAOGA,yBAPH,GAOgCN,KAPhC,EAQGO,aARH,WAQGA,aARH,GAQoBP,KARpB,EASEqD,YATF,CADF;UAYD;QACF;;QAgBD,IAAIe,aAAa,CAACxD,MAAd,GAAuB,CAA3B,EAA8B;UAC5B,IAAA+D,0BAAA,EACEpD,IADF,EAEEK,WAFF,EAGEwC,aAHF,EAIE,CAACQ,gBAAD,EAAmBC,KAAnB,KAA6B;YACQ;cACjC,IAAIhD,WAAJ,EAAiB;YAClB;;YACD,KAAK,MAAMiD,IAAX,IAAmB9C,KAAnB,EAA0B;cAExB,IAAI2B,WAAA,CAAEP,aAAF,YAAAO,WAAA,CAAEP,aAAF,CAAkB0B,IAAI,CAAC/C,IAAvB,KAAgC+C,IAAI,CAAC/C,IAAL,CAAUgD,MAA9C,EAAsD;cACtDD,IAAI,CAACE,QAAL,CAAcJ,gBAAd,EAAgCC,KAAhC;YACD;UACF,CAbH;QAeD;;QAGD,MAAMI,WAAW,GAAGX,SAAS,CAAC/C,IAAD,CAA7B;QACA0D,WAAW,CAACC,YAAZ,CAAyB,CAAC,GAAGnB,iBAAJ,EAAuB,GAAGG,SAA1B,CAAzB;;QACA,IAAIC,WAAW,CAACvD,MAAZ,GAAqB,CAAzB,EAA4B;UAC1BqE,WAAW,CAACE,WAAZ,CAAwBhB,WAAxB;QACD;;QACD,IAAIE,eAAe,CAACzD,MAAhB,GAAyB,CAA7B,EAAgC;UAC9BqE,WAAW,CACRG,IADH,CACQC,MAAM,IAAIA,MAAM,CAACC,WAAP,MAAwBD,MAAM,CAACE,aAAP,EAD1C,EAEGJ,WAFH,CAEed,eAFf;QAGD;MACF,CA/LM;;MAiMPmB,wBAAwB,CAACjE,IAAD,EAAO;QAAEN;MAAF,CAAP,EAAiB;QACJ;UACjC,IAAIA,IAAI,CAACE,GAAL,CAASvB,UAAT,MAAyBL,OAA7B,EAAsC;UAEtC,MAAMkG,IAAI,GAAGlE,IAAI,CAACJ,GAAL,CAAS,aAAT,CAAb;;UAEA,IAAIsE,IAAI,CAAChE,kBAAL,MAA6B,IAAAK,yBAAA,EAAc2D,IAAI,CAAC1D,IAAnB,CAAjC,EAA2D;YACzD,IAAI0D,IAAI,CAAC1D,IAAL,CAAUY,EAAd,EAAkB;cAIhB,IAAA+C,qCAAA,EAAuBnE,IAAvB;YACD,CALD,MAKO;cAGLkE,IAAI,CAAC1D,IAAL,CAAU4D,IAAV,GAAiB,iBAAjB;YACD;UACF;QACF;MACF;;IApNM;EAbJ,CAAP;AAoOD"}