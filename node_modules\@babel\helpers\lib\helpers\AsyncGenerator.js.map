{"version": 3, "names": ["AsyncGenerator", "gen", "front", "back", "send", "key", "arg", "Promise", "resolve", "reject", "request", "next", "resume", "result", "value", "overloaded", "OverloadYield", "v", "then", "<PERSON><PERSON><PERSON>", "k", "done", "settle", "err", "type", "_invoke", "return", "undefined", "prototype", "Symbol", "asyncIterator", "throw"], "sources": ["../../src/helpers/AsyncGenerator.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport <PERSON>load<PERSON><PERSON> from \"OverloadYield\";\n\nexport default function AsyncGenerator(gen) {\n  var front, back;\n\n  function send(key, arg) {\n    return new Promise(function (resolve, reject) {\n      var request = {\n        key: key,\n        arg: arg,\n        resolve: resolve,\n        reject: reject,\n        next: null,\n      };\n\n      if (back) {\n        back = back.next = request;\n      } else {\n        front = back = request;\n        resume(key, arg);\n      }\n    });\n  }\n\n  function resume(key, arg) {\n    try {\n      var result = gen[key](arg);\n      var value = result.value;\n      var overloaded = value instanceof OverloadYield;\n\n      Promise.resolve(overloaded ? value.v : value).then(\n        function (arg) {\n          if (overloaded) {\n            // Overloaded yield requires calling into the generator twice:\n            //  - first we get the iterator result wrapped in a promise\n            //    (the gen[key](arg) call above)\n            //  - then we await it (the Promise.resolve call above)\n            //  - then we give the result back to the iterator, so that it can:\n            //    * if it was an await, use its result\n            //    * if it was a yield*, possibly return the `done: true` signal\n            //      so that yield* knows that the iterator is finished.\n            //      This needs to happen in the second call, because in the\n            //      first one `done: true` was hidden in the promise and thus\n            //      not visible to the (sync) yield*.\n            //      The other part of this implementation is in asyncGeneratorDelegate.\n            var nextKey = key === \"return\" ? \"return\" : \"next\";\n            if (!value.k || arg.done) {\n              // await or end of yield*\n              return resume(nextKey, arg);\n            } else {\n              // yield*, not done\n              arg = gen[nextKey](arg).value;\n            }\n          }\n\n          settle(result.done ? \"return\" : \"normal\", arg);\n        },\n        function (err) {\n          resume(\"throw\", err);\n        }\n      );\n    } catch (err) {\n      settle(\"throw\", err);\n    }\n  }\n\n  function settle(type, value) {\n    switch (type) {\n      case \"return\":\n        front.resolve({ value: value, done: true });\n        break;\n      case \"throw\":\n        front.reject(value);\n        break;\n      default:\n        front.resolve({ value: value, done: false });\n        break;\n    }\n\n    front = front.next;\n    if (front) {\n      resume(front.key, front.arg);\n    } else {\n      back = null;\n    }\n  }\n\n  this._invoke = send;\n\n  // Hide \"return\" method if generator return is not supported\n  if (typeof gen.return !== \"function\") {\n    this.return = undefined;\n  }\n}\n\nAsyncGenerator.prototype[\n  (typeof Symbol === \"function\" && Symbol.asyncIterator) || \"@@asyncIterator\"\n] = function () {\n  return this;\n};\n\nAsyncGenerator.prototype.next = function (arg) {\n  return this._invoke(\"next\", arg);\n};\nAsyncGenerator.prototype.throw = function (arg) {\n  return this._invoke(\"throw\", arg);\n};\nAsyncGenerator.prototype.return = function (arg) {\n  return this._invoke(\"return\", arg);\n};\n"], "mappings": ";;;;;;;AAEA;;AAEe,SAASA,cAAT,CAAwBC,GAAxB,EAA6B;EAC1C,IAAIC,KAAJ,EAAWC,IAAX;;EAEA,SAASC,IAAT,CAAcC,GAAd,EAAmBC,GAAnB,EAAwB;IACtB,OAAO,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmBC,MAAnB,EAA2B;MAC5C,IAAIC,OAAO,GAAG;QACZL,GAAG,EAAEA,GADO;QAEZC,GAAG,EAAEA,GAFO;QAGZE,OAAO,EAAEA,OAHG;QAIZC,MAAM,EAAEA,MAJI;QAKZE,IAAI,EAAE;MALM,CAAd;;MAQA,IAAIR,IAAJ,EAAU;QACRA,IAAI,GAAGA,IAAI,CAACQ,IAAL,GAAYD,OAAnB;MACD,CAFD,MAEO;QACLR,KAAK,GAAGC,IAAI,GAAGO,OAAf;QACAE,MAAM,CAACP,GAAD,EAAMC,GAAN,CAAN;MACD;IACF,CAfM,CAAP;EAgBD;;EAED,SAASM,MAAT,CAAgBP,GAAhB,EAAqBC,GAArB,EAA0B;IACxB,IAAI;MACF,IAAIO,MAAM,GAAGZ,GAAG,CAACI,GAAD,CAAH,CAASC,GAAT,CAAb;MACA,IAAIQ,KAAK,GAAGD,MAAM,CAACC,KAAnB;MACA,IAAIC,UAAU,GAAGD,KAAK,YAAYE,cAAlC;MAEAT,OAAO,CAACC,OAAR,CAAgBO,UAAU,GAAGD,KAAK,CAACG,CAAT,GAAaH,KAAvC,EAA8CI,IAA9C,CACE,UAAUZ,GAAV,EAAe;QACb,IAAIS,UAAJ,EAAgB;UAad,IAAII,OAAO,GAAGd,GAAG,KAAK,QAAR,GAAmB,QAAnB,GAA8B,MAA5C;;UACA,IAAI,CAACS,KAAK,CAACM,CAAP,IAAYd,GAAG,CAACe,IAApB,EAA0B;YAExB,OAAOT,MAAM,CAACO,OAAD,EAAUb,GAAV,CAAb;UACD,CAHD,MAGO;YAELA,GAAG,GAAGL,GAAG,CAACkB,OAAD,CAAH,CAAab,GAAb,EAAkBQ,KAAxB;UACD;QACF;;QAEDQ,MAAM,CAACT,MAAM,CAACQ,IAAP,GAAc,QAAd,GAAyB,QAA1B,EAAoCf,GAApC,CAAN;MACD,CA1BH,EA2BE,UAAUiB,GAAV,EAAe;QACbX,MAAM,CAAC,OAAD,EAAUW,GAAV,CAAN;MACD,CA7BH;IA+BD,CApCD,CAoCE,OAAOA,GAAP,EAAY;MACZD,MAAM,CAAC,OAAD,EAAUC,GAAV,CAAN;IACD;EACF;;EAED,SAASD,MAAT,CAAgBE,IAAhB,EAAsBV,KAAtB,EAA6B;IAC3B,QAAQU,IAAR;MACE,KAAK,QAAL;QACEtB,KAAK,CAACM,OAAN,CAAc;UAAEM,KAAK,EAAEA,KAAT;UAAgBO,IAAI,EAAE;QAAtB,CAAd;QACA;;MACF,KAAK,OAAL;QACEnB,KAAK,CAACO,MAAN,CAAaK,KAAb;QACA;;MACF;QACEZ,KAAK,CAACM,OAAN,CAAc;UAAEM,KAAK,EAAEA,KAAT;UAAgBO,IAAI,EAAE;QAAtB,CAAd;QACA;IATJ;;IAYAnB,KAAK,GAAGA,KAAK,CAACS,IAAd;;IACA,IAAIT,KAAJ,EAAW;MACTU,MAAM,CAACV,KAAK,CAACG,GAAP,EAAYH,KAAK,CAACI,GAAlB,CAAN;IACD,CAFD,MAEO;MACLH,IAAI,GAAG,IAAP;IACD;EACF;;EAED,KAAKsB,OAAL,GAAerB,IAAf;;EAGA,IAAI,OAAOH,GAAG,CAACyB,MAAX,KAAsB,UAA1B,EAAsC;IACpC,KAAKA,MAAL,GAAcC,SAAd;EACD;AACF;;AAED3B,cAAc,CAAC4B,SAAf,CACG,OAAOC,MAAP,KAAkB,UAAlB,IAAgCA,MAAM,CAACC,aAAxC,IAA0D,iBAD5D,IAEI,YAAY;EACd,OAAO,IAAP;AACD,CAJD;;AAMA9B,cAAc,CAAC4B,SAAf,CAAyBjB,IAAzB,GAAgC,UAAUL,GAAV,EAAe;EAC7C,OAAO,KAAKmB,OAAL,CAAa,MAAb,EAAqBnB,GAArB,CAAP;AACD,CAFD;;AAGAN,cAAc,CAAC4B,SAAf,CAAyBG,KAAzB,GAAiC,UAAUzB,GAAV,EAAe;EAC9C,OAAO,KAAKmB,OAAL,CAAa,OAAb,EAAsBnB,GAAtB,CAAP;AACD,CAFD;;AAGAN,cAAc,CAAC4B,SAAf,CAAyBF,MAAzB,GAAkC,UAAUpB,GAAV,EAAe;EAC/C,OAAO,KAAKmB,OAAL,CAAa,QAAb,EAAuBnB,GAAvB,CAAP;AACD,CAFD"}