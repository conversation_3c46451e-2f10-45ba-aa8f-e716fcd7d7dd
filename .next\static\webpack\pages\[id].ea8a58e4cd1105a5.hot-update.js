"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/save-button.tsx":
/*!************************************!*\
  !*** ./components/save-button.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-ui/core */ \"./node_modules/@material-ui/core/esm/index.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/**\n * SaveButton Component\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nvar useStyles = (0,_material_ui_core__WEBPACK_IMPORTED_MODULE_3__.makeStyles)({\n    saveButton: {\n        minWidth: \"80px\",\n        fontWeight: \"bold\",\n        textTransform: \"none\",\n        borderRadius: \"8px\",\n        boxShadow: \"none !important\",\n        \"&:hover\": {\n            opacity: 0.8,\n            boxShadow: \"none !important\"\n        },\n        \"&:focus\": {\n            boxShadow: \"none !important\"\n        },\n        \"&:active\": {\n            boxShadow: \"none !important\"\n        }\n    },\n    viewButton: {\n        backgroundColor: \"#6B7280 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#4B5563 !important\"\n        }\n    },\n    saveStateButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    },\n    syncingButton: {\n        backgroundColor: \"#3185eb !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#2563EB !important\"\n        }\n    },\n    syncedButton: {\n        backgroundColor: \"#FBBF24 !important\",\n        color: \"#000000 !important\",\n        \"&:hover\": {\n            backgroundColor: \"#F59E0B !important\"\n        }\n    },\n    failedButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    }\n});\nvar SaveButton = function(param) {\n    var className = param.className;\n    _s();\n    var classes = useStyles();\n    var ref = libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer(), syncToServer = ref.syncToServer, note = ref.note, currentEditorContent = ref.currentEditorContent;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"), syncStatus = ref1[0], setSyncStatus = ref1[1];\n    var syncedTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var syncTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 重构：基于快照对比的状态检测机制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (!(note === null || note === void 0 ? void 0 : note.id)) {\n            setSyncStatus(\"view\");\n            return;\n        }\n        var checkContentChanges = function() {\n            try {\n                // 🔧 正确的对比：currentEditorContent vs note.content\n                // 都是 JSON 格式，可以直接对比\n                if (currentEditorContent !== note.content) {\n                    // 有变化：设置为save状态\n                    if (syncStatus !== \"save\" && syncStatus !== \"syncing\") {\n                        setSyncStatus(\"save\");\n                    }\n                } else {\n                    // 无变化：设置为view状态\n                    if (syncStatus === \"save\") {\n                        setSyncStatus(\"view\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"检查内容变化失败:\", error);\n            }\n        };\n        // 立即检查一次\n        checkContentChanges();\n        // 定期检查内容变化\n        var interval = setInterval(checkContentChanges, 500);\n        return function() {\n            clearInterval(interval);\n            if (syncedTimeoutRef.current) {\n                clearTimeout(syncedTimeoutRef.current);\n            }\n            if (syncTimeoutRef.current) {\n                clearTimeout(syncTimeoutRef.current);\n            }\n        };\n    }, [\n        note,\n        getEditorState,\n        syncStatus\n    ]);\n    // 手动保存流程\n    var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function() {\n        var syncSuccess, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    setSyncStatus(\"syncing\");\n                    if (syncedTimeoutRef.current) {\n                        clearTimeout(syncedTimeoutRef.current);\n                    }\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                    }\n                    // 设置超时保护\n                    syncTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"fail\");\n                        setTimeout(function() {\n                            setSyncStatus(\"view\");\n                        }, 2000);\n                    }, 30000);\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        syncToServer()\n                    ];\n                case 2:\n                    syncSuccess = _state.sent();\n                    if (!syncSuccess) {\n                        throw new Error(\"同步到服务器失败\");\n                    }\n                    // 清除超时\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"synced\");\n                    // 3秒后自动变回view状态\n                    syncedTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 3000);\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"手动保存失败:\", error);\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"fail\");\n                    setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 2000);\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        syncToServer,\n        saveCurrentContent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (true) {\n            window.saveButtonStatus = syncStatus;\n            window.saveButtonAutoSave = handleSave;\n        }\n        return function() {\n            if (true) {\n                delete window.saveButtonStatus;\n                delete window.saveButtonAutoSave;\n            }\n        };\n    }, [\n        syncStatus,\n        handleSave\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var handleKeyDown = function(e) {\n            if ((e.ctrlKey || e.metaKey) && e.key === \"s\") {\n                var target = e.target;\n                var isInEditor = target.closest(\".ProseMirror\") || target.closest(\"[contenteditable]\") || target.closest(\"textarea\") || target.closest(\"input\");\n                if (isInEditor) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                    handleSave();\n                }\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown, true);\n        return function() {\n            return document.removeEventListener(\"keydown\", handleKeyDown, true);\n        };\n    }, [\n        handleSave\n    ]);\n    var getButtonIcon = function() {\n        switch(syncStatus){\n            case \"view\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 231,\n                    columnNumber: 24\n                }, _this);\n            case \"save\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.DocumentIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 24\n                }, _this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.UploadIcon, {\n                    className: \"w-4 h-4 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 24\n                }, _this);\n            case \"synced\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.CheckIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 24\n                }, _this);\n            case \"fail\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.XIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 24\n                }, _this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 24\n                }, _this);\n        }\n    };\n    var getButtonText = function() {\n        switch(syncStatus){\n            case \"view\":\n                return \"View\";\n            case \"save\":\n                return \"Save\";\n            case \"syncing\":\n                return \"Syncing...\";\n            case \"synced\":\n                return \"Synced\";\n            case \"fail\":\n                return \"Failed\";\n            default:\n                return \"View\";\n        }\n    };\n    var getButtonClassName = function() {\n        var baseClass = \"\".concat(classes.saveButton);\n        switch(syncStatus){\n            case \"view\":\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n            case \"save\":\n                return \"\".concat(baseClass, \" \").concat(classes.saveStateButton);\n            case \"syncing\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncingButton);\n            case \"synced\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncedButton);\n            case \"fail\":\n                return \"\".concat(baseClass, \" \").concat(classes.failedButton);\n            default:\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n        }\n    };\n    var isButtonDisabled = function() {\n        return syncStatus === \"syncing\" || syncStatus === \"view\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_ui_core__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"contained\",\n        startIcon: getButtonIcon(),\n        onClick: handleSave,\n        disabled: isButtonDisabled(),\n        className: \"\".concat(getButtonClassName(), \" \").concat(className || \"\"),\n        size: \"small\",\n        \"data-save-button\": \"true\",\n        children: getButtonText()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n        lineNumber: 285,\n        columnNumber: 9\n    }, _this);\n};\n_s(SaveButton, \"wG5EL0aV1QLOu2g3Kpcm6t9tK8s=\", false, function() {\n    return [\n        useStyles,\n        libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer\n    ];\n});\n_c = SaveButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SaveButton);\nvar _c;\n$RefreshReg$(_c, \"SaveButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/save-button.tsx\n"));

/***/ })

});