{"version": 3, "names": ["bugfixAsyncArrowsInClass", "bugfixEdgeDefaultParameters", "bugfixEdgeFunctionName", "bugfixSafariBlockShadowing", "bugfixSafariForShadowing", "bugfixSafariIdDestructuringCollisionInFunctionExpression", "bugfixTaggedTemplateCaching", "bugfixV8SpreadParametersInOptionalChaining", "syntaxAsyncGenerators", "syntaxClassProperties", "syntaxClassStaticBlock", "syntaxDynamicImport", "syntaxExportNamespaceFrom", "syntaxImportAssertions", "syntaxJsonStrings", "syntaxLogicalAssignmentOperators", "syntaxNullishCoalescingOperator", "syntaxNumericSeparator", "syntaxObjectRestSpread", "syntaxOptionalCatchBinding", "syntaxOptionalChaining", "syntaxPrivatePropertyInObject", "syntaxTopLevelAwait", "transformArrowFunctions", "proposalAsyncGeneratorFunctions", "transformAsyncToGenerator", "transformBlockScopedFunctions", "transformBlockScoping", "proposalClassProperties", "proposalClassStaticBlock", "transformClasses", "transformComputedProperties", "transformDestructuring", "transformDotallRegex", "transformDuplicateKeys", "proposalDynamicImport", "transformExponentialOperator", "proposalExportNamespaceFrom", "transformForOf", "transformFunctionName", "proposalJsonStrings", "transformLiterals", "proposalLogicalAssignmentOperators", "transformMemberExpressionLiterals", "transformModulesAmd", "transformModulesCommonjs", "transformModulesSystemjs", "transformModulesUmd", "transformNamedCapturingGroupsRegex", "transformNewTarget", "proposalNullishCoalescingOperator", "proposalNumericSeparator", "proposalObjectRestSpread", "transformObjectSuper", "proposalOptionalCatchBinding", "proposalOptionalChaining", "transformParameters", "proposalPrivateMethods", "proposalPrivatePropertyInObject", "transformPropertyLiterals", "transformRegenerator", "transformReservedWords", "transformShorthandProperties", "transformSpread", "transformStickyRegex", "transformTemplateLiterals", "transformTypeofSymbol", "transformUnicodeEscapes", "proposalUnicodePropertyRegex", "transformUnicodeRegex", "minVersions"], "sources": ["../src/available-plugins.ts"], "sourcesContent": ["/* eslint sort-keys: \"error\" */\n\nimport syntaxAsyncGenerators from \"@babel/plugin-syntax-async-generators\";\nimport syntaxClassProperties from \"@babel/plugin-syntax-class-properties\";\nimport syntaxClassStaticBlock from \"@babel/plugin-syntax-class-static-block\";\nimport syntaxDynamicImport from \"@babel/plugin-syntax-dynamic-import\";\nimport syntaxExportNamespaceFrom from \"@babel/plugin-syntax-export-namespace-from\";\nimport syntaxImportAssertions from \"@babel/plugin-syntax-import-assertions\";\nimport syntaxJsonStrings from \"@babel/plugin-syntax-json-strings\";\nimport syntaxLogicalAssignmentOperators from \"@babel/plugin-syntax-logical-assignment-operators\";\nimport syntaxNullishCoalescingOperator from \"@babel/plugin-syntax-nullish-coalescing-operator\";\nimport syntaxNumericSeparator from \"@babel/plugin-syntax-numeric-separator\";\nimport syntaxObjectRestSpread from \"@babel/plugin-syntax-object-rest-spread\";\nimport syntaxOptionalCatchBinding from \"@babel/plugin-syntax-optional-catch-binding\";\nimport syntaxOptionalChaining from \"@babel/plugin-syntax-optional-chaining\";\nimport syntaxPrivatePropertyInObject from \"@babel/plugin-syntax-private-property-in-object\";\nimport syntaxTopLevelAwait from \"@babel/plugin-syntax-top-level-await\";\nimport proposalAsyncGeneratorFunctions from \"@babel/plugin-proposal-async-generator-functions\";\nimport proposalClassProperties from \"@babel/plugin-proposal-class-properties\";\nimport proposalClassStaticBlock from \"@babel/plugin-proposal-class-static-block\";\nimport proposalDynamicImport from \"@babel/plugin-proposal-dynamic-import\";\nimport proposalExportNamespaceFrom from \"@babel/plugin-proposal-export-namespace-from\";\nimport proposalJsonStrings from \"@babel/plugin-proposal-json-strings\";\nimport proposalLogicalAssignmentOperators from \"@babel/plugin-proposal-logical-assignment-operators\";\nimport proposalNullishCoalescingOperator from \"@babel/plugin-proposal-nullish-coalescing-operator\";\nimport proposalNumericSeparator from \"@babel/plugin-proposal-numeric-separator\";\nimport proposalObjectRestSpread from \"@babel/plugin-proposal-object-rest-spread\";\nimport proposalOptionalCatchBinding from \"@babel/plugin-proposal-optional-catch-binding\";\nimport proposalOptionalChaining from \"@babel/plugin-proposal-optional-chaining\";\nimport proposalPrivateMethods from \"@babel/plugin-proposal-private-methods\";\nimport proposalPrivatePropertyInObject from \"@babel/plugin-proposal-private-property-in-object\";\nimport proposalUnicodePropertyRegex from \"@babel/plugin-proposal-unicode-property-regex\";\nimport transformAsyncToGenerator from \"@babel/plugin-transform-async-to-generator\";\nimport transformArrowFunctions from \"@babel/plugin-transform-arrow-functions\";\nimport transformBlockScopedFunctions from \"@babel/plugin-transform-block-scoped-functions\";\nimport transformBlockScoping from \"@babel/plugin-transform-block-scoping\";\nimport transformClasses from \"@babel/plugin-transform-classes\";\nimport transformComputedProperties from \"@babel/plugin-transform-computed-properties\";\nimport transformDestructuring from \"@babel/plugin-transform-destructuring\";\nimport transformDotallRegex from \"@babel/plugin-transform-dotall-regex\";\nimport transformDuplicateKeys from \"@babel/plugin-transform-duplicate-keys\";\nimport transformExponentialOperator from \"@babel/plugin-transform-exponentiation-operator\";\nimport transformForOf from \"@babel/plugin-transform-for-of\";\nimport transformFunctionName from \"@babel/plugin-transform-function-name\";\nimport transformLiterals from \"@babel/plugin-transform-literals\";\nimport transformMemberExpressionLiterals from \"@babel/plugin-transform-member-expression-literals\";\nimport transformModulesAmd from \"@babel/plugin-transform-modules-amd\";\nimport transformModulesCommonjs from \"@babel/plugin-transform-modules-commonjs\";\nimport transformModulesSystemjs from \"@babel/plugin-transform-modules-systemjs\";\nimport transformModulesUmd from \"@babel/plugin-transform-modules-umd\";\nimport transformNamedCapturingGroupsRegex from \"@babel/plugin-transform-named-capturing-groups-regex\";\nimport transformNewTarget from \"@babel/plugin-transform-new-target\";\nimport transformObjectSuper from \"@babel/plugin-transform-object-super\";\nimport transformParameters from \"@babel/plugin-transform-parameters\";\nimport transformPropertyLiterals from \"@babel/plugin-transform-property-literals\";\nimport transformRegenerator from \"@babel/plugin-transform-regenerator\";\nimport transformReservedWords from \"@babel/plugin-transform-reserved-words\";\nimport transformShorthandProperties from \"@babel/plugin-transform-shorthand-properties\";\nimport transformSpread from \"@babel/plugin-transform-spread\";\nimport transformStickyRegex from \"@babel/plugin-transform-sticky-regex\";\nimport transformTemplateLiterals from \"@babel/plugin-transform-template-literals\";\nimport transformTypeofSymbol from \"@babel/plugin-transform-typeof-symbol\";\nimport transformUnicodeEscapes from \"@babel/plugin-transform-unicode-escapes\";\nimport transformUnicodeRegex from \"@babel/plugin-transform-unicode-regex\";\n\nimport bugfixAsyncArrowsInClass from \"@babel/preset-modules/lib/plugins/transform-async-arrows-in-class\";\nimport bugfixEdgeDefaultParameters from \"@babel/preset-modules/lib/plugins/transform-edge-default-parameters\";\nimport bugfixEdgeFunctionName from \"@babel/preset-modules/lib/plugins/transform-edge-function-name\";\nimport bugfixTaggedTemplateCaching from \"@babel/preset-modules/lib/plugins/transform-tagged-template-caching\";\nimport bugfixSafariBlockShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-block-shadowing\";\nimport bugfixSafariForShadowing from \"@babel/preset-modules/lib/plugins/transform-safari-for-shadowing\";\nimport bugfixSafariIdDestructuringCollisionInFunctionExpression from \"@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression\";\nimport bugfixV8SpreadParametersInOptionalChaining from \"@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining\";\n\nexport default {\n  \"bugfix/transform-async-arrows-in-class\": () => bugfixAsyncArrowsInClass,\n  \"bugfix/transform-edge-default-parameters\": () => bugfixEdgeDefaultParameters,\n  \"bugfix/transform-edge-function-name\": () => bugfixEdgeFunctionName,\n  \"bugfix/transform-safari-block-shadowing\": () => bugfixSafariBlockShadowing,\n  \"bugfix/transform-safari-for-shadowing\": () => bugfixSafariForShadowing,\n  \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n    () => bugfixSafariIdDestructuringCollisionInFunctionExpression,\n  \"bugfix/transform-tagged-template-caching\": () => bugfixTaggedTemplateCaching,\n  \"bugfix/transform-v8-spread-parameters-in-optional-chaining\": () =>\n    bugfixV8SpreadParametersInOptionalChaining,\n  \"syntax-async-generators\": () => syntaxAsyncGenerators,\n  \"syntax-class-properties\": () => syntaxClassProperties,\n  \"syntax-class-static-block\": () => syntaxClassStaticBlock,\n  \"syntax-dynamic-import\": () => syntaxDynamicImport,\n  \"syntax-export-namespace-from\": () => syntaxExportNamespaceFrom,\n  \"syntax-import-assertions\": () => syntaxImportAssertions,\n  \"syntax-json-strings\": () => syntaxJsonStrings,\n  \"syntax-logical-assignment-operators\": () => syntaxLogicalAssignmentOperators,\n  \"syntax-nullish-coalescing-operator\": () => syntaxNullishCoalescingOperator,\n  \"syntax-numeric-separator\": () => syntaxNumericSeparator,\n  \"syntax-object-rest-spread\": () => syntaxObjectRestSpread,\n  \"syntax-optional-catch-binding\": () => syntaxOptionalCatchBinding,\n  \"syntax-optional-chaining\": () => syntaxOptionalChaining,\n  \"syntax-private-property-in-object\": () => syntaxPrivatePropertyInObject,\n  \"syntax-top-level-await\": () => syntaxTopLevelAwait,\n  \"transform-arrow-functions\": () => transformArrowFunctions,\n  \"transform-async-generator-functions\": () => proposalAsyncGeneratorFunctions,\n  \"transform-async-to-generator\": () => transformAsyncToGenerator,\n  \"transform-block-scoped-functions\": () => transformBlockScopedFunctions,\n  \"transform-block-scoping\": () => transformBlockScoping,\n  \"transform-class-properties\": () => proposalClassProperties,\n  \"transform-class-static-block\": () => proposalClassStaticBlock,\n  \"transform-classes\": () => transformClasses,\n  \"transform-computed-properties\": () => transformComputedProperties,\n  \"transform-destructuring\": () => transformDestructuring,\n  \"transform-dotall-regex\": () => transformDotallRegex,\n  \"transform-duplicate-keys\": () => transformDuplicateKeys,\n  \"transform-dynamic-import\": () => proposalDynamicImport,\n  \"transform-exponentiation-operator\": () => transformExponentialOperator,\n  \"transform-export-namespace-from\": () => proposalExportNamespaceFrom,\n  \"transform-for-of\": () => transformForOf,\n  \"transform-function-name\": () => transformFunctionName,\n  \"transform-json-strings\": () => proposalJsonStrings,\n  \"transform-literals\": () => transformLiterals,\n  \"transform-logical-assignment-operators\": () =>\n    proposalLogicalAssignmentOperators,\n  \"transform-member-expression-literals\": () =>\n    transformMemberExpressionLiterals,\n  \"transform-modules-amd\": () => transformModulesAmd,\n  \"transform-modules-commonjs\": () => transformModulesCommonjs,\n  \"transform-modules-systemjs\": () => transformModulesSystemjs,\n  \"transform-modules-umd\": () => transformModulesUmd,\n  \"transform-named-capturing-groups-regex\": () =>\n    transformNamedCapturingGroupsRegex,\n  \"transform-new-target\": () => transformNewTarget,\n  \"transform-nullish-coalescing-operator\": () =>\n    proposalNullishCoalescingOperator,\n  \"transform-numeric-separator\": () => proposalNumericSeparator,\n  \"transform-object-rest-spread\": () => proposalObjectRestSpread,\n  \"transform-object-super\": () => transformObjectSuper,\n  \"transform-optional-catch-binding\": () => proposalOptionalCatchBinding,\n  \"transform-optional-chaining\": () => proposalOptionalChaining,\n  \"transform-parameters\": () => transformParameters,\n  \"transform-private-methods\": () => proposalPrivateMethods,\n  \"transform-private-property-in-object\": () => proposalPrivatePropertyInObject,\n  \"transform-property-literals\": () => transformPropertyLiterals,\n  \"transform-regenerator\": () => transformRegenerator,\n  \"transform-reserved-words\": () => transformReservedWords,\n  \"transform-shorthand-properties\": () => transformShorthandProperties,\n  \"transform-spread\": () => transformSpread,\n  \"transform-sticky-regex\": () => transformStickyRegex,\n  \"transform-template-literals\": () => transformTemplateLiterals,\n  \"transform-typeof-symbol\": () => transformTypeofSymbol,\n  \"transform-unicode-escapes\": () => transformUnicodeEscapes,\n  \"transform-unicode-property-regex\": () => proposalUnicodePropertyRegex,\n  \"transform-unicode-regex\": () => transformUnicodeRegex,\n};\n\nexport const minVersions = {\n  \"bugfix/transform-safari-id-destructuring-collision-in-function-expression\":\n    \"7.16.0\",\n  \"transform-class-static-block\": \"7.12.0\",\n  \"transform-private-property-in-object\": \"7.10.0\",\n};\n"], "mappings": ";;;;;;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AAEA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;AACA;;eAEe;EACb,0CAA0C,MAAMA,4BADnC;EAEb,4CAA4C,MAAMC,+BAFrC;EAGb,uCAAuC,MAAMC,0BAHhC;EAIb,2CAA2C,MAAMC,8BAJpC;EAKb,yCAAyC,MAAMC,4BALlC;EAMb,6EACE,MAAMC,uEAPK;EAQb,4CAA4C,MAAMC,+BARrC;EASb,8DAA8D,MAC5DC,yDAVW;EAWb,2BAA2B,MAAMC,4BAXpB;EAYb,2BAA2B,MAAMC,4BAZpB;EAab,6BAA6B,MAAMC,6BAbtB;EAcb,yBAAyB,MAAMC,0BAdlB;EAeb,gCAAgC,MAAMC,gCAfzB;EAgBb,4BAA4B,MAAMC,qCAhBrB;EAiBb,uBAAuB,MAAMC,wBAjBhB;EAkBb,uCAAuC,MAAMC,uCAlBhC;EAmBb,sCAAsC,MAAMC,sCAnB/B;EAoBb,4BAA4B,MAAMC,6BApBrB;EAqBb,6BAA6B,MAAMC,6BArBtB;EAsBb,iCAAiC,MAAMC,iCAtB1B;EAuBb,4BAA4B,MAAMC,6BAvBrB;EAwBb,qCAAqC,MAAMC,oCAxB9B;EAyBb,0BAA0B,MAAMC,0BAzBnB;EA0Bb,6BAA6B,MAAMC,sCA1BtB;EA2Bb,uCAAuC,MAAMC,8CA3BhC;EA4Bb,gCAAgC,MAAMC,wCA5BzB;EA6Bb,oCAAoC,MAAMC,4CA7B7B;EA8Bb,2BAA2B,MAAMC,oCA9BpB;EA+Bb,8BAA8B,MAAMC,sCA/BvB;EAgCb,gCAAgC,MAAMC,uCAhCzB;EAiCb,qBAAqB,MAAMC,+BAjCd;EAkCb,iCAAiC,MAAMC,0CAlC1B;EAmCb,2BAA2B,MAAMC,qCAnCpB;EAoCb,0BAA0B,MAAMC,mCApCnB;EAqCb,4BAA4B,MAAMC,qCArCrB;EAsCb,4BAA4B,MAAMC,oCAtCrB;EAuCb,qCAAqC,MAAMC,8CAvC9B;EAwCb,mCAAmC,MAAMC,0CAxC5B;EAyCb,oBAAoB,MAAMC,6BAzCb;EA0Cb,2BAA2B,MAAMC,oCA1CpB;EA2Cb,0BAA0B,MAAMC,kCA3CnB;EA4Cb,sBAAsB,MAAMC,gCA5Cf;EA6Cb,0CAA0C,MACxCC,iDA9CW;EA+Cb,wCAAwC,MACtCC,gDAhDW;EAiDb,yBAAyB,MAAMC,kCAjDlB;EAkDb,8BAA8B,MAAMC,uCAlDvB;EAmDb,8BAA8B,MAAMC,uCAnDvB;EAoDb,yBAAyB,MAAMC,kCApDlB;EAqDb,0CAA0C,MACxCC,iDAtDW;EAuDb,wBAAwB,MAAMC,iCAvDjB;EAwDb,yCAAyC,MACvCC,gDAzDW;EA0Db,+BAA+B,MAAMC,uCA1DxB;EA2Db,gCAAgC,MAAMC,uCA3DzB;EA4Db,0BAA0B,MAAMC,mCA5DnB;EA6Db,oCAAoC,MAAMC,2CA7D7B;EA8Db,+BAA+B,MAAMC,uCA9DxB;EA+Db,wBAAwB,MAAMC,kCA/DjB;EAgEb,6BAA6B,MAAMC,qCAhEtB;EAiEb,wCAAwC,MAAMC,8CAjEjC;EAkEb,+BAA+B,MAAMC,wCAlExB;EAmEb,yBAAyB,MAAMC,mCAnElB;EAoEb,4BAA4B,MAAMC,qCApErB;EAqEb,kCAAkC,MAAMC,2CArE3B;EAsEb,oBAAoB,MAAMC,8BAtEb;EAuEb,0BAA0B,MAAMC,mCAvEnB;EAwEb,+BAA+B,MAAMC,wCAxExB;EAyEb,2BAA2B,MAAMC,oCAzEpB;EA0Eb,6BAA6B,MAAMC,sCA1EtB;EA2Eb,oCAAoC,MAAMC,2CA3E7B;EA4Eb,2BAA2B,MAAMC;AA5EpB,C;;AA+ER,MAAMC,WAAW,GAAG;EACzB,6EACE,QAFuB;EAGzB,gCAAgC,QAHP;EAIzB,wCAAwC;AAJf,CAApB"}