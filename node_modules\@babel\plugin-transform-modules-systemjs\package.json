{"name": "@babel/plugin-transform-modules-systemjs", "version": "7.19.0", "description": "This plugin transforms ES2015 modules to SystemJS", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-modules-systemjs"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-hoist-variables": "^7.18.6", "@babel/helper-module-transforms": "^7.19.0", "@babel/helper-plugin-utils": "^7.19.0", "@babel/helper-validator-identifier": "^7.18.6", "babel-plugin-dynamic-import-node": "^2.3.3"}, "keywords": ["babel-plugin"], "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/plugin-syntax-dynamic-import": "^7.8.3"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-modules-systemjs", "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}