/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_document";
exports.ids = ["pages/_document"];
exports.modules = {

/***/ "./node_modules/next/dist/pages/_document.js":
/*!***************************************************!*\
  !*** ./node_modules/next/dist/pages/_document.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports.Html = Html;\nexports.Main = Main;\nexports[\"default\"] = void 0;\nvar _react = _interopRequireWildcard(__webpack_require__(/*! react */ \"react\"));\nvar _constants = __webpack_require__(/*! ../shared/lib/constants */ \"../shared/lib/constants\");\nvar _getPageFiles = __webpack_require__(/*! ../server/get-page-files */ \"../server/get-page-files\");\nvar _htmlescape = __webpack_require__(/*! ../server/htmlescape */ \"../server/htmlescape\");\nvar _isError = _interopRequireDefault(__webpack_require__(/*! ../lib/is-error */ \"./node_modules/next/dist/lib/is-error.js\"));\nvar _htmlContext = __webpack_require__(/*! ../shared/lib/html-context */ \"../shared/lib/html-context\");\nclass Document extends _react.default.Component {\n    /**\n   * `getInitialProps` hook returns the context object with the addition of `renderPage`.\n   * `renderPage` callback executes `React` rendering logic synchronously to support server-rendering wrappers\n   */ static getInitialProps(ctx) {\n        return ctx.defaultGetInitialProps(ctx);\n    }\n    render() {\n        return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n    }\n}\nexports[\"default\"] = Document;\nfunction _interopRequireDefault(obj) {\n    return obj && obj.__esModule ? obj : {\n        default: obj\n    };\n}\nfunction _getRequireWildcardCache() {\n    if (typeof WeakMap !== \"function\") return null;\n    var cache = new WeakMap();\n    _getRequireWildcardCache = function() {\n        return cache;\n    };\n    return cache;\n}\nfunction _interopRequireWildcard(obj) {\n    if (obj && obj.__esModule) {\n        return obj;\n    }\n    if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") {\n        return {\n            default: obj\n        };\n    }\n    var cache = _getRequireWildcardCache();\n    if (cache && cache.has(obj)) {\n        return cache.get(obj);\n    }\n    var newObj = {};\n    var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n    for(var key in obj){\n        if (Object.prototype.hasOwnProperty.call(obj, key)) {\n            var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n            if (desc && (desc.get || desc.set)) {\n                Object.defineProperty(newObj, key, desc);\n            } else {\n                newObj[key] = obj[key];\n            }\n        }\n    }\n    newObj.default = obj;\n    if (cache) {\n        cache.set(obj, newObj);\n    }\n    return newObj;\n}\nfunction getDocumentFiles(buildManifest, pathname, inAmpMode) {\n    const sharedFiles = (0, _getPageFiles).getPageFiles(buildManifest, \"/_app\");\n    const pageFiles =  true && inAmpMode ? [] : (0, _getPageFiles).getPageFiles(buildManifest, pathname);\n    return {\n        sharedFiles,\n        pageFiles,\n        allFiles: [\n            ...new Set([\n                ...sharedFiles,\n                ...pageFiles\n            ])\n        ]\n    };\n}\nfunction getPolyfillScripts(context, props) {\n    // polyfills.js has to be rendered as nomodule without async\n    // It also has to be the first script to load\n    const { assetPrefix , buildManifest , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return buildManifest.polyfillFiles.filter((polyfill)=>polyfill.endsWith(\".js\") && !polyfill.endsWith(\".module.js\")).map((polyfill)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: polyfill,\n            defer: !disableOptimizedLoading,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin,\n            noModule: true,\n            src: `${assetPrefix}/_next/${polyfill}${devOnlyCacheBusterQueryString}`\n        }));\n}\nfunction hasComponentProps(child) {\n    return !!child && !!child.props;\n}\nfunction AmpStyles({ styles  }) {\n    if (!styles) return null;\n    // try to parse styles from fragment for backwards compat\n    const curStyles = Array.isArray(styles) ? styles : [];\n    if (styles.props && // @ts-ignore Property 'props' does not exist on type ReactElement\n    Array.isArray(styles.props.children)) {\n        const hasStyles = (el)=>{\n            var ref, ref1;\n            return el == null ? void 0 : (ref = el.props) == null ? void 0 : (ref1 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref1.__html;\n        };\n        // @ts-ignore Property 'props' does not exist on type ReactElement\n        styles.props.children.forEach((child)=>{\n            if (Array.isArray(child)) {\n                child.forEach((el)=>hasStyles(el) && curStyles.push(el));\n            } else if (hasStyles(child)) {\n                curStyles.push(child);\n            }\n        });\n    }\n    /* Add custom styles before AMP styles to prevent accidental overrides */ return /*#__PURE__*/ _react.default.createElement(\"style\", {\n        \"amp-custom\": \"\",\n        dangerouslySetInnerHTML: {\n            __html: curStyles.map((style)=>style.props.dangerouslySetInnerHTML.__html).join(\"\").replace(/\\/\\*# sourceMappingURL=.*\\*\\//g, \"\").replace(/\\/\\*@ sourceURL=.*?\\*\\//g, \"\")\n        }\n    });\n}\nfunction getDynamicChunks(context, props, files) {\n    const { dynamicImports , assetPrefix , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    return dynamicImports.map((file)=>{\n        if (!file.endsWith(\".js\") || files.allFiles.includes(file)) return null;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getScripts(context, props, files) {\n    var ref;\n    const { assetPrefix , buildManifest , isDevelopment , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = context;\n    const normalScripts = files.allFiles.filter((file)=>file.endsWith(\".js\"));\n    const lowPriorityScripts = (ref = buildManifest.lowPriorityFiles) == null ? void 0 : ref.filter((file)=>file.endsWith(\".js\"));\n    return [\n        ...normalScripts,\n        ...lowPriorityScripts\n    ].map((file)=>{\n        return /*#__PURE__*/ _react.default.createElement(\"script\", {\n            key: file,\n            src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n            nonce: props.nonce,\n            async: !isDevelopment && disableOptimizedLoading,\n            defer: !disableOptimizedLoading,\n            crossOrigin: props.crossOrigin || crossOrigin\n        });\n    });\n}\nfunction getPreNextWorkerScripts(context, props) {\n    const { assetPrefix , scriptLoader , crossOrigin , nextScriptWorkers  } = context;\n    // disable `nextScriptWorkers` in edge runtime\n    if (!nextScriptWorkers || \"nodejs\" === \"edge\") return null;\n    try {\n        let { partytownSnippet  } = require(\"@builder.io/partytown/integration\");\n        const children = Array.isArray(props.children) ? props.children : [\n            props.children\n        ];\n        // Check to see if the user has defined their own Partytown configuration\n        const userDefinedConfig = children.find((child)=>{\n            var ref, ref2;\n            return hasComponentProps(child) && (child == null ? void 0 : (ref = child.props) == null ? void 0 : (ref2 = ref.dangerouslySetInnerHTML) == null ? void 0 : ref2.__html.length) && \"data-partytown-config\" in child.props;\n        });\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !userDefinedConfig && /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown-config\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `\n            partytown = {\n              lib: \"${assetPrefix}/_next/static/~partytown/\"\n            };\n          `\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            \"data-partytown\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: partytownSnippet()\n            }\n        }), (scriptLoader.worker || []).map((file, index)=>{\n            const { strategy , src , children: scriptChildren , dangerouslySetInnerHTML , ...scriptProps } = file;\n            let srcProps = {};\n            if (src) {\n                // Use external src if provided\n                srcProps.src = src;\n            } else if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                // Embed inline script if provided with dangerouslySetInnerHTML\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: dangerouslySetInnerHTML.__html\n                };\n            } else if (scriptChildren) {\n                // Embed inline script if provided with children\n                srcProps.dangerouslySetInnerHTML = {\n                    __html: typeof scriptChildren === \"string\" ? scriptChildren : Array.isArray(scriptChildren) ? scriptChildren.join(\"\") : \"\"\n                };\n            } else {\n                throw new Error(\"Invalid usage of next/script. Did you forget to include a src attribute or an inline script? https://nextjs.org/docs/messages/invalid-script\");\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, srcProps, scriptProps, {\n                type: \"text/partytown\",\n                key: src || index,\n                nonce: props.nonce,\n                \"data-nscript\": \"worker\",\n                crossOrigin: props.crossOrigin || crossOrigin\n            }));\n        }));\n    } catch (err) {\n        if ((0, _isError).default(err) && err.code !== \"MODULE_NOT_FOUND\") {\n            console.warn(`Warning: ${err.message}`);\n        }\n        return null;\n    }\n}\nfunction getPreNextScripts(context, props) {\n    const { scriptLoader , disableOptimizedLoading , crossOrigin  } = context;\n    const webWorkerScripts = getPreNextWorkerScripts(context, props);\n    const beforeInteractiveScripts = (scriptLoader.beforeInteractive || []).filter((script)=>script.src).map((file, index)=>{\n        const { strategy , ...scriptProps } = file;\n        var _defer;\n        return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n            key: scriptProps.src || index,\n            defer: (_defer = scriptProps.defer) != null ? _defer : !disableOptimizedLoading,\n            nonce: props.nonce,\n            \"data-nscript\": \"beforeInteractive\",\n            crossOrigin: props.crossOrigin || crossOrigin\n        }));\n    });\n    return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, webWorkerScripts, beforeInteractiveScripts);\n}\nfunction getHeadHTMLProps(props) {\n    const { crossOrigin , nonce , ...restProps } = props;\n    // This assignment is necessary for additional type checking to avoid unsupported attributes in <head>\n    const headProps = restProps;\n    return headProps;\n}\nfunction getAmpPath(ampPath, asPath) {\n    return ampPath || `${asPath}${asPath.includes(\"?\") ? \"&\" : \"?\"}amp=1`;\n}\nclass Head extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getCssLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , dynamicImports , crossOrigin , optimizeCss , optimizeFonts ,  } = this.context;\n        const cssFiles = files.allFiles.filter((f)=>f.endsWith(\".css\"));\n        const sharedFiles = new Set(files.sharedFiles);\n        // Unmanaged files are CSS files that will be handled directly by the\n        // webpack runtime (`mini-css-extract-plugin`).\n        let unmangedFiles = new Set([]);\n        let dynamicCssFiles = Array.from(new Set(dynamicImports.filter((file)=>file.endsWith(\".css\"))));\n        if (dynamicCssFiles.length) {\n            const existing = new Set(cssFiles);\n            dynamicCssFiles = dynamicCssFiles.filter((f)=>!(existing.has(f) || sharedFiles.has(f)));\n            unmangedFiles = new Set(dynamicCssFiles);\n            cssFiles.push(...dynamicCssFiles);\n        }\n        let cssLinkElements = [];\n        cssFiles.forEach((file)=>{\n            const isSharedFile = sharedFiles.has(file);\n            if (!optimizeCss) {\n                cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: `${file}-preload`,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"style\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                }));\n            }\n            const isUnmanagedFile = unmangedFiles.has(file);\n            cssLinkElements.push(/*#__PURE__*/ _react.default.createElement(\"link\", {\n                key: file,\n                nonce: this.props.nonce,\n                rel: \"stylesheet\",\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                \"data-n-g\": isUnmanagedFile ? undefined : isSharedFile ? \"\" : undefined,\n                \"data-n-p\": isUnmanagedFile ? undefined : isSharedFile ? undefined : \"\"\n            }));\n        });\n        if (false) {}\n        return cssLinkElements.length === 0 ? null : cssLinkElements;\n    }\n    getPreloadDynamicChunks() {\n        const { dynamicImports , assetPrefix , devOnlyCacheBusterQueryString , crossOrigin ,  } = this.context;\n        return dynamicImports.map((file)=>{\n            if (!file.endsWith(\".js\")) {\n                return null;\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"link\", {\n                rel: \"preload\",\n                key: file,\n                href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                as: \"script\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            });\n        }) // Filter out nulled scripts\n        .filter(Boolean);\n    }\n    getPreloadMainLinks(files) {\n        const { assetPrefix , devOnlyCacheBusterQueryString , scriptLoader , crossOrigin ,  } = this.context;\n        const preloadFiles = files.allFiles.filter((file)=>{\n            return file.endsWith(\".js\");\n        });\n        return [\n            ...(scriptLoader.beforeInteractive || []).map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file.src,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: file.src,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })),\n            ...preloadFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"link\", {\n                    key: file,\n                    nonce: this.props.nonce,\n                    rel: \"preload\",\n                    href: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                    as: \"script\",\n                    crossOrigin: this.props.crossOrigin || crossOrigin\n                })), \n        ];\n    }\n    getBeforeInteractiveInlineScripts() {\n        const { scriptLoader  } = this.context;\n        const { nonce , crossOrigin  } = this.props;\n        return (scriptLoader.beforeInteractive || []).filter((script)=>!script.src && (script.dangerouslySetInnerHTML || script.children)).map((file, index)=>{\n            const { strategy , children , dangerouslySetInnerHTML , src , ...scriptProps } = file;\n            let html = \"\";\n            if (dangerouslySetInnerHTML && dangerouslySetInnerHTML.__html) {\n                html = dangerouslySetInnerHTML.__html;\n            } else if (children) {\n                html = typeof children === \"string\" ? children : Array.isArray(children) ? children.join(\"\") : \"\";\n            }\n            return /*#__PURE__*/ _react.default.createElement(\"script\", Object.assign({}, scriptProps, {\n                dangerouslySetInnerHTML: {\n                    __html: html\n                },\n                key: scriptProps.id || index,\n                nonce: nonce,\n                \"data-nscript\": \"beforeInteractive\",\n                crossOrigin: crossOrigin || undefined\n            }));\n        });\n    }\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    makeStylesheetInert(node) {\n        return _react.default.Children.map(node, (c)=>{\n            var ref5, ref3;\n            if ((c == null ? void 0 : c.type) === \"link\" && (c == null ? void 0 : (ref5 = c.props) == null ? void 0 : ref5.href) && _constants.OPTIMIZED_FONT_PROVIDERS.some(({ url  })=>{\n                var ref, ref4;\n                return c == null ? void 0 : (ref = c.props) == null ? void 0 : (ref4 = ref.href) == null ? void 0 : ref4.startsWith(url);\n            })) {\n                const newProps = {\n                    ...c.props || {},\n                    \"data-href\": c.props.href,\n                    href: undefined\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps);\n            } else if (c == null ? void 0 : (ref3 = c.props) == null ? void 0 : ref3.children) {\n                const newProps1 = {\n                    ...c.props || {},\n                    children: this.makeStylesheetInert(c.props.children)\n                };\n                return /*#__PURE__*/ _react.default.cloneElement(c, newProps1);\n            }\n            return c;\n        }).filter(Boolean);\n    }\n    render() {\n        const { styles , ampPath , inAmpMode , hybridAmp , canonicalBase , __NEXT_DATA__ , dangerousAsPath , headTags , unstable_runtimeJS , unstable_JsPreload , disableOptimizedLoading , optimizeCss , optimizeFonts ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        const disableJsPreload = unstable_JsPreload === false || !disableOptimizedLoading;\n        this.context.docComponentsRendered.Head = true;\n        let { head  } = this.context;\n        let cssPreloads = [];\n        let otherHeadElements = [];\n        if (head) {\n            head.forEach((c)=>{\n                if (c && c.type === \"link\" && c.props[\"rel\"] === \"preload\" && c.props[\"as\"] === \"style\") {\n                    cssPreloads.push(c);\n                } else {\n                    c && otherHeadElements.push(c);\n                }\n            });\n            head = cssPreloads.concat(otherHeadElements);\n        }\n        let children = _react.default.Children.toArray(this.props.children).filter(Boolean);\n        // show a warning if Head contains <title> (only in development)\n        if (true) {\n            children = _react.default.Children.map(children, (child)=>{\n                var ref;\n                const isReactHelmet = child == null ? void 0 : (ref = child.props) == null ? void 0 : ref[\"data-react-helmet\"];\n                if (!isReactHelmet) {\n                    var ref6;\n                    if ((child == null ? void 0 : child.type) === \"title\") {\n                        console.warn(\"Warning: <title> should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-title\");\n                    } else if ((child == null ? void 0 : child.type) === \"meta\" && (child == null ? void 0 : (ref6 = child.props) == null ? void 0 : ref6.name) === \"viewport\") {\n                        console.warn(\"Warning: viewport meta tags should not be used in _document.js's <Head>. https://nextjs.org/docs/messages/no-document-viewport-meta\");\n                    }\n                }\n                return child;\n            });\n            if (this.props.crossOrigin) console.warn(\"Warning: `Head` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        if (false) {}\n        let hasAmphtmlRel = false;\n        let hasCanonicalRel = false;\n        // show warning and remove conflicting amp head tags\n        head = _react.default.Children.map(head || [], (child)=>{\n            if (!child) return child;\n            const { type , props  } = child;\n            if ( true && inAmpMode) {\n                let badProp = \"\";\n                if (type === \"meta\" && props.name === \"viewport\") {\n                    badProp = 'name=\"viewport\"';\n                } else if (type === \"link\" && props.rel === \"canonical\") {\n                    hasCanonicalRel = true;\n                } else if (type === \"script\") {\n                    // only block if\n                    // 1. it has a src and isn't pointing to ampproject's CDN\n                    // 2. it is using dangerouslySetInnerHTML without a type or\n                    // a type of text/javascript\n                    if (props.src && props.src.indexOf(\"ampproject\") < -1 || props.dangerouslySetInnerHTML && (!props.type || props.type === \"text/javascript\")) {\n                        badProp = \"<script\";\n                        Object.keys(props).forEach((prop)=>{\n                            badProp += ` ${prop}=\"${props[prop]}\"`;\n                        });\n                        badProp += \"/>\";\n                    }\n                }\n                if (badProp) {\n                    console.warn(`Found conflicting amp tag \"${child.type}\" with conflicting prop ${badProp} in ${__NEXT_DATA__.page}. https://nextjs.org/docs/messages/conflicting-amp-tag`);\n                    return null;\n                }\n            } else {\n                // non-amp mode\n                if (type === \"link\" && props.rel === \"amphtml\") {\n                    hasAmphtmlRel = true;\n                }\n            }\n            return child;\n        });\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        var _nonce, _nonce1;\n        return /*#__PURE__*/ _react.default.createElement(\"head\", Object.assign({}, getHeadHTMLProps(this.props)), this.context.isDevelopment && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined,\n            dangerouslySetInnerHTML: {\n                __html: `body{display:none}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-next-hide-fouc\": true,\n            \"data-ampdevmode\":  true && inAmpMode ? \"true\" : undefined\n        }, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            dangerouslySetInnerHTML: {\n                __html: `body{display:block}`\n            }\n        }))), head, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-head-count\",\n            content: _react.default.Children.count(head || []).toString()\n        }), children, optimizeFonts && /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"next-font-preconnect\"\n        }),  true && inAmpMode && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, /*#__PURE__*/ _react.default.createElement(\"meta\", {\n            name: \"viewport\",\n            content: \"width=device-width,minimum-scale=1,initial-scale=1\"\n        }), !hasCanonicalRel && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"canonical\",\n            href: canonicalBase + (__webpack_require__(/*! ../server/utils */ \"../server/utils\").cleanAmpPath)(dangerousAsPath)\n        }), /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"preload\",\n            as: \"script\",\n            href: \"https://cdn.ampproject.org/v0.js\"\n        }), /*#__PURE__*/ _react.default.createElement(AmpStyles, {\n            styles: styles\n        }), /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}`\n            }\n        }), /*#__PURE__*/ _react.default.createElement(\"noscript\", null, /*#__PURE__*/ _react.default.createElement(\"style\", {\n            \"amp-boilerplate\": \"\",\n            dangerouslySetInnerHTML: {\n                __html: `body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`\n            }\n        })), /*#__PURE__*/ _react.default.createElement(\"script\", {\n            async: true,\n            src: \"https://cdn.ampproject.org/v0.js\"\n        })), !( true && inAmpMode) && /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !hasAmphtmlRel && hybridAmp && /*#__PURE__*/ _react.default.createElement(\"link\", {\n            rel: \"amphtml\",\n            href: canonicalBase + getAmpPath(ampPath, dangerousAsPath)\n        }), this.getBeforeInteractiveInlineScripts(), !optimizeCss && this.getCssLinks(files), !optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce = this.props.nonce) != null ? _nonce : \"\"\n        }), !disableRuntimeJS && !disableJsPreload && this.getPreloadDynamicChunks(), !disableRuntimeJS && !disableJsPreload && this.getPreloadMainLinks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), !disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), !disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files), optimizeCss && this.getCssLinks(files), optimizeCss && /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            \"data-n-css\": (_nonce1 = this.props.nonce) != null ? _nonce1 : \"\"\n        }), this.context.isDevelopment && // this element is used to mount development styles so the\n        // ordering matches production\n        // (by default, style-loader injects at the bottom of <head />)\n        /*#__PURE__*/ _react.default.createElement(\"noscript\", {\n            id: \"__next_css__DO_NOT_USE__\"\n        }), styles || null), /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, {}, ...headTags || []));\n    }\n}\nexports.Head = Head;\nfunction handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props) {\n    var ref10, ref7, ref8, ref9;\n    if (!props.children) return;\n    const scriptLoaderItems = [];\n    const children = Array.isArray(props.children) ? props.children : [\n        props.children\n    ];\n    const headChildren = (ref10 = children.find((child)=>child.type === Head)) == null ? void 0 : (ref7 = ref10.props) == null ? void 0 : ref7.children;\n    const bodyChildren = (ref8 = children.find((child)=>child.type === \"body\")) == null ? void 0 : (ref9 = ref8.props) == null ? void 0 : ref9.children;\n    // Scripts with beforeInteractive can be placed inside Head or <body> so children of both needs to be traversed\n    const combinedChildren = [\n        ...Array.isArray(headChildren) ? headChildren : [\n            headChildren\n        ],\n        ...Array.isArray(bodyChildren) ? bodyChildren : [\n            bodyChildren\n        ], \n    ];\n    _react.default.Children.forEach(combinedChildren, (child)=>{\n        var ref;\n        if (!child) return;\n        // When using the `next/script` component, register it in script loader.\n        if ((ref = child.type) == null ? void 0 : ref.__nextScript) {\n            if (child.props.strategy === \"beforeInteractive\") {\n                scriptLoader.beforeInteractive = (scriptLoader.beforeInteractive || []).concat([\n                    {\n                        ...child.props\n                    }, \n                ]);\n                return;\n            } else if ([\n                \"lazyOnload\",\n                \"afterInteractive\",\n                \"worker\"\n            ].includes(child.props.strategy)) {\n                scriptLoaderItems.push(child.props);\n                return;\n            }\n        }\n    });\n    __NEXT_DATA__.scriptLoader = scriptLoaderItems;\n}\nclass NextScript extends _react.default.Component {\n    static contextType = _htmlContext.HtmlContext;\n    getDynamicChunks(files) {\n        return getDynamicChunks(this.context, this.props, files);\n    }\n    getPreNextScripts() {\n        return getPreNextScripts(this.context, this.props);\n    }\n    getScripts(files) {\n        return getScripts(this.context, this.props, files);\n    }\n    getPolyfillScripts() {\n        return getPolyfillScripts(this.context, this.props);\n    }\n    static getInlineScriptSource(context) {\n        const { __NEXT_DATA__ , largePageDataBytes  } = context;\n        try {\n            const data = JSON.stringify(__NEXT_DATA__);\n            const bytes =  false ? 0 : Buffer.from(data).byteLength;\n            const prettyBytes = (__webpack_require__(/*! ../lib/pretty-bytes */ \"./node_modules/next/dist/lib/pretty-bytes.js\")[\"default\"]);\n            if (largePageDataBytes && bytes > largePageDataBytes) {\n                console.warn(`Warning: data for page \"${__NEXT_DATA__.page}\"${__NEXT_DATA__.page === context.dangerousAsPath ? \"\" : ` (path \"${context.dangerousAsPath}\")`} is ${prettyBytes(bytes)} which exceeds the threshold of ${prettyBytes(largePageDataBytes)}, this amount of data can reduce performance.\\nSee more info here: https://nextjs.org/docs/messages/large-page-data`);\n            }\n            return (0, _htmlescape).htmlEscapeJsonString(data);\n        } catch (err) {\n            if ((0, _isError).default(err) && err.message.indexOf(\"circular structure\") !== -1) {\n                throw new Error(`Circular structure in \"getInitialProps\" result of page \"${__NEXT_DATA__.page}\". https://nextjs.org/docs/messages/circular-structure`);\n            }\n            throw err;\n        }\n    }\n    render() {\n        const { assetPrefix , inAmpMode , buildManifest , unstable_runtimeJS , docComponentsRendered , devOnlyCacheBusterQueryString , disableOptimizedLoading , crossOrigin ,  } = this.context;\n        const disableRuntimeJS = unstable_runtimeJS === false;\n        docComponentsRendered.NextScript = true;\n        if ( true && inAmpMode) {\n            if (false) {}\n            const ampDevFiles = [\n                ...buildManifest.devFiles,\n                ...buildManifest.polyfillFiles,\n                ...buildManifest.ampDevFiles, \n            ];\n            return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n                id: \"__NEXT_DATA__\",\n                type: \"application/json\",\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin,\n                dangerouslySetInnerHTML: {\n                    __html: NextScript.getInlineScriptSource(this.context)\n                },\n                \"data-ampdevmode\": true\n            }), ampDevFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                    key: file,\n                    src: `${assetPrefix}/_next/${file}${devOnlyCacheBusterQueryString}`,\n                    nonce: this.props.nonce,\n                    crossOrigin: this.props.crossOrigin || crossOrigin,\n                    \"data-ampdevmode\": true\n                })));\n        }\n        if (true) {\n            if (this.props.crossOrigin) console.warn(\"Warning: `NextScript` attribute `crossOrigin` is deprecated. https://nextjs.org/docs/messages/doc-crossorigin-deprecated\");\n        }\n        const files = getDocumentFiles(this.context.buildManifest, this.context.__NEXT_DATA__.page,  true && inAmpMode);\n        return /*#__PURE__*/ _react.default.createElement(_react.default.Fragment, null, !disableRuntimeJS && buildManifest.devFiles ? buildManifest.devFiles.map((file)=>/*#__PURE__*/ _react.default.createElement(\"script\", {\n                key: file,\n                src: `${assetPrefix}/_next/${encodeURI(file)}${devOnlyCacheBusterQueryString}`,\n                nonce: this.props.nonce,\n                crossOrigin: this.props.crossOrigin || crossOrigin\n            })) : null, disableRuntimeJS ? null : /*#__PURE__*/ _react.default.createElement(\"script\", {\n            id: \"__NEXT_DATA__\",\n            type: \"application/json\",\n            nonce: this.props.nonce,\n            crossOrigin: this.props.crossOrigin || crossOrigin,\n            dangerouslySetInnerHTML: {\n                __html: NextScript.getInlineScriptSource(this.context)\n            }\n        }), disableOptimizedLoading && !disableRuntimeJS && this.getPolyfillScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getPreNextScripts(), disableOptimizedLoading && !disableRuntimeJS && this.getDynamicChunks(files), disableOptimizedLoading && !disableRuntimeJS && this.getScripts(files));\n    }\n}\nexports.NextScript = NextScript;\nfunction Html(props) {\n    const { inAmpMode , docComponentsRendered , locale , scriptLoader , __NEXT_DATA__ ,  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Html = true;\n    handleDocumentScriptLoaderItems(scriptLoader, __NEXT_DATA__, props);\n    return /*#__PURE__*/ _react.default.createElement(\"html\", Object.assign({}, props, {\n        lang: props.lang || locale || undefined,\n        amp:  true && inAmpMode ? \"\" : undefined,\n        \"data-ampdevmode\":  true && inAmpMode && \"development\" !== \"production\" ? \"\" : undefined\n    }));\n}\nfunction Main() {\n    const { docComponentsRendered  } = (0, _react).useContext(_htmlContext.HtmlContext);\n    docComponentsRendered.Main = true;\n    // @ts-ignore\n    return /*#__PURE__*/ _react.default.createElement(\"next-js-internal-body-render-target\", null);\n}\n// Add a special property to the built-in `Document` component so later we can\n// identify if a user customized `Document` is used or not.\nconst InternalFunctionDocument = function InternalFunctionDocument() {\n    return /*#__PURE__*/ _react.default.createElement(Html, null, /*#__PURE__*/ _react.default.createElement(Head, null), /*#__PURE__*/ _react.default.createElement(\"body\", null, /*#__PURE__*/ _react.default.createElement(Main, null), /*#__PURE__*/ _react.default.createElement(NextScript, null)));\n};\nDocument[_constants.NEXT_BUILTIN_DOCUMENT] = InternalFunctionDocument; //# sourceMappingURL=_document.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/pages/_document.js\n");

/***/ }),

/***/ "./pages/_document.tsx":
/*!*****************************!*\
  !*** ./pages/_document.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @material-ui/core */ \"@material-ui/core\");\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_material_ui_core__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/document */ \"./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n\n\n\n\nclass MyDocument extends (next_document__WEBPACK_IMPORTED_MODULE_2___default()) {\n    render() {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Html, {\n            className: \"nightwind\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Head, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"application-name\",\n                            content: \"Notea\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"apple-mobile-web-app-capable\",\n                            content: \"yes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"apple-mobile-web-app-status-bar-style\",\n                            content: \"default\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 12,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"apple-mobile-web-app-title\",\n                            content: \"Notea\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: \"Self hosted note taking app stored on S3.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"format-detection\",\n                            content: \"telephone=no\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"mobile-web-app-capable\",\n                            content: \"yes\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"msapplication-TileColor\",\n                            content: \"#2B5797\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"msapplication-tap-highlight\",\n                            content: \"no\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"theme-color\",\n                            content: \"#ffffff\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"apple-touch-icon\",\n                            sizes: \"192x192\",\n                            href: \"/static/icons/icon-192x192.png\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"icon\",\n                            type: \"image/png\",\n                            sizes: \"128x128\",\n                            href: \"/static/icons/icon-128x128.png\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                            rel: \"manifest\",\n                            href: \"/static/manifest.json\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 38,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                    className: \"bg-gray-50 text-gray-800 overflow-x-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.Main, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_2__.NextScript, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                            lineNumber: 42,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n            lineNumber: 8,\n            columnNumber: 13\n        }, this);\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyDocument);\n// `getInitialProps` belongs to `_document` (instead of `_app`),\n// it's compatible with server-side generation (SSG).\nMyDocument.getInitialProps = async (ctx)=>{\n    // Resolution order\n    //\n    // On the server:\n    // 1. app.getInitialProps\n    // 2. page.getInitialProps\n    // 3. document.getInitialProps\n    // 4. app.render\n    // 5. page.render\n    // 6. document.render\n    //\n    // On the server with error:\n    // 1. document.getInitialProps\n    // 2. app.render\n    // 3. page.render\n    // 4. document.render\n    //\n    // On the client\n    // 1. app.getInitialProps\n    // 2. page.getInitialProps\n    // 3. app.render\n    // 4. page.render\n    // Render app and page and get the context of the page with collected side effects.\n    const sheets = new _material_ui_core__WEBPACK_IMPORTED_MODULE_1__.ServerStyleSheets();\n    const originalRenderPage = ctx.renderPage;\n    ctx.renderPage = ()=>originalRenderPage({\n            enhanceApp: (App)=>(props)=>sheets.collect(/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(App, {\n                        ...props\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_document.tsx\",\n                        lineNumber: 82,\n                        columnNumber: 60\n                    }, undefined))\n        });\n    const initialProps = await next_document__WEBPACK_IMPORTED_MODULE_2___default().getInitialProps(ctx);\n    return {\n        ...initialProps,\n        // Styles fragment is rendered after the app and page rendering finish.\n        styles: [\n            ...react__WEBPACK_IMPORTED_MODULE_3__.Children.toArray(initialProps.styles),\n            sheets.getStyleElement(), \n        ]\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_document.tsx\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/is-error.js":
/*!************************************************!*\
  !*** ./node_modules/next/dist/lib/is-error.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = isError;\nexports.getProperError = getProperError;\nvar _isPlainObject = __webpack_require__(/*! ../shared/lib/is-plain-object */ \"../shared/lib/is-plain-object\");\nfunction isError(err) {\n    return typeof err === \"object\" && err !== null && \"name\" in err && \"message\" in err;\n}\nfunction getProperError(err) {\n    if (isError(err)) {\n        return err;\n    }\n    if (true) {\n        // provide better error for case where `throw undefined`\n        // is called in development\n        if (typeof err === \"undefined\") {\n            return new Error(\"An undefined error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n        if (err === null) {\n            return new Error(\"A null error was thrown, \" + \"see here for more info: https://nextjs.org/docs/messages/threw-undefined\");\n        }\n    }\n    return new Error((0, _isPlainObject).isPlainObject(err) ? JSON.stringify(err) : err + \"\");\n}\n\n//# sourceMappingURL=is-error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/is-error.js\n");

/***/ }),

/***/ "./node_modules/next/dist/lib/pretty-bytes.js":
/*!****************************************************!*\
  !*** ./node_modules/next/dist/lib/pretty-bytes.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports) => {

"use strict";
eval("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nexports[\"default\"] = prettyBytes;\nfunction prettyBytes(number, options) {\n    if (!Number.isFinite(number)) {\n        throw new TypeError(`Expected a finite number, got ${typeof number}: ${number}`);\n    }\n    options = Object.assign({}, options);\n    if (options.signed && number === 0) {\n        return \" 0 B\";\n    }\n    const isNegative = number < 0;\n    const prefix = isNegative ? \"-\" : options.signed ? \"+\" : \"\";\n    if (isNegative) {\n        number = -number;\n    }\n    if (number < 1) {\n        const numberString = toLocaleString(number, options.locale);\n        return prefix + numberString + \" B\";\n    }\n    const exponent = Math.min(Math.floor(Math.log10(number) / 3), UNITS.length - 1);\n    number = Number((number / Math.pow(1000, exponent)).toPrecision(3));\n    const numberString = toLocaleString(number, options.locale);\n    const unit = UNITS[exponent];\n    return prefix + numberString + \" \" + unit;\n}\n/*\nMIT License\n\nCopyright (c) Sindre Sorhus <<EMAIL>> (sindresorhus.com)\n\nPermission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the \"Software\"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n*/ const UNITS = [\n    \"B\",\n    \"kB\",\n    \"MB\",\n    \"GB\",\n    \"TB\",\n    \"PB\",\n    \"EB\",\n    \"ZB\",\n    \"YB\"\n];\n/*\nFormats the given number using `Number#toLocaleString`.\n- If locale is a string, the value is expected to be a locale-key (for example: `de`).\n- If locale is true, the system default locale is used for translation.\n- If no value for locale is specified, the number is returned unmodified.\n*/ const toLocaleString = (number, locale)=>{\n    let result = number;\n    if (typeof locale === \"string\") {\n        result = number.toLocaleString(locale);\n    } else if (locale === true) {\n        result = number.toLocaleString();\n    }\n    return result;\n};\n\n//# sourceMappingURL=pretty-bytes.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///./node_modules/next/dist/lib/pretty-bytes.js\n");

/***/ }),

/***/ "./node_modules/next/document.js":
/*!***************************************!*\
  !*** ./node_modules/next/document.js ***!
  \***************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("module.exports = __webpack_require__(/*! ./dist/pages/_document */ \"./node_modules/next/dist/pages/_document.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kb2N1bWVudC5qcy5qcyIsIm1hcHBpbmdzIjoiQUFBQSxpSEFBa0QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL25vZGVfbW9kdWxlcy9uZXh0L2RvY3VtZW50LmpzPzlhMTQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Rpc3QvcGFnZXMvX2RvY3VtZW50JylcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./node_modules/next/document.js\n");

/***/ }),

/***/ "@material-ui/core":
/*!************************************!*\
  !*** external "@material-ui/core" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@material-ui/core");

/***/ }),

/***/ "../server/get-page-files":
/*!*****************************************************!*\
  !*** external "next/dist/server/get-page-files.js" ***!
  \*****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/get-page-files.js");

/***/ }),

/***/ "../server/htmlescape":
/*!*************************************************!*\
  !*** external "next/dist/server/htmlescape.js" ***!
  \*************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/htmlescape.js");

/***/ }),

/***/ "../server/utils":
/*!********************************************!*\
  !*** external "next/dist/server/utils.js" ***!
  \********************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/utils.js");

/***/ }),

/***/ "../shared/lib/constants":
/*!****************************************************!*\
  !*** external "next/dist/shared/lib/constants.js" ***!
  \****************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/constants.js");

/***/ }),

/***/ "../shared/lib/html-context":
/*!*******************************************************!*\
  !*** external "next/dist/shared/lib/html-context.js" ***!
  \*******************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/html-context.js");

/***/ }),

/***/ "../shared/lib/is-plain-object":
/*!**********************************************************!*\
  !*** external "next/dist/shared/lib/is-plain-object.js" ***!
  \**********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/is-plain-object.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_document.tsx"));
module.exports = __webpack_exports__;

})();