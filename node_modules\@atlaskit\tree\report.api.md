## API Report File for "@atlaskit/tree"

> Do not edit this file. It is a report generated by [API Extractor](https://api-extractor.com/).

<!--
	Generated API Report version: 2.0
-->

[Learn more about API reports](https://hello.atlassian.net/wiki/spaces/UR/pages/1825484529/Package+API+Reports)

```ts
import { Component } from 'react';
import { DraggableId } from 'react-beautiful-dnd-next';
import { DraggableLocation } from 'react-beautiful-dnd-next';
import { DraggableProvided } from 'react-beautiful-dnd-next';
import { DraggableProvidedDraggableProps } from 'react-beautiful-dnd-next';
import { DraggableProvidedDragHandleProps } from 'react-beautiful-dnd-next';
import { DraggableStateSnapshot } from 'react-beautiful-dnd-next';
import { DragStart } from 'react-beautiful-dnd-next';
import { DragUpdate } from 'react-beautiful-dnd-next';
import { DroppableId } from 'react-beautiful-dnd-next';
import { DroppableProvided } from 'react-beautiful-dnd-next';
import { DropResult } from 'react-beautiful-dnd-next';
import { ReactNode } from 'react';

// @public (undocumented)
type Combine = {
  draggableId: DraggableId;
  droppableId: DroppableId;
};

// @public (undocumented)
class DelayedFunction {
  constructor(delay: number);
  // (undocumented)
  delay: number;
  // (undocumented)
  fn?: Function;
  // (undocumented)
  start(fn: Function): void;
  // (undocumented)
  stop(): void;
  // (undocumented)
  timeoutId: number | undefined;
}

// @public (undocumented)
type DragState = {
  source: DraggableLocation;
  mode: string;
  destination?: DraggableLocation;
  horizontalLevel?: number;
  combine?: Combine;
};

// @public (undocumented)
type FlattenedItem = {
  item: TreeItem;
  path: Path;
};

// @public (undocumented)
type FlattenedTree = FlattenedItem[];

// @public (undocumented)
export type ItemId = string | number;

// @public (undocumented)
export const moveItemOnTree: (
  tree: TreeData,
  from: TreeSourcePosition,
  to: TreeDestinationPosition,
) => TreeData;

// @public (undocumented)
export const mutateTree: (
  tree: TreeData,
  itemId: ItemId,
  mutation: TreeItemMutation,
) => TreeData;

// @public (undocumented)
export type Path = number[];

// @public (undocumented)
type Props = {
  tree: TreeData;
  onExpand: (itemId: ItemId, path: Path) => void;
  onCollapse: (itemId: ItemId, path: Path) => void;
  onDragStart: (itemId: ItemId) => void;
  onDragEnd: (
    sourcePosition: TreeSourcePosition,
    destinationPosition?: TreeDestinationPosition,
  ) => void;
  renderItem: (item: RenderItemParams) => ReactNode;
  offsetPerLevel: number;
  isDragEnabled: boolean | ((item: TreeItem) => boolean);
  isNestingEnabled: boolean;
};

// @public (undocumented)
export type RenderItemParams = {
  item: TreeItem;
  depth: number;
  onExpand: (itemId: ItemId) => void;
  onCollapse: (itemId: ItemId) => void;
  provided: TreeDraggableProvided;
  snapshot: DraggableStateSnapshot;
};

// @public (undocumented)
type State = {
  flattenedTree: FlattenedTree;
  draggedItemId?: ItemId;
};

// @public (undocumented)
class Tree extends Component<Props, State> {
  // (undocumented)
  calculateEffectivePath: (
    flatItem: FlattenedItem,
    snapshot: DraggableStateSnapshot,
  ) => Path;
  // (undocumented)
  static closeParentIfNeeded(tree: TreeData, draggedItemId?: ItemId): TreeData;
  // (undocumented)
  containerElement: HTMLElement | undefined;
  // (undocumented)
  static defaultProps: {
    tree: {
      children: never[];
    };
    onExpand: () => void;
    onCollapse: () => void;
    onDragStart: () => void;
    onDragEnd: () => void;
    renderItem: () => void;
    offsetPerLevel: number;
    isDragEnabled: boolean;
    isNestingEnabled: boolean;
  };
  // (undocumented)
  dragState?: DragState;
  // (undocumented)
  expandTimer: DelayedFunction;
  // (undocumented)
  static getDerivedStateFromProps(
    props: Props,
    state: State,
  ): {
    flattenedTree: FlattenedItem[];
    draggedItemId?: ItemId | undefined;
  };
  // (undocumented)
  getDroppedLevel: () => number | undefined;
  // (undocumented)
  isExpandable: (item: FlattenedItem) => boolean;
  // (undocumented)
  itemsElement: Record<ItemId, HTMLElement | undefined>;
  // (undocumented)
  onDragEnd: (result: DropResult) => void;
  // (undocumented)
  onDragStart: (result: DragStart) => void;
  // (undocumented)
  onDragUpdate: (update: DragUpdate) => void;
  // (undocumented)
  onDropAnimating: () => void;
  // (undocumented)
  onPointerMove: () => void;
  // (undocumented)
  patchDroppableProvided: (provided: DroppableProvided) => DroppableProvided;
  // (undocumented)
  render(): JSX.Element;
  // (undocumented)
  renderDraggableItem: (
    flatItem: FlattenedItem,
  ) => (
    provided: DraggableProvided,
    snapshot: DraggableStateSnapshot,
  ) => JSX.Element;
  // (undocumented)
  renderItem: (flatItem: FlattenedItem, index: number) => ReactNode;
  // (undocumented)
  renderItems: () => Array<ReactNode>;
  // (undocumented)
  renderTreeItem: ({
    flatItem,
    path,
    provided,
    snapshot,
  }: {
    flatItem: FlattenedItem;
    path: Path;
    provided: DraggableProvided;
    snapshot: DraggableStateSnapshot;
  }) => JSX.Element;
  // (undocumented)
  setItemRef: (itemId: ItemId, el: HTMLElement | null) => void;
  // (undocumented)
  state: {
    flattenedTree: never[];
    draggedItemId: undefined;
  };
}
export default Tree;

// @public (undocumented)
export interface TreeData {
  // (undocumented)
  items: Record<ItemId, TreeItem>;
  // (undocumented)
  rootId: ItemId;
}

// @public (undocumented)
export type TreeDestinationPosition = {
  parentId: ItemId;
  index?: number;
};

// @public (undocumented)
type TreeDraggableProvided = {
  draggableProps: DraggableProvidedDraggableProps;
  dragHandleProps: DraggableProvidedDragHandleProps | null;
  innerRef: (el: HTMLElement | null) => void;
};

// @public (undocumented)
export type TreeItem = {
  id: ItemId;
  children: ItemId[];
  hasChildren?: boolean;
  isExpanded?: boolean;
  isChildrenLoading?: boolean;
  data?: TreeItemData;
};

// @public (undocumented)
type TreeItemData = any;

// @public (undocumented)
type TreeItemMutation = {
  id?: ItemId;
  children?: ItemId[];
  hasChildren?: boolean;
  isExpanded?: boolean;
  isChildrenLoading?: boolean;
  data?: TreeItemData;
};

// @public (undocumented)
export type TreeSourcePosition = {
  parentId: ItemId;
  index: number;
};

// (No @packageDocumentation comment for this package)
```
