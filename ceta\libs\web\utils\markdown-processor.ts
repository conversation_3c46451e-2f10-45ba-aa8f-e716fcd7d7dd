import { createEditor } from 'lexical';
import { $convertFromMarkdownString, $convertToMarkdownString, TRANSFORMERS } from '@lexical/markdown';
import { HeadingNode, QuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeHighlightNode, CodeNode } from '@lexical/code';
import { AutoLinkNode, LinkNode } from '@lexical/link';
import { HorizontalRuleNode } from '@lexical/react/LexicalHorizontalRuleNode';

/**
 * Lexical Markdown 处理器
 * 专门用于Lexical编辑器的markdown处理
 * 现在将Markdown转换为Lexical JSON格式进行存储
 */
class LexicalMarkdownProcessor {

    /**
     * 对于Lexical编辑器，我们不需要将markdown转换为HTML
     * Lexical有自己的markdown解析器($convertFromMarkdownString)
     * 这个方法现在只是简单地返回清理后的markdown
     */
    processMarkdown(markdown: string): string {
        if (!markdown || markdown.trim() === '') {
            return '';
        }

        try {
            // 清理和标准化markdown内容
            let cleanMarkdown = markdown;

            // 1. 标准化换行符
            cleanMarkdown = cleanMarkdown.replace(/\r\n/g, '\n').replace(/\r/g, '\n');

            // 2. 清理开头和结尾的空白，但保留内容中的空行
            cleanMarkdown = cleanMarkdown.trim();

            // 3. 确保checkbox格式正确
            cleanMarkdown = cleanMarkdown.replace(/^(\s*)-\s*\[\s*\]\s*/gm, '$1- [ ] ');
            cleanMarkdown = cleanMarkdown.replace(/^(\s*)-\s*\[x\]\s*/gm, '$1- [x] ');

            return cleanMarkdown;
        } catch (error) {
            console.error('Error processing markdown:', error);
            return markdown; // 如果处理失败，返回原始内容
        }
    }

    /**
     * 检查内容是否看起来像 markdown
     */
    isMarkdownContent(content: string): boolean {
        if (!content || content.trim() === '') {
            return false;
        }

        // 检查常见的 markdown 语法
        const markdownPatterns = [
            /^#{1,6}\s+/m,           // 标题 # ## ###
            /^\*\s+/m,               // 无序列表 *
            /^-\s+/m,                // 无序列表 -
            /^\d+\.\s+/m,            // 有序列表 1.
            /^-\s+\[[ x]\]\s+/m,     // checkbox列表 - [ ] 或 - [x]
            /```[\s\S]*?```/,        // 代码块
            /`[^`]+`/,               // 行内代码
            /\*\*[^*]+\*\*/,         // 粗体
            /\*[^*]+\*/,             // 斜体
            /\[[^\]]+\]\([^)]+\)/,   // 链接
            /!\[[^\]]*\]\([^)]+\)/,  // 图片
            /^>\s+/m,                // 引用
            /^\|.*\|$/m,             // 表格
            /^---+$/m,               // 分隔线
        ];

        return markdownPatterns.some(pattern => pattern.test(content));
    }

    /**
     * 处理导入的内容
     * 将Markdown转换为Lexical JSON格式
     */
    processImportedContent(content: string): string {
        if (this.isMarkdownContent(content)) {
            console.log('Detected markdown content, converting to Lexical JSON...');
            return this.convertMarkdownToLexicalJSON(content);
        } else {
            console.log('Content does not appear to be markdown, creating plain text JSON...');
            return this.convertPlainTextToLexicalJSON(content);
        }
    }

    /**
     * 将Markdown转换为Lexical JSON格式
     */
    private convertMarkdownToLexicalJSON(markdown: string): string {
        try {
            // 创建临时编辑器进行转换
            const tempEditor = createEditor({
                nodes: [
                    HeadingNode,
                    ListNode,
                    ListItemNode,
                    QuoteNode,
                    CodeNode,
                    CodeHighlightNode,
                    AutoLinkNode,
                    LinkNode,
                    HorizontalRuleNode,
                ],
                onError: (error: Error) => console.error('Temp editor error:', error),
            });

            let jsonResult = '';

            tempEditor.update(() => {
                // 先清理markdown内容
                const cleanMarkdown = this.processMarkdown(markdown);

                // 使用Lexical的Markdown解析器
                $convertFromMarkdownString(cleanMarkdown, TRANSFORMERS);

                // 获取编辑器状态并转换为JSON
                const editorState = tempEditor.getEditorState();
                jsonResult = JSON.stringify(editorState.toJSON());
            });

            console.log('Successfully converted markdown to Lexical JSON');
            return jsonResult;
        } catch (error) {
            console.error('Error converting markdown to Lexical JSON:', error);
            // 如果转换失败，创建一个包含原始内容的简单JSON
            return this.convertPlainTextToLexicalJSON(markdown);
        }
    }

    /**
     * 将纯文本转换为Lexical JSON格式
     */
    private convertPlainTextToLexicalJSON(text: string): string {
        try {
            // 创建一个简单的Lexical编辑器状态JSON
            const simpleEditorState = {
                root: {
                    children: [
                        {
                            children: [
                                {
                                    detail: 0,
                                    format: 0,
                                    mode: "normal",
                                    style: "",
                                    text: text.trim(),
                                    type: "text",
                                    version: 1
                                }
                            ],
                            direction: "ltr",
                            format: "",
                            indent: 0,
                            type: "paragraph",
                            version: 1
                        }
                    ],
                    direction: "ltr",
                    format: "",
                    indent: 0,
                    type: "root",
                    version: 1
                }
            };

            return JSON.stringify(simpleEditorState);
        } catch (error) {
            console.error('Error creating plain text JSON:', error);
            return '{"root":{"children":[{"children":[],"direction":"ltr","format":"","indent":0,"type":"paragraph","version":1}],"direction":"ltr","format":"","indent":0,"type":"root","version":1}}';
        }
    }

    /**
     * 将Lexical JSON格式转换为Markdown（用于导出）
     * 这个方法可以在客户端和服务器端使用
     */
    convertLexicalJSONToMarkdown(jsonString: string): string {
        try {
            const editorStateData = JSON.parse(jsonString);

            // 创建一个临时编辑器来进行转换
            const tempEditor = createEditor({
                nodes: [
                    HeadingNode,
                    ListNode,
                    ListItemNode,
                    QuoteNode,
                    CodeNode,
                    CodeHighlightNode,
                    AutoLinkNode,
                    LinkNode,
                    HorizontalRuleNode,
                ],
                onError: (error: Error) => console.error('Temp editor error:', error),
            });

            let markdownResult = '';

            // 恢复编辑器状态
            const editorState = tempEditor.parseEditorState(editorStateData);
            tempEditor.setEditorState(editorState);

            tempEditor.getEditorState().read(() => {
                markdownResult = $convertToMarkdownString(TRANSFORMERS);
            });

            return markdownResult;
        } catch (error) {
            console.error('JSON to Markdown conversion failed:', error);
            return '';
        }
    }

    /**
     * 验证markdown格式
     * 确保常见的markdown语法正确
     */
    validateMarkdown(markdown: string): { isValid: boolean; errors: string[] } {
        const errors: string[] = [];

        // 检查checkbox格式
        const invalidCheckboxes = markdown.match(/^(\s*)-\s*\[[^\sx ]\]/gm);
        if (invalidCheckboxes) {
            errors.push(`Invalid checkbox format found: ${invalidCheckboxes.join(', ')}`);
        }

        // 检查列表格式
        const lines = markdown.split('\n');
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i];

            // 检查有序列表格式
            if (/^\s*\d+\.\s/.test(line)) {
                const match = line.match(/^\s*(\d+)\.\s/);
                if (match) {
                    const num = parseInt(match[1]);
                    // 这里可以添加更多的列表验证逻辑
                }
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

// 创建单例实例
const lexicalMarkdownProcessor = new LexicalMarkdownProcessor();

export default lexicalMarkdownProcessor;
