{"version": 3, "names": ["isDeclareExportDeclaration", "isStatement", "AnyTypeAnnotation", "word", "ArrayTypeAnnotation", "node", "print", "elementType", "token", "BooleanTypeAnnotation", "BooleanLiteralTypeAnnotation", "value", "NullLiteralTypeAnnotation", "DeclareClass", "parent", "space", "_interfaceish", "DeclareFunction", "id", "typeAnnotation", "predicate", "semicolon", "InferredPredicate", "DeclaredPredicate", "DeclareInterface", "InterfaceDeclaration", "DeclareModule", "body", "DeclareModuleExports", "DeclareTypeAlias", "TypeAlias", "DeclareOpaqueType", "OpaqueType", "DeclareVariable", "DeclareExportDeclaration", "default", "FlowExportDeclaration", "call", "DeclareExportAllDeclaration", "ExportAllDeclaration", "EnumDeclaration", "enumExplicitType", "context", "name", "hasExplicitType", "enumBody", "members", "indent", "newline", "member", "hasUnknownMembers", "dedent", "EnumBooleanBody", "explicitType", "EnumNumberBody", "EnumStringBody", "EnumSymbolBody", "EnumDefaultedMember", "enumInitializedMember", "init", "EnumBooleanMember", "EnumNumberMember", "EnumStringMember", "declaration", "declar", "specifiers", "length", "printList", "source", "ExistsTypeAnnotation", "FunctionTypeAnnotation", "typeParameters", "this", "params", "rest", "type", "method", "returnType", "FunctionTypeParam", "optional", "InterfaceExtends", "extends", "mixins", "implements", "_variance", "variance", "kind", "andSeparator", "InterfaceTypeAnnotation", "IntersectionTypeAnnotation", "printJoin", "types", "separator", "MixedTypeAnnotation", "EmptyTypeAnnotation", "NullableTypeAnnotation", "NumberTypeAnnotation", "StringTypeAnnotation", "ThisTypeAnnotation", "TupleTypeAnnotation", "TypeofTypeAnnotation", "argument", "right", "TypeAnnotation", "TypeParameterInstantiation", "TypeParameter", "bound", "supertype", "impltype", "ObjectTypeAnnotation", "exact", "props", "properties", "callProperties", "indexers", "internalSlots", "addNewlines", "leading", "statement", "iterator", "inexact", "ObjectTypeInternalSlot", "static", "ObjectTypeCallProperty", "ObjectTypeIndexer", "key", "ObjectTypeProperty", "proto", "ObjectTypeSpreadProperty", "QualifiedTypeIdentifier", "qualification", "SymbolTypeAnnotation", "orSeparator", "UnionTypeAnnotation", "TypeCastExpression", "expression", "<PERSON><PERSON><PERSON>", "VoidTypeAnnotation", "IndexedAccessType", "objectType", "indexType", "OptionalIndexedAccessType"], "sources": ["../../src/generators/flow.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport { isDeclareExportDeclaration, isStatement } from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport { ExportAllDeclaration } from \"./modules\";\n\nexport function AnyTypeAnnotation(this: Printer) {\n  this.word(\"any\");\n}\n\nexport function ArrayTypeAnnotation(\n  this: Printer,\n  node: t.ArrayTypeAnnotation,\n) {\n  this.print(node.elementType, node, true);\n  this.token(\"[\");\n  this.token(\"]\");\n}\n\nexport function BooleanTypeAnnotation(this: Printer) {\n  this.word(\"boolean\");\n}\n\nexport function BooleanLiteralTypeAnnotation(\n  this: Printer,\n  node: t.BooleanLiteralTypeAnnotation,\n) {\n  this.word(node.value ? \"true\" : \"false\");\n}\n\nexport function NullLiteralTypeAnnotation(this: Printer) {\n  this.word(\"null\");\n}\n\nexport function DeclareClass(\n  this: Printer,\n  node: t.DeclareClass,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"class\");\n  this.space();\n  this._interfaceish(node);\n}\n\nexport function DeclareFunction(\n  this: Printer,\n  node: t.DeclareFunction,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"function\");\n  this.space();\n  this.print(node.id, node);\n  // @ts-ignore(Babel 7 vs Babel 8) TODO(Babel 8) Remove this comment, since we'll remove the Noop node\n  this.print(node.id.typeAnnotation.typeAnnotation, node);\n\n  if (node.predicate) {\n    this.space();\n    this.print(node.predicate, node);\n  }\n\n  this.semicolon();\n}\n\nexport function InferredPredicate(this: Printer) {\n  this.token(\"%\");\n  this.word(\"checks\");\n}\n\nexport function DeclaredPredicate(this: Printer, node: t.DeclaredPredicate) {\n  this.token(\"%\");\n  this.word(\"checks\");\n  this.token(\"(\");\n  this.print(node.value, node);\n  this.token(\")\");\n}\n\nexport function DeclareInterface(this: Printer, node: t.DeclareInterface) {\n  this.word(\"declare\");\n  this.space();\n  this.InterfaceDeclaration(node);\n}\n\nexport function DeclareModule(this: Printer, node: t.DeclareModule) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"module\");\n  this.space();\n  this.print(node.id, node);\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function DeclareModuleExports(\n  this: Printer,\n  node: t.DeclareModuleExports,\n) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"module\");\n  this.token(\".\");\n  this.word(\"exports\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function DeclareTypeAlias(this: Printer, node: t.DeclareTypeAlias) {\n  this.word(\"declare\");\n  this.space();\n  this.TypeAlias(node);\n}\n\nexport function DeclareOpaqueType(\n  this: Printer,\n  node: t.DeclareOpaqueType,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.OpaqueType(node);\n}\n\nexport function DeclareVariable(\n  this: Printer,\n  node: t.DeclareVariable,\n  parent: t.Node,\n) {\n  if (!isDeclareExportDeclaration(parent)) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"var\");\n  this.space();\n  this.print(node.id, node);\n  this.print(node.id.typeAnnotation, node);\n  this.semicolon();\n}\n\nexport function DeclareExportDeclaration(\n  this: Printer,\n  node: t.DeclareExportDeclaration,\n) {\n  this.word(\"declare\");\n  this.space();\n  this.word(\"export\");\n  this.space();\n  if (node.default) {\n    this.word(\"default\");\n    this.space();\n  }\n\n  FlowExportDeclaration.call(this, node);\n}\n\nexport function DeclareExportAllDeclaration(\n  this: Printer,\n  node: t.DeclareExportAllDeclaration,\n) {\n  this.word(\"declare\");\n  this.space();\n  ExportAllDeclaration.call(this, node);\n}\n\nexport function EnumDeclaration(this: Printer, node: t.EnumDeclaration) {\n  const { id, body } = node;\n  this.word(\"enum\");\n  this.space();\n  this.print(id, node);\n  this.print(body, node);\n}\n\nfunction enumExplicitType(\n  context: Printer,\n  name: string,\n  hasExplicitType: boolean,\n) {\n  if (hasExplicitType) {\n    context.space();\n    context.word(\"of\");\n    context.space();\n    context.word(name);\n  }\n  context.space();\n}\n\nfunction enumBody(context: Printer, node: t.EnumBody) {\n  const { members } = node;\n  context.token(\"{\");\n  context.indent();\n  context.newline();\n  for (const member of members) {\n    context.print(member, node);\n    context.newline();\n  }\n  if (node.hasUnknownMembers) {\n    context.token(\"...\");\n    context.newline();\n  }\n  context.dedent();\n  context.token(\"}\");\n}\n\nexport function EnumBooleanBody(this: Printer, node: t.EnumBooleanBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"boolean\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumNumberBody(this: Printer, node: t.EnumNumberBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"number\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumStringBody(this: Printer, node: t.EnumStringBody) {\n  const { explicitType } = node;\n  enumExplicitType(this, \"string\", explicitType);\n  enumBody(this, node);\n}\n\nexport function EnumSymbolBody(this: Printer, node: t.EnumSymbolBody) {\n  enumExplicitType(this, \"symbol\", true);\n  enumBody(this, node);\n}\n\nexport function EnumDefaultedMember(\n  this: Printer,\n  node: t.EnumDefaultedMember,\n) {\n  const { id } = node;\n  this.print(id, node);\n  this.token(\",\");\n}\n\nfunction enumInitializedMember(\n  context: Printer,\n  node: t.EnumBooleanMember | t.EnumNumberMember | t.EnumStringMember,\n) {\n  const { id, init } = node;\n  context.print(id, node);\n  context.space();\n  context.token(\"=\");\n  context.space();\n  context.print(init, node);\n  context.token(\",\");\n}\n\nexport function EnumBooleanMember(this: Printer, node: t.EnumBooleanMember) {\n  enumInitializedMember(this, node);\n}\n\nexport function EnumNumberMember(this: Printer, node: t.EnumNumberMember) {\n  enumInitializedMember(this, node);\n}\n\nexport function EnumStringMember(this: Printer, node: t.EnumStringMember) {\n  enumInitializedMember(this, node);\n}\n\nfunction FlowExportDeclaration(\n  this: Printer,\n  node: t.DeclareExportDeclaration,\n) {\n  if (node.declaration) {\n    const declar = node.declaration;\n    this.print(declar, node);\n    if (!isStatement(declar)) this.semicolon();\n  } else {\n    this.token(\"{\");\n    if (node.specifiers.length) {\n      this.space();\n      this.printList(node.specifiers, node);\n      this.space();\n    }\n    this.token(\"}\");\n\n    if (node.source) {\n      this.space();\n      this.word(\"from\");\n      this.space();\n      this.print(node.source, node);\n    }\n\n    this.semicolon();\n  }\n}\n\nexport function ExistsTypeAnnotation(this: Printer) {\n  this.token(\"*\");\n}\n\nexport function FunctionTypeAnnotation(\n  this: Printer,\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node | void,\n) {\n  this.print(node.typeParameters, node);\n  this.token(\"(\");\n\n  if (node.this) {\n    this.word(\"this\");\n    this.token(\":\");\n    this.space();\n    this.print(node.this.typeAnnotation, node);\n    if (node.params.length || node.rest) {\n      this.token(\",\");\n      this.space();\n    }\n  }\n\n  this.printList(node.params, node);\n\n  if (node.rest) {\n    if (node.params.length) {\n      this.token(\",\");\n      this.space();\n    }\n    this.token(\"...\");\n    this.print(node.rest, node);\n  }\n\n  this.token(\")\");\n\n  // this node type is overloaded, not sure why but it makes it EXTREMELY annoying\n  if (\n    parent &&\n    (parent.type === \"ObjectTypeCallProperty\" ||\n      parent.type === \"ObjectTypeInternalSlot\" ||\n      parent.type === \"DeclareFunction\" ||\n      (parent.type === \"ObjectTypeProperty\" && parent.method))\n  ) {\n    this.token(\":\");\n  } else {\n    this.space();\n    this.token(\"=>\");\n  }\n\n  this.space();\n  this.print(node.returnType, node);\n}\n\nexport function FunctionTypeParam(this: Printer, node: t.FunctionTypeParam) {\n  this.print(node.name, node);\n  if (node.optional) this.token(\"?\");\n  if (node.name) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.typeAnnotation, node);\n}\n\nexport function InterfaceExtends(this: Printer, node: t.InterfaceExtends) {\n  this.print(node.id, node);\n  this.print(node.typeParameters, node, true);\n}\n\nexport {\n  InterfaceExtends as ClassImplements,\n  InterfaceExtends as GenericTypeAnnotation,\n};\n\nexport function _interfaceish(\n  this: Printer,\n  node: t.InterfaceDeclaration | t.DeclareInterface | t.DeclareClass,\n) {\n  this.print(node.id, node);\n  this.print(node.typeParameters, node);\n  if (node.extends?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(node.extends, node);\n  }\n  if (node.mixins && node.mixins.length) {\n    this.space();\n    this.word(\"mixins\");\n    this.space();\n    this.printList(node.mixins, node);\n  }\n  if (node.implements && node.implements.length) {\n    this.space();\n    this.word(\"implements\");\n    this.space();\n    this.printList(node.implements, node);\n  }\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function _variance(\n  this: Printer,\n  node:\n    | t.TypeParameter\n    | t.ObjectTypeIndexer\n    | t.ObjectTypeProperty\n    | t.ClassProperty\n    | t.ClassPrivateProperty\n    | t.ClassAccessorProperty,\n) {\n  if (node.variance) {\n    if (node.variance.kind === \"plus\") {\n      this.token(\"+\");\n    } else if (node.variance.kind === \"minus\") {\n      this.token(\"-\");\n    }\n  }\n}\n\nexport function InterfaceDeclaration(\n  this: Printer,\n  node: t.InterfaceDeclaration | t.DeclareInterface,\n) {\n  this.word(\"interface\");\n  this.space();\n  this._interfaceish(node);\n}\n\nfunction andSeparator(this: Printer) {\n  this.space();\n  this.token(\"&\");\n  this.space();\n}\n\nexport function InterfaceTypeAnnotation(\n  this: Printer,\n  node: t.InterfaceTypeAnnotation,\n) {\n  this.word(\"interface\");\n  if (node.extends && node.extends.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(node.extends, node);\n  }\n  this.space();\n  this.print(node.body, node);\n}\n\nexport function IntersectionTypeAnnotation(\n  this: Printer,\n  node: t.IntersectionTypeAnnotation,\n) {\n  this.printJoin(node.types, node, { separator: andSeparator });\n}\n\nexport function MixedTypeAnnotation(this: Printer) {\n  this.word(\"mixed\");\n}\n\nexport function EmptyTypeAnnotation(this: Printer) {\n  this.word(\"empty\");\n}\n\nexport function NullableTypeAnnotation(\n  this: Printer,\n  node: t.NullableTypeAnnotation,\n) {\n  this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport {\n  NumericLiteral as NumberLiteralTypeAnnotation,\n  StringLiteral as StringLiteralTypeAnnotation,\n} from \"./types\";\n\nexport function NumberTypeAnnotation(this: Printer) {\n  this.word(\"number\");\n}\n\nexport function StringTypeAnnotation(this: Printer) {\n  this.word(\"string\");\n}\n\nexport function ThisTypeAnnotation(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TupleTypeAnnotation(\n  this: Printer,\n  node: t.TupleTypeAnnotation,\n) {\n  this.token(\"[\");\n  this.printList(node.types, node);\n  this.token(\"]\");\n}\n\nexport function TypeofTypeAnnotation(\n  this: Printer,\n  node: t.TypeofTypeAnnotation,\n) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.argument, node);\n}\n\nexport function TypeAlias(\n  this: Printer,\n  node: t.TypeAlias | t.DeclareTypeAlias,\n) {\n  this.word(\"type\");\n  this.space();\n  this.print(node.id, node);\n  this.print(node.typeParameters, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.right, node);\n  this.semicolon();\n}\n\nexport function TypeAnnotation(this: Printer, node: t.TypeAnnotation) {\n  this.token(\":\");\n  this.space();\n  // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n  if (node.optional) this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TypeParameterInstantiation(\n  this: Printer,\n  node: t.TypeParameterInstantiation,\n): void {\n  this.token(\"<\");\n  this.printList(node.params, node, {});\n  this.token(\">\");\n}\n\nexport { TypeParameterInstantiation as TypeParameterDeclaration };\n\nexport function TypeParameter(this: Printer, node: t.TypeParameter) {\n  this._variance(node);\n\n  this.word(node.name);\n\n  if (node.bound) {\n    this.print(node.bound, node);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default, node);\n  }\n}\n\nexport function OpaqueType(\n  this: Printer,\n  node: t.OpaqueType | t.DeclareOpaqueType,\n) {\n  this.word(\"opaque\");\n  this.space();\n  this.word(\"type\");\n  this.space();\n  this.print(node.id, node);\n  this.print(node.typeParameters, node);\n  if (node.supertype) {\n    this.token(\":\");\n    this.space();\n    this.print(node.supertype, node);\n  }\n\n  if (node.impltype) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.impltype, node);\n  }\n  this.semicolon();\n}\n\nexport function ObjectTypeAnnotation(\n  this: Printer,\n  node: t.ObjectTypeAnnotation,\n) {\n  if (node.exact) {\n    this.token(\"{|\");\n  } else {\n    this.token(\"{\");\n  }\n\n  // TODO: remove the array fallbacks and instead enforce the types to require an array\n  const props = [\n    ...node.properties,\n    ...(node.callProperties || []),\n    ...(node.indexers || []),\n    ...(node.internalSlots || []),\n  ];\n\n  if (props.length) {\n    this.newline();\n\n    this.space();\n\n    this.printJoin(props, node, {\n      addNewlines(leading) {\n        if (leading && !props[0]) return 1;\n      },\n      indent: true,\n      statement: true,\n      iterator: () => {\n        if (props.length !== 1 || node.inexact) {\n          this.token(\",\");\n          this.space();\n        }\n      },\n    });\n\n    this.space();\n  }\n\n  if (node.inexact) {\n    this.indent();\n    this.token(\"...\");\n    if (props.length) {\n      this.newline();\n    }\n    this.dedent();\n  }\n\n  if (node.exact) {\n    this.token(\"|}\");\n  } else {\n    this.token(\"}\");\n  }\n}\n\nexport function ObjectTypeInternalSlot(\n  this: Printer,\n  node: t.ObjectTypeInternalSlot,\n) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.token(\"[\");\n  this.token(\"[\");\n  this.print(node.id, node);\n  this.token(\"]\");\n  this.token(\"]\");\n  if (node.optional) this.token(\"?\");\n  if (!node.method) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeCallProperty(\n  this: Printer,\n  node: t.ObjectTypeCallProperty,\n) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeIndexer(this: Printer, node: t.ObjectTypeIndexer) {\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  this._variance(node);\n  this.token(\"[\");\n  if (node.id) {\n    this.print(node.id, node);\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.key, node);\n  this.token(\"]\");\n  this.token(\":\");\n  this.space();\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeProperty(this: Printer, node: t.ObjectTypeProperty) {\n  if (node.proto) {\n    this.word(\"proto\");\n    this.space();\n  }\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (node.kind === \"get\" || node.kind === \"set\") {\n    this.word(node.kind);\n    this.space();\n  }\n  this._variance(node);\n  this.print(node.key, node);\n  if (node.optional) this.token(\"?\");\n  if (!node.method) {\n    this.token(\":\");\n    this.space();\n  }\n  this.print(node.value, node);\n}\n\nexport function ObjectTypeSpreadProperty(\n  this: Printer,\n  node: t.ObjectTypeSpreadProperty,\n) {\n  this.token(\"...\");\n  this.print(node.argument, node);\n}\n\nexport function QualifiedTypeIdentifier(\n  this: Printer,\n  node: t.QualifiedTypeIdentifier,\n) {\n  this.print(node.qualification, node);\n  this.token(\".\");\n  this.print(node.id, node);\n}\n\nexport function SymbolTypeAnnotation(this: Printer) {\n  this.word(\"symbol\");\n}\n\nfunction orSeparator(this: Printer) {\n  this.space();\n  this.token(\"|\");\n  this.space();\n}\n\nexport function UnionTypeAnnotation(\n  this: Printer,\n  node: t.UnionTypeAnnotation,\n) {\n  this.printJoin(node.types, node, { separator: orSeparator });\n}\n\nexport function TypeCastExpression(this: Printer, node: t.TypeCastExpression) {\n  this.token(\"(\");\n  this.print(node.expression, node);\n  this.print(node.typeAnnotation, node);\n  this.token(\")\");\n}\n\nexport function Variance(this: Printer, node: t.Variance) {\n  if (node.kind === \"plus\") {\n    this.token(\"+\");\n  } else {\n    this.token(\"-\");\n  }\n}\n\nexport function VoidTypeAnnotation(this: Printer) {\n  this.word(\"void\");\n}\n\nexport function IndexedAccessType(this: Printer, node: t.IndexedAccessType) {\n  this.print(node.objectType, node, true);\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n\nexport function OptionalIndexedAccessType(\n  this: Printer,\n  node: t.OptionalIndexedAccessType,\n) {\n  this.print(node.objectType, node);\n  if (node.optional) {\n    this.token(\"?.\");\n  }\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;;AAEA;;AAidA;;;EAndSA,0B;EAA4BC;;;AAI9B,SAASC,iBAAT,GAA0C;EAC/C,KAAKC,IAAL,CAAU,KAAV;AACD;;AAEM,SAASC,mBAAT,CAELC,IAFK,EAGL;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACE,WAAhB,EAA6BF,IAA7B,EAAmC,IAAnC;EACA,KAAKG,SAAL;EACA,KAAKA,SAAL;AACD;;AAEM,SAASC,qBAAT,GAA8C;EACnD,KAAKN,IAAL,CAAU,SAAV;AACD;;AAEM,SAASO,4BAAT,CAELL,IAFK,EAGL;EACA,KAAKF,IAAL,CAAUE,IAAI,CAACM,KAAL,GAAa,MAAb,GAAsB,OAAhC;AACD;;AAEM,SAASC,yBAAT,GAAkD;EACvD,KAAKT,IAAL,CAAU,MAAV;AACD;;AAEM,SAASU,YAAT,CAELR,IAFK,EAGLS,MAHK,EAIL;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAD,CAA/B,EAAyC;IACvC,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKZ,IAAL,CAAU,OAAV;EACA,KAAKY,KAAL;;EACA,KAAKC,aAAL,CAAmBX,IAAnB;AACD;;AAEM,SAASY,eAAT,CAELZ,IAFK,EAGLS,MAHK,EAIL;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAD,CAA/B,EAAyC;IACvC,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKZ,IAAL,CAAU,UAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EAEA,KAAKC,KAAL,CAAWD,IAAI,CAACa,EAAL,CAAQC,cAAR,CAAuBA,cAAlC,EAAkDd,IAAlD;;EAEA,IAAIA,IAAI,CAACe,SAAT,EAAoB;IAClB,KAAKL,KAAL;IACA,KAAKT,KAAL,CAAWD,IAAI,CAACe,SAAhB,EAA2Bf,IAA3B;EACD;;EAED,KAAKgB,SAAL;AACD;;AAEM,SAASC,iBAAT,GAA0C;EAC/C,KAAKd,SAAL;EACA,KAAKL,IAAL,CAAU,QAAV;AACD;;AAEM,SAASoB,iBAAT,CAA0ClB,IAA1C,EAAqE;EAC1E,KAAKG,SAAL;EACA,KAAKL,IAAL,CAAU,QAAV;EACA,KAAKK,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACM,KAAhB,EAAuBN,IAAvB;EACA,KAAKG,SAAL;AACD;;AAEM,SAASgB,gBAAT,CAAyCnB,IAAzC,EAAmE;EACxE,KAAKF,IAAL,CAAU,SAAV;EACA,KAAKY,KAAL;EACA,KAAKU,oBAAL,CAA0BpB,IAA1B;AACD;;AAEM,SAASqB,aAAT,CAAsCrB,IAAtC,EAA6D;EAClE,KAAKF,IAAL,CAAU,SAAV;EACA,KAAKY,KAAL;EACA,KAAKZ,IAAL,CAAU,QAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKU,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACsB,IAAhB,EAAsBtB,IAAtB;AACD;;AAEM,SAASuB,oBAAT,CAELvB,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,SAAV;EACA,KAAKY,KAAL;EACA,KAAKZ,IAAL,CAAU,QAAV;EACA,KAAKK,SAAL;EACA,KAAKL,IAAL,CAAU,SAAV;EACA,KAAKG,KAAL,CAAWD,IAAI,CAACc,cAAhB,EAAgCd,IAAhC;AACD;;AAEM,SAASwB,gBAAT,CAAyCxB,IAAzC,EAAmE;EACxE,KAAKF,IAAL,CAAU,SAAV;EACA,KAAKY,KAAL;EACA,KAAKe,SAAL,CAAezB,IAAf;AACD;;AAEM,SAAS0B,iBAAT,CAEL1B,IAFK,EAGLS,MAHK,EAIL;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAD,CAA/B,EAAyC;IACvC,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKiB,UAAL,CAAgB3B,IAAhB;AACD;;AAEM,SAAS4B,eAAT,CAEL5B,IAFK,EAGLS,MAHK,EAIL;EACA,IAAI,CAACd,0BAA0B,CAACc,MAAD,CAA/B,EAAyC;IACvC,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKZ,IAAL,CAAU,KAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACa,EAAL,CAAQC,cAAnB,EAAmCd,IAAnC;EACA,KAAKgB,SAAL;AACD;;AAEM,SAASa,wBAAT,CAEL7B,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,SAAV;EACA,KAAKY,KAAL;EACA,KAAKZ,IAAL,CAAU,QAAV;EACA,KAAKY,KAAL;;EACA,IAAIV,IAAI,CAAC8B,OAAT,EAAkB;IAChB,KAAKhC,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;EACD;;EAEDqB,qBAAqB,CAACC,IAAtB,CAA2B,IAA3B,EAAiChC,IAAjC;AACD;;AAEM,SAASiC,2BAAT,CAELjC,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,SAAV;EACA,KAAKY,KAAL;;EACAwB,6BAAA,CAAqBF,IAArB,CAA0B,IAA1B,EAAgChC,IAAhC;AACD;;AAEM,SAASmC,eAAT,CAAwCnC,IAAxC,EAAiE;EACtE,MAAM;IAAEa,EAAF;IAAMS;EAAN,IAAetB,IAArB;EACA,KAAKF,IAAL,CAAU,MAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWY,EAAX,EAAeb,IAAf;EACA,KAAKC,KAAL,CAAWqB,IAAX,EAAiBtB,IAAjB;AACD;;AAED,SAASoC,gBAAT,CACEC,OADF,EAEEC,IAFF,EAGEC,eAHF,EAIE;EACA,IAAIA,eAAJ,EAAqB;IACnBF,OAAO,CAAC3B,KAAR;IACA2B,OAAO,CAACvC,IAAR,CAAa,IAAb;IACAuC,OAAO,CAAC3B,KAAR;IACA2B,OAAO,CAACvC,IAAR,CAAawC,IAAb;EACD;;EACDD,OAAO,CAAC3B,KAAR;AACD;;AAED,SAAS8B,QAAT,CAAkBH,OAAlB,EAAoCrC,IAApC,EAAsD;EACpD,MAAM;IAAEyC;EAAF,IAAczC,IAApB;EACAqC,OAAO,CAAClC,KAAR,CAAc,GAAd;EACAkC,OAAO,CAACK,MAAR;EACAL,OAAO,CAACM,OAAR;;EACA,KAAK,MAAMC,MAAX,IAAqBH,OAArB,EAA8B;IAC5BJ,OAAO,CAACpC,KAAR,CAAc2C,MAAd,EAAsB5C,IAAtB;IACAqC,OAAO,CAACM,OAAR;EACD;;EACD,IAAI3C,IAAI,CAAC6C,iBAAT,EAA4B;IAC1BR,OAAO,CAAClC,KAAR,CAAc,KAAd;IACAkC,OAAO,CAACM,OAAR;EACD;;EACDN,OAAO,CAACS,MAAR;EACAT,OAAO,CAAClC,KAAR,CAAc,GAAd;AACD;;AAEM,SAAS4C,eAAT,CAAwC/C,IAAxC,EAAiE;EACtE,MAAM;IAAEgD;EAAF,IAAmBhD,IAAzB;EACAoC,gBAAgB,CAAC,IAAD,EAAO,SAAP,EAAkBY,YAAlB,CAAhB;EACAR,QAAQ,CAAC,IAAD,EAAOxC,IAAP,CAAR;AACD;;AAEM,SAASiD,cAAT,CAAuCjD,IAAvC,EAA+D;EACpE,MAAM;IAAEgD;EAAF,IAAmBhD,IAAzB;EACAoC,gBAAgB,CAAC,IAAD,EAAO,QAAP,EAAiBY,YAAjB,CAAhB;EACAR,QAAQ,CAAC,IAAD,EAAOxC,IAAP,CAAR;AACD;;AAEM,SAASkD,cAAT,CAAuClD,IAAvC,EAA+D;EACpE,MAAM;IAAEgD;EAAF,IAAmBhD,IAAzB;EACAoC,gBAAgB,CAAC,IAAD,EAAO,QAAP,EAAiBY,YAAjB,CAAhB;EACAR,QAAQ,CAAC,IAAD,EAAOxC,IAAP,CAAR;AACD;;AAEM,SAASmD,cAAT,CAAuCnD,IAAvC,EAA+D;EACpEoC,gBAAgB,CAAC,IAAD,EAAO,QAAP,EAAiB,IAAjB,CAAhB;EACAI,QAAQ,CAAC,IAAD,EAAOxC,IAAP,CAAR;AACD;;AAEM,SAASoD,mBAAT,CAELpD,IAFK,EAGL;EACA,MAAM;IAAEa;EAAF,IAASb,IAAf;EACA,KAAKC,KAAL,CAAWY,EAAX,EAAeb,IAAf;EACA,KAAKG,SAAL;AACD;;AAED,SAASkD,qBAAT,CACEhB,OADF,EAEErC,IAFF,EAGE;EACA,MAAM;IAAEa,EAAF;IAAMyC;EAAN,IAAetD,IAArB;EACAqC,OAAO,CAACpC,KAAR,CAAcY,EAAd,EAAkBb,IAAlB;EACAqC,OAAO,CAAC3B,KAAR;EACA2B,OAAO,CAAClC,KAAR,CAAc,GAAd;EACAkC,OAAO,CAAC3B,KAAR;EACA2B,OAAO,CAACpC,KAAR,CAAcqD,IAAd,EAAoBtD,IAApB;EACAqC,OAAO,CAAClC,KAAR,CAAc,GAAd;AACD;;AAEM,SAASoD,iBAAT,CAA0CvD,IAA1C,EAAqE;EAC1EqD,qBAAqB,CAAC,IAAD,EAAOrD,IAAP,CAArB;AACD;;AAEM,SAASwD,gBAAT,CAAyCxD,IAAzC,EAAmE;EACxEqD,qBAAqB,CAAC,IAAD,EAAOrD,IAAP,CAArB;AACD;;AAEM,SAASyD,gBAAT,CAAyCzD,IAAzC,EAAmE;EACxEqD,qBAAqB,CAAC,IAAD,EAAOrD,IAAP,CAArB;AACD;;AAED,SAAS+B,qBAAT,CAEE/B,IAFF,EAGE;EACA,IAAIA,IAAI,CAAC0D,WAAT,EAAsB;IACpB,MAAMC,MAAM,GAAG3D,IAAI,CAAC0D,WAApB;IACA,KAAKzD,KAAL,CAAW0D,MAAX,EAAmB3D,IAAnB;IACA,IAAI,CAACJ,WAAW,CAAC+D,MAAD,CAAhB,EAA0B,KAAK3C,SAAL;EAC3B,CAJD,MAIO;IACL,KAAKb,SAAL;;IACA,IAAIH,IAAI,CAAC4D,UAAL,CAAgBC,MAApB,EAA4B;MAC1B,KAAKnD,KAAL;MACA,KAAKoD,SAAL,CAAe9D,IAAI,CAAC4D,UAApB,EAAgC5D,IAAhC;MACA,KAAKU,KAAL;IACD;;IACD,KAAKP,SAAL;;IAEA,IAAIH,IAAI,CAAC+D,MAAT,EAAiB;MACf,KAAKrD,KAAL;MACA,KAAKZ,IAAL,CAAU,MAAV;MACA,KAAKY,KAAL;MACA,KAAKT,KAAL,CAAWD,IAAI,CAAC+D,MAAhB,EAAwB/D,IAAxB;IACD;;IAED,KAAKgB,SAAL;EACD;AACF;;AAEM,SAASgD,oBAAT,GAA6C;EAClD,KAAK7D,SAAL;AACD;;AAEM,SAAS8D,sBAAT,CAELjE,IAFK,EAGLS,MAHK,EAIL;EACA,KAAKR,KAAL,CAAWD,IAAI,CAACkE,cAAhB,EAAgClE,IAAhC;EACA,KAAKG,SAAL;;EAEA,IAAIH,IAAI,CAACmE,IAAT,EAAe;IACb,KAAKrE,IAAL,CAAU,MAAV;IACA,KAAKK,SAAL;IACA,KAAKO,KAAL;IACA,KAAKT,KAAL,CAAWD,IAAI,CAACmE,IAAL,CAAUrD,cAArB,EAAqCd,IAArC;;IACA,IAAIA,IAAI,CAACoE,MAAL,CAAYP,MAAZ,IAAsB7D,IAAI,CAACqE,IAA/B,EAAqC;MACnC,KAAKlE,SAAL;MACA,KAAKO,KAAL;IACD;EACF;;EAED,KAAKoD,SAAL,CAAe9D,IAAI,CAACoE,MAApB,EAA4BpE,IAA5B;;EAEA,IAAIA,IAAI,CAACqE,IAAT,EAAe;IACb,IAAIrE,IAAI,CAACoE,MAAL,CAAYP,MAAhB,EAAwB;MACtB,KAAK1D,SAAL;MACA,KAAKO,KAAL;IACD;;IACD,KAAKP,KAAL,CAAW,KAAX;IACA,KAAKF,KAAL,CAAWD,IAAI,CAACqE,IAAhB,EAAsBrE,IAAtB;EACD;;EAED,KAAKG,SAAL;;EAGA,IACEM,MAAM,KACLA,MAAM,CAAC6D,IAAP,KAAgB,wBAAhB,IACC7D,MAAM,CAAC6D,IAAP,KAAgB,wBADjB,IAEC7D,MAAM,CAAC6D,IAAP,KAAgB,iBAFjB,IAGE7D,MAAM,CAAC6D,IAAP,KAAgB,oBAAhB,IAAwC7D,MAAM,CAAC8D,MAJ5C,CADR,EAME;IACA,KAAKpE,SAAL;EACD,CARD,MAQO;IACL,KAAKO,KAAL;IACA,KAAKP,KAAL,CAAW,IAAX;EACD;;EAED,KAAKO,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACwE,UAAhB,EAA4BxE,IAA5B;AACD;;AAEM,SAASyE,iBAAT,CAA0CzE,IAA1C,EAAqE;EAC1E,KAAKC,KAAL,CAAWD,IAAI,CAACsC,IAAhB,EAAsBtC,IAAtB;EACA,IAAIA,IAAI,CAAC0E,QAAT,EAAmB,KAAKvE,SAAL;;EACnB,IAAIH,IAAI,CAACsC,IAAT,EAAe;IACb,KAAKnC,SAAL;IACA,KAAKO,KAAL;EACD;;EACD,KAAKT,KAAL,CAAWD,IAAI,CAACc,cAAhB,EAAgCd,IAAhC;AACD;;AAEM,SAAS2E,gBAAT,CAAyC3E,IAAzC,EAAmE;EACxE,KAAKC,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACkE,cAAhB,EAAgClE,IAAhC,EAAsC,IAAtC;AACD;;AAOM,SAASW,aAAT,CAELX,IAFK,EAGL;EAAA;;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACkE,cAAhB,EAAgClE,IAAhC;;EACA,qBAAIA,IAAI,CAAC4E,OAAT,aAAI,cAAcf,MAAlB,EAA0B;IACxB,KAAKnD,KAAL;IACA,KAAKZ,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;IACA,KAAKoD,SAAL,CAAe9D,IAAI,CAAC4E,OAApB,EAA6B5E,IAA7B;EACD;;EACD,IAAIA,IAAI,CAAC6E,MAAL,IAAe7E,IAAI,CAAC6E,MAAL,CAAYhB,MAA/B,EAAuC;IACrC,KAAKnD,KAAL;IACA,KAAKZ,IAAL,CAAU,QAAV;IACA,KAAKY,KAAL;IACA,KAAKoD,SAAL,CAAe9D,IAAI,CAAC6E,MAApB,EAA4B7E,IAA5B;EACD;;EACD,IAAIA,IAAI,CAAC8E,UAAL,IAAmB9E,IAAI,CAAC8E,UAAL,CAAgBjB,MAAvC,EAA+C;IAC7C,KAAKnD,KAAL;IACA,KAAKZ,IAAL,CAAU,YAAV;IACA,KAAKY,KAAL;IACA,KAAKoD,SAAL,CAAe9D,IAAI,CAAC8E,UAApB,EAAgC9E,IAAhC;EACD;;EACD,KAAKU,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACsB,IAAhB,EAAsBtB,IAAtB;AACD;;AAEM,SAAS+E,SAAT,CAEL/E,IAFK,EASL;EACA,IAAIA,IAAI,CAACgF,QAAT,EAAmB;IACjB,IAAIhF,IAAI,CAACgF,QAAL,CAAcC,IAAd,KAAuB,MAA3B,EAAmC;MACjC,KAAK9E,SAAL;IACD,CAFD,MAEO,IAAIH,IAAI,CAACgF,QAAL,CAAcC,IAAd,KAAuB,OAA3B,EAAoC;MACzC,KAAK9E,SAAL;IACD;EACF;AACF;;AAEM,SAASiB,oBAAT,CAELpB,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,WAAV;EACA,KAAKY,KAAL;;EACA,KAAKC,aAAL,CAAmBX,IAAnB;AACD;;AAED,SAASkF,YAAT,GAAqC;EACnC,KAAKxE,KAAL;EACA,KAAKP,SAAL;EACA,KAAKO,KAAL;AACD;;AAEM,SAASyE,uBAAT,CAELnF,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,WAAV;;EACA,IAAIE,IAAI,CAAC4E,OAAL,IAAgB5E,IAAI,CAAC4E,OAAL,CAAaf,MAAjC,EAAyC;IACvC,KAAKnD,KAAL;IACA,KAAKZ,IAAL,CAAU,SAAV;IACA,KAAKY,KAAL;IACA,KAAKoD,SAAL,CAAe9D,IAAI,CAAC4E,OAApB,EAA6B5E,IAA7B;EACD;;EACD,KAAKU,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACsB,IAAhB,EAAsBtB,IAAtB;AACD;;AAEM,SAASoF,0BAAT,CAELpF,IAFK,EAGL;EACA,KAAKqF,SAAL,CAAerF,IAAI,CAACsF,KAApB,EAA2BtF,IAA3B,EAAiC;IAAEuF,SAAS,EAAEL;EAAb,CAAjC;AACD;;AAEM,SAASM,mBAAT,GAA4C;EACjD,KAAK1F,IAAL,CAAU,OAAV;AACD;;AAEM,SAAS2F,mBAAT,GAA4C;EACjD,KAAK3F,IAAL,CAAU,OAAV;AACD;;AAEM,SAAS4F,sBAAT,CAEL1F,IAFK,EAGL;EACA,KAAKG,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACc,cAAhB,EAAgCd,IAAhC;AACD;;AAOM,SAAS2F,oBAAT,GAA6C;EAClD,KAAK7F,IAAL,CAAU,QAAV;AACD;;AAEM,SAAS8F,oBAAT,GAA6C;EAClD,KAAK9F,IAAL,CAAU,QAAV;AACD;;AAEM,SAAS+F,kBAAT,GAA2C;EAChD,KAAK/F,IAAL,CAAU,MAAV;AACD;;AAEM,SAASgG,mBAAT,CAEL9F,IAFK,EAGL;EACA,KAAKG,SAAL;EACA,KAAK2D,SAAL,CAAe9D,IAAI,CAACsF,KAApB,EAA2BtF,IAA3B;EACA,KAAKG,SAAL;AACD;;AAEM,SAAS4F,oBAAT,CAEL/F,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,QAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACgG,QAAhB,EAA0BhG,IAA1B;AACD;;AAEM,SAASyB,SAAT,CAELzB,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,MAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACkE,cAAhB,EAAgClE,IAAhC;EACA,KAAKU,KAAL;EACA,KAAKP,SAAL;EACA,KAAKO,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACiG,KAAhB,EAAuBjG,IAAvB;EACA,KAAKgB,SAAL;AACD;;AAEM,SAASkF,cAAT,CAAuClG,IAAvC,EAA+D;EACpE,KAAKG,SAAL;EACA,KAAKO,KAAL;EAEA,IAAIV,IAAI,CAAC0E,QAAT,EAAmB,KAAKvE,SAAL;EACnB,KAAKF,KAAL,CAAWD,IAAI,CAACc,cAAhB,EAAgCd,IAAhC;AACD;;AAEM,SAASmG,0BAAT,CAELnG,IAFK,EAGC;EACN,KAAKG,SAAL;EACA,KAAK2D,SAAL,CAAe9D,IAAI,CAACoE,MAApB,EAA4BpE,IAA5B,EAAkC,EAAlC;EACA,KAAKG,SAAL;AACD;;AAIM,SAASiG,aAAT,CAAsCpG,IAAtC,EAA6D;EAClE,KAAK+E,SAAL,CAAe/E,IAAf;;EAEA,KAAKF,IAAL,CAAUE,IAAI,CAACsC,IAAf;;EAEA,IAAItC,IAAI,CAACqG,KAAT,EAAgB;IACd,KAAKpG,KAAL,CAAWD,IAAI,CAACqG,KAAhB,EAAuBrG,IAAvB;EACD;;EAED,IAAIA,IAAI,CAAC8B,OAAT,EAAkB;IAChB,KAAKpB,KAAL;IACA,KAAKP,SAAL;IACA,KAAKO,KAAL;IACA,KAAKT,KAAL,CAAWD,IAAI,CAAC8B,OAAhB,EAAyB9B,IAAzB;EACD;AACF;;AAEM,SAAS2B,UAAT,CAEL3B,IAFK,EAGL;EACA,KAAKF,IAAL,CAAU,QAAV;EACA,KAAKY,KAAL;EACA,KAAKZ,IAAL,CAAU,MAAV;EACA,KAAKY,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACkE,cAAhB,EAAgClE,IAAhC;;EACA,IAAIA,IAAI,CAACsG,SAAT,EAAoB;IAClB,KAAKnG,SAAL;IACA,KAAKO,KAAL;IACA,KAAKT,KAAL,CAAWD,IAAI,CAACsG,SAAhB,EAA2BtG,IAA3B;EACD;;EAED,IAAIA,IAAI,CAACuG,QAAT,EAAmB;IACjB,KAAK7F,KAAL;IACA,KAAKP,SAAL;IACA,KAAKO,KAAL;IACA,KAAKT,KAAL,CAAWD,IAAI,CAACuG,QAAhB,EAA0BvG,IAA1B;EACD;;EACD,KAAKgB,SAAL;AACD;;AAEM,SAASwF,oBAAT,CAELxG,IAFK,EAGL;EACA,IAAIA,IAAI,CAACyG,KAAT,EAAgB;IACd,KAAKtG,KAAL,CAAW,IAAX;EACD,CAFD,MAEO;IACL,KAAKA,SAAL;EACD;;EAGD,MAAMuG,KAAK,GAAG,CACZ,GAAG1G,IAAI,CAAC2G,UADI,EAEZ,IAAI3G,IAAI,CAAC4G,cAAL,IAAuB,EAA3B,CAFY,EAGZ,IAAI5G,IAAI,CAAC6G,QAAL,IAAiB,EAArB,CAHY,EAIZ,IAAI7G,IAAI,CAAC8G,aAAL,IAAsB,EAA1B,CAJY,CAAd;;EAOA,IAAIJ,KAAK,CAAC7C,MAAV,EAAkB;IAChB,KAAKlB,OAAL;IAEA,KAAKjC,KAAL;IAEA,KAAK2E,SAAL,CAAeqB,KAAf,EAAsB1G,IAAtB,EAA4B;MAC1B+G,WAAW,CAACC,OAAD,EAAU;QACnB,IAAIA,OAAO,IAAI,CAACN,KAAK,CAAC,CAAD,CAArB,EAA0B,OAAO,CAAP;MAC3B,CAHyB;;MAI1BhE,MAAM,EAAE,IAJkB;MAK1BuE,SAAS,EAAE,IALe;MAM1BC,QAAQ,EAAE,MAAM;QACd,IAAIR,KAAK,CAAC7C,MAAN,KAAiB,CAAjB,IAAsB7D,IAAI,CAACmH,OAA/B,EAAwC;UACtC,KAAKhH,SAAL;UACA,KAAKO,KAAL;QACD;MACF;IAXyB,CAA5B;IAcA,KAAKA,KAAL;EACD;;EAED,IAAIV,IAAI,CAACmH,OAAT,EAAkB;IAChB,KAAKzE,MAAL;IACA,KAAKvC,KAAL,CAAW,KAAX;;IACA,IAAIuG,KAAK,CAAC7C,MAAV,EAAkB;MAChB,KAAKlB,OAAL;IACD;;IACD,KAAKG,MAAL;EACD;;EAED,IAAI9C,IAAI,CAACyG,KAAT,EAAgB;IACd,KAAKtG,KAAL,CAAW,IAAX;EACD,CAFD,MAEO;IACL,KAAKA,SAAL;EACD;AACF;;AAEM,SAASiH,sBAAT,CAELpH,IAFK,EAGL;EACA,IAAIA,IAAI,CAACqH,MAAT,EAAiB;IACf,KAAKvH,IAAL,CAAU,QAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKP,SAAL;EACA,KAAKA,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;EACA,KAAKG,SAAL;EACA,KAAKA,SAAL;EACA,IAAIH,IAAI,CAAC0E,QAAT,EAAmB,KAAKvE,SAAL;;EACnB,IAAI,CAACH,IAAI,CAACuE,MAAV,EAAkB;IAChB,KAAKpE,SAAL;IACA,KAAKO,KAAL;EACD;;EACD,KAAKT,KAAL,CAAWD,IAAI,CAACM,KAAhB,EAAuBN,IAAvB;AACD;;AAEM,SAASsH,sBAAT,CAELtH,IAFK,EAGL;EACA,IAAIA,IAAI,CAACqH,MAAT,EAAiB;IACf,KAAKvH,IAAL,CAAU,QAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKT,KAAL,CAAWD,IAAI,CAACM,KAAhB,EAAuBN,IAAvB;AACD;;AAEM,SAASuH,iBAAT,CAA0CvH,IAA1C,EAAqE;EAC1E,IAAIA,IAAI,CAACqH,MAAT,EAAiB;IACf,KAAKvH,IAAL,CAAU,QAAV;IACA,KAAKY,KAAL;EACD;;EACD,KAAKqE,SAAL,CAAe/E,IAAf;;EACA,KAAKG,SAAL;;EACA,IAAIH,IAAI,CAACa,EAAT,EAAa;IACX,KAAKZ,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;IACA,KAAKG,SAAL;IACA,KAAKO,KAAL;EACD;;EACD,KAAKT,KAAL,CAAWD,IAAI,CAACwH,GAAhB,EAAqBxH,IAArB;EACA,KAAKG,SAAL;EACA,KAAKA,SAAL;EACA,KAAKO,KAAL;EACA,KAAKT,KAAL,CAAWD,IAAI,CAACM,KAAhB,EAAuBN,IAAvB;AACD;;AAEM,SAASyH,kBAAT,CAA2CzH,IAA3C,EAAuE;EAC5E,IAAIA,IAAI,CAAC0H,KAAT,EAAgB;IACd,KAAK5H,IAAL,CAAU,OAAV;IACA,KAAKY,KAAL;EACD;;EACD,IAAIV,IAAI,CAACqH,MAAT,EAAiB;IACf,KAAKvH,IAAL,CAAU,QAAV;IACA,KAAKY,KAAL;EACD;;EACD,IAAIV,IAAI,CAACiF,IAAL,KAAc,KAAd,IAAuBjF,IAAI,CAACiF,IAAL,KAAc,KAAzC,EAAgD;IAC9C,KAAKnF,IAAL,CAAUE,IAAI,CAACiF,IAAf;IACA,KAAKvE,KAAL;EACD;;EACD,KAAKqE,SAAL,CAAe/E,IAAf;;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACwH,GAAhB,EAAqBxH,IAArB;EACA,IAAIA,IAAI,CAAC0E,QAAT,EAAmB,KAAKvE,SAAL;;EACnB,IAAI,CAACH,IAAI,CAACuE,MAAV,EAAkB;IAChB,KAAKpE,SAAL;IACA,KAAKO,KAAL;EACD;;EACD,KAAKT,KAAL,CAAWD,IAAI,CAACM,KAAhB,EAAuBN,IAAvB;AACD;;AAEM,SAAS2H,wBAAT,CAEL3H,IAFK,EAGL;EACA,KAAKG,KAAL,CAAW,KAAX;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACgG,QAAhB,EAA0BhG,IAA1B;AACD;;AAEM,SAAS4H,uBAAT,CAEL5H,IAFK,EAGL;EACA,KAAKC,KAAL,CAAWD,IAAI,CAAC6H,aAAhB,EAA+B7H,IAA/B;EACA,KAAKG,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACa,EAAhB,EAAoBb,IAApB;AACD;;AAEM,SAAS8H,oBAAT,GAA6C;EAClD,KAAKhI,IAAL,CAAU,QAAV;AACD;;AAED,SAASiI,WAAT,GAAoC;EAClC,KAAKrH,KAAL;EACA,KAAKP,SAAL;EACA,KAAKO,KAAL;AACD;;AAEM,SAASsH,mBAAT,CAELhI,IAFK,EAGL;EACA,KAAKqF,SAAL,CAAerF,IAAI,CAACsF,KAApB,EAA2BtF,IAA3B,EAAiC;IAAEuF,SAAS,EAAEwC;EAAb,CAAjC;AACD;;AAEM,SAASE,kBAAT,CAA2CjI,IAA3C,EAAuE;EAC5E,KAAKG,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACkI,UAAhB,EAA4BlI,IAA5B;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACc,cAAhB,EAAgCd,IAAhC;EACA,KAAKG,SAAL;AACD;;AAEM,SAASgI,QAAT,CAAiCnI,IAAjC,EAAmD;EACxD,IAAIA,IAAI,CAACiF,IAAL,KAAc,MAAlB,EAA0B;IACxB,KAAK9E,SAAL;EACD,CAFD,MAEO;IACL,KAAKA,SAAL;EACD;AACF;;AAEM,SAASiI,kBAAT,GAA2C;EAChD,KAAKtI,IAAL,CAAU,MAAV;AACD;;AAEM,SAASuI,iBAAT,CAA0CrI,IAA1C,EAAqE;EAC1E,KAAKC,KAAL,CAAWD,IAAI,CAACsI,UAAhB,EAA4BtI,IAA5B,EAAkC,IAAlC;EACA,KAAKG,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACuI,SAAhB,EAA2BvI,IAA3B;EACA,KAAKG,SAAL;AACD;;AAEM,SAASqI,yBAAT,CAELxI,IAFK,EAGL;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACsI,UAAhB,EAA4BtI,IAA5B;;EACA,IAAIA,IAAI,CAAC0E,QAAT,EAAmB;IACjB,KAAKvE,KAAL,CAAW,IAAX;EACD;;EACD,KAAKA,SAAL;EACA,KAAKF,KAAL,CAAWD,IAAI,CAACuI,SAAhB,EAA2BvI,IAA3B;EACA,KAAKG,SAAL;AACD"}