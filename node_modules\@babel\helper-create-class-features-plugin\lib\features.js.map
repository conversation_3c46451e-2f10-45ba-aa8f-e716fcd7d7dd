{"version": 3, "names": ["FEATURES", "Object", "freeze", "fields", "privateMethods", "decorators", "privateIn", "staticBlocks", "featuresSameLoose", "Map", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "looseLowPriorityKey", "enableFeature", "file", "feature", "loose", "hasFeature", "canIgnoreLoose", "set", "get", "setL<PERSON>e", "resolvedLoose", "higherPriorityPluginName", "mask", "name", "isLoose", "Error", "undefined", "console", "warn", "shouldTransform", "path", "decoratorPath", "publicFieldPath", "privateFieldPath", "privateMethodPath", "staticBlockPath", "hasOwnDecorators", "node", "el", "isClassProperty", "isClassPrivateProperty", "isClassPrivateMethod", "isStaticBlock", "buildCodeFrameError"], "sources": ["../src/features.ts"], "sourcesContent": ["import type { File, types as t } from \"@babel/core\";\nimport type { NodePath } from \"@babel/traverse\";\nimport { hasOwnDecorators } from \"./decorators\";\n\nexport const FEATURES = Object.freeze({\n  //classes: 1 << 0,\n  fields: 1 << 1,\n  privateMethods: 1 << 2,\n  // TODO(Babel 8): Remove this\n  decorators: 1 << 3,\n  privateIn: 1 << 4,\n  staticBlocks: 1 << 5,\n});\n\nconst featuresSameLoose = new Map([\n  [FEATURES.fields, \"@babel/plugin-proposal-class-properties\"],\n  [FEATURES.privateMethods, \"@babel/plugin-proposal-private-methods\"],\n  [FEATURES.privateIn, \"@babel/plugin-proposal-private-property-in-object\"],\n]);\n\n// We can't use a symbol because this needs to always be the same, even if\n// this package isn't deduped by npm. e.g.\n//  - node_modules/\n//    - @babel/plugin-class-features\n//    - @babel/plugin-proposal-decorators\n//      - node_modules\n//        - @babel-plugin-class-features\nconst featuresKey = \"@babel/plugin-class-features/featuresKey\";\nconst looseKey = \"@babel/plugin-class-features/looseKey\";\n\n// See https://github.com/babel/babel/issues/11622.\n// Since preset-env sets loose for the fields and private methods plugins, it can\n// cause conflicts with the loose mode set by an explicit plugin in the config.\n// To solve this problem, we ignore preset-env's loose mode if another plugin\n// explicitly sets it\n// The code to handle this logic doesn't check that \"low priority loose\" is always\n// the same. However, it is only set by the preset and not directly by users:\n// unless someone _wants_ to break it, it shouldn't be a problem.\nconst looseLowPriorityKey =\n  \"@babel/plugin-class-features/looseLowPriorityKey/#__internal__@babel/preset-env__please-overwrite-loose-instead-of-throwing\";\n\nexport function enableFeature(file: File, feature: number, loose: boolean) {\n  // We can't blindly enable the feature because, if it was already set,\n  // \"loose\" can't be changed, so that\n  //   @babel/plugin-class-properties { loose: true }\n  //   @babel/plugin-class-properties { loose: false }\n  // is transformed in loose mode.\n  // We only enabled the feature if it was previously disabled.\n  if (!hasFeature(file, feature) || canIgnoreLoose(file, feature)) {\n    file.set(featuresKey, file.get(featuresKey) | feature);\n    if (\n      // @ts-expect-error comparing loose with internal private magic string\n      loose ===\n      \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n    ) {\n      setLoose(file, feature, true);\n      file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) | feature);\n    } else if (\n      // @ts-expect-error comparing loose with internal private magic string\n      loose ===\n      \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\"\n    ) {\n      setLoose(file, feature, false);\n      file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) | feature);\n    } else {\n      setLoose(file, feature, loose);\n    }\n  }\n\n  let resolvedLoose: boolean | undefined;\n  let higherPriorityPluginName: string | undefined;\n\n  for (const [mask, name] of featuresSameLoose) {\n    if (!hasFeature(file, mask)) continue;\n\n    const loose = isLoose(file, mask);\n\n    if (canIgnoreLoose(file, mask)) {\n      continue;\n    } else if (resolvedLoose === !loose) {\n      throw new Error(\n        \"'loose' mode configuration must be the same for @babel/plugin-proposal-class-properties, \" +\n          \"@babel/plugin-proposal-private-methods and \" +\n          \"@babel/plugin-proposal-private-property-in-object (when they are enabled).\",\n      );\n    } else {\n      resolvedLoose = loose;\n      higherPriorityPluginName = name;\n    }\n  }\n\n  if (resolvedLoose !== undefined) {\n    for (const [mask, name] of featuresSameLoose) {\n      if (hasFeature(file, mask) && isLoose(file, mask) !== resolvedLoose) {\n        setLoose(file, mask, resolvedLoose);\n        console.warn(\n          `Though the \"loose\" option was set to \"${!resolvedLoose}\" in your @babel/preset-env ` +\n            `config, it will not be used for ${name} since the \"loose\" mode option was set to ` +\n            `\"${resolvedLoose}\" for ${higherPriorityPluginName}.\\nThe \"loose\" option must be the ` +\n            `same for @babel/plugin-proposal-class-properties, @babel/plugin-proposal-private-methods ` +\n            `and @babel/plugin-proposal-private-property-in-object (when they are enabled): you can ` +\n            `silence this warning by explicitly adding\\n` +\n            `\\t[\"${name}\", { \"loose\": ${resolvedLoose} }]\\n` +\n            `to the \"plugins\" section of your Babel config.`,\n        );\n      }\n    }\n  }\n}\n\nfunction hasFeature(file: File, feature: number) {\n  return !!(file.get(featuresKey) & feature);\n}\n\nexport function isLoose(file: File, feature: number) {\n  return !!(file.get(looseKey) & feature);\n}\n\nfunction setLoose(file: File, feature: number, loose: boolean) {\n  if (loose) file.set(looseKey, file.get(looseKey) | feature);\n  else file.set(looseKey, file.get(looseKey) & ~feature);\n\n  file.set(looseLowPriorityKey, file.get(looseLowPriorityKey) & ~feature);\n}\n\nfunction canIgnoreLoose(file: File, feature: number) {\n  return !!(file.get(looseLowPriorityKey) & feature);\n}\n\nexport function shouldTransform(path: NodePath<t.Class>, file: File): boolean {\n  let decoratorPath: NodePath<t.Decorator> | null = null;\n  let publicFieldPath: NodePath<t.ClassProperty> | null = null;\n  let privateFieldPath: NodePath<t.ClassPrivateProperty> | null = null;\n  let privateMethodPath: NodePath<t.ClassPrivateMethod> | null = null;\n  let staticBlockPath: NodePath<t.StaticBlock> | null = null;\n\n  if (hasOwnDecorators(path.node)) {\n    decoratorPath = path.get(\"decorators.0\");\n  }\n  for (const el of path.get(\"body.body\")) {\n    if (!decoratorPath && hasOwnDecorators(el.node)) {\n      decoratorPath = el.get(\"decorators.0\");\n    }\n    if (!publicFieldPath && el.isClassProperty()) {\n      publicFieldPath = el;\n    }\n    if (!privateFieldPath && el.isClassPrivateProperty()) {\n      privateFieldPath = el;\n    }\n    // NOTE: path.isClassPrivateMethod() it isn't supported in <7.2.0\n    if (!privateMethodPath && el.isClassPrivateMethod?.()) {\n      privateMethodPath = el;\n    }\n    if (!staticBlockPath && el.isStaticBlock?.()) {\n      staticBlockPath = el;\n    }\n  }\n\n  if (decoratorPath && privateFieldPath) {\n    throw privateFieldPath.buildCodeFrameError(\n      \"Private fields in decorated classes are not supported yet.\",\n    );\n  }\n  if (decoratorPath && privateMethodPath) {\n    throw privateMethodPath.buildCodeFrameError(\n      \"Private methods in decorated classes are not supported yet.\",\n    );\n  }\n\n  if (decoratorPath && !hasFeature(file, FEATURES.decorators)) {\n    throw path.buildCodeFrameError(\n      \"Decorators are not enabled.\" +\n        \"\\nIf you are using \" +\n        '[\"@babel/plugin-proposal-decorators\", { \"version\": \"legacy\" }], ' +\n        'make sure it comes *before* \"@babel/plugin-proposal-class-properties\" ' +\n        \"and enable loose mode, like so:\\n\" +\n        '\\t[\"@babel/plugin-proposal-decorators\", { \"version\": \"legacy\" }]\\n' +\n        '\\t[\"@babel/plugin-proposal-class-properties\", { \"loose\": true }]',\n    );\n  }\n\n  if (privateMethodPath && !hasFeature(file, FEATURES.privateMethods)) {\n    throw privateMethodPath.buildCodeFrameError(\n      \"Class private methods are not enabled. \" +\n        \"Please add `@babel/plugin-proposal-private-methods` to your configuration.\",\n    );\n  }\n\n  if (\n    (publicFieldPath || privateFieldPath) &&\n    !hasFeature(file, FEATURES.fields) &&\n    // We want to allow enabling the private-methods plugin even without enabling\n    // the class-properties plugin. Class fields will still be compiled in classes\n    // that contain private methods.\n    // This is already allowed with the other various class features plugins, but\n    // it's because they can fallback to a transform separated from this helper.\n    !hasFeature(file, FEATURES.privateMethods)\n  ) {\n    throw path.buildCodeFrameError(\n      \"Class fields are not enabled. \" +\n        \"Please add `@babel/plugin-proposal-class-properties` to your configuration.\",\n    );\n  }\n\n  if (staticBlockPath && !hasFeature(file, FEATURES.staticBlocks)) {\n    throw path.buildCodeFrameError(\n      \"Static class blocks are not enabled. \" +\n        \"Please add `@babel/plugin-proposal-class-static-block` to your configuration.\",\n    );\n  }\n\n  if (decoratorPath || privateMethodPath || staticBlockPath) {\n    // If one of those feature is used we know that its transform is\n    // enabled, otherwise the previous checks throw.\n    return true;\n  }\n  if (\n    (publicFieldPath || privateFieldPath) &&\n    hasFeature(file, FEATURES.fields)\n  ) {\n    return true;\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;AAEA;;AAEO,MAAMA,QAAQ,GAAGC,MAAM,CAACC,MAAP,CAAc;EAEpCC,MAAM,EAAE,KAAK,CAFuB;EAGpCC,cAAc,EAAE,KAAK,CAHe;EAKpCC,UAAU,EAAE,KAAK,CALmB;EAMpCC,SAAS,EAAE,KAAK,CANoB;EAOpCC,YAAY,EAAE,KAAK;AAPiB,CAAd,CAAjB;;AAUP,MAAMC,iBAAiB,GAAG,IAAIC,GAAJ,CAAQ,CAChC,CAACT,QAAQ,CAACG,MAAV,EAAkB,yCAAlB,CADgC,EAEhC,CAACH,QAAQ,CAACI,cAAV,EAA0B,wCAA1B,CAFgC,EAGhC,CAACJ,QAAQ,CAACM,SAAV,EAAqB,mDAArB,CAHgC,CAAR,CAA1B;AAaA,MAAMI,WAAW,GAAG,0CAApB;AACA,MAAMC,QAAQ,GAAG,uCAAjB;AAUA,MAAMC,mBAAmB,GACvB,6HADF;;AAGO,SAASC,aAAT,CAAuBC,IAAvB,EAAmCC,OAAnC,EAAoDC,KAApD,EAAoE;EAOzE,IAAI,CAACC,UAAU,CAACH,IAAD,EAAOC,OAAP,CAAX,IAA8BG,cAAc,CAACJ,IAAD,EAAOC,OAAP,CAAhD,EAAiE;IAC/DD,IAAI,CAACK,GAAL,CAAST,WAAT,EAAsBI,IAAI,CAACM,GAAL,CAASV,WAAT,IAAwBK,OAA9C;;IACA,IAEEC,KAAK,KACL,qFAHF,EAIE;MACAK,QAAQ,CAACP,IAAD,EAAOC,OAAP,EAAgB,IAAhB,CAAR;MACAD,IAAI,CAACK,GAAL,CAASP,mBAAT,EAA8BE,IAAI,CAACM,GAAL,CAASR,mBAAT,IAAgCG,OAA9D;IACD,CAPD,MAOO,IAELC,KAAK,KACL,qFAHK,EAIL;MACAK,QAAQ,CAACP,IAAD,EAAOC,OAAP,EAAgB,KAAhB,CAAR;MACAD,IAAI,CAACK,GAAL,CAASP,mBAAT,EAA8BE,IAAI,CAACM,GAAL,CAASR,mBAAT,IAAgCG,OAA9D;IACD,CAPM,MAOA;MACLM,QAAQ,CAACP,IAAD,EAAOC,OAAP,EAAgBC,KAAhB,CAAR;IACD;EACF;;EAED,IAAIM,aAAJ;EACA,IAAIC,wBAAJ;;EAEA,KAAK,MAAM,CAACC,IAAD,EAAOC,IAAP,CAAX,IAA2BjB,iBAA3B,EAA8C;IAC5C,IAAI,CAACS,UAAU,CAACH,IAAD,EAAOU,IAAP,CAAf,EAA6B;IAE7B,MAAMR,KAAK,GAAGU,OAAO,CAACZ,IAAD,EAAOU,IAAP,CAArB;;IAEA,IAAIN,cAAc,CAACJ,IAAD,EAAOU,IAAP,CAAlB,EAAgC;MAC9B;IACD,CAFD,MAEO,IAAIF,aAAa,KAAK,CAACN,KAAvB,EAA8B;MACnC,MAAM,IAAIW,KAAJ,CACJ,8FACE,6CADF,GAEE,4EAHE,CAAN;IAKD,CANM,MAMA;MACLL,aAAa,GAAGN,KAAhB;MACAO,wBAAwB,GAAGE,IAA3B;IACD;EACF;;EAED,IAAIH,aAAa,KAAKM,SAAtB,EAAiC;IAC/B,KAAK,MAAM,CAACJ,IAAD,EAAOC,IAAP,CAAX,IAA2BjB,iBAA3B,EAA8C;MAC5C,IAAIS,UAAU,CAACH,IAAD,EAAOU,IAAP,CAAV,IAA0BE,OAAO,CAACZ,IAAD,EAAOU,IAAP,CAAP,KAAwBF,aAAtD,EAAqE;QACnED,QAAQ,CAACP,IAAD,EAAOU,IAAP,EAAaF,aAAb,CAAR;QACAO,OAAO,CAACC,IAAR,CACG,yCAAwC,CAACR,aAAc,8BAAxD,GACG,mCAAkCG,IAAK,4CAD1C,GAEG,IAAGH,aAAc,SAAQC,wBAAyB,oCAFrD,GAGG,2FAHH,GAIG,yFAJH,GAKG,6CALH,GAMG,OAAME,IAAK,iBAAgBH,aAAc,OAN5C,GAOG,gDARL;MAUD;IACF;EACF;AACF;;AAED,SAASL,UAAT,CAAoBH,IAApB,EAAgCC,OAAhC,EAAiD;EAC/C,OAAO,CAAC,EAAED,IAAI,CAACM,GAAL,CAASV,WAAT,IAAwBK,OAA1B,CAAR;AACD;;AAEM,SAASW,OAAT,CAAiBZ,IAAjB,EAA6BC,OAA7B,EAA8C;EACnD,OAAO,CAAC,EAAED,IAAI,CAACM,GAAL,CAAST,QAAT,IAAqBI,OAAvB,CAAR;AACD;;AAED,SAASM,QAAT,CAAkBP,IAAlB,EAA8BC,OAA9B,EAA+CC,KAA/C,EAA+D;EAC7D,IAAIA,KAAJ,EAAWF,IAAI,CAACK,GAAL,CAASR,QAAT,EAAmBG,IAAI,CAACM,GAAL,CAAST,QAAT,IAAqBI,OAAxC,EAAX,KACKD,IAAI,CAACK,GAAL,CAASR,QAAT,EAAmBG,IAAI,CAACM,GAAL,CAAST,QAAT,IAAqB,CAACI,OAAzC;EAELD,IAAI,CAACK,GAAL,CAASP,mBAAT,EAA8BE,IAAI,CAACM,GAAL,CAASR,mBAAT,IAAgC,CAACG,OAA/D;AACD;;AAED,SAASG,cAAT,CAAwBJ,IAAxB,EAAoCC,OAApC,EAAqD;EACnD,OAAO,CAAC,EAAED,IAAI,CAACM,GAAL,CAASR,mBAAT,IAAgCG,OAAlC,CAAR;AACD;;AAEM,SAASgB,eAAT,CAAyBC,IAAzB,EAAkDlB,IAAlD,EAAuE;EAC5E,IAAImB,aAA2C,GAAG,IAAlD;EACA,IAAIC,eAAiD,GAAG,IAAxD;EACA,IAAIC,gBAAyD,GAAG,IAAhE;EACA,IAAIC,iBAAwD,GAAG,IAA/D;EACA,IAAIC,eAA+C,GAAG,IAAtD;;EAEA,IAAI,IAAAC,4BAAA,EAAiBN,IAAI,CAACO,IAAtB,CAAJ,EAAiC;IAC/BN,aAAa,GAAGD,IAAI,CAACZ,GAAL,CAAS,cAAT,CAAhB;EACD;;EACD,KAAK,MAAMoB,EAAX,IAAiBR,IAAI,CAACZ,GAAL,CAAS,WAAT,CAAjB,EAAwC;IACtC,IAAI,CAACa,aAAD,IAAkB,IAAAK,4BAAA,EAAiBE,EAAE,CAACD,IAApB,CAAtB,EAAiD;MAC/CN,aAAa,GAAGO,EAAE,CAACpB,GAAH,CAAO,cAAP,CAAhB;IACD;;IACD,IAAI,CAACc,eAAD,IAAoBM,EAAE,CAACC,eAAH,EAAxB,EAA8C;MAC5CP,eAAe,GAAGM,EAAlB;IACD;;IACD,IAAI,CAACL,gBAAD,IAAqBK,EAAE,CAACE,sBAAH,EAAzB,EAAsD;MACpDP,gBAAgB,GAAGK,EAAnB;IACD;;IAED,IAAI,CAACJ,iBAAD,IAAsBI,EAAE,CAACG,oBAAzB,YAAsBH,EAAE,CAACG,oBAAH,EAA1B,EAAuD;MACrDP,iBAAiB,GAAGI,EAApB;IACD;;IACD,IAAI,CAACH,eAAD,IAAoBG,EAAE,CAACI,aAAvB,YAAoBJ,EAAE,CAACI,aAAH,EAAxB,EAA8C;MAC5CP,eAAe,GAAGG,EAAlB;IACD;EACF;;EAED,IAAIP,aAAa,IAAIE,gBAArB,EAAuC;IACrC,MAAMA,gBAAgB,CAACU,mBAAjB,CACJ,4DADI,CAAN;EAGD;;EACD,IAAIZ,aAAa,IAAIG,iBAArB,EAAwC;IACtC,MAAMA,iBAAiB,CAACS,mBAAlB,CACJ,6DADI,CAAN;EAGD;;EAED,IAAIZ,aAAa,IAAI,CAAChB,UAAU,CAACH,IAAD,EAAOd,QAAQ,CAACK,UAAhB,CAAhC,EAA6D;IAC3D,MAAM2B,IAAI,CAACa,mBAAL,CACJ,gCACE,qBADF,GAEE,kEAFF,GAGE,wEAHF,GAIE,mCAJF,GAKE,oEALF,GAME,kEAPE,CAAN;EASD;;EAED,IAAIT,iBAAiB,IAAI,CAACnB,UAAU,CAACH,IAAD,EAAOd,QAAQ,CAACI,cAAhB,CAApC,EAAqE;IACnE,MAAMgC,iBAAiB,CAACS,mBAAlB,CACJ,4CACE,4EAFE,CAAN;EAID;;EAED,IACE,CAACX,eAAe,IAAIC,gBAApB,KACA,CAAClB,UAAU,CAACH,IAAD,EAAOd,QAAQ,CAACG,MAAhB,CADX,IAOA,CAACc,UAAU,CAACH,IAAD,EAAOd,QAAQ,CAACI,cAAhB,CARb,EASE;IACA,MAAM4B,IAAI,CAACa,mBAAL,CACJ,mCACE,6EAFE,CAAN;EAID;;EAED,IAAIR,eAAe,IAAI,CAACpB,UAAU,CAACH,IAAD,EAAOd,QAAQ,CAACO,YAAhB,CAAlC,EAAiE;IAC/D,MAAMyB,IAAI,CAACa,mBAAL,CACJ,0CACE,+EAFE,CAAN;EAID;;EAED,IAAIZ,aAAa,IAAIG,iBAAjB,IAAsCC,eAA1C,EAA2D;IAGzD,OAAO,IAAP;EACD;;EACD,IACE,CAACH,eAAe,IAAIC,gBAApB,KACAlB,UAAU,CAACH,IAAD,EAAOd,QAAQ,CAACG,MAAhB,CAFZ,EAGE;IACA,OAAO,IAAP;EACD;;EAED,OAAO,KAAP;AACD"}