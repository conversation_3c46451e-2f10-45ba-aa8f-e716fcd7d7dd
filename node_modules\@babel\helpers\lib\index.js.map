{"version": 3, "names": ["assignmentExpression", "cloneNode", "expressionStatement", "file", "identifier", "<PERSON><PERSON><PERSON>", "path", "parts", "parentPath", "push", "key", "inList", "<PERSON><PERSON><PERSON>", "reverse", "join", "FileClass", "undefined", "getHelperMetadata", "globals", "Set", "localBindingNames", "dependencies", "Map", "exportName", "exportPath", "exportBindingAssignments", "importPaths", "importBindingsReferences", "dependencyVisitor", "ImportDeclaration", "child", "name", "node", "source", "value", "helpers", "buildCodeFrameError", "get", "length", "isImportDefaultSpecifier", "bindingIdentifier", "specifiers", "local", "set", "ExportDefaultDeclaration", "decl", "isFunctionDeclaration", "id", "ExportAllDeclaration", "ExportNamedDeclaration", "Statement", "isModuleDeclaration", "skip", "referenceVisitor", "Program", "bindings", "scope", "getAllBindings", "Object", "keys", "for<PERSON>ach", "has", "add", "ReferencedIdentifier", "binding", "getBinding", "AssignmentExpression", "left", "getBindingIdentifiers", "isIdentifier", "isProgram", "traverse", "ast", "Error", "Array", "from", "permuteHelperAST", "metadata", "localBindings", "getDependency", "dependenciesRefs", "to<PERSON><PERSON><PERSON>", "newName", "type", "exp", "imps", "map", "p", "impsBindingRefs", "replaceWith", "assignPath", "assign", "pushContainer", "rename", "remove", "helperData", "create", "loadHelper", "helper", "ReferenceError", "code", "fn", "fakeFile", "stop", "filename", "inputMap", "minVersion", "build", "nodes", "program", "body", "getDependencies", "values", "ensure", "newFileClass", "list", "replace"], "sources": ["../src/index.ts"], "sourcesContent": ["import type { File } from \"@babel/core\";\nimport type { NodePath, Visitor } from \"@babel/traverse\";\nimport traverse from \"@babel/traverse\";\nimport {\n  assignmentExpression,\n  cloneNode,\n  expressionStatement,\n  file,\n  identifier,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport helpers from \"./helpers\";\n\nfunction makePath(path: NodePath) {\n  const parts = [];\n\n  for (; path.parentPath; path = path.parentPath) {\n    parts.push(path.key);\n    if (path.inList) parts.push(path.listKey);\n  }\n\n  return parts.reverse().join(\".\");\n}\n\nlet FileClass: typeof File | undefined = undefined;\n\ninterface HelperMetadata {\n  globals: string[];\n  localBindingNames: string[];\n  dependencies: Map<t.Identifier, string>;\n  exportBindingAssignments: string[];\n  exportPath: string;\n  exportName: string;\n  importBindingsReferences: string[];\n  importPaths: string[];\n}\n\n/**\n * Given a file AST for a given helper, get a bunch of metadata about it so that <PERSON><PERSON> can quickly render\n * the helper is whatever context it is needed in.\n */\nfunction getHelperMetadata(file: File): HelperMetadata {\n  const globals = new Set<string>();\n  const localBindingNames = new Set<string>();\n  // Maps imported identifier -> helper name\n  const dependencies = new Map<t.Identifier, string>();\n\n  let exportName: string | undefined;\n  let exportPath: string | undefined;\n  const exportBindingAssignments: string[] = [];\n  const importPaths: string[] = [];\n  const importBindingsReferences: string[] = [];\n\n  const dependencyVisitor: Visitor = {\n    ImportDeclaration(child) {\n      const name = child.node.source.value;\n      if (!helpers[name]) {\n        throw child.buildCodeFrameError(`Unknown helper ${name}`);\n      }\n      if (\n        child.get(\"specifiers\").length !== 1 ||\n        // @ts-expect-error isImportDefaultSpecifier does not work with NodePath union\n        !child.get(\"specifiers.0\").isImportDefaultSpecifier()\n      ) {\n        throw child.buildCodeFrameError(\n          \"Helpers can only import a default value\",\n        );\n      }\n      const bindingIdentifier = child.node.specifiers[0].local;\n      dependencies.set(bindingIdentifier, name);\n      importPaths.push(makePath(child));\n    },\n    ExportDefaultDeclaration(child) {\n      const decl = child.get(\"declaration\");\n\n      if (!decl.isFunctionDeclaration() || !decl.node.id) {\n        throw decl.buildCodeFrameError(\n          \"Helpers can only export named function declarations\",\n        );\n      }\n\n      exportName = decl.node.id.name;\n      exportPath = makePath(child);\n    },\n    ExportAllDeclaration(child) {\n      throw child.buildCodeFrameError(\"Helpers can only export default\");\n    },\n    ExportNamedDeclaration(child) {\n      throw child.buildCodeFrameError(\"Helpers can only export default\");\n    },\n    Statement(child) {\n      if (child.isModuleDeclaration()) return;\n\n      child.skip();\n    },\n  };\n\n  const referenceVisitor: Visitor = {\n    Program(path) {\n      const bindings = path.scope.getAllBindings();\n\n      Object.keys(bindings).forEach(name => {\n        if (name === exportName) return;\n        if (dependencies.has(bindings[name].identifier)) return;\n\n        localBindingNames.add(name);\n      });\n    },\n    ReferencedIdentifier(child) {\n      const name = child.node.name;\n      const binding = child.scope.getBinding(name);\n      if (!binding) {\n        globals.add(name);\n      } else if (dependencies.has(binding.identifier)) {\n        importBindingsReferences.push(makePath(child));\n      }\n    },\n    AssignmentExpression(child) {\n      const left = child.get(\"left\");\n\n      if (!(exportName in left.getBindingIdentifiers())) return;\n\n      if (!left.isIdentifier()) {\n        throw left.buildCodeFrameError(\n          \"Only simple assignments to exports are allowed in helpers\",\n        );\n      }\n\n      const binding = child.scope.getBinding(exportName);\n\n      if (binding?.scope.path.isProgram()) {\n        exportBindingAssignments.push(makePath(child));\n      }\n    },\n  };\n\n  traverse(file.ast, dependencyVisitor, file.scope);\n  traverse(file.ast, referenceVisitor, file.scope);\n\n  if (!exportPath) throw new Error(\"Helpers must have a default export.\");\n\n  // Process these in reverse so that mutating the references does not invalidate any later paths in\n  // the list.\n  exportBindingAssignments.reverse();\n\n  return {\n    globals: Array.from(globals),\n    localBindingNames: Array.from(localBindingNames),\n    dependencies,\n    exportBindingAssignments,\n    exportPath,\n    exportName,\n    importBindingsReferences,\n    importPaths,\n  };\n}\n\ntype GetDependency = (name: string) => t.Expression;\n\n/**\n * Given a helper AST and information about how it will be used, update the AST to match the usage.\n */\nfunction permuteHelperAST(\n  file: File,\n  metadata: HelperMetadata,\n  id?: t.Identifier | t.MemberExpression,\n  localBindings?: string[],\n  getDependency?: GetDependency,\n) {\n  if (localBindings && !id) {\n    throw new Error(\"Unexpected local bindings for module-based helpers.\");\n  }\n\n  if (!id) return;\n\n  const {\n    localBindingNames,\n    dependencies,\n    exportBindingAssignments,\n    exportPath,\n    exportName,\n    importBindingsReferences,\n    importPaths,\n  } = metadata;\n\n  const dependenciesRefs: Record<string, t.Expression> = {};\n  dependencies.forEach((name, id) => {\n    dependenciesRefs[id.name] =\n      (typeof getDependency === \"function\" && getDependency(name)) || id;\n  });\n\n  const toRename: Record<string, string> = {};\n  const bindings = new Set(localBindings || []);\n  localBindingNames.forEach(name => {\n    let newName = name;\n    while (bindings.has(newName)) newName = \"_\" + newName;\n\n    if (newName !== name) toRename[name] = newName;\n  });\n\n  if (id.type === \"Identifier\" && exportName !== id.name) {\n    toRename[exportName] = id.name;\n  }\n\n  const { path } = file;\n\n  // We need to compute these in advance because removing nodes would\n  // invalidate the paths.\n  const exp: NodePath<t.ExportDefaultDeclaration> = path.get(exportPath);\n  const imps: NodePath<t.ImportDeclaration>[] = importPaths.map(p =>\n    path.get(p),\n  );\n  const impsBindingRefs: NodePath<t.Identifier>[] =\n    importBindingsReferences.map(p => path.get(p));\n\n  // We assert that this is a FunctionDeclaration in dependencyVisitor.\n  const decl = exp.get(\"declaration\") as NodePath<t.FunctionDeclaration>;\n\n  if (id.type === \"Identifier\") {\n    exp.replaceWith(decl);\n  } else if (id.type === \"MemberExpression\") {\n    exportBindingAssignments.forEach(assignPath => {\n      const assign: NodePath<t.Expression> = path.get(assignPath);\n      assign.replaceWith(assignmentExpression(\"=\", id, assign.node));\n    });\n    exp.replaceWith(decl);\n    path.pushContainer(\n      \"body\",\n      expressionStatement(\n        assignmentExpression(\"=\", id, identifier(exportName)),\n      ),\n    );\n  } else {\n    throw new Error(\"Unexpected helper format.\");\n  }\n\n  Object.keys(toRename).forEach(name => {\n    path.scope.rename(name, toRename[name]);\n  });\n\n  for (const path of imps) path.remove();\n  for (const path of impsBindingRefs) {\n    const node = cloneNode(dependenciesRefs[path.node.name]);\n    path.replaceWith(node);\n  }\n}\n\ninterface HelperData {\n  build: (\n    getDependency: GetDependency,\n    id: t.Identifier | t.MemberExpression,\n    localBindings: string[],\n  ) => {\n    nodes: t.Program[\"body\"];\n    globals: string[];\n  };\n  minVersion: string;\n  getDependencies: () => string[];\n}\n\nconst helperData: Record<string, HelperData> = Object.create(null);\nfunction loadHelper(name: string) {\n  if (!helperData[name]) {\n    const helper = helpers[name];\n    if (!helper) {\n      throw Object.assign(new ReferenceError(`Unknown helper ${name}`), {\n        code: \"BABEL_HELPER_UNKNOWN\",\n        helper: name,\n      });\n    }\n\n    const fn = (): File => {\n      if (!process.env.BABEL_8_BREAKING) {\n        if (!FileClass) {\n          const fakeFile = { ast: file(helper.ast()), path: null } as File;\n          traverse(fakeFile.ast, {\n            Program: path => (fakeFile.path = path).stop(),\n          });\n          return fakeFile;\n        }\n      }\n      return new FileClass(\n        { filename: `babel-helper://${name}` },\n        {\n          ast: file(helper.ast()),\n          code: \"[internal Babel helper code]\",\n          inputMap: null,\n        },\n      );\n    };\n\n    // We compute the helper metadata lazily, so that we skip that\n    // work if we only need the `.minVersion` (for example because\n    // of a call to `.availableHelper` when `@babel/rutime`).\n    let metadata: HelperMetadata | null = null;\n\n    helperData[name] = {\n      minVersion: helper.minVersion,\n      build(getDependency, id, localBindings) {\n        const file = fn();\n        metadata ||= getHelperMetadata(file);\n        permuteHelperAST(file, metadata, id, localBindings, getDependency);\n\n        return {\n          nodes: file.ast.program.body,\n          globals: metadata.globals,\n        };\n      },\n      getDependencies() {\n        metadata ||= getHelperMetadata(fn());\n        return Array.from(metadata.dependencies.values());\n      },\n    };\n  }\n\n  return helperData[name];\n}\n\nexport function get(\n  name: string,\n  getDependency?: GetDependency,\n  id?: t.Identifier | t.MemberExpression,\n  localBindings?: string[],\n) {\n  return loadHelper(name).build(getDependency, id, localBindings);\n}\n\nexport function minVersion(name: string) {\n  return loadHelper(name).minVersion;\n}\n\nexport function getDependencies(name: string): ReadonlyArray<string> {\n  return loadHelper(name).getDependencies();\n}\n\nexport function ensure(name: string, newFileClass: typeof File) {\n  // We inject the File class here rather than importing it to avoid\n  // circular dependencies between @babel/core and @babel/helpers.\n  FileClass ||= newFileClass;\n\n  loadHelper(name);\n}\n\nexport const list = Object.keys(helpers).map(name => name.replace(/^_/, \"\"));\n\nexport default get;\n"], "mappings": ";;;;;;;;;;;;AAEA;;AACA;;AAQA;;;EAPEA,oB;EACAC,S;EACAC,mB;EACAC,I;EACAC;;;AAKF,SAASC,QAAT,CAAkBC,IAAlB,EAAkC;EAChC,MAAMC,KAAK,GAAG,EAAd;;EAEA,OAAOD,IAAI,CAACE,UAAZ,EAAwBF,IAAI,GAAGA,IAAI,CAACE,UAApC,EAAgD;IAC9CD,KAAK,CAACE,IAAN,CAAWH,IAAI,CAACI,GAAhB;IACA,IAAIJ,IAAI,CAACK,MAAT,EAAiBJ,KAAK,CAACE,IAAN,CAAWH,IAAI,CAACM,OAAhB;EAClB;;EAED,OAAOL,KAAK,CAACM,OAAN,GAAgBC,IAAhB,CAAqB,GAArB,CAAP;AACD;;AAED,IAAIC,SAAkC,GAAGC,SAAzC;;AAiBA,SAASC,iBAAT,CAA2Bd,IAA3B,EAAuD;EACrD,MAAMe,OAAO,GAAG,IAAIC,GAAJ,EAAhB;EACA,MAAMC,iBAAiB,GAAG,IAAID,GAAJ,EAA1B;EAEA,MAAME,YAAY,GAAG,IAAIC,GAAJ,EAArB;EAEA,IAAIC,UAAJ;EACA,IAAIC,UAAJ;EACA,MAAMC,wBAAkC,GAAG,EAA3C;EACA,MAAMC,WAAqB,GAAG,EAA9B;EACA,MAAMC,wBAAkC,GAAG,EAA3C;EAEA,MAAMC,iBAA0B,GAAG;IACjCC,iBAAiB,CAACC,KAAD,EAAQ;MACvB,MAAMC,IAAI,GAAGD,KAAK,CAACE,IAAN,CAAWC,MAAX,CAAkBC,KAA/B;;MACA,IAAI,CAACC,gBAAA,CAAQJ,IAAR,CAAL,EAAoB;QAClB,MAAMD,KAAK,CAACM,mBAAN,CAA2B,kBAAiBL,IAAK,EAAjD,CAAN;MACD;;MACD,IACED,KAAK,CAACO,GAAN,CAAU,YAAV,EAAwBC,MAAxB,KAAmC,CAAnC,IAEA,CAACR,KAAK,CAACO,GAAN,CAAU,cAAV,EAA0BE,wBAA1B,EAHH,EAIE;QACA,MAAMT,KAAK,CAACM,mBAAN,CACJ,yCADI,CAAN;MAGD;;MACD,MAAMI,iBAAiB,GAAGV,KAAK,CAACE,IAAN,CAAWS,UAAX,CAAsB,CAAtB,EAAyBC,KAAnD;MACArB,YAAY,CAACsB,GAAb,CAAiBH,iBAAjB,EAAoCT,IAApC;MACAL,WAAW,CAACjB,IAAZ,CAAiBJ,QAAQ,CAACyB,KAAD,CAAzB;IACD,CAlBgC;;IAmBjCc,wBAAwB,CAACd,KAAD,EAAQ;MAC9B,MAAMe,IAAI,GAAGf,KAAK,CAACO,GAAN,CAAU,aAAV,CAAb;;MAEA,IAAI,CAACQ,IAAI,CAACC,qBAAL,EAAD,IAAiC,CAACD,IAAI,CAACb,IAAL,CAAUe,EAAhD,EAAoD;QAClD,MAAMF,IAAI,CAACT,mBAAL,CACJ,qDADI,CAAN;MAGD;;MAEDb,UAAU,GAAGsB,IAAI,CAACb,IAAL,CAAUe,EAAV,CAAahB,IAA1B;MACAP,UAAU,GAAGnB,QAAQ,CAACyB,KAAD,CAArB;IACD,CA9BgC;;IA+BjCkB,oBAAoB,CAAClB,KAAD,EAAQ;MAC1B,MAAMA,KAAK,CAACM,mBAAN,CAA0B,iCAA1B,CAAN;IACD,CAjCgC;;IAkCjCa,sBAAsB,CAACnB,KAAD,EAAQ;MAC5B,MAAMA,KAAK,CAACM,mBAAN,CAA0B,iCAA1B,CAAN;IACD,CApCgC;;IAqCjCc,SAAS,CAACpB,KAAD,EAAQ;MACf,IAAIA,KAAK,CAACqB,mBAAN,EAAJ,EAAiC;MAEjCrB,KAAK,CAACsB,IAAN;IACD;;EAzCgC,CAAnC;EA4CA,MAAMC,gBAAyB,GAAG;IAChCC,OAAO,CAAChD,IAAD,EAAO;MACZ,MAAMiD,QAAQ,GAAGjD,IAAI,CAACkD,KAAL,CAAWC,cAAX,EAAjB;MAEAC,MAAM,CAACC,IAAP,CAAYJ,QAAZ,EAAsBK,OAAtB,CAA8B7B,IAAI,IAAI;QACpC,IAAIA,IAAI,KAAKR,UAAb,EAAyB;QACzB,IAAIF,YAAY,CAACwC,GAAb,CAAiBN,QAAQ,CAACxB,IAAD,CAAR,CAAe3B,UAAhC,CAAJ,EAAiD;QAEjDgB,iBAAiB,CAAC0C,GAAlB,CAAsB/B,IAAtB;MACD,CALD;IAMD,CAV+B;;IAWhCgC,oBAAoB,CAACjC,KAAD,EAAQ;MAC1B,MAAMC,IAAI,GAAGD,KAAK,CAACE,IAAN,CAAWD,IAAxB;MACA,MAAMiC,OAAO,GAAGlC,KAAK,CAAC0B,KAAN,CAAYS,UAAZ,CAAuBlC,IAAvB,CAAhB;;MACA,IAAI,CAACiC,OAAL,EAAc;QACZ9C,OAAO,CAAC4C,GAAR,CAAY/B,IAAZ;MACD,CAFD,MAEO,IAAIV,YAAY,CAACwC,GAAb,CAAiBG,OAAO,CAAC5D,UAAzB,CAAJ,EAA0C;QAC/CuB,wBAAwB,CAAClB,IAAzB,CAA8BJ,QAAQ,CAACyB,KAAD,CAAtC;MACD;IACF,CAnB+B;;IAoBhCoC,oBAAoB,CAACpC,KAAD,EAAQ;MAC1B,MAAMqC,IAAI,GAAGrC,KAAK,CAACO,GAAN,CAAU,MAAV,CAAb;MAEA,IAAI,EAAEd,UAAU,IAAI4C,IAAI,CAACC,qBAAL,EAAhB,CAAJ,EAAmD;;MAEnD,IAAI,CAACD,IAAI,CAACE,YAAL,EAAL,EAA0B;QACxB,MAAMF,IAAI,CAAC/B,mBAAL,CACJ,2DADI,CAAN;MAGD;;MAED,MAAM4B,OAAO,GAAGlC,KAAK,CAAC0B,KAAN,CAAYS,UAAZ,CAAuB1C,UAAvB,CAAhB;;MAEA,IAAIyC,OAAJ,YAAIA,OAAO,CAAER,KAAT,CAAelD,IAAf,CAAoBgE,SAApB,EAAJ,EAAqC;QACnC7C,wBAAwB,CAAChB,IAAzB,CAA8BJ,QAAQ,CAACyB,KAAD,CAAtC;MACD;IACF;;EApC+B,CAAlC;EAuCA,IAAAyC,iBAAA,EAASpE,IAAI,CAACqE,GAAd,EAAmB5C,iBAAnB,EAAsCzB,IAAI,CAACqD,KAA3C;EACA,IAAAe,iBAAA,EAASpE,IAAI,CAACqE,GAAd,EAAmBnB,gBAAnB,EAAqClD,IAAI,CAACqD,KAA1C;EAEA,IAAI,CAAChC,UAAL,EAAiB,MAAM,IAAIiD,KAAJ,CAAU,qCAAV,CAAN;EAIjBhD,wBAAwB,CAACZ,OAAzB;EAEA,OAAO;IACLK,OAAO,EAAEwD,KAAK,CAACC,IAAN,CAAWzD,OAAX,CADJ;IAELE,iBAAiB,EAAEsD,KAAK,CAACC,IAAN,CAAWvD,iBAAX,CAFd;IAGLC,YAHK;IAILI,wBAJK;IAKLD,UALK;IAMLD,UANK;IAOLI,wBAPK;IAQLD;EARK,CAAP;AAUD;;AAOD,SAASkD,gBAAT,CACEzE,IADF,EAEE0E,QAFF,EAGE9B,EAHF,EAIE+B,aAJF,EAKEC,aALF,EAME;EACA,IAAID,aAAa,IAAI,CAAC/B,EAAtB,EAA0B;IACxB,MAAM,IAAI0B,KAAJ,CAAU,qDAAV,CAAN;EACD;;EAED,IAAI,CAAC1B,EAAL,EAAS;EAET,MAAM;IACJ3B,iBADI;IAEJC,YAFI;IAGJI,wBAHI;IAIJD,UAJI;IAKJD,UALI;IAMJI,wBANI;IAOJD;EAPI,IAQFmD,QARJ;EAUA,MAAMG,gBAA8C,GAAG,EAAvD;EACA3D,YAAY,CAACuC,OAAb,CAAqB,CAAC7B,IAAD,EAAOgB,EAAP,KAAc;IACjCiC,gBAAgB,CAACjC,EAAE,CAAChB,IAAJ,CAAhB,GACG,OAAOgD,aAAP,KAAyB,UAAzB,IAAuCA,aAAa,CAAChD,IAAD,CAArD,IAAgEgB,EADlE;EAED,CAHD;EAKA,MAAMkC,QAAgC,GAAG,EAAzC;EACA,MAAM1B,QAAQ,GAAG,IAAIpC,GAAJ,CAAQ2D,aAAa,IAAI,EAAzB,CAAjB;EACA1D,iBAAiB,CAACwC,OAAlB,CAA0B7B,IAAI,IAAI;IAChC,IAAImD,OAAO,GAAGnD,IAAd;;IACA,OAAOwB,QAAQ,CAACM,GAAT,CAAaqB,OAAb,CAAP,EAA8BA,OAAO,GAAG,MAAMA,OAAhB;;IAE9B,IAAIA,OAAO,KAAKnD,IAAhB,EAAsBkD,QAAQ,CAAClD,IAAD,CAAR,GAAiBmD,OAAjB;EACvB,CALD;;EAOA,IAAInC,EAAE,CAACoC,IAAH,KAAY,YAAZ,IAA4B5D,UAAU,KAAKwB,EAAE,CAAChB,IAAlD,EAAwD;IACtDkD,QAAQ,CAAC1D,UAAD,CAAR,GAAuBwB,EAAE,CAAChB,IAA1B;EACD;;EAED,MAAM;IAAEzB;EAAF,IAAWH,IAAjB;EAIA,MAAMiF,GAAyC,GAAG9E,IAAI,CAAC+B,GAAL,CAASb,UAAT,CAAlD;EACA,MAAM6D,IAAqC,GAAG3D,WAAW,CAAC4D,GAAZ,CAAgBC,CAAC,IAC7DjF,IAAI,CAAC+B,GAAL,CAASkD,CAAT,CAD4C,CAA9C;EAGA,MAAMC,eAAyC,GAC7C7D,wBAAwB,CAAC2D,GAAzB,CAA6BC,CAAC,IAAIjF,IAAI,CAAC+B,GAAL,CAASkD,CAAT,CAAlC,CADF;EAIA,MAAM1C,IAAI,GAAGuC,GAAG,CAAC/C,GAAJ,CAAQ,aAAR,CAAb;;EAEA,IAAIU,EAAE,CAACoC,IAAH,KAAY,YAAhB,EAA8B;IAC5BC,GAAG,CAACK,WAAJ,CAAgB5C,IAAhB;EACD,CAFD,MAEO,IAAIE,EAAE,CAACoC,IAAH,KAAY,kBAAhB,EAAoC;IACzC1D,wBAAwB,CAACmC,OAAzB,CAAiC8B,UAAU,IAAI;MAC7C,MAAMC,MAA8B,GAAGrF,IAAI,CAAC+B,GAAL,CAASqD,UAAT,CAAvC;MACAC,MAAM,CAACF,WAAP,CAAmBzF,oBAAoB,CAAC,GAAD,EAAM+C,EAAN,EAAU4C,MAAM,CAAC3D,IAAjB,CAAvC;IACD,CAHD;IAIAoD,GAAG,CAACK,WAAJ,CAAgB5C,IAAhB;IACAvC,IAAI,CAACsF,aAAL,CACE,MADF,EAEE1F,mBAAmB,CACjBF,oBAAoB,CAAC,GAAD,EAAM+C,EAAN,EAAU3C,UAAU,CAACmB,UAAD,CAApB,CADH,CAFrB;EAMD,CAZM,MAYA;IACL,MAAM,IAAIkD,KAAJ,CAAU,2BAAV,CAAN;EACD;;EAEDf,MAAM,CAACC,IAAP,CAAYsB,QAAZ,EAAsBrB,OAAtB,CAA8B7B,IAAI,IAAI;IACpCzB,IAAI,CAACkD,KAAL,CAAWqC,MAAX,CAAkB9D,IAAlB,EAAwBkD,QAAQ,CAAClD,IAAD,CAAhC;EACD,CAFD;;EAIA,KAAK,MAAMzB,IAAX,IAAmB+E,IAAnB,EAAyB/E,IAAI,CAACwF,MAAL;;EACzB,KAAK,MAAMxF,IAAX,IAAmBkF,eAAnB,EAAoC;IAClC,MAAMxD,IAAI,GAAG/B,SAAS,CAAC+E,gBAAgB,CAAC1E,IAAI,CAAC0B,IAAL,CAAUD,IAAX,CAAjB,CAAtB;IACAzB,IAAI,CAACmF,WAAL,CAAiBzD,IAAjB;EACD;AACF;;AAeD,MAAM+D,UAAsC,GAAGrC,MAAM,CAACsC,MAAP,CAAc,IAAd,CAA/C;;AACA,SAASC,UAAT,CAAoBlE,IAApB,EAAkC;EAChC,IAAI,CAACgE,UAAU,CAAChE,IAAD,CAAf,EAAuB;IACrB,MAAMmE,MAAM,GAAG/D,gBAAA,CAAQJ,IAAR,CAAf;;IACA,IAAI,CAACmE,MAAL,EAAa;MACX,MAAMxC,MAAM,CAACiC,MAAP,CAAc,IAAIQ,cAAJ,CAAoB,kBAAiBpE,IAAK,EAA1C,CAAd,EAA4D;QAChEqE,IAAI,EAAE,sBAD0D;QAEhEF,MAAM,EAAEnE;MAFwD,CAA5D,CAAN;IAID;;IAED,MAAMsE,EAAE,GAAG,MAAY;MACc;QACjC,IAAI,CAACtF,SAAL,EAAgB;UACd,MAAMuF,QAAQ,GAAG;YAAE9B,GAAG,EAAErE,IAAI,CAAC+F,MAAM,CAAC1B,GAAP,EAAD,CAAX;YAA2BlE,IAAI,EAAE;UAAjC,CAAjB;UACA,IAAAiE,iBAAA,EAAS+B,QAAQ,CAAC9B,GAAlB,EAAuB;YACrBlB,OAAO,EAAEhD,IAAI,IAAI,CAACgG,QAAQ,CAAChG,IAAT,GAAgBA,IAAjB,EAAuBiG,IAAvB;UADI,CAAvB;UAGA,OAAOD,QAAP;QACD;MACF;MACD,OAAO,IAAIvF,SAAJ,CACL;QAAEyF,QAAQ,EAAG,kBAAiBzE,IAAK;MAAnC,CADK,EAEL;QACEyC,GAAG,EAAErE,IAAI,CAAC+F,MAAM,CAAC1B,GAAP,EAAD,CADX;QAEE4B,IAAI,EAAE,8BAFR;QAGEK,QAAQ,EAAE;MAHZ,CAFK,CAAP;IAQD,CAlBD;;IAuBA,IAAI5B,QAA+B,GAAG,IAAtC;IAEAkB,UAAU,CAAChE,IAAD,CAAV,GAAmB;MACjB2E,UAAU,EAAER,MAAM,CAACQ,UADF;;MAEjBC,KAAK,CAAC5B,aAAD,EAAgBhC,EAAhB,EAAoB+B,aAApB,EAAmC;QACtC,MAAM3E,IAAI,GAAGkG,EAAE,EAAf;QACAxB,QAAQ,KAARA,QAAQ,GAAK5D,iBAAiB,CAACd,IAAD,CAAtB,CAAR;QACAyE,gBAAgB,CAACzE,IAAD,EAAO0E,QAAP,EAAiB9B,EAAjB,EAAqB+B,aAArB,EAAoCC,aAApC,CAAhB;QAEA,OAAO;UACL6B,KAAK,EAAEzG,IAAI,CAACqE,GAAL,CAASqC,OAAT,CAAiBC,IADnB;UAEL5F,OAAO,EAAE2D,QAAQ,CAAC3D;QAFb,CAAP;MAID,CAXgB;;MAYjB6F,eAAe,GAAG;QAChBlC,QAAQ,KAARA,QAAQ,GAAK5D,iBAAiB,CAACoF,EAAE,EAAH,CAAtB,CAAR;QACA,OAAO3B,KAAK,CAACC,IAAN,CAAWE,QAAQ,CAACxD,YAAT,CAAsB2F,MAAtB,EAAX,CAAP;MACD;;IAfgB,CAAnB;EAiBD;;EAED,OAAOjB,UAAU,CAAChE,IAAD,CAAjB;AACD;;AAEM,SAASM,GAAT,CACLN,IADK,EAELgD,aAFK,EAGLhC,EAHK,EAIL+B,aAJK,EAKL;EACA,OAAOmB,UAAU,CAAClE,IAAD,CAAV,CAAiB4E,KAAjB,CAAuB5B,aAAvB,EAAsChC,EAAtC,EAA0C+B,aAA1C,CAAP;AACD;;AAEM,SAAS4B,UAAT,CAAoB3E,IAApB,EAAkC;EACvC,OAAOkE,UAAU,CAAClE,IAAD,CAAV,CAAiB2E,UAAxB;AACD;;AAEM,SAASK,eAAT,CAAyBhF,IAAzB,EAA8D;EACnE,OAAOkE,UAAU,CAAClE,IAAD,CAAV,CAAiBgF,eAAjB,EAAP;AACD;;AAEM,SAASE,MAAT,CAAgBlF,IAAhB,EAA8BmF,YAA9B,EAAyD;EAG9DnG,SAAS,KAATA,SAAS,GAAKmG,YAAL,CAAT;EAEAjB,UAAU,CAAClE,IAAD,CAAV;AACD;;AAEM,MAAMoF,IAAI,GAAGzD,MAAM,CAACC,IAAP,CAAYxB,gBAAZ,EAAqBmD,GAArB,CAAyBvD,IAAI,IAAIA,IAAI,CAACqF,OAAL,CAAa,IAAb,EAAmB,EAAnB,CAAjC,CAAb;;eAEQ/E,G"}