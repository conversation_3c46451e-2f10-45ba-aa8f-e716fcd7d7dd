/**
 * Auto Save on Leave Hook
 *
 * 🔧 优化后的逻辑：
 * 1. 用户选择离开 = 不触发自动保存 + 清空所有快照
 * 2. 保存成功后 = 清空所有快照
 * 3. 快照对比机制由 SaveButton 负责，通过 JSON-to-JSON 对比判断笔记状态
 * 4. view 状态：不调用任何自动保存逻辑
 * 5. save 状态：根据离开类型采用不同策略
 *
 * Copyright (c) 2025 waycaan
 * Licensed under the MIT License
 */

import { useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/router';

interface UseAutoSaveOnLeaveOptions {
    enabled?: boolean;
}

const useAutoSaveOnLeave = (options: UseAutoSaveOnLeaveOptions = {}) => {
    const { enabled = true } = options;
    const router = useRouter();
    const isAutoSavingRef = useRef(false);


    const shouldAutoSave = useCallback(() => {
        if (typeof window !== 'undefined' && (window as any).saveButtonStatus) {
            const status = (window as any).saveButtonStatus;
            return status === 'save';
        }
        return false;
    }, []);

    const performAutoSave = useCallback(async () => {
        if (typeof window !== 'undefined' && (window as any).saveButtonAutoSave) {
            try {
                await (window as any).saveButtonAutoSave();
                return true;
            } catch (error) {
                return false;
            }
        }
        return false;
    }, []);

    // 🔧 新增：清空所有快照的函数
    const clearAllSnapshots = useCallback(() => {
        if (typeof window !== 'undefined' && (window as any).clearSnapshots) {
            try {
                (window as any).clearSnapshots();
            } catch (error) {
                console.error('清空快照失败:', error);
            }
        }
    }, []);

    // 页面关闭/刷新处理 - 弹窗提示机制
    const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
        if (!enabled) return;

        // 只有 save 状态才弹窗
        if (shouldAutoSave()) {
            // 显示确认对话框
            const message = '您有未保存的更改。确定要离开吗？';
            event.returnValue = message;

            // 🔧 优化：使用延迟检测用户选择
            setTimeout(() => {
                // 如果能执行到这里，说明用户选择了"取消"，执行自动保存
                performAutoSave();
            }, 100);

            // 🔧 新增：如果用户选择"离开"，页面会立即卸载，在 unload 事件中清空快照
            const handleUnload = () => {
                clearAllSnapshots();
                window.removeEventListener('unload', handleUnload);
            };
            window.addEventListener('unload', handleUnload);

            return message;
        }
        // view 状态：直接允许离开，不弹窗
    }, [enabled, shouldAutoSave, performAutoSave]);

    // 路由变化处理 - 严格按照用户要求
    const handleRouteChangeStart = useCallback(async (url: string) => {
        if (!enabled || isAutoSavingRef.current) return;

        // 只有 save 状态才处理
        if (shouldAutoSave()) {
            // 检查是否是笔记跳转
            const isNoteNavigation = url.match(/^\/[a-zA-Z0-9-]+(\?.*)?$/) || url === '/' || url.includes('?new');

            if (isNoteNavigation) {
                // 笔记跳转：自动保存，不弹窗，不阻止跳转
                isAutoSavingRef.current = true;

                // 在后台执行自动保存，不阻止路由跳转
                performAutoSave().finally(() => {
                    isAutoSavingRef.current = false;
                });

                // 直接允许跳转，不阻止
                return;
            } else {
                // 非笔记跳转：阻止跳转，弹窗询问
                router.events.emit('routeChangeError', new Error('User confirmation required'), url);

                const confirmed = window.confirm('您有未保存的更改。确定要离开吗？');
                if (confirmed) {
                    // 🔧 用户选择离开：不触发自动保存，清空所有快照，直接跳转
                    clearAllSnapshots();
                    router.push(url);
                } else {
                    // 🔧 用户选择取消：自动保存
                    await performAutoSave();
                }
            }
        }
        // view 状态：直接允许跳转，不做任何处理
    }, [enabled, shouldAutoSave, performAutoSave, router]);



    useEffect(() => {
        if (!enabled) return;

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [enabled, handleBeforeUnload]);

    useEffect(() => {
        if (!enabled) return;

        router.events.on('routeChangeStart', handleRouteChangeStart);

        return () => {
            router.events.off('routeChangeStart', handleRouteChangeStart);
        };
    }, [enabled, handleRouteChangeStart, router.events]);

    return {
        shouldAutoSave,
        performAutoSave,
    };
};

export default useAutoSaveOnLeave;
