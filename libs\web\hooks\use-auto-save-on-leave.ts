/**
 * Auto Save on Leave Hook
 *
 * 简单逻辑：
 * 1. 用户选择离开/重新加载 = 不自动保存 + 清空快照
 * 2. 保存成功后 = 清空快照
 * 3. 笔记跳转时 = 自动保存
 * 4. 页面关闭/刷新时 = 弹窗询问（重新加载和离开都代表不保存）
 * 5. 非笔记跳转时 = 直接清空快照（不弹窗）
 *
 * Copyright (c) 2025 waycaan
 * Licensed under the MIT License
 */

import { useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/router';
import { isNoteLink } from 'libs/shared/note';

interface UseAutoSaveOnLeaveOptions {
    enabled?: boolean;
}

const useAutoSaveOnLeave = (options: UseAutoSaveOnLeaveOptions = {}) => {
    const { enabled = true } = options;
    const router = useRouter();
    const isAutoSavingRef = useRef(false);

    const hasUnsavedChanges = useCallback(() => {
        return typeof window !== 'undefined' &&
               (window as any).saveButtonStatus === 'save';
    }, []);

    const autoSave = useCallback(async () => {
        if (typeof window !== 'undefined' && (window as any).saveButtonAutoSave) {
            try {
                await (window as any).saveButtonAutoSave();
                return true;
            } catch (error) {
                return false;
            }
        }
        return false;
    }, []);

    const clearSnapshots = useCallback(() => {
        if (typeof window !== 'undefined' && (window as any).clearSnapshots) {
            try {
                (window as any).clearSnapshots();
            } catch (error) {
                console.error('清空快照失败:', error);
            }
        }
    }, []);

    // 页面关闭/刷新时弹窗
    const handleBeforeUnload = useCallback((event: BeforeUnloadEvent) => {
        if (!enabled || !hasUnsavedChanges()) return;

        const message = '您有未保存的更改。确定要离开吗？';
        event.returnValue = message;

        // 用户选择离开或重新加载时清空快照
        const handleUnload = () => {
            clearSnapshots();
            window.removeEventListener('unload', handleUnload);
        };
        window.addEventListener('unload', handleUnload);

        return message;
    }, [enabled, hasUnsavedChanges, clearSnapshots]);

    // 路由跳转时处理
    const handleRouteChange = useCallback(async (url: string) => {
        if (!enabled || isAutoSavingRef.current || !hasUnsavedChanges()) return;

        // 检查是否是笔记跳转
        if (isNoteLink(url) || url === '/' || url.includes('?new')) {
            // 笔记跳转：后台自动保存
            isAutoSavingRef.current = true;
            autoSave().finally(() => {
                isAutoSavingRef.current = false;
            });
            return;
        }

        // 非笔记跳转：直接清空快照并跳转（不弹窗）
        // 重新加载和离开都代表不保存
        clearSnapshots();
        // 不阻止跳转，直接允许
    }, [enabled, hasUnsavedChanges, autoSave, clearSnapshots, router]);


    useEffect(() => {
        if (!enabled) return;
        window.addEventListener('beforeunload', handleBeforeUnload);
        return () => window.removeEventListener('beforeunload', handleBeforeUnload);
    }, [enabled, handleBeforeUnload]);

    useEffect(() => {
        if (!enabled) return;
        router.events.on('routeChangeStart', handleRouteChange);
        return () => router.events.off('routeChangeStart', handleRouteChange);
    }, [enabled, handleRouteChange, router.events]);

    return {
        hasUnsavedChanges,
        autoSave,
    };
};

export default useAutoSaveOnLeave;
