{"version": 3, "names": ["_awaitAsyncGenerator", "value", "OverloadYield"], "sources": ["../../src/helpers/awaitAsyncGenerator.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport OverloadYield from \"OverloadYield\";\n\nexport default function _awaitAsyncGenerator(value) {\n  return new OverloadYield(value, /* kind: await */ 0);\n}\n"], "mappings": ";;;;;;;AAEA;;AAEe,SAASA,oBAAT,CAA8BC,KAA9B,EAAqC;EAClD,OAAO,IAAIC,cAAJ,CAAkBD,KAAlB,EAA2C,CAA3C,CAAP;AACD"}