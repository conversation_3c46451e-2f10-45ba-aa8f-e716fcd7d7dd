{"version": 3, "names": ["buildPrivateNamesMap", "props", "privateNamesMap", "Map", "prop", "isPrivate", "name", "node", "key", "id", "update", "has", "get", "scope", "generateUidIdentifier", "static", "method", "isProperty", "isClassPrivateMethod", "kind", "getId", "setId", "methodId", "set", "buildPrivateNamesNodes", "privateFieldsAsProperties", "state", "initNodes", "value", "isStatic", "isMethod", "isAccessor", "t", "cloneNode", "init", "callExpression", "addHelper", "stringLiteral", "newExpression", "identifier", "annotateAsPure", "push", "template", "statement", "ast", "privateNameVisitorFactory", "visitor", "privateNameVisitor", "Class", "path", "body", "visiblePrivateNames", "redeclared", "delete", "length", "traverse", "nestedVisitor", "<PERSON><PERSON><PERSON>", "visitors", "merge", "environmentVisitor", "PrivateName", "noDocumentAll", "parentPath", "isMemberExpression", "property", "isOptionalMemberExpression", "includes", "handle", "unshadow", "innerBinding", "hasBinding", "bindingIdentifierEquals", "rename", "parent", "privateInVisitor", "BinaryExpression", "operator", "left", "right", "isPrivateName", "classRef", "replaceWith", "expression", "privateNameHandlerSpec", "memoise", "member", "count", "object", "memo", "maybeGenerateMemoised", "memoiser", "receiver", "file", "helper<PERSON><PERSON>", "availableHelper", "sequenceExpression", "console", "warn", "boundGet", "memberExpression", "destructureSet", "helper", "Error", "call", "args", "optimiseCall", "optionalCall", "privateNameHandlerLoose", "BASE", "REF", "PROP", "simpleSet", "optionalCallExpression", "transformPrivateNamesUsage", "ref", "size", "handler", "memberExpressionToFunctions", "buildPrivateFieldInitLoose", "buildUndefinedNode", "buildPrivateInstanceFieldInitSpec", "thisExpression", "buildPrivateStaticFieldInitSpec", "privateName", "initAdded", "buildPrivateMethodInitLoose", "buildPrivateInstanceMethodInitSpec", "buildPrivateAccessorInitialization", "buildPrivateInstanceMethodInitalization", "buildPublicFieldInitLoose", "computed", "expressionStatement", "assignmentExpression", "isLiteral", "buildPublicFieldInitSpec", "buildPrivateStaticMethodInitLoose", "buildPrivateMethodDeclaration", "getterDeclared", "setter<PERSON><PERSON><PERSON>ed", "params", "generator", "async", "isGetter", "isSetter", "declId", "functionDeclaration", "thisContextVisitor", "ThisExpression", "needsClassRef", "MetaProperty", "meta", "isIdentifier", "innerReferencesVisitor", "ReferencedIdentifier", "replaceThisContext", "getSuperRef", "isStaticBlock", "constant<PERSON>uper", "innerBindingRef", "replacer", "ReplaceSupers", "methodPath", "refToPreserve", "getObjectRef", "replace", "isNameOrLength", "type", "buildFieldsInitNodes", "superRef", "setPublicClassFields", "injectSuperRef", "staticNodes", "instanceNodes", "pureStaticNodes", "generateUidIdentifierBasedOnNode", "isClassProperty", "ts", "assertFieldTransformed", "isInstance", "isPublic", "isField", "replaced", "blockBody", "isExpressionStatement", "unshift", "filter", "Boolean", "wrapClass", "remove", "superClass", "isClassExpression"], "sources": ["../src/fields.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { NodePath, Visitor, Scope } from \"@babel/traverse\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\nimport memberExpressionToFunctions from \"@babel/helper-member-expression-to-functions\";\nimport type {\n  Hand<PERSON>,\n  HandlerState,\n} from \"@babel/helper-member-expression-to-functions\";\nimport optimiseCall from \"@babel/helper-optimise-call-expression\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport * as ts from \"./typescript\";\n\ninterface PrivateNameMetadata {\n  id: t.Identifier;\n  static: boolean;\n  method: boolean;\n  getId?: t.Identifier;\n  setId?: t.Identifier;\n  methodId?: t.Identifier;\n  initAdded?: boolean;\n  getterDeclared?: boolean;\n  setterDeclared?: boolean;\n}\n\ntype PrivateNamesMap = Map<string, PrivateNameMetadata>;\n\nexport function buildPrivateNamesMap(props: PropPath[]) {\n  const privateNamesMap: PrivateNamesMap = new Map();\n  for (const prop of props) {\n    if (prop.isPrivate()) {\n      const { name } = prop.node.key.id;\n      const update: PrivateNameMetadata = privateNamesMap.has(name)\n        ? privateNamesMap.get(name)\n        : {\n            id: prop.scope.generateUidIdentifier(name),\n            static: prop.node.static,\n            method: !prop.isProperty(),\n          };\n      if (prop.isClassPrivateMethod()) {\n        if (prop.node.kind === \"get\") {\n          update.getId = prop.scope.generateUidIdentifier(`get_${name}`);\n        } else if (prop.node.kind === \"set\") {\n          update.setId = prop.scope.generateUidIdentifier(`set_${name}`);\n        } else if (prop.node.kind === \"method\") {\n          update.methodId = prop.scope.generateUidIdentifier(name);\n        }\n      }\n      privateNamesMap.set(name, update);\n    }\n  }\n  return privateNamesMap;\n}\n\nexport function buildPrivateNamesNodes(\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsProperties: boolean,\n  state: File,\n) {\n  const initNodes: t.Statement[] = [];\n\n  for (const [name, value] of privateNamesMap) {\n    // When the privateFieldsAsProperties assumption is enabled,\n    // both static and instance fields are transpiled using a\n    // secret non-enumerable property. Hence, we also need to generate that\n    // key (using the classPrivateFieldLooseKey helper).\n    // In spec mode, only instance fields need a \"private name\" initializer\n    // because static fields are directly assigned to a variable in the\n    // buildPrivateStaticFieldInitSpec function.\n    const { static: isStatic, method: isMethod, getId, setId } = value;\n    const isAccessor = getId || setId;\n    const id = t.cloneNode(value.id);\n\n    let init: t.Expression;\n\n    if (privateFieldsAsProperties) {\n      init = t.callExpression(state.addHelper(\"classPrivateFieldLooseKey\"), [\n        t.stringLiteral(name),\n      ]);\n    } else if (!isStatic) {\n      init = t.newExpression(\n        t.identifier(!isMethod || isAccessor ? \"WeakMap\" : \"WeakSet\"),\n        [],\n      );\n    }\n\n    if (init) {\n      annotateAsPure(init);\n      initNodes.push(template.statement.ast`var ${id} = ${init}`);\n    }\n  }\n\n  return initNodes;\n}\n\ninterface PrivateNameVisitorState {\n  privateNamesMap: PrivateNamesMap;\n  privateFieldsAsProperties: boolean;\n  redeclared?: string[];\n}\n\n// Traverses the class scope, handling private name references. If an inner\n// class redeclares the same private name, it will hand off traversal to the\n// restricted visitor (which doesn't traverse the inner class's inner scope).\nfunction privateNameVisitorFactory<S>(\n  visitor: Visitor<PrivateNameVisitorState & S>,\n) {\n  const privateNameVisitor: Visitor<PrivateNameVisitorState & S> = {\n    ...visitor,\n\n    Class(path) {\n      const { privateNamesMap } = this;\n      const body = path.get(\"body.body\");\n\n      const visiblePrivateNames = new Map(privateNamesMap);\n      const redeclared = [];\n      for (const prop of body) {\n        if (!prop.isPrivate()) continue;\n        const { name } = prop.node.key.id;\n        visiblePrivateNames.delete(name);\n        redeclared.push(name);\n      }\n\n      // If the class doesn't redeclare any private fields, we can continue with\n      // our overall traversal.\n      if (!redeclared.length) {\n        return;\n      }\n\n      // This class redeclares some private field. We need to process the outer\n      // environment with access to all the outer privates, then we can process\n      // the inner environment with only the still-visible outer privates.\n      path.get(\"body\").traverse(nestedVisitor, {\n        ...this,\n        redeclared,\n      });\n      path.traverse(privateNameVisitor, {\n        ...this,\n        privateNamesMap: visiblePrivateNames,\n      });\n\n      // We'll eventually hit this class node again with the overall Class\n      // Features visitor, which'll process the redeclared privates.\n      path.skipKey(\"body\");\n    },\n  };\n\n  // Traverses the outer portion of a class, without touching the class's inner\n  // scope, for private names.\n  const nestedVisitor = traverse.visitors.merge([\n    {\n      ...visitor,\n    },\n    environmentVisitor,\n  ]);\n\n  return privateNameVisitor;\n}\n\ninterface PrivateNameState {\n  privateNamesMap: PrivateNamesMap;\n  classRef: t.Identifier;\n  file: File;\n  noDocumentAll: boolean;\n  innerBinding?: t.Identifier;\n}\n\nconst privateNameVisitor = privateNameVisitorFactory<\n  HandlerState<PrivateNameState> & PrivateNameState\n>({\n  PrivateName(path, { noDocumentAll }) {\n    const { privateNamesMap, redeclared } = this;\n    const { node, parentPath } = path;\n\n    if (\n      !parentPath.isMemberExpression({ property: node }) &&\n      !parentPath.isOptionalMemberExpression({ property: node })\n    ) {\n      return;\n    }\n    const { name } = node.id;\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared && redeclared.includes(name)) return;\n\n    this.handle(parentPath, noDocumentAll);\n  },\n});\n\n// rename all bindings that shadows innerBinding\nfunction unshadow(\n  name: string,\n  scope: Scope,\n  innerBinding: t.Identifier | undefined,\n) {\n  // in some cases, scope.getBinding(name) === undefined\n  // so we check hasBinding to avoid keeping looping\n  // see: https://github.com/babel/babel/pull/13656#discussion_r686030715\n  while (\n    scope?.hasBinding(name) &&\n    !scope.bindingIdentifierEquals(name, innerBinding)\n  ) {\n    scope.rename(name);\n    scope = scope.parent;\n  }\n}\n\nconst privateInVisitor = privateNameVisitorFactory<{\n  classRef: t.Identifier;\n  file: File;\n  innerBinding?: t.Identifier;\n}>({\n  BinaryExpression(path) {\n    const { operator, left, right } = path.node;\n    if (operator !== \"in\") return;\n    if (!t.isPrivateName(left)) return;\n\n    const { privateFieldsAsProperties, privateNamesMap, redeclared } = this;\n\n    const { name } = left.id;\n\n    if (!privateNamesMap.has(name)) return;\n    if (redeclared && redeclared.includes(name)) return;\n\n    // if there are any local variable shadowing classRef, unshadow it\n    // see #12960\n    unshadow(this.classRef.name, path.scope, this.innerBinding);\n\n    if (privateFieldsAsProperties) {\n      const { id } = privateNamesMap.get(name);\n      path.replaceWith(template.expression.ast`\n        Object.prototype.hasOwnProperty.call(${right}, ${t.cloneNode(id)})\n      `);\n      return;\n    }\n\n    const { id, static: isStatic } = privateNamesMap.get(name);\n\n    if (isStatic) {\n      path.replaceWith(template.expression.ast`${right} === ${this.classRef}`);\n      return;\n    }\n\n    path.replaceWith(template.expression.ast`${t.cloneNode(id)}.has(${right})`);\n  },\n});\n\ninterface Receiver {\n  receiver(\n    this: HandlerState<PrivateNameState> & PrivateNameState,\n    member: NodePath<t.MemberExpression | t.OptionalMemberExpression>,\n  ): t.Expression;\n}\n\nconst privateNameHandlerSpec: Handler<PrivateNameState & Receiver> & Receiver =\n  {\n    memoise(member, count) {\n      const { scope } = member;\n      const { object } = member.node as { object: t.Expression };\n\n      const memo = scope.maybeGenerateMemoised(object);\n      if (!memo) {\n        return;\n      }\n\n      this.memoiser.set(object, memo, count);\n    },\n\n    receiver(member) {\n      const { object } = member.node as { object: t.Expression };\n\n      if (this.memoiser.has(object)) {\n        return t.cloneNode(this.memoiser.get(object));\n      }\n\n      return t.cloneNode(object);\n    },\n\n    get(member) {\n      const { classRef, privateNamesMap, file, innerBinding } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        methodId,\n        getId,\n        setId,\n      } = privateNamesMap.get(name);\n      const isAccessor = getId || setId;\n\n      if (isStatic) {\n        const helperName =\n          isMethod && !isAccessor\n            ? \"classStaticPrivateMethodGet\"\n            : \"classStaticPrivateFieldSpecGet\";\n\n        // if there are any local variable shadowing classRef, unshadow it\n        // see #12960\n        unshadow(classRef.name, member.scope, innerBinding);\n\n        return t.callExpression(file.addHelper(helperName), [\n          this.receiver(member),\n          t.cloneNode(classRef),\n          t.cloneNode(id),\n        ]);\n      }\n\n      if (isMethod) {\n        if (isAccessor) {\n          if (!getId && setId) {\n            if (file.availableHelper(\"writeOnlyError\")) {\n              return t.sequenceExpression([\n                this.receiver(member),\n                t.callExpression(file.addHelper(\"writeOnlyError\"), [\n                  t.stringLiteral(`#${name}`),\n                ]),\n              ]);\n            }\n            console.warn(\n              `@babel/helpers is outdated, update it to silence this warning.`,\n            );\n          }\n          return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n          ]);\n        }\n        return t.callExpression(file.addHelper(\"classPrivateMethodGet\"), [\n          this.receiver(member),\n          t.cloneNode(id),\n          t.cloneNode(methodId),\n        ]);\n      }\n      return t.callExpression(file.addHelper(\"classPrivateFieldGet\"), [\n        this.receiver(member),\n        t.cloneNode(id),\n      ]);\n    },\n\n    boundGet(member) {\n      this.memoise(member, 1);\n\n      return t.callExpression(\n        t.memberExpression(this.get(member), t.identifier(\"bind\")),\n        [this.receiver(member)],\n      );\n    },\n\n    set(member, value) {\n      const { classRef, privateNamesMap, file } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const {\n        id,\n        static: isStatic,\n        method: isMethod,\n        setId,\n        getId,\n      } = privateNamesMap.get(name);\n      const isAccessor = getId || setId;\n\n      if (isStatic) {\n        const helperName =\n          isMethod && !isAccessor\n            ? \"classStaticPrivateMethodSet\"\n            : \"classStaticPrivateFieldSpecSet\";\n\n        return t.callExpression(file.addHelper(helperName), [\n          this.receiver(member),\n          t.cloneNode(classRef),\n          t.cloneNode(id),\n          value,\n        ]);\n      }\n      if (isMethod) {\n        if (setId) {\n          return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n            this.receiver(member),\n            t.cloneNode(id),\n            value,\n          ]);\n        }\n        return t.sequenceExpression([\n          this.receiver(member),\n          value,\n          t.callExpression(file.addHelper(\"readOnlyError\"), [\n            t.stringLiteral(`#${name}`),\n          ]),\n        ]);\n      }\n      return t.callExpression(file.addHelper(\"classPrivateFieldSet\"), [\n        this.receiver(member),\n        t.cloneNode(id),\n        value,\n      ]);\n    },\n\n    destructureSet(member) {\n      const { classRef, privateNamesMap, file } = this;\n      const { name } = (member.node.property as t.PrivateName).id;\n      const { id, static: isStatic } = privateNamesMap.get(name);\n      if (isStatic) {\n        try {\n          // classStaticPrivateFieldDestructureSet was introduced in 7.13.10\n          // eslint-disable-next-line no-var\n          var helper = file.addHelper(\"classStaticPrivateFieldDestructureSet\");\n        } catch {\n          throw new Error(\n            \"Babel can not transpile `[C.#p] = [0]` with @babel/helpers < 7.13.10, \\n\" +\n              \"please update @babel/helpers to the latest version.\",\n          );\n        }\n        return t.memberExpression(\n          t.callExpression(helper, [\n            this.receiver(member),\n            t.cloneNode(classRef),\n            t.cloneNode(id),\n          ]),\n          t.identifier(\"value\"),\n        );\n      }\n\n      return t.memberExpression(\n        t.callExpression(file.addHelper(\"classPrivateFieldDestructureSet\"), [\n          this.receiver(member),\n          t.cloneNode(id),\n        ]),\n        t.identifier(\"value\"),\n      );\n    },\n\n    call(member, args: (t.Expression | t.SpreadElement)[]) {\n      // The first access (the get) should do the memo assignment.\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, false);\n    },\n\n    optionalCall(member, args: (t.Expression | t.SpreadElement)[]) {\n      this.memoise(member, 1);\n\n      return optimiseCall(this.get(member), this.receiver(member), args, true);\n    },\n  };\n\nconst privateNameHandlerLoose: Handler<PrivateNameState> = {\n  get(member) {\n    const { privateNamesMap, file } = this;\n    const { object } = member.node;\n    const { name } = (member.node.property as t.PrivateName).id;\n\n    return template.expression`BASE(REF, PROP)[PROP]`({\n      BASE: file.addHelper(\"classPrivateFieldLooseBase\"),\n      REF: t.cloneNode(object),\n      PROP: t.cloneNode(privateNamesMap.get(name).id),\n    });\n  },\n\n  set() {\n    // noop\n    throw new Error(\"private name handler with loose = true don't need set()\");\n  },\n\n  boundGet(member) {\n    return t.callExpression(\n      t.memberExpression(this.get(member), t.identifier(\"bind\")),\n      [t.cloneNode(member.node.object as t.Expression)],\n    );\n  },\n\n  simpleSet(member) {\n    return this.get(member);\n  },\n\n  destructureSet(member) {\n    return this.get(member);\n  },\n\n  call(member, args) {\n    return t.callExpression(this.get(member), args);\n  },\n\n  optionalCall(member, args) {\n    return t.optionalCallExpression(this.get(member), args, true);\n  },\n};\n\nexport function transformPrivateNamesUsage(\n  ref: t.Identifier,\n  path: NodePath<t.Class>,\n  privateNamesMap: PrivateNamesMap,\n  {\n    privateFieldsAsProperties,\n    noDocumentAll,\n    innerBinding,\n  }: {\n    privateFieldsAsProperties: boolean;\n    noDocumentAll: boolean;\n    innerBinding: t.Identifier;\n  },\n  state: File,\n) {\n  if (!privateNamesMap.size) return;\n\n  const body = path.get(\"body\");\n  const handler = privateFieldsAsProperties\n    ? privateNameHandlerLoose\n    : privateNameHandlerSpec;\n\n  memberExpressionToFunctions<PrivateNameState>(body, privateNameVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    ...handler,\n    noDocumentAll,\n    innerBinding,\n  });\n  body.traverse(privateInVisitor, {\n    privateNamesMap,\n    classRef: ref,\n    file: state,\n    privateFieldsAsProperties,\n    innerBinding,\n  });\n}\n\nfunction buildPrivateFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return template.statement.ast`\n    Object.defineProperty(${ref}, ${t.cloneNode(id)}, {\n      // configurable is false by default\n      // enumerable is false by default\n      writable: true,\n      value: ${value}\n    });\n  `;\n}\n\nfunction buildPrivateInstanceFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const { id } = privateNamesMap.get(prop.node.key.id.name);\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return template.statement.ast`${t.cloneNode(id)}.set(${ref}, {\n        // configurable is always false for private elements\n        // enumerable is always false for private elements\n        writable: true,\n        value: ${value},\n      })`;\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return template.statement.ast`${helper}(\n    ${t.thisExpression()},\n    ${t.cloneNode(id)},\n    {\n      writable: true,\n      value: ${value}\n    },\n  )`;\n}\n\nfunction buildPrivateStaticFieldInitSpec(\n  prop: NodePath<t.ClassPrivateProperty>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, getId, setId, initAdded } = privateName;\n  const isAccessor = getId || setId;\n\n  if (!prop.isProperty() && (initAdded || !isAccessor)) return;\n\n  if (isAccessor) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return template.statement.ast`\n      var ${t.cloneNode(id)} = {\n        // configurable is false by default\n        // enumerable is false by default\n        // writable is false by default\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      }\n    `;\n  }\n\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n  return template.statement.ast`\n    var ${t.cloneNode(id)} = {\n      // configurable is false by default\n      // enumerable is false by default\n      writable: true,\n      value: ${value}\n    };\n  `;\n}\n\nfunction buildPrivateMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { methodId, id, getId, setId, initAdded } = privateName;\n  if (initAdded) return;\n\n  if (methodId) {\n    return template.statement.ast`\n        Object.defineProperty(${ref}, ${id}, {\n          // configurable is false by default\n          // enumerable is false by default\n          // writable is false by default\n          value: ${methodId.name}\n        });\n      `;\n  }\n  const isAccessor = getId || setId;\n  if (isAccessor) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return template.statement.ast`\n      Object.defineProperty(${ref}, ${id}, {\n        // configurable is false by default\n        // enumerable is false by default\n        // writable is false by default\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      });\n    `;\n  }\n}\n\nfunction buildPrivateInstanceMethodInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { getId, setId, initAdded } = privateName;\n\n  if (initAdded) return;\n\n  const isAccessor = getId || setId;\n  if (isAccessor) {\n    return buildPrivateAccessorInitialization(\n      ref,\n      prop,\n      privateNamesMap,\n      state,\n    );\n  }\n\n  return buildPrivateInstanceMethodInitalization(\n    ref,\n    prop,\n    privateNamesMap,\n    state,\n  );\n}\n\nfunction buildPrivateAccessorInitialization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, getId, setId } = privateName;\n\n  privateNamesMap.set(prop.node.key.id.name, {\n    ...privateName,\n    initAdded: true,\n  });\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateFieldInitSpec\")) {\n      return template.statement.ast`\n      ${id}.set(${ref}, {\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      });\n    `;\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateFieldInitSpec\");\n  return template.statement.ast`${helper}(\n    ${t.thisExpression()},\n    ${t.cloneNode(id)},\n    {\n      get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n      set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n    },\n  )`;\n}\n\nfunction buildPrivateInstanceMethodInitalization(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id } = privateName;\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (!state.availableHelper(\"classPrivateMethodInitSpec\")) {\n      return template.statement.ast`${id}.add(${ref})`;\n    }\n  }\n\n  const helper = state.addHelper(\"classPrivateMethodInitSpec\");\n  return template.statement.ast`${helper}(\n    ${t.thisExpression()},\n    ${t.cloneNode(id)}\n  )`;\n}\n\nfunction buildPublicFieldInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return t.expressionStatement(\n    t.assignmentExpression(\n      \"=\",\n      t.memberExpression(ref, key, computed || t.isLiteral(key)),\n      value,\n    ),\n  );\n}\n\nfunction buildPublicFieldInitSpec(\n  ref: t.Expression,\n  prop: NodePath<t.ClassProperty>,\n  state: File,\n) {\n  const { key, computed } = prop.node;\n  const value = prop.node.value || prop.scope.buildUndefinedNode();\n\n  return t.expressionStatement(\n    t.callExpression(state.addHelper(\"defineProperty\"), [\n      ref,\n      computed || t.isLiteral(key)\n        ? key\n        : t.stringLiteral((key as t.Identifier).name),\n      value,\n    ]),\n  );\n}\n\nfunction buildPrivateStaticMethodInitLoose(\n  ref: t.Expression,\n  prop: NodePath<t.ClassPrivateMethod>,\n  state: File,\n  privateNamesMap: PrivateNamesMap,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const { id, methodId, getId, setId, initAdded } = privateName;\n\n  if (initAdded) return;\n\n  const isAccessor = getId || setId;\n  if (isAccessor) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      initAdded: true,\n    });\n\n    return template.statement.ast`\n      Object.defineProperty(${ref}, ${id}, {\n        // configurable is false by default\n        // enumerable is false by default\n        // writable is false by default\n        get: ${getId ? getId.name : prop.scope.buildUndefinedNode()},\n        set: ${setId ? setId.name : prop.scope.buildUndefinedNode()}\n      })\n    `;\n  }\n\n  return template.statement.ast`\n    Object.defineProperty(${ref}, ${id}, {\n      // configurable is false by default\n      // enumerable is false by default\n      // writable is false by default\n      value: ${methodId.name}\n    });\n  `;\n}\n\nfunction buildPrivateMethodDeclaration(\n  prop: NodePath<t.ClassPrivateMethod>,\n  privateNamesMap: PrivateNamesMap,\n  privateFieldsAsProperties = false,\n) {\n  const privateName = privateNamesMap.get(prop.node.key.id.name);\n  const {\n    id,\n    methodId,\n    getId,\n    setId,\n    getterDeclared,\n    setterDeclared,\n    static: isStatic,\n  } = privateName;\n  const { params, body, generator, async } = prop.node;\n  const isGetter = getId && !getterDeclared && params.length === 0;\n  const isSetter = setId && !setterDeclared && params.length > 0;\n\n  let declId = methodId;\n\n  if (isGetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      getterDeclared: true,\n    });\n    declId = getId;\n  } else if (isSetter) {\n    privateNamesMap.set(prop.node.key.id.name, {\n      ...privateName,\n      setterDeclared: true,\n    });\n    declId = setId;\n  } else if (isStatic && !privateFieldsAsProperties) {\n    declId = id;\n  }\n\n  return t.functionDeclaration(\n    t.cloneNode(declId),\n    // @ts-expect-error params for ClassMethod has TSParameterProperty\n    params,\n    body,\n    generator,\n    async,\n  );\n}\n\ntype ReplaceThisState = {\n  classRef: t.Identifier;\n  needsClassRef: boolean;\n  innerBinding: t.Identifier | null;\n};\n\nconst thisContextVisitor = traverse.visitors.merge<ReplaceThisState>([\n  {\n    ThisExpression(path, state) {\n      state.needsClassRef = true;\n      path.replaceWith(t.cloneNode(state.classRef));\n    },\n    MetaProperty(path) {\n      const meta = path.get(\"meta\");\n      const property = path.get(\"property\");\n      const { scope } = path;\n      // if there are `new.target` in static field\n      // we should replace it with `undefined`\n      if (\n        meta.isIdentifier({ name: \"new\" }) &&\n        property.isIdentifier({ name: \"target\" })\n      ) {\n        path.replaceWith(scope.buildUndefinedNode());\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst innerReferencesVisitor: Visitor<ReplaceThisState> = {\n  ReferencedIdentifier(path, state) {\n    if (\n      path.scope.bindingIdentifierEquals(path.node.name, state.innerBinding)\n    ) {\n      state.needsClassRef = true;\n      path.node.name = state.classRef.name;\n    }\n  },\n};\n\nfunction replaceThisContext(\n  path: PropPath,\n  ref: t.Identifier,\n  getSuperRef: () => t.Identifier,\n  file: File,\n  isStaticBlock: boolean,\n  constantSuper: boolean,\n  innerBindingRef: t.Identifier | null,\n) {\n  const state: ReplaceThisState = {\n    classRef: ref,\n    needsClassRef: false,\n    innerBinding: innerBindingRef,\n  };\n\n  const replacer = new ReplaceSupers({\n    methodPath: path,\n    constantSuper,\n    file,\n    refToPreserve: ref,\n    getSuperRef,\n    getObjectRef() {\n      state.needsClassRef = true;\n      // @ts-expect-error: TS doesn't infer that path.node is not a StaticBlock\n      return t.isStaticBlock?.(path.node) || path.node.static\n        ? ref\n        : t.memberExpression(ref, t.identifier(\"prototype\"));\n    },\n  });\n  replacer.replace();\n  if (isStaticBlock || path.isProperty()) {\n    path.traverse(thisContextVisitor, state);\n  }\n\n  // todo: use innerBinding.referencePaths to avoid full traversal\n  if (\n    innerBindingRef != null &&\n    state.classRef?.name &&\n    state.classRef.name !== innerBindingRef?.name\n  ) {\n    path.traverse(innerReferencesVisitor, state);\n  }\n\n  return state.needsClassRef;\n}\n\nexport type PropNode =\n  | t.ClassProperty\n  | t.ClassPrivateMethod\n  | t.ClassPrivateProperty\n  | t.StaticBlock;\nexport type PropPath = NodePath<PropNode>;\n\nfunction isNameOrLength({ key, computed }: t.ClassProperty) {\n  if (key.type === \"Identifier\") {\n    return !computed && (key.name === \"name\" || key.name === \"length\");\n  }\n  if (key.type === \"StringLiteral\") {\n    return key.value === \"name\" || key.value === \"length\";\n  }\n  return false;\n}\n\nexport function buildFieldsInitNodes(\n  ref: t.Identifier,\n  superRef: t.Expression | undefined,\n  props: PropPath[],\n  privateNamesMap: PrivateNamesMap,\n  state: File,\n  setPublicClassFields: boolean,\n  privateFieldsAsProperties: boolean,\n  constantSuper: boolean,\n  innerBindingRef: t.Identifier,\n) {\n  let needsClassRef = false;\n  let injectSuperRef: t.Identifier;\n  const staticNodes: t.Statement[] = [];\n  const instanceNodes: t.Statement[] = [];\n  // These nodes are pure and can be moved to the closest statement position\n  const pureStaticNodes: t.FunctionDeclaration[] = [];\n\n  const getSuperRef = t.isIdentifier(superRef)\n    ? () => superRef\n    : () => {\n        injectSuperRef ??=\n          props[0].scope.generateUidIdentifierBasedOnNode(superRef);\n        return injectSuperRef;\n      };\n\n  for (const prop of props) {\n    prop.isClassProperty() && ts.assertFieldTransformed(prop);\n\n    // @ts-expect-error: TS doesn't infer that prop.node is not a StaticBlock\n    const isStatic = !t.isStaticBlock?.(prop.node) && prop.node.static;\n    const isInstance = !isStatic;\n    const isPrivate = prop.isPrivate();\n    const isPublic = !isPrivate;\n    const isField = prop.isProperty();\n    const isMethod = !isField;\n    const isStaticBlock = prop.isStaticBlock?.();\n\n    if (isStatic || (isMethod && isPrivate) || isStaticBlock) {\n      const replaced = replaceThisContext(\n        prop,\n        ref,\n        getSuperRef,\n        state,\n        isStaticBlock,\n        constantSuper,\n        innerBindingRef,\n      );\n      needsClassRef = needsClassRef || replaced;\n    }\n\n    // TODO(ts): there are so many `ts-expect-error` inside cases since\n    // ts can not infer type from pre-computed values (or a case test)\n    // even change `isStaticBlock` to `t.isStaticBlock(prop)` will not make prop\n    // a `NodePath<t.StaticBlock>`\n    // this maybe a bug for ts\n    switch (true) {\n      case isStaticBlock: {\n        const blockBody = (prop.node as t.StaticBlock).body;\n        // We special-case the single expression case to avoid the iife, since\n        // it's common.\n        if (blockBody.length === 1 && t.isExpressionStatement(blockBody[0])) {\n          staticNodes.push(blockBody[0] as t.ExpressionStatement);\n        } else {\n          staticNodes.push(template.statement.ast`(() => { ${blockBody} })()`);\n        }\n        break;\n      }\n      case isStatic && isPrivate && isField && privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPrivateFieldInitLoose(t.cloneNode(ref), prop, privateNamesMap),\n        );\n        break;\n      case isStatic && isPrivate && isField && !privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPrivateStaticFieldInitSpec(prop, privateNamesMap),\n        );\n        break;\n      case isStatic && isPublic && isField && setPublicClassFields:\n        // Functions always have non-writable .name and .length properties,\n        // so we must always use [[Define]] for them.\n        // It might still be possible to a computed static fields whose resulting\n        // key is \"name\" or \"length\", but the assumption is telling us that it's\n        // not going to happen.\n        // @ts-expect-error checked in switch\n        if (!isNameOrLength(prop.node)) {\n          needsClassRef = true;\n          // @ts-expect-error checked in switch\n          staticNodes.push(buildPublicFieldInitLoose(t.cloneNode(ref), prop));\n          break;\n        }\n      // falls through\n      case isStatic && isPublic && isField && !setPublicClassFields:\n        needsClassRef = true;\n        staticNodes.push(\n          // @ts-expect-error checked in switch\n          buildPublicFieldInitSpec(t.cloneNode(ref), prop, state),\n        );\n        break;\n      case isInstance && isPrivate && isField && privateFieldsAsProperties:\n        instanceNodes.push(\n          // @ts-expect-error checked in switch\n          buildPrivateFieldInitLoose(t.thisExpression(), prop, privateNamesMap),\n        );\n        break;\n      case isInstance && isPrivate && isField && !privateFieldsAsProperties:\n        instanceNodes.push(\n          buildPrivateInstanceFieldInitSpec(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            state,\n          ),\n        );\n        break;\n      case isInstance && isPrivate && isMethod && privateFieldsAsProperties:\n        instanceNodes.unshift(\n          buildPrivateMethodInitLoose(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isInstance && isPrivate && isMethod && !privateFieldsAsProperties:\n        instanceNodes.unshift(\n          buildPrivateInstanceMethodInitSpec(\n            t.thisExpression(),\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            state,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isStatic && isPrivate && isMethod && !privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.unshift(\n          // @ts-expect-error checked in switch\n          buildPrivateStaticFieldInitSpec(prop, privateNamesMap),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isStatic && isPrivate && isMethod && privateFieldsAsProperties:\n        needsClassRef = true;\n        staticNodes.unshift(\n          buildPrivateStaticMethodInitLoose(\n            t.cloneNode(ref),\n            // @ts-expect-error checked in switch\n            prop,\n            state,\n            privateNamesMap,\n          ),\n        );\n        pureStaticNodes.push(\n          buildPrivateMethodDeclaration(\n            // @ts-expect-error checked in switch\n            prop,\n            privateNamesMap,\n            privateFieldsAsProperties,\n          ),\n        );\n        break;\n      case isInstance && isPublic && isField && setPublicClassFields:\n        // @ts-expect-error checked in switch\n        instanceNodes.push(buildPublicFieldInitLoose(t.thisExpression(), prop));\n        break;\n      case isInstance && isPublic && isField && !setPublicClassFields:\n        instanceNodes.push(\n          // @ts-expect-error checked in switch\n          buildPublicFieldInitSpec(t.thisExpression(), prop, state),\n        );\n        break;\n      default:\n        throw new Error(\"Unreachable.\");\n    }\n  }\n\n  return {\n    staticNodes: staticNodes.filter(Boolean),\n    instanceNodes: instanceNodes.filter(Boolean),\n    pureStaticNodes: pureStaticNodes.filter(Boolean),\n    wrapClass(path: NodePath<t.Class>) {\n      for (const prop of props) {\n        prop.remove();\n      }\n\n      if (injectSuperRef) {\n        path.scope.push({ id: t.cloneNode(injectSuperRef) });\n        path.set(\n          \"superClass\",\n          t.assignmentExpression(\"=\", injectSuperRef, path.node.superClass),\n        );\n      }\n\n      if (!needsClassRef) return path;\n\n      if (path.isClassExpression()) {\n        path.scope.push({ id: ref });\n        path.replaceWith(\n          t.assignmentExpression(\"=\", t.cloneNode(ref), path.node),\n        );\n      } else if (!path.node.id) {\n        // Anonymous class declaration\n        path.node.id = ref;\n      }\n\n      return path;\n    },\n  };\n}\n"], "mappings": ";;;;;;;;;;AAAA;;AAGA;;AACA;;AACA;;AAKA;;AACA;;AAEA;;AAgBO,SAASA,oBAAT,CAA8BC,KAA9B,EAAiD;EACtD,MAAMC,eAAgC,GAAG,IAAIC,GAAJ,EAAzC;;EACA,KAAK,MAAMC,IAAX,IAAmBH,KAAnB,EAA0B;IACxB,IAAIG,IAAI,CAACC,SAAL,EAAJ,EAAsB;MACpB,MAAM;QAAEC;MAAF,IAAWF,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAA/B;MACA,MAAMC,MAA2B,GAAGR,eAAe,CAACS,GAAhB,CAAoBL,IAApB,IAChCJ,eAAe,CAACU,GAAhB,CAAoBN,IAApB,CADgC,GAEhC;QACEG,EAAE,EAAEL,IAAI,CAACS,KAAL,CAAWC,qBAAX,CAAiCR,IAAjC,CADN;QAEES,MAAM,EAAEX,IAAI,CAACG,IAAL,CAAUQ,MAFpB;QAGEC,MAAM,EAAE,CAACZ,IAAI,CAACa,UAAL;MAHX,CAFJ;;MAOA,IAAIb,IAAI,CAACc,oBAAL,EAAJ,EAAiC;QAC/B,IAAId,IAAI,CAACG,IAAL,CAAUY,IAAV,KAAmB,KAAvB,EAA8B;UAC5BT,MAAM,CAACU,KAAP,GAAehB,IAAI,CAACS,KAAL,CAAWC,qBAAX,CAAkC,OAAMR,IAAK,EAA7C,CAAf;QACD,CAFD,MAEO,IAAIF,IAAI,CAACG,IAAL,CAAUY,IAAV,KAAmB,KAAvB,EAA8B;UACnCT,MAAM,CAACW,KAAP,GAAejB,IAAI,CAACS,KAAL,CAAWC,qBAAX,CAAkC,OAAMR,IAAK,EAA7C,CAAf;QACD,CAFM,MAEA,IAAIF,IAAI,CAACG,IAAL,CAAUY,IAAV,KAAmB,QAAvB,EAAiC;UACtCT,MAAM,CAACY,QAAP,GAAkBlB,IAAI,CAACS,KAAL,CAAWC,qBAAX,CAAiCR,IAAjC,CAAlB;QACD;MACF;;MACDJ,eAAe,CAACqB,GAAhB,CAAoBjB,IAApB,EAA0BI,MAA1B;IACD;EACF;;EACD,OAAOR,eAAP;AACD;;AAEM,SAASsB,sBAAT,CACLtB,eADK,EAELuB,yBAFK,EAGLC,KAHK,EAIL;EACA,MAAMC,SAAwB,GAAG,EAAjC;;EAEA,KAAK,MAAM,CAACrB,IAAD,EAAOsB,KAAP,CAAX,IAA4B1B,eAA5B,EAA6C;IAQ3C,MAAM;MAAEa,MAAM,EAAEc,QAAV;MAAoBb,MAAM,EAAEc,QAA5B;MAAsCV,KAAtC;MAA6CC;IAA7C,IAAuDO,KAA7D;IACA,MAAMG,UAAU,GAAGX,KAAK,IAAIC,KAA5B;;IACA,MAAMZ,EAAE,GAAGuB,WAAA,CAAEC,SAAF,CAAYL,KAAK,CAACnB,EAAlB,CAAX;;IAEA,IAAIyB,IAAJ;;IAEA,IAAIT,yBAAJ,EAA+B;MAC7BS,IAAI,GAAGF,WAAA,CAAEG,cAAF,CAAiBT,KAAK,CAACU,SAAN,CAAgB,2BAAhB,CAAjB,EAA+D,CACpEJ,WAAA,CAAEK,aAAF,CAAgB/B,IAAhB,CADoE,CAA/D,CAAP;IAGD,CAJD,MAIO,IAAI,CAACuB,QAAL,EAAe;MACpBK,IAAI,GAAGF,WAAA,CAAEM,aAAF,CACLN,WAAA,CAAEO,UAAF,CAAa,CAACT,QAAD,IAAaC,UAAb,GAA0B,SAA1B,GAAsC,SAAnD,CADK,EAEL,EAFK,CAAP;IAID;;IAED,IAAIG,IAAJ,EAAU;MACR,IAAAM,6BAAA,EAAeN,IAAf;MACAP,SAAS,CAACc,IAAV,CAAeC,cAAA,CAASC,SAAT,CAAmBC,GAAI,OAAMnC,EAAG,MAAKyB,IAAK,EAAzD;IACD;EACF;;EAED,OAAOP,SAAP;AACD;;AAWD,SAASkB,yBAAT,CACEC,OADF,EAEE;EACA,MAAMC,kBAAwD,qBACzDD,OADyD;IAG5DE,KAAK,CAACC,IAAD,EAAO;MACV,MAAM;QAAE/C;MAAF,IAAsB,IAA5B;MACA,MAAMgD,IAAI,GAAGD,IAAI,CAACrC,GAAL,CAAS,WAAT,CAAb;MAEA,MAAMuC,mBAAmB,GAAG,IAAIhD,GAAJ,CAAQD,eAAR,CAA5B;MACA,MAAMkD,UAAU,GAAG,EAAnB;;MACA,KAAK,MAAMhD,IAAX,IAAmB8C,IAAnB,EAAyB;QACvB,IAAI,CAAC9C,IAAI,CAACC,SAAL,EAAL,EAAuB;QACvB,MAAM;UAAEC;QAAF,IAAWF,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAA/B;QACA0C,mBAAmB,CAACE,MAApB,CAA2B/C,IAA3B;QACA8C,UAAU,CAACX,IAAX,CAAgBnC,IAAhB;MACD;;MAID,IAAI,CAAC8C,UAAU,CAACE,MAAhB,EAAwB;QACtB;MACD;;MAKDL,IAAI,CAACrC,GAAL,CAAS,MAAT,EAAiB2C,QAAjB,CAA0BC,aAA1B,oBACK,IADL;QAEEJ;MAFF;MAIAH,IAAI,CAACM,QAAL,CAAcR,kBAAd,oBACK,IADL;QAEE7C,eAAe,EAAEiD;MAFnB;MAOAF,IAAI,CAACQ,OAAL,CAAa,MAAb;IACD;;EArC2D,EAA9D;;EA0CA,MAAMD,aAAa,GAAGD,cAAA,CAASG,QAAT,CAAkBC,KAAlB,CAAwB,mBAEvCb,OAFuC,GAI5Cc,iCAJ4C,CAAxB,CAAtB;;EAOA,OAAOb,kBAAP;AACD;;AAUD,MAAMA,kBAAkB,GAAGF,yBAAyB,CAElD;EACAgB,WAAW,CAACZ,IAAD,EAAO;IAAEa;EAAF,CAAP,EAA0B;IACnC,MAAM;MAAE5D,eAAF;MAAmBkD;IAAnB,IAAkC,IAAxC;IACA,MAAM;MAAE7C,IAAF;MAAQwD;IAAR,IAAuBd,IAA7B;;IAEA,IACE,CAACc,UAAU,CAACC,kBAAX,CAA8B;MAAEC,QAAQ,EAAE1D;IAAZ,CAA9B,CAAD,IACA,CAACwD,UAAU,CAACG,0BAAX,CAAsC;MAAED,QAAQ,EAAE1D;IAAZ,CAAtC,CAFH,EAGE;MACA;IACD;;IACD,MAAM;MAAED;IAAF,IAAWC,IAAI,CAACE,EAAtB;IACA,IAAI,CAACP,eAAe,CAACS,GAAhB,CAAoBL,IAApB,CAAL,EAAgC;IAChC,IAAI8C,UAAU,IAAIA,UAAU,CAACe,QAAX,CAAoB7D,IAApB,CAAlB,EAA6C;IAE7C,KAAK8D,MAAL,CAAYL,UAAZ,EAAwBD,aAAxB;EACD;;AAhBD,CAFkD,CAApD;;AAsBA,SAASO,QAAT,CACE/D,IADF,EAEEO,KAFF,EAGEyD,YAHF,EAIE;EAIA,OACE,UAAAzD,KAAK,SAAL,WAAO0D,UAAP,CAAkBjE,IAAlB,KACA,CAACO,KAAK,CAAC2D,uBAAN,CAA8BlE,IAA9B,EAAoCgE,YAApC,CAFH,EAGE;IAAA;;IACAzD,KAAK,CAAC4D,MAAN,CAAanE,IAAb;IACAO,KAAK,GAAGA,KAAK,CAAC6D,MAAd;EACD;AACF;;AAED,MAAMC,gBAAgB,GAAG9B,yBAAyB,CAI/C;EACD+B,gBAAgB,CAAC3B,IAAD,EAAO;IACrB,MAAM;MAAE4B,QAAF;MAAYC,IAAZ;MAAkBC;IAAlB,IAA4B9B,IAAI,CAAC1C,IAAvC;IACA,IAAIsE,QAAQ,KAAK,IAAjB,EAAuB;IACvB,IAAI,CAAC7C,WAAA,CAAEgD,aAAF,CAAgBF,IAAhB,CAAL,EAA4B;IAE5B,MAAM;MAAErD,yBAAF;MAA6BvB,eAA7B;MAA8CkD;IAA9C,IAA6D,IAAnE;IAEA,MAAM;MAAE9C;IAAF,IAAWwE,IAAI,CAACrE,EAAtB;IAEA,IAAI,CAACP,eAAe,CAACS,GAAhB,CAAoBL,IAApB,CAAL,EAAgC;IAChC,IAAI8C,UAAU,IAAIA,UAAU,CAACe,QAAX,CAAoB7D,IAApB,CAAlB,EAA6C;IAI7C+D,QAAQ,CAAC,KAAKY,QAAL,CAAc3E,IAAf,EAAqB2C,IAAI,CAACpC,KAA1B,EAAiC,KAAKyD,YAAtC,CAAR;;IAEA,IAAI7C,yBAAJ,EAA+B;MAC7B,MAAM;QAAEhB;MAAF,IAASP,eAAe,CAACU,GAAhB,CAAoBN,IAApB,CAAf;MACA2C,IAAI,CAACiC,WAAL,CAAiBxC,cAAA,CAASyC,UAAT,CAAoBvC,GAAI;AAC/C,+CAA+CmC,KAAM,KAAI/C,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AACzE,OAFM;MAGA;IACD;;IAED,MAAM;MAAEA,EAAF;MAAMM,MAAM,EAAEc;IAAd,IAA2B3B,eAAe,CAACU,GAAhB,CAAoBN,IAApB,CAAjC;;IAEA,IAAIuB,QAAJ,EAAc;MACZoB,IAAI,CAACiC,WAAL,CAAiBxC,cAAA,CAASyC,UAAT,CAAoBvC,GAAI,GAAEmC,KAAM,QAAO,KAAKE,QAAS,EAAtE;MACA;IACD;;IAEDhC,IAAI,CAACiC,WAAL,CAAiBxC,cAAA,CAASyC,UAAT,CAAoBvC,GAAI,GAAEZ,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB,QAAOsE,KAAM,GAAxE;EACD;;AAjCA,CAJ+C,CAAlD;AA+CA,MAAMK,sBAAuE,GAC3E;EACEC,OAAO,CAACC,MAAD,EAASC,KAAT,EAAgB;IACrB,MAAM;MAAE1E;IAAF,IAAYyE,MAAlB;IACA,MAAM;MAAEE;IAAF,IAAaF,MAAM,CAAC/E,IAA1B;IAEA,MAAMkF,IAAI,GAAG5E,KAAK,CAAC6E,qBAAN,CAA4BF,MAA5B,CAAb;;IACA,IAAI,CAACC,IAAL,EAAW;MACT;IACD;;IAED,KAAKE,QAAL,CAAcpE,GAAd,CAAkBiE,MAAlB,EAA0BC,IAA1B,EAAgCF,KAAhC;EACD,CAXH;;EAaEK,QAAQ,CAACN,MAAD,EAAS;IACf,MAAM;MAAEE;IAAF,IAAaF,MAAM,CAAC/E,IAA1B;;IAEA,IAAI,KAAKoF,QAAL,CAAchF,GAAd,CAAkB6E,MAAlB,CAAJ,EAA+B;MAC7B,OAAOxD,WAAA,CAAEC,SAAF,CAAY,KAAK0D,QAAL,CAAc/E,GAAd,CAAkB4E,MAAlB,CAAZ,CAAP;IACD;;IAED,OAAOxD,WAAA,CAAEC,SAAF,CAAYuD,MAAZ,CAAP;EACD,CArBH;;EAuBE5E,GAAG,CAAC0E,MAAD,EAAS;IACV,MAAM;MAAEL,QAAF;MAAY/E,eAAZ;MAA6B2F,IAA7B;MAAmCvB;IAAnC,IAAoD,IAA1D;IACA,MAAM;MAAEhE;IAAF,IAAYgF,MAAM,CAAC/E,IAAP,CAAY0D,QAAb,CAAwCxD,EAAzD;IACA,MAAM;MACJA,EADI;MAEJM,MAAM,EAAEc,QAFJ;MAGJb,MAAM,EAAEc,QAHJ;MAIJR,QAJI;MAKJF,KALI;MAMJC;IANI,IAOFnB,eAAe,CAACU,GAAhB,CAAoBN,IAApB,CAPJ;IAQA,MAAMyB,UAAU,GAAGX,KAAK,IAAIC,KAA5B;;IAEA,IAAIQ,QAAJ,EAAc;MACZ,MAAMiE,UAAU,GACdhE,QAAQ,IAAI,CAACC,UAAb,GACI,6BADJ,GAEI,gCAHN;MAOAsC,QAAQ,CAACY,QAAQ,CAAC3E,IAAV,EAAgBgF,MAAM,CAACzE,KAAvB,EAA8ByD,YAA9B,CAAR;MAEA,OAAOtC,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe0D,UAAf,CAAjB,EAA6C,CAClD,KAAKF,QAAL,CAAcN,MAAd,CADkD,EAElDtD,WAAA,CAAEC,SAAF,CAAYgD,QAAZ,CAFkD,EAGlDjD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAHkD,CAA7C,CAAP;IAKD;;IAED,IAAIqB,QAAJ,EAAc;MACZ,IAAIC,UAAJ,EAAgB;QACd,IAAI,CAACX,KAAD,IAAUC,KAAd,EAAqB;UACnB,IAAIwE,IAAI,CAACE,eAAL,CAAqB,gBAArB,CAAJ,EAA4C;YAC1C,OAAO/D,WAAA,CAAEgE,kBAAF,CAAqB,CAC1B,KAAKJ,QAAL,CAAcN,MAAd,CAD0B,EAE1BtD,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,gBAAf,CAAjB,EAAmD,CACjDJ,WAAA,CAAEK,aAAF,CAAiB,IAAG/B,IAAK,EAAzB,CADiD,CAAnD,CAF0B,CAArB,CAAP;UAMD;;UACD2F,OAAO,CAACC,IAAR,CACG,gEADH;QAGD;;QACD,OAAOlE,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,sBAAf,CAAjB,EAAyD,CAC9D,KAAKwD,QAAL,CAAcN,MAAd,CAD8D,EAE9DtD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAF8D,CAAzD,CAAP;MAID;;MACD,OAAOuB,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,uBAAf,CAAjB,EAA0D,CAC/D,KAAKwD,QAAL,CAAcN,MAAd,CAD+D,EAE/DtD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAF+D,EAG/DuB,WAAA,CAAEC,SAAF,CAAYX,QAAZ,CAH+D,CAA1D,CAAP;IAKD;;IACD,OAAOU,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,sBAAf,CAAjB,EAAyD,CAC9D,KAAKwD,QAAL,CAAcN,MAAd,CAD8D,EAE9DtD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAF8D,CAAzD,CAAP;EAID,CAnFH;;EAqFE0F,QAAQ,CAACb,MAAD,EAAS;IACf,KAAKD,OAAL,CAAaC,MAAb,EAAqB,CAArB;IAEA,OAAOtD,WAAA,CAAEG,cAAF,CACLH,WAAA,CAAEoE,gBAAF,CAAmB,KAAKxF,GAAL,CAAS0E,MAAT,CAAnB,EAAqCtD,WAAA,CAAEO,UAAF,CAAa,MAAb,CAArC,CADK,EAEL,CAAC,KAAKqD,QAAL,CAAcN,MAAd,CAAD,CAFK,CAAP;EAID,CA5FH;;EA8FE/D,GAAG,CAAC+D,MAAD,EAAS1D,KAAT,EAAgB;IACjB,MAAM;MAAEqD,QAAF;MAAY/E,eAAZ;MAA6B2F;IAA7B,IAAsC,IAA5C;IACA,MAAM;MAAEvF;IAAF,IAAYgF,MAAM,CAAC/E,IAAP,CAAY0D,QAAb,CAAwCxD,EAAzD;IACA,MAAM;MACJA,EADI;MAEJM,MAAM,EAAEc,QAFJ;MAGJb,MAAM,EAAEc,QAHJ;MAIJT,KAJI;MAKJD;IALI,IAMFlB,eAAe,CAACU,GAAhB,CAAoBN,IAApB,CANJ;IAOA,MAAMyB,UAAU,GAAGX,KAAK,IAAIC,KAA5B;;IAEA,IAAIQ,QAAJ,EAAc;MACZ,MAAMiE,UAAU,GACdhE,QAAQ,IAAI,CAACC,UAAb,GACI,6BADJ,GAEI,gCAHN;MAKA,OAAOC,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe0D,UAAf,CAAjB,EAA6C,CAClD,KAAKF,QAAL,CAAcN,MAAd,CADkD,EAElDtD,WAAA,CAAEC,SAAF,CAAYgD,QAAZ,CAFkD,EAGlDjD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAHkD,EAIlDmB,KAJkD,CAA7C,CAAP;IAMD;;IACD,IAAIE,QAAJ,EAAc;MACZ,IAAIT,KAAJ,EAAW;QACT,OAAOW,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,sBAAf,CAAjB,EAAyD,CAC9D,KAAKwD,QAAL,CAAcN,MAAd,CAD8D,EAE9DtD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAF8D,EAG9DmB,KAH8D,CAAzD,CAAP;MAKD;;MACD,OAAOI,WAAA,CAAEgE,kBAAF,CAAqB,CAC1B,KAAKJ,QAAL,CAAcN,MAAd,CAD0B,EAE1B1D,KAF0B,EAG1BI,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,eAAf,CAAjB,EAAkD,CAChDJ,WAAA,CAAEK,aAAF,CAAiB,IAAG/B,IAAK,EAAzB,CADgD,CAAlD,CAH0B,CAArB,CAAP;IAOD;;IACD,OAAO0B,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,sBAAf,CAAjB,EAAyD,CAC9D,KAAKwD,QAAL,CAAcN,MAAd,CAD8D,EAE9DtD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAF8D,EAG9DmB,KAH8D,CAAzD,CAAP;EAKD,CA5IH;;EA8IEyE,cAAc,CAACf,MAAD,EAAS;IACrB,MAAM;MAAEL,QAAF;MAAY/E,eAAZ;MAA6B2F;IAA7B,IAAsC,IAA5C;IACA,MAAM;MAAEvF;IAAF,IAAYgF,MAAM,CAAC/E,IAAP,CAAY0D,QAAb,CAAwCxD,EAAzD;IACA,MAAM;MAAEA,EAAF;MAAMM,MAAM,EAAEc;IAAd,IAA2B3B,eAAe,CAACU,GAAhB,CAAoBN,IAApB,CAAjC;;IACA,IAAIuB,QAAJ,EAAc;MACZ,IAAI;QAGF,IAAIyE,MAAM,GAAGT,IAAI,CAACzD,SAAL,CAAe,uCAAf,CAAb;MACD,CAJD,CAIE,gBAAM;QACN,MAAM,IAAImE,KAAJ,CACJ,6EACE,qDAFE,CAAN;MAID;;MACD,OAAOvE,WAAA,CAAEoE,gBAAF,CACLpE,WAAA,CAAEG,cAAF,CAAiBmE,MAAjB,EAAyB,CACvB,KAAKV,QAAL,CAAcN,MAAd,CADuB,EAEvBtD,WAAA,CAAEC,SAAF,CAAYgD,QAAZ,CAFuB,EAGvBjD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAHuB,CAAzB,CADK,EAMLuB,WAAA,CAAEO,UAAF,CAAa,OAAb,CANK,CAAP;IAQD;;IAED,OAAOP,WAAA,CAAEoE,gBAAF,CACLpE,WAAA,CAAEG,cAAF,CAAiB0D,IAAI,CAACzD,SAAL,CAAe,iCAAf,CAAjB,EAAoE,CAClE,KAAKwD,QAAL,CAAcN,MAAd,CADkE,EAElEtD,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAFkE,CAApE,CADK,EAKLuB,WAAA,CAAEO,UAAF,CAAa,OAAb,CALK,CAAP;EAOD,CA9KH;;EAgLEiE,IAAI,CAAClB,MAAD,EAASmB,IAAT,EAAmD;IAErD,KAAKpB,OAAL,CAAaC,MAAb,EAAqB,CAArB;IAEA,OAAO,IAAAoB,qCAAA,EAAa,KAAK9F,GAAL,CAAS0E,MAAT,CAAb,EAA+B,KAAKM,QAAL,CAAcN,MAAd,CAA/B,EAAsDmB,IAAtD,EAA4D,KAA5D,CAAP;EACD,CArLH;;EAuLEE,YAAY,CAACrB,MAAD,EAASmB,IAAT,EAAmD;IAC7D,KAAKpB,OAAL,CAAaC,MAAb,EAAqB,CAArB;IAEA,OAAO,IAAAoB,qCAAA,EAAa,KAAK9F,GAAL,CAAS0E,MAAT,CAAb,EAA+B,KAAKM,QAAL,CAAcN,MAAd,CAA/B,EAAsDmB,IAAtD,EAA4D,IAA5D,CAAP;EACD;;AA3LH,CADF;AA+LA,MAAMG,uBAAkD,GAAG;EACzDhG,GAAG,CAAC0E,MAAD,EAAS;IACV,MAAM;MAAEpF,eAAF;MAAmB2F;IAAnB,IAA4B,IAAlC;IACA,MAAM;MAAEL;IAAF,IAAaF,MAAM,CAAC/E,IAA1B;IACA,MAAM;MAAED;IAAF,IAAYgF,MAAM,CAAC/E,IAAP,CAAY0D,QAAb,CAAwCxD,EAAzD;IAEA,OAAOiC,cAAA,CAASyC,UAAW,uBAApB,CAA2C;MAChD0B,IAAI,EAAEhB,IAAI,CAACzD,SAAL,CAAe,4BAAf,CAD0C;MAEhD0E,GAAG,EAAE9E,WAAA,CAAEC,SAAF,CAAYuD,MAAZ,CAF2C;MAGhDuB,IAAI,EAAE/E,WAAA,CAAEC,SAAF,CAAY/B,eAAe,CAACU,GAAhB,CAAoBN,IAApB,EAA0BG,EAAtC;IAH0C,CAA3C,CAAP;EAKD,CAXwD;;EAazDc,GAAG,GAAG;IAEJ,MAAM,IAAIgF,KAAJ,CAAU,yDAAV,CAAN;EACD,CAhBwD;;EAkBzDJ,QAAQ,CAACb,MAAD,EAAS;IACf,OAAOtD,WAAA,CAAEG,cAAF,CACLH,WAAA,CAAEoE,gBAAF,CAAmB,KAAKxF,GAAL,CAAS0E,MAAT,CAAnB,EAAqCtD,WAAA,CAAEO,UAAF,CAAa,MAAb,CAArC,CADK,EAEL,CAACP,WAAA,CAAEC,SAAF,CAAYqD,MAAM,CAAC/E,IAAP,CAAYiF,MAAxB,CAAD,CAFK,CAAP;EAID,CAvBwD;;EAyBzDwB,SAAS,CAAC1B,MAAD,EAAS;IAChB,OAAO,KAAK1E,GAAL,CAAS0E,MAAT,CAAP;EACD,CA3BwD;;EA6BzDe,cAAc,CAACf,MAAD,EAAS;IACrB,OAAO,KAAK1E,GAAL,CAAS0E,MAAT,CAAP;EACD,CA/BwD;;EAiCzDkB,IAAI,CAAClB,MAAD,EAASmB,IAAT,EAAe;IACjB,OAAOzE,WAAA,CAAEG,cAAF,CAAiB,KAAKvB,GAAL,CAAS0E,MAAT,CAAjB,EAAmCmB,IAAnC,CAAP;EACD,CAnCwD;;EAqCzDE,YAAY,CAACrB,MAAD,EAASmB,IAAT,EAAe;IACzB,OAAOzE,WAAA,CAAEiF,sBAAF,CAAyB,KAAKrG,GAAL,CAAS0E,MAAT,CAAzB,EAA2CmB,IAA3C,EAAiD,IAAjD,CAAP;EACD;;AAvCwD,CAA3D;;AA0CO,SAASS,0BAAT,CACLC,GADK,EAELlE,IAFK,EAGL/C,eAHK,EAIL;EACEuB,yBADF;EAEEqC,aAFF;EAGEQ;AAHF,CAJK,EAaL5C,KAbK,EAcL;EACA,IAAI,CAACxB,eAAe,CAACkH,IAArB,EAA2B;EAE3B,MAAMlE,IAAI,GAAGD,IAAI,CAACrC,GAAL,CAAS,MAAT,CAAb;EACA,MAAMyG,OAAO,GAAG5F,yBAAyB,GACrCmF,uBADqC,GAErCxB,sBAFJ;EAIA,IAAAkC,0CAAA,EAA8CpE,IAA9C,EAAoDH,kBAApD;IACE7C,eADF;IAEE+E,QAAQ,EAAEkC,GAFZ;IAGEtB,IAAI,EAAEnE;EAHR,GAIK2F,OAJL;IAKEvD,aALF;IAMEQ;EANF;EAQApB,IAAI,CAACK,QAAL,CAAcoB,gBAAd,EAAgC;IAC9BzE,eAD8B;IAE9B+E,QAAQ,EAAEkC,GAFoB;IAG9BtB,IAAI,EAAEnE,KAHwB;IAI9BD,yBAJ8B;IAK9B6C;EAL8B,CAAhC;AAOD;;AAED,SAASiD,0BAAT,CACEJ,GADF,EAEE/G,IAFF,EAGEF,eAHF,EAIE;EACA,MAAM;IAAEO;EAAF,IAASP,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAAf;EACA,MAAMsB,KAAK,GAAGxB,IAAI,CAACG,IAAL,CAAUqB,KAAV,IAAmBxB,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAjC;EAEA,OAAO9E,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAChC,4BAA4BuE,GAAI,KAAInF,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AACpD;AACA;AACA;AACA,eAAemB,KAAM;AACrB;AACA,GAPE;AAQD;;AAED,SAAS6F,iCAAT,CACEN,GADF,EAEE/G,IAFF,EAGEF,eAHF,EAIEwB,KAJF,EAKE;EACA,MAAM;IAAEjB;EAAF,IAASP,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAAf;EACA,MAAMsB,KAAK,GAAGxB,IAAI,CAACG,IAAL,CAAUqB,KAAV,IAAmBxB,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAjC;EAEmC;IACjC,IAAI,CAAC9F,KAAK,CAACqE,eAAN,CAAsB,2BAAtB,CAAL,EAAyD;MACvD,OAAOrD,cAAA,CAASC,SAAT,CAAmBC,GAAI,GAAEZ,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB,QAAO0G,GAAI;AACjE;AACA;AACA;AACA,iBAAiBvF,KAAM;AACvB,SALM;IAMD;EACF;EAED,MAAM0E,MAAM,GAAG5E,KAAK,CAACU,SAAN,CAAgB,2BAAhB,CAAf;EACA,OAAOM,cAAA,CAASC,SAAT,CAAmBC,GAAI,GAAE0D,MAAO;AACzC,MAAMtE,WAAA,CAAE0F,cAAF,EAAmB;AACzB,MAAM1F,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AACtB;AACA;AACA,eAAemB,KAAM;AACrB;AACA,IAPE;AAQD;;AAED,SAAS+F,+BAAT,CACEvH,IADF,EAEEF,eAFF,EAGE;EACA,MAAM0H,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IAAEG,EAAF;IAAMW,KAAN;IAAaC,KAAb;IAAoBwG;EAApB,IAAkCD,WAAxC;EACA,MAAM7F,UAAU,GAAGX,KAAK,IAAIC,KAA5B;EAEA,IAAI,CAACjB,IAAI,CAACa,UAAL,EAAD,KAAuB4G,SAAS,IAAI,CAAC9F,UAArC,CAAJ,EAAsD;;EAEtD,IAAIA,UAAJ,EAAgB;IACd7B,eAAe,CAACqB,GAAhB,CAAoBnB,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,oBACKsH,WADL;MAEEC,SAAS,EAAE;IAFb;IAKA,OAAOnF,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAClC,YAAYZ,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AAC5B;AACA;AACA;AACA,eAAeW,KAAK,GAAGA,KAAK,CAACd,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE,eAAenG,KAAK,GAAGA,KAAK,CAACf,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE;AACA,KARI;EASD;;EAED,MAAM5F,KAAK,GAAGxB,IAAI,CAACG,IAAL,CAAUqB,KAAV,IAAmBxB,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAjC;EACA,OAAO9E,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAChC,UAAUZ,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AAC1B;AACA;AACA;AACA,eAAemB,KAAM;AACrB;AACA,GAPE;AAQD;;AAED,SAASkG,2BAAT,CACEX,GADF,EAEE/G,IAFF,EAGEF,eAHF,EAIE;EACA,MAAM0H,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IAAEgB,QAAF;IAAYb,EAAZ;IAAgBW,KAAhB;IAAuBC,KAAvB;IAA8BwG;EAA9B,IAA4CD,WAAlD;EACA,IAAIC,SAAJ,EAAe;;EAEf,IAAIvG,QAAJ,EAAc;IACZ,OAAOoB,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAClC,gCAAgCuE,GAAI,KAAI1G,EAAG;AAC3C;AACA;AACA;AACA,mBAAmBa,QAAQ,CAAChB,IAAK;AACjC;AACA,OAPI;EAQD;;EACD,MAAMyB,UAAU,GAAGX,KAAK,IAAIC,KAA5B;;EACA,IAAIU,UAAJ,EAAgB;IACd7B,eAAe,CAACqB,GAAhB,CAAoBnB,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,oBACKsH,WADL;MAEEC,SAAS,EAAE;IAFb;IAKA,OAAOnF,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAClC,8BAA8BuE,GAAI,KAAI1G,EAAG;AACzC;AACA;AACA;AACA,eAAeW,KAAK,GAAGA,KAAK,CAACd,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE,eAAenG,KAAK,GAAGA,KAAK,CAACf,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE;AACA,KARI;EASD;AACF;;AAED,SAASO,kCAAT,CACEZ,GADF,EAEE/G,IAFF,EAGEF,eAHF,EAIEwB,KAJF,EAKE;EACA,MAAMkG,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IAAEc,KAAF;IAASC,KAAT;IAAgBwG;EAAhB,IAA8BD,WAApC;EAEA,IAAIC,SAAJ,EAAe;EAEf,MAAM9F,UAAU,GAAGX,KAAK,IAAIC,KAA5B;;EACA,IAAIU,UAAJ,EAAgB;IACd,OAAOiG,kCAAkC,CACvCb,GADuC,EAEvC/G,IAFuC,EAGvCF,eAHuC,EAIvCwB,KAJuC,CAAzC;EAMD;;EAED,OAAOuG,uCAAuC,CAC5Cd,GAD4C,EAE5C/G,IAF4C,EAG5CF,eAH4C,EAI5CwB,KAJ4C,CAA9C;AAMD;;AAED,SAASsG,kCAAT,CACEb,GADF,EAEE/G,IAFF,EAGEF,eAHF,EAIEwB,KAJF,EAKE;EACA,MAAMkG,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IAAEG,EAAF;IAAMW,KAAN;IAAaC;EAAb,IAAuBuG,WAA7B;EAEA1H,eAAe,CAACqB,GAAhB,CAAoBnB,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,oBACKsH,WADL;IAEEC,SAAS,EAAE;EAFb;EAKmC;IACjC,IAAI,CAACnG,KAAK,CAACqE,eAAN,CAAsB,2BAAtB,CAAL,EAAyD;MACvD,OAAOrD,cAAA,CAASC,SAAT,CAAmBC,GAAI;AACpC,QAAQnC,EAAG,QAAO0G,GAAI;AACtB,eAAe/F,KAAK,GAAGA,KAAK,CAACd,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE,eAAenG,KAAK,GAAGA,KAAK,CAACf,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE;AACA,KALM;IAMD;EACF;EAED,MAAMlB,MAAM,GAAG5E,KAAK,CAACU,SAAN,CAAgB,2BAAhB,CAAf;EACA,OAAOM,cAAA,CAASC,SAAT,CAAmBC,GAAI,GAAE0D,MAAO;AACzC,MAAMtE,WAAA,CAAE0F,cAAF,EAAmB;AACzB,MAAM1F,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AACtB;AACA,aAAaW,KAAK,GAAGA,KAAK,CAACd,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AAClE,aAAanG,KAAK,GAAGA,KAAK,CAACf,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AAClE;AACA,IAPE;AAQD;;AAED,SAASS,uCAAT,CACEd,GADF,EAEE/G,IAFF,EAGEF,eAHF,EAIEwB,KAJF,EAKE;EACA,MAAMkG,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IAAEG;EAAF,IAASmH,WAAf;EAEmC;IACjC,IAAI,CAAClG,KAAK,CAACqE,eAAN,CAAsB,4BAAtB,CAAL,EAA0D;MACxD,OAAOrD,cAAA,CAASC,SAAT,CAAmBC,GAAI,GAAEnC,EAAG,QAAO0G,GAAI,GAA9C;IACD;EACF;EAED,MAAMb,MAAM,GAAG5E,KAAK,CAACU,SAAN,CAAgB,4BAAhB,CAAf;EACA,OAAOM,cAAA,CAASC,SAAT,CAAmBC,GAAI,GAAE0D,MAAO;AACzC,MAAMtE,WAAA,CAAE0F,cAAF,EAAmB;AACzB,MAAM1F,WAAA,CAAEC,SAAF,CAAYxB,EAAZ,CAAgB;AACtB,IAHE;AAID;;AAED,SAASyH,yBAAT,CACEf,GADF,EAEE/G,IAFF,EAGE;EACA,MAAM;IAAEI,GAAF;IAAO2H;EAAP,IAAoB/H,IAAI,CAACG,IAA/B;EACA,MAAMqB,KAAK,GAAGxB,IAAI,CAACG,IAAL,CAAUqB,KAAV,IAAmBxB,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAjC;EAEA,OAAOxF,WAAA,CAAEoG,mBAAF,CACLpG,WAAA,CAAEqG,oBAAF,CACE,GADF,EAEErG,WAAA,CAAEoE,gBAAF,CAAmBe,GAAnB,EAAwB3G,GAAxB,EAA6B2H,QAAQ,IAAInG,WAAA,CAAEsG,SAAF,CAAY9H,GAAZ,CAAzC,CAFF,EAGEoB,KAHF,CADK,CAAP;AAOD;;AAED,SAAS2G,wBAAT,CACEpB,GADF,EAEE/G,IAFF,EAGEsB,KAHF,EAIE;EACA,MAAM;IAAElB,GAAF;IAAO2H;EAAP,IAAoB/H,IAAI,CAACG,IAA/B;EACA,MAAMqB,KAAK,GAAGxB,IAAI,CAACG,IAAL,CAAUqB,KAAV,IAAmBxB,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAjC;EAEA,OAAOxF,WAAA,CAAEoG,mBAAF,CACLpG,WAAA,CAAEG,cAAF,CAAiBT,KAAK,CAACU,SAAN,CAAgB,gBAAhB,CAAjB,EAAoD,CAClD+E,GADkD,EAElDgB,QAAQ,IAAInG,WAAA,CAAEsG,SAAF,CAAY9H,GAAZ,CAAZ,GACIA,GADJ,GAEIwB,WAAA,CAAEK,aAAF,CAAiB7B,GAAD,CAAsBF,IAAtC,CAJ8C,EAKlDsB,KALkD,CAApD,CADK,CAAP;AASD;;AAED,SAAS4G,iCAAT,CACErB,GADF,EAEE/G,IAFF,EAGEsB,KAHF,EAIExB,eAJF,EAKE;EACA,MAAM0H,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IAAEG,EAAF;IAAMa,QAAN;IAAgBF,KAAhB;IAAuBC,KAAvB;IAA8BwG;EAA9B,IAA4CD,WAAlD;EAEA,IAAIC,SAAJ,EAAe;EAEf,MAAM9F,UAAU,GAAGX,KAAK,IAAIC,KAA5B;;EACA,IAAIU,UAAJ,EAAgB;IACd7B,eAAe,CAACqB,GAAhB,CAAoBnB,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,oBACKsH,WADL;MAEEC,SAAS,EAAE;IAFb;IAKA,OAAOnF,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAClC,8BAA8BuE,GAAI,KAAI1G,EAAG;AACzC;AACA;AACA;AACA,eAAeW,KAAK,GAAGA,KAAK,CAACd,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE,eAAenG,KAAK,GAAGA,KAAK,CAACf,IAAT,GAAgBF,IAAI,CAACS,KAAL,CAAW2G,kBAAX,EAAgC;AACpE;AACA,KARI;EASD;;EAED,OAAO9E,cAAA,CAASC,SAAT,CAAmBC,GAAI;AAChC,4BAA4BuE,GAAI,KAAI1G,EAAG;AACvC;AACA;AACA;AACA,eAAea,QAAQ,CAAChB,IAAK;AAC7B;AACA,GAPE;AAQD;;AAED,SAASmI,6BAAT,CACErI,IADF,EAEEF,eAFF,EAGEuB,yBAAyB,GAAG,KAH9B,EAIE;EACA,MAAMmG,WAAW,GAAG1H,eAAe,CAACU,GAAhB,CAAoBR,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,CAApB;EACA,MAAM;IACJG,EADI;IAEJa,QAFI;IAGJF,KAHI;IAIJC,KAJI;IAKJqH,cALI;IAMJC,cANI;IAOJ5H,MAAM,EAAEc;EAPJ,IAQF+F,WARJ;EASA,MAAM;IAAEgB,MAAF;IAAU1F,IAAV;IAAgB2F,SAAhB;IAA2BC;EAA3B,IAAqC1I,IAAI,CAACG,IAAhD;EACA,MAAMwI,QAAQ,GAAG3H,KAAK,IAAI,CAACsH,cAAV,IAA4BE,MAAM,CAACtF,MAAP,KAAkB,CAA/D;EACA,MAAM0F,QAAQ,GAAG3H,KAAK,IAAI,CAACsH,cAAV,IAA4BC,MAAM,CAACtF,MAAP,GAAgB,CAA7D;EAEA,IAAI2F,MAAM,GAAG3H,QAAb;;EAEA,IAAIyH,QAAJ,EAAc;IACZ7I,eAAe,CAACqB,GAAhB,CAAoBnB,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,oBACKsH,WADL;MAEEc,cAAc,EAAE;IAFlB;IAIAO,MAAM,GAAG7H,KAAT;EACD,CAND,MAMO,IAAI4H,QAAJ,EAAc;IACnB9I,eAAe,CAACqB,GAAhB,CAAoBnB,IAAI,CAACG,IAAL,CAAUC,GAAV,CAAcC,EAAd,CAAiBH,IAArC,oBACKsH,WADL;MAEEe,cAAc,EAAE;IAFlB;IAIAM,MAAM,GAAG5H,KAAT;EACD,CANM,MAMA,IAAIQ,QAAQ,IAAI,CAACJ,yBAAjB,EAA4C;IACjDwH,MAAM,GAAGxI,EAAT;EACD;;EAED,OAAOuB,WAAA,CAAEkH,mBAAF,CACLlH,WAAA,CAAEC,SAAF,CAAYgH,MAAZ,CADK,EAGLL,MAHK,EAIL1F,IAJK,EAKL2F,SALK,EAMLC,KANK,CAAP;AAQD;;AAQD,MAAMK,kBAAkB,GAAG5F,cAAA,CAASG,QAAT,CAAkBC,KAAlB,CAA0C,CACnE;EACEyF,cAAc,CAACnG,IAAD,EAAOvB,KAAP,EAAc;IAC1BA,KAAK,CAAC2H,aAAN,GAAsB,IAAtB;IACApG,IAAI,CAACiC,WAAL,CAAiBlD,WAAA,CAAEC,SAAF,CAAYP,KAAK,CAACuD,QAAlB,CAAjB;EACD,CAJH;;EAKEqE,YAAY,CAACrG,IAAD,EAAO;IACjB,MAAMsG,IAAI,GAAGtG,IAAI,CAACrC,GAAL,CAAS,MAAT,CAAb;IACA,MAAMqD,QAAQ,GAAGhB,IAAI,CAACrC,GAAL,CAAS,UAAT,CAAjB;IACA,MAAM;MAAEC;IAAF,IAAYoC,IAAlB;;IAGA,IACEsG,IAAI,CAACC,YAAL,CAAkB;MAAElJ,IAAI,EAAE;IAAR,CAAlB,KACA2D,QAAQ,CAACuF,YAAT,CAAsB;MAAElJ,IAAI,EAAE;IAAR,CAAtB,CAFF,EAGE;MACA2C,IAAI,CAACiC,WAAL,CAAiBrE,KAAK,CAAC2G,kBAAN,EAAjB;IACD;EACF;;AAjBH,CADmE,EAoBnE5D,iCApBmE,CAA1C,CAA3B;;AAuBA,MAAM6F,sBAAiD,GAAG;EACxDC,oBAAoB,CAACzG,IAAD,EAAOvB,KAAP,EAAc;IAChC,IACEuB,IAAI,CAACpC,KAAL,CAAW2D,uBAAX,CAAmCvB,IAAI,CAAC1C,IAAL,CAAUD,IAA7C,EAAmDoB,KAAK,CAAC4C,YAAzD,CADF,EAEE;MACA5C,KAAK,CAAC2H,aAAN,GAAsB,IAAtB;MACApG,IAAI,CAAC1C,IAAL,CAAUD,IAAV,GAAiBoB,KAAK,CAACuD,QAAN,CAAe3E,IAAhC;IACD;EACF;;AARuD,CAA1D;;AAWA,SAASqJ,kBAAT,CACE1G,IADF,EAEEkE,GAFF,EAGEyC,WAHF,EAIE/D,IAJF,EAKEgE,aALF,EAMEC,aANF,EAOEC,eAPF,EAQE;EAAA;;EACA,MAAMrI,KAAuB,GAAG;IAC9BuD,QAAQ,EAAEkC,GADoB;IAE9BkC,aAAa,EAAE,KAFe;IAG9B/E,YAAY,EAAEyF;EAHgB,CAAhC;EAMA,MAAMC,QAAQ,GAAG,IAAIC,4BAAJ,CAAkB;IACjCC,UAAU,EAAEjH,IADqB;IAEjC6G,aAFiC;IAGjCjE,IAHiC;IAIjCsE,aAAa,EAAEhD,GAJkB;IAKjCyC,WALiC;;IAMjCQ,YAAY,GAAG;MACb1I,KAAK,CAAC2H,aAAN,GAAsB,IAAtB;MAEA,OAAOrH,WAAA,CAAE6H,aAAF,YAAA7H,WAAA,CAAE6H,aAAF,CAAkB5G,IAAI,CAAC1C,IAAvB,KAAgC0C,IAAI,CAAC1C,IAAL,CAAUQ,MAA1C,GACHoG,GADG,GAEHnF,WAAA,CAAEoE,gBAAF,CAAmBe,GAAnB,EAAwBnF,WAAA,CAAEO,UAAF,CAAa,WAAb,CAAxB,CAFJ;IAGD;;EAZgC,CAAlB,CAAjB;EAcAyH,QAAQ,CAACK,OAAT;;EACA,IAAIR,aAAa,IAAI5G,IAAI,CAAChC,UAAL,EAArB,EAAwC;IACtCgC,IAAI,CAACM,QAAL,CAAc4F,kBAAd,EAAkCzH,KAAlC;EACD;;EAGD,IACEqI,eAAe,IAAI,IAAnB,uBACArI,KAAK,CAACuD,QADN,aACA,gBAAgB3E,IADhB,IAEAoB,KAAK,CAACuD,QAAN,CAAe3E,IAAf,MAAwByJ,eAAxB,oBAAwBA,eAAe,CAAEzJ,IAAzC,CAHF,EAIE;IACA2C,IAAI,CAACM,QAAL,CAAckG,sBAAd,EAAsC/H,KAAtC;EACD;;EAED,OAAOA,KAAK,CAAC2H,aAAb;AACD;;AASD,SAASiB,cAAT,CAAwB;EAAE9J,GAAF;EAAO2H;AAAP,CAAxB,EAA4D;EAC1D,IAAI3H,GAAG,CAAC+J,IAAJ,KAAa,YAAjB,EAA+B;IAC7B,OAAO,CAACpC,QAAD,KAAc3H,GAAG,CAACF,IAAJ,KAAa,MAAb,IAAuBE,GAAG,CAACF,IAAJ,KAAa,QAAlD,CAAP;EACD;;EACD,IAAIE,GAAG,CAAC+J,IAAJ,KAAa,eAAjB,EAAkC;IAChC,OAAO/J,GAAG,CAACoB,KAAJ,KAAc,MAAd,IAAwBpB,GAAG,CAACoB,KAAJ,KAAc,QAA7C;EACD;;EACD,OAAO,KAAP;AACD;;AAEM,SAAS4I,oBAAT,CACLrD,GADK,EAELsD,QAFK,EAGLxK,KAHK,EAILC,eAJK,EAKLwB,KALK,EAMLgJ,oBANK,EAOLjJ,yBAPK,EAQLqI,aARK,EASLC,eATK,EAUL;EACA,IAAIV,aAAa,GAAG,KAApB;EACA,IAAIsB,cAAJ;EACA,MAAMC,WAA0B,GAAG,EAAnC;EACA,MAAMC,aAA4B,GAAG,EAArC;EAEA,MAAMC,eAAwC,GAAG,EAAjD;EAEA,MAAMlB,WAAW,GAAG5H,WAAA,CAAEwH,YAAF,CAAeiB,QAAf,IAChB,MAAMA,QADU,GAEhB,MAAM;IAAA;;IACJ,mBAAAE,cAAc,SAAd,qBAAAA,cAAc,GACZ1K,KAAK,CAAC,CAAD,CAAL,CAASY,KAAT,CAAekK,gCAAf,CAAgDN,QAAhD,CADF;IAEA,OAAOE,cAAP;EACD,CANL;;EAQA,KAAK,MAAMvK,IAAX,IAAmBH,KAAnB,EAA0B;IACxBG,IAAI,CAAC4K,eAAL,MAA0BC,EAAE,CAACC,sBAAH,CAA0B9K,IAA1B,CAA1B;IAGA,MAAMyB,QAAQ,GAAG,EAACG,WAAA,CAAE6H,aAAH,YAAC7H,WAAA,CAAE6H,aAAF,CAAkBzJ,IAAI,CAACG,IAAvB,CAAD,KAAiCH,IAAI,CAACG,IAAL,CAAUQ,MAA5D;IACA,MAAMoK,UAAU,GAAG,CAACtJ,QAApB;IACA,MAAMxB,SAAS,GAAGD,IAAI,CAACC,SAAL,EAAlB;IACA,MAAM+K,QAAQ,GAAG,CAAC/K,SAAlB;IACA,MAAMgL,OAAO,GAAGjL,IAAI,CAACa,UAAL,EAAhB;IACA,MAAMa,QAAQ,GAAG,CAACuJ,OAAlB;IACA,MAAMxB,aAAa,GAAGzJ,IAAI,CAACyJ,aAAR,oBAAGzJ,IAAI,CAACyJ,aAAL,EAAtB;;IAEA,IAAIhI,QAAQ,IAAKC,QAAQ,IAAIzB,SAAzB,IAAuCwJ,aAA3C,EAA0D;MACxD,MAAMyB,QAAQ,GAAG3B,kBAAkB,CACjCvJ,IADiC,EAEjC+G,GAFiC,EAGjCyC,WAHiC,EAIjClI,KAJiC,EAKjCmI,aALiC,EAMjCC,aANiC,EAOjCC,eAPiC,CAAnC;MASAV,aAAa,GAAGA,aAAa,IAAIiC,QAAjC;IACD;;IAOD,QAAQ,IAAR;MACE,KAAKzB,aAAL;QAAoB;UAClB,MAAM0B,SAAS,GAAInL,IAAI,CAACG,IAAN,CAA6B2C,IAA/C;;UAGA,IAAIqI,SAAS,CAACjI,MAAV,KAAqB,CAArB,IAA0BtB,WAAA,CAAEwJ,qBAAF,CAAwBD,SAAS,CAAC,CAAD,CAAjC,CAA9B,EAAqE;YACnEX,WAAW,CAACnI,IAAZ,CAAiB8I,SAAS,CAAC,CAAD,CAA1B;UACD,CAFD,MAEO;YACLX,WAAW,CAACnI,IAAZ,CAAiBC,cAAA,CAASC,SAAT,CAAmBC,GAAI,YAAW2I,SAAU,OAA7D;UACD;;UACD;QACD;;MACD,KAAK1J,QAAQ,IAAIxB,SAAZ,IAAyBgL,OAAzB,IAAoC5J,yBAAzC;QACE4H,aAAa,GAAG,IAAhB;QACAuB,WAAW,CAACnI,IAAZ,CAEE8E,0BAA0B,CAACvF,WAAA,CAAEC,SAAF,CAAYkF,GAAZ,CAAD,EAAmB/G,IAAnB,EAAyBF,eAAzB,CAF5B;QAIA;;MACF,KAAK2B,QAAQ,IAAIxB,SAAZ,IAAyBgL,OAAzB,IAAoC,CAAC5J,yBAA1C;QACE4H,aAAa,GAAG,IAAhB;QACAuB,WAAW,CAACnI,IAAZ,CAEEkF,+BAA+B,CAACvH,IAAD,EAAOF,eAAP,CAFjC;QAIA;;MACF,KAAK2B,QAAQ,IAAIuJ,QAAZ,IAAwBC,OAAxB,IAAmCX,oBAAxC;QAOE,IAAI,CAACJ,cAAc,CAAClK,IAAI,CAACG,IAAN,CAAnB,EAAgC;UAC9B8I,aAAa,GAAG,IAAhB;UAEAuB,WAAW,CAACnI,IAAZ,CAAiByF,yBAAyB,CAAClG,WAAA,CAAEC,SAAF,CAAYkF,GAAZ,CAAD,EAAmB/G,IAAnB,CAA1C;UACA;QACD;;MAEH,KAAKyB,QAAQ,IAAIuJ,QAAZ,IAAwBC,OAAxB,IAAmC,CAACX,oBAAzC;QACErB,aAAa,GAAG,IAAhB;QACAuB,WAAW,CAACnI,IAAZ,CAEE8F,wBAAwB,CAACvG,WAAA,CAAEC,SAAF,CAAYkF,GAAZ,CAAD,EAAmB/G,IAAnB,EAAyBsB,KAAzB,CAF1B;QAIA;;MACF,KAAKyJ,UAAU,IAAI9K,SAAd,IAA2BgL,OAA3B,IAAsC5J,yBAA3C;QACEoJ,aAAa,CAACpI,IAAd,CAEE8E,0BAA0B,CAACvF,WAAA,CAAE0F,cAAF,EAAD,EAAqBtH,IAArB,EAA2BF,eAA3B,CAF5B;QAIA;;MACF,KAAKiL,UAAU,IAAI9K,SAAd,IAA2BgL,OAA3B,IAAsC,CAAC5J,yBAA5C;QACEoJ,aAAa,CAACpI,IAAd,CACEgF,iCAAiC,CAC/BzF,WAAA,CAAE0F,cAAF,EAD+B,EAG/BtH,IAH+B,EAI/BF,eAJ+B,EAK/BwB,KAL+B,CADnC;QASA;;MACF,KAAKyJ,UAAU,IAAI9K,SAAd,IAA2ByB,QAA3B,IAAuCL,yBAA5C;QACEoJ,aAAa,CAACY,OAAd,CACE3D,2BAA2B,CACzB9F,WAAA,CAAE0F,cAAF,EADyB,EAGzBtH,IAHyB,EAIzBF,eAJyB,CAD7B;QAQA4K,eAAe,CAACrI,IAAhB,CACEgG,6BAA6B,CAE3BrI,IAF2B,EAG3BF,eAH2B,EAI3BuB,yBAJ2B,CAD/B;QAQA;;MACF,KAAK0J,UAAU,IAAI9K,SAAd,IAA2ByB,QAA3B,IAAuC,CAACL,yBAA7C;QACEoJ,aAAa,CAACY,OAAd,CACE1D,kCAAkC,CAChC/F,WAAA,CAAE0F,cAAF,EADgC,EAGhCtH,IAHgC,EAIhCF,eAJgC,EAKhCwB,KALgC,CADpC;QASAoJ,eAAe,CAACrI,IAAhB,CACEgG,6BAA6B,CAE3BrI,IAF2B,EAG3BF,eAH2B,EAI3BuB,yBAJ2B,CAD/B;QAQA;;MACF,KAAKI,QAAQ,IAAIxB,SAAZ,IAAyByB,QAAzB,IAAqC,CAACL,yBAA3C;QACE4H,aAAa,GAAG,IAAhB;QACAuB,WAAW,CAACa,OAAZ,CAEE9D,+BAA+B,CAACvH,IAAD,EAAOF,eAAP,CAFjC;QAIA4K,eAAe,CAACrI,IAAhB,CACEgG,6BAA6B,CAE3BrI,IAF2B,EAG3BF,eAH2B,EAI3BuB,yBAJ2B,CAD/B;QAQA;;MACF,KAAKI,QAAQ,IAAIxB,SAAZ,IAAyByB,QAAzB,IAAqCL,yBAA1C;QACE4H,aAAa,GAAG,IAAhB;QACAuB,WAAW,CAACa,OAAZ,CACEjD,iCAAiC,CAC/BxG,WAAA,CAAEC,SAAF,CAAYkF,GAAZ,CAD+B,EAG/B/G,IAH+B,EAI/BsB,KAJ+B,EAK/BxB,eAL+B,CADnC;QASA4K,eAAe,CAACrI,IAAhB,CACEgG,6BAA6B,CAE3BrI,IAF2B,EAG3BF,eAH2B,EAI3BuB,yBAJ2B,CAD/B;QAQA;;MACF,KAAK0J,UAAU,IAAIC,QAAd,IAA0BC,OAA1B,IAAqCX,oBAA1C;QAEEG,aAAa,CAACpI,IAAd,CAAmByF,yBAAyB,CAAClG,WAAA,CAAE0F,cAAF,EAAD,EAAqBtH,IAArB,CAA5C;QACA;;MACF,KAAK+K,UAAU,IAAIC,QAAd,IAA0BC,OAA1B,IAAqC,CAACX,oBAA3C;QACEG,aAAa,CAACpI,IAAd,CAEE8F,wBAAwB,CAACvG,WAAA,CAAE0F,cAAF,EAAD,EAAqBtH,IAArB,EAA2BsB,KAA3B,CAF1B;QAIA;;MACF;QACE,MAAM,IAAI6E,KAAJ,CAAU,cAAV,CAAN;IAnJJ;EAqJD;;EAED,OAAO;IACLqE,WAAW,EAAEA,WAAW,CAACc,MAAZ,CAAmBC,OAAnB,CADR;IAELd,aAAa,EAAEA,aAAa,CAACa,MAAd,CAAqBC,OAArB,CAFV;IAGLb,eAAe,EAAEA,eAAe,CAACY,MAAhB,CAAuBC,OAAvB,CAHZ;;IAILC,SAAS,CAAC3I,IAAD,EAA0B;MACjC,KAAK,MAAM7C,IAAX,IAAmBH,KAAnB,EAA0B;QACxBG,IAAI,CAACyL,MAAL;MACD;;MAED,IAAIlB,cAAJ,EAAoB;QAClB1H,IAAI,CAACpC,KAAL,CAAW4B,IAAX,CAAgB;UAAEhC,EAAE,EAAEuB,WAAA,CAAEC,SAAF,CAAY0I,cAAZ;QAAN,CAAhB;QACA1H,IAAI,CAAC1B,GAAL,CACE,YADF,EAEES,WAAA,CAAEqG,oBAAF,CAAuB,GAAvB,EAA4BsC,cAA5B,EAA4C1H,IAAI,CAAC1C,IAAL,CAAUuL,UAAtD,CAFF;MAID;;MAED,IAAI,CAACzC,aAAL,EAAoB,OAAOpG,IAAP;;MAEpB,IAAIA,IAAI,CAAC8I,iBAAL,EAAJ,EAA8B;QAC5B9I,IAAI,CAACpC,KAAL,CAAW4B,IAAX,CAAgB;UAAEhC,EAAE,EAAE0G;QAAN,CAAhB;QACAlE,IAAI,CAACiC,WAAL,CACElD,WAAA,CAAEqG,oBAAF,CAAuB,GAAvB,EAA4BrG,WAAA,CAAEC,SAAF,CAAYkF,GAAZ,CAA5B,EAA8ClE,IAAI,CAAC1C,IAAnD,CADF;MAGD,CALD,MAKO,IAAI,CAAC0C,IAAI,CAAC1C,IAAL,CAAUE,EAAf,EAAmB;QAExBwC,IAAI,CAAC1C,IAAL,CAAUE,EAAV,GAAe0G,GAAf;MACD;;MAED,OAAOlE,IAAP;IACD;;EA9BI,CAAP;AAgCD"}