{"version": 3, "names": ["File", "node", "program", "print", "interpreter", "Program", "printInnerComments", "directivesLen", "directives", "length", "newline", "body", "printSequence", "trailingCommentsLineOffset", "trailingComments", "BlockStatement", "token", "indent", "sourceWithOffset", "loc", "rightBrace", "Directive", "value", "semicolon", "unescapedSingleQuoteRE", "unescapedDoubleQuoteRE", "DirectiveLiteral", "raw", "getPossibleRaw", "format", "minified", "undefined", "test", "Error", "InterpreterDirective", "Placeholder", "name", "expectedNode"], "sources": ["../../src/generators/base.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function File(this: Printer, node: t.File) {\n  if (node.program) {\n    // Print this here to ensure that Program node 'leadingComments' still\n    // get printed after the hashbang.\n    this.print(node.program.interpreter, node);\n  }\n\n  this.print(node.program, node);\n}\n\nexport function Program(this: Printer, node: t.Program) {\n  this.printInnerComments(node, false);\n\n  const directivesLen = node.directives?.length;\n  if (directivesLen) {\n    const newline = node.body.length ? 2 : 1;\n    this.printSequence(node.directives, node, {\n      trailingCommentsLineOffset: newline,\n    });\n    if (!node.directives[directivesLen - 1].trailingComments?.length) {\n      this.newline(newline);\n    }\n  }\n\n  this.printSequence(node.body, node);\n}\n\nexport function BlockStatement(this: Printer, node: t.BlockStatement) {\n  this.token(\"{\");\n  this.printInnerComments(node);\n\n  const directivesLen = node.directives?.length;\n  if (directivesLen) {\n    const newline = node.body.length ? 2 : 1;\n    this.printSequence(node.directives, node, {\n      indent: true,\n      trailingCommentsLineOffset: newline,\n    });\n    if (!node.directives[directivesLen - 1].trailingComments?.length) {\n      this.newline(newline);\n    }\n  }\n\n  this.printSequence(node.body, node, { indent: true });\n\n  this.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n  this.rightBrace();\n}\n\nexport function Directive(this: Printer, node: t.Directive) {\n  this.print(node.value, node);\n  this.semicolon();\n}\n\n// These regexes match an even number of \\ followed by a quote\nconst unescapedSingleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*'/;\nconst unescapedDoubleQuoteRE = /(?:^|[^\\\\])(?:\\\\\\\\)*\"/;\n\nexport function DirectiveLiteral(this: Printer, node: t.DirectiveLiteral) {\n  const raw = this.getPossibleRaw(node);\n  if (!this.format.minified && raw !== undefined) {\n    this.token(raw);\n    return;\n  }\n\n  const { value } = node;\n\n  // NOTE: In directives we can't change escapings,\n  // because they change the behavior.\n  // e.g. \"us\\x65 string\" (\\x65 is e) is not a \"use strict\" directive.\n\n  if (!unescapedDoubleQuoteRE.test(value)) {\n    this.token(`\"${value}\"`);\n  } else if (!unescapedSingleQuoteRE.test(value)) {\n    this.token(`'${value}'`);\n  } else {\n    throw new Error(\n      \"Malformed AST: it is not possible to print a directive containing\" +\n        \" both unescaped single and double quotes.\",\n    );\n  }\n}\n\nexport function InterpreterDirective(\n  this: Printer,\n  node: t.InterpreterDirective,\n) {\n  this.token(`#!${node.value}`);\n  this.newline(1, true);\n}\n\nexport function Placeholder(this: Printer, node: t.Placeholder) {\n  this.token(\"%%\");\n  this.print(node.name);\n  this.token(\"%%\");\n\n  if (node.expectedNode === \"Statement\") {\n    this.semicolon();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;AAGO,SAASA,IAAT,CAA6BC,IAA7B,EAA2C;EAChD,IAAIA,IAAI,CAACC,OAAT,EAAkB;IAGhB,KAAKC,KAAL,CAAWF,IAAI,CAACC,OAAL,CAAaE,WAAxB,EAAqCH,IAArC;EACD;;EAED,KAAKE,KAAL,CAAWF,IAAI,CAACC,OAAhB,EAAyBD,IAAzB;AACD;;AAEM,SAASI,OAAT,CAAgCJ,IAAhC,EAAiD;EAAA;;EACtD,KAAKK,kBAAL,CAAwBL,IAAxB,EAA8B,KAA9B;EAEA,MAAMM,aAAa,uBAAGN,IAAI,CAACO,UAAR,qBAAG,iBAAiBC,MAAvC;;EACA,IAAIF,aAAJ,EAAmB;IAAA;;IACjB,MAAMG,OAAO,GAAGT,IAAI,CAACU,IAAL,CAAUF,MAAV,GAAmB,CAAnB,GAAuB,CAAvC;IACA,KAAKG,aAAL,CAAmBX,IAAI,CAACO,UAAxB,EAAoCP,IAApC,EAA0C;MACxCY,0BAA0B,EAAEH;IADY,CAA1C;;IAGA,IAAI,2BAACT,IAAI,CAACO,UAAL,CAAgBD,aAAa,GAAG,CAAhC,EAAmCO,gBAApC,aAAC,sBAAqDL,MAAtD,CAAJ,EAAkE;MAChE,KAAKC,OAAL,CAAaA,OAAb;IACD;EACF;;EAED,KAAKE,aAAL,CAAmBX,IAAI,CAACU,IAAxB,EAA8BV,IAA9B;AACD;;AAEM,SAASc,cAAT,CAAuCd,IAAvC,EAA+D;EAAA;;EACpE,KAAKe,SAAL;EACA,KAAKV,kBAAL,CAAwBL,IAAxB;EAEA,MAAMM,aAAa,wBAAGN,IAAI,CAACO,UAAR,qBAAG,kBAAiBC,MAAvC;;EACA,IAAIF,aAAJ,EAAmB;IAAA;;IACjB,MAAMG,OAAO,GAAGT,IAAI,CAACU,IAAL,CAAUF,MAAV,GAAmB,CAAnB,GAAuB,CAAvC;IACA,KAAKG,aAAL,CAAmBX,IAAI,CAACO,UAAxB,EAAoCP,IAApC,EAA0C;MACxCgB,MAAM,EAAE,IADgC;MAExCJ,0BAA0B,EAAEH;IAFY,CAA1C;;IAIA,IAAI,4BAACT,IAAI,CAACO,UAAL,CAAgBD,aAAa,GAAG,CAAhC,EAAmCO,gBAApC,aAAC,uBAAqDL,MAAtD,CAAJ,EAAkE;MAChE,KAAKC,OAAL,CAAaA,OAAb;IACD;EACF;;EAED,KAAKE,aAAL,CAAmBX,IAAI,CAACU,IAAxB,EAA8BV,IAA9B,EAAoC;IAAEgB,MAAM,EAAE;EAAV,CAApC;EAEA,KAAKC,gBAAL,CAAsB,KAAtB,EAA6BjB,IAAI,CAACkB,GAAlC,EAAuC,CAAvC,EAA0C,CAAC,CAA3C;EAEA,KAAKC,UAAL;AACD;;AAEM,SAASC,SAAT,CAAkCpB,IAAlC,EAAqD;EAC1D,KAAKE,KAAL,CAAWF,IAAI,CAACqB,KAAhB,EAAuBrB,IAAvB;EACA,KAAKsB,SAAL;AACD;;AAGD,MAAMC,sBAAsB,GAAG,uBAA/B;AACA,MAAMC,sBAAsB,GAAG,uBAA/B;;AAEO,SAASC,gBAAT,CAAyCzB,IAAzC,EAAmE;EACxE,MAAM0B,GAAG,GAAG,KAAKC,cAAL,CAAoB3B,IAApB,CAAZ;;EACA,IAAI,CAAC,KAAK4B,MAAL,CAAYC,QAAb,IAAyBH,GAAG,KAAKI,SAArC,EAAgD;IAC9C,KAAKf,KAAL,CAAWW,GAAX;IACA;EACD;;EAED,MAAM;IAAEL;EAAF,IAAYrB,IAAlB;;EAMA,IAAI,CAACwB,sBAAsB,CAACO,IAAvB,CAA4BV,KAA5B,CAAL,EAAyC;IACvC,KAAKN,KAAL,CAAY,IAAGM,KAAM,GAArB;EACD,CAFD,MAEO,IAAI,CAACE,sBAAsB,CAACQ,IAAvB,CAA4BV,KAA5B,CAAL,EAAyC;IAC9C,KAAKN,KAAL,CAAY,IAAGM,KAAM,GAArB;EACD,CAFM,MAEA;IACL,MAAM,IAAIW,KAAJ,CACJ,sEACE,2CAFE,CAAN;EAID;AACF;;AAEM,SAASC,oBAAT,CAELjC,IAFK,EAGL;EACA,KAAKe,KAAL,CAAY,KAAIf,IAAI,CAACqB,KAAM,EAA3B;EACA,KAAKZ,OAAL,CAAa,CAAb,EAAgB,IAAhB;AACD;;AAEM,SAASyB,WAAT,CAAoClC,IAApC,EAAyD;EAC9D,KAAKe,KAAL,CAAW,IAAX;EACA,KAAKb,KAAL,CAAWF,IAAI,CAACmC,IAAhB;EACA,KAAKpB,KAAL,CAAW,IAAX;;EAEA,IAAIf,IAAI,CAACoC,YAAL,KAAsB,WAA1B,EAAuC;IACrC,KAAKd,SAAL;EACD;AACF"}