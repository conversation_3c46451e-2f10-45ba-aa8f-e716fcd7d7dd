"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * 🔧 快照对比机制说明：\n * - SaveButton 负责监听快照对比机制，通过 JSON-to-JSON 对比判断笔记状态\n * - 若一致，为 view 状态，不保存且不调用 useAutoSaveOnLeave\n * - 若不一致，为 save 状态，此时用户可以：\n *   1. 手动保存：进入正常保存操作，成功后 save 状态转为 view 状态\n *   2. 离开编辑器：笔记状态依然为 save 状态，调用 AutoSaveOnLeave：\n *      - 跳转到其他笔记时（笔记ID变动）：自动保存，允许正常跳转\n *      - 刷新或关闭页面：弹窗提示\n *        - 选择离开：弃用在 indexDB 中的修改 JSON，直接离开页面\n *        - 选择取消：自动保存\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // 🔧 检查是否应该自动保存 - 以笔记的状态为主！\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            var status = window.saveButtonStatus;\n            // 🔧 只有 save 状态才需要自动保存，view 状态不需要\n            return status === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        // 🔧 关键：只有 save 状态才弹窗，view 状态直接允许离开\n        if (shouldAutoSave()) {\n            // 显示确认对话框\n            var message = \"您有未保存的更改。确定要离开吗？\";\n            event.returnValue = message;\n            // 🔧 优化：使用延迟检测用户选择\n            // 如果用户选择\"离开\"，页面会立即卸载，setTimeout不会执行\n            // 如果用户选择\"取消\"，setTimeout会在用户回到页面后执行自动保存\n            setTimeout(function() {\n                performAutoSave();\n            }, 100);\n            return message;\n        }\n    // 🔧 view 状态：直接允许离开，不弹窗，不调用 useAutoSaveOnLeave\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 🔧 路由变化处理 - 严格按照用户要求的逻辑\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, confirmed;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            4\n                        ];\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                        if (!isNoteNavigation) return [\n                            3,\n                            1\n                        ];\n                        // 🔧 笔记跳转：自动保存，不弹窗，允许正常跳转\n                        isAutoSavingRef.current = true;\n                        // 在后台执行自动保存，不阻止路由跳转\n                        performAutoSave().finally(function() {\n                            isAutoSavingRef.current = false;\n                        });\n                        // 直接允许跳转，不阻止\n                        return [\n                            2\n                        ];\n                    case 1:\n                        // 🔧 非笔记跳转：阻止跳转，弹窗询问\n                        router.events.emit(\"routeChangeError\", new Error(\"User confirmation required\"), url);\n                        confirmed = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed) return [\n                            3,\n                            2\n                        ];\n                        // 🔧 用户选择离开：弃用在indexDB中的修改JSON，直接离开页面\n                        router.push(url);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        // 🔧 用户选择取消：自动保存\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 3:\n                        _state.sent();\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        // 🔧 view 状态：直接允许跳转，不做任何处理，不调用 useAutoSaveOnLeave\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});