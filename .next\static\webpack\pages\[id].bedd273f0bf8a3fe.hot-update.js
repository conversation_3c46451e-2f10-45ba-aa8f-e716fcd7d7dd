"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/save-button.tsx":
/*!************************************!*\
  !*** ./components/save-button.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @material-ui/core */ \"./node_modules/@material-ui/core/esm/index.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/**\n * SaveButton Component\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nvar useStyles = (0,_material_ui_core__WEBPACK_IMPORTED_MODULE_4__.makeStyles)({\n    saveButton: {\n        minWidth: \"80px\",\n        fontWeight: \"bold\",\n        textTransform: \"none\",\n        borderRadius: \"8px\",\n        boxShadow: \"none !important\",\n        \"&:hover\": {\n            opacity: 0.8,\n            boxShadow: \"none !important\"\n        },\n        \"&:focus\": {\n            boxShadow: \"none !important\"\n        },\n        \"&:active\": {\n            boxShadow: \"none !important\"\n        }\n    },\n    viewButton: {\n        backgroundColor: \"#6B7280 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#4B5563 !important\"\n        }\n    },\n    saveStateButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    },\n    syncingButton: {\n        backgroundColor: \"#3185eb !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#2563EB !important\"\n        }\n    },\n    syncedButton: {\n        backgroundColor: \"#FBBF24 !important\",\n        color: \"#000000 !important\",\n        \"&:hover\": {\n            backgroundColor: \"#F59E0B !important\"\n        }\n    },\n    failedButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    }\n});\nvar SaveButton = function(param) {\n    var className = param.className;\n    _s();\n    var classes = useStyles();\n    var ref = libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer(), syncToServer = ref.syncToServer, note = ref.note;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"), syncStatus = ref1[0], setSyncStatus = ref1[1];\n    var syncedTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var syncTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 简化：使用 ceta 版本的简单逻辑\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (!(note === null || note === void 0 ? void 0 : note.id)) {\n            setSyncStatus(\"view\");\n            return;\n        }\n        var isEditing = false;\n        var checkIndexedDBChanges = function() {\n            var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function() {\n                var localNote, error;\n                return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            _state.trys.push([\n                                0,\n                                2,\n                                ,\n                                3\n                            ]);\n                            return [\n                                4,\n                                libs_web_cache_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getItem(note.id)\n                            ];\n                        case 1:\n                            localNote = _state.sent();\n                            if (localNote && localNote.content !== note.content) {\n                                if (!isEditing) {\n                                    isEditing = true;\n                                    setSyncStatus(\"save\");\n                                }\n                            } else {\n                                // 如果内容一致，重置为 view 状态\n                                if (isEditing) {\n                                    isEditing = false;\n                                    setSyncStatus(\"view\");\n                                }\n                            }\n                            return [\n                                3,\n                                3\n                            ];\n                        case 2:\n                            error = _state.sent();\n                            console.error(\"检查 IndexedDB 变化失败:\", error);\n                            return [\n                                3,\n                                3\n                            ];\n                        case 3:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function checkIndexedDBChanges() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        // 立即检查一次\n        checkIndexedDBChanges();\n        // 定期检查\n        var interval = setInterval(checkIndexedDBChanges, 1000);\n        return function() {\n            clearInterval(interval);\n            if (syncedTimeoutRef.current) {\n                clearTimeout(syncedTimeoutRef.current);\n            }\n            if (syncTimeoutRef.current) {\n                clearTimeout(syncTimeoutRef.current);\n            }\n        };\n    }, [\n        note,\n        syncStatus\n    ]);\n    // 手动保存流程\n    var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(function() {\n        var syncSuccess, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    setSyncStatus(\"syncing\");\n                    if (syncedTimeoutRef.current) {\n                        clearTimeout(syncedTimeoutRef.current);\n                    }\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                    }\n                    // 设置超时保护\n                    syncTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"fail\");\n                        setTimeout(function() {\n                            setSyncStatus(\"view\");\n                        }, 2000);\n                    }, 30000);\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        syncToServer()\n                    ];\n                case 2:\n                    syncSuccess = _state.sent();\n                    if (!syncSuccess) {\n                        throw new Error(\"同步到服务器失败\");\n                    }\n                    // 清除超时\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"synced\");\n                    // 3秒后自动变回view状态\n                    syncedTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 3000);\n                    return [\n                        3,\n                        4\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"手动保存失败:\", error);\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"fail\");\n                    setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 2000);\n                    return [\n                        3,\n                        4\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        syncToServer,\n        saveCurrentContent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (true) {\n            window.saveButtonStatus = syncStatus;\n            window.saveButtonAutoSave = handleSave;\n        }\n        return function() {\n            if (true) {\n                delete window.saveButtonStatus;\n                delete window.saveButtonAutoSave;\n            }\n        };\n    }, [\n        syncStatus,\n        handleSave\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var handleKeyDown = function(e) {\n            if ((e.ctrlKey || e.metaKey) && e.key === \"s\") {\n                var target = e.target;\n                var isInEditor = target.closest(\".ProseMirror\") || target.closest(\"[contenteditable]\") || target.closest(\"textarea\") || target.closest(\"input\");\n                if (isInEditor) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                    handleSave();\n                }\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown, true);\n        return function() {\n            return document.removeEventListener(\"keydown\", handleKeyDown, true);\n        };\n    }, [\n        handleSave\n    ]);\n    var getButtonIcon = function() {\n        switch(syncStatus){\n            case \"view\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 24\n                }, _this);\n            case \"save\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__.DocumentIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 235,\n                    columnNumber: 24\n                }, _this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__.UploadIcon, {\n                    className: \"w-4 h-4 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 24\n                }, _this);\n            case \"synced\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__.CheckIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 24\n                }, _this);\n            case \"fail\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__.XIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 24\n                }, _this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_7__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 24\n                }, _this);\n        }\n    };\n    var getButtonText = function() {\n        switch(syncStatus){\n            case \"view\":\n                return \"View\";\n            case \"save\":\n                return \"Save\";\n            case \"syncing\":\n                return \"Syncing...\";\n            case \"synced\":\n                return \"Synced\";\n            case \"fail\":\n                return \"Failed\";\n            default:\n                return \"View\";\n        }\n    };\n    var getButtonClassName = function() {\n        var baseClass = \"\".concat(classes.saveButton);\n        switch(syncStatus){\n            case \"view\":\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n            case \"save\":\n                return \"\".concat(baseClass, \" \").concat(classes.saveStateButton);\n            case \"syncing\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncingButton);\n            case \"synced\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncedButton);\n            case \"fail\":\n                return \"\".concat(baseClass, \" \").concat(classes.failedButton);\n            default:\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n        }\n    };\n    var isButtonDisabled = function() {\n        return syncStatus === \"syncing\" || syncStatus === \"view\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_ui_core__WEBPACK_IMPORTED_MODULE_4__.Button, {\n        variant: \"contained\",\n        startIcon: getButtonIcon(),\n        onClick: handleSave,\n        disabled: isButtonDisabled(),\n        className: \"\".concat(getButtonClassName(), \" \").concat(className || \"\"),\n        size: \"small\",\n        \"data-save-button\": \"true\",\n        children: getButtonText()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n        lineNumber: 287,\n        columnNumber: 9\n    }, _this);\n};\n_s(SaveButton, \"YnryxWRWB+DXCdoeyI0tZBTAuxI=\", false, function() {\n    return [\n        useStyles,\n        libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer\n    ];\n});\n_c = SaveButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SaveButton);\nvar _c;\n$RefreshReg$(_c, \"SaveButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/save-button.tsx\n"));

/***/ })

});