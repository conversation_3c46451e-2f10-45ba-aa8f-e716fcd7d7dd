{"version": 3, "names": ["generateRegexpuOptions", "pattern", "toTransform", "feat", "name", "ok", "hasFeature", "FEATURES", "featDuplicateNamedGroups", "regex", "seen", "Set", "match", "exec", "add", "has", "unicodeFlag", "unicodeSetsFlag", "dotAllFlag", "unicodePropertyEscapes", "namedGroups", "onNamedGroup", "canSkipRegexpu", "node", "options", "flags", "includes", "test", "transformFlags", "regexpuOptions", "replace"], "sources": ["../src/util.ts"], "sourcesContent": ["import type { types as t } from \"@babel/core\";\nimport { FEATURES, hasFeature } from \"./features\";\n\nimport type { RegexpuOptions } from \"regexpu-core\";\n\nexport function generateRegexpuOptions(\n  pattern: string,\n  toTransform: number,\n): RegexpuOptions {\n  type Experimental = 1;\n\n  const feat = <Stability extends 0 | 1 = 0>(\n    name: keyof typeof FEATURES,\n    ok: \"transform\" | (Stability extends 0 ? never : \"parse\") = \"transform\",\n  ) => {\n    return hasFeature(toTransform, FEATURES[name]) ? ok : false;\n  };\n\n  const featDuplicateNamedGroups = (): \"transform\" | false => {\n    if (!feat(\"duplicateNamedCaptureGroups\")) return false;\n\n    // This can return false positive, for example for /\\(?<a>\\)/.\n    // However, it's such a rare occurrence that it's ok to compile\n    // the regexp even if we only need to compile regexps with\n    // duplicate named capturing groups.\n    const regex = /\\(\\?<([^>]+)>/g;\n    const seen = new Set();\n    for (let match; (match = regex.exec(pattern)); seen.add(match[1])) {\n      if (seen.has(match[1])) return \"transform\";\n    }\n    return false;\n  };\n\n  return {\n    unicodeFlag: feat(\"unicodeFlag\"),\n    unicodeSetsFlag:\n      feat<Experimental>(\"unicodeSetsFlag\") ||\n      feat<Experimental>(\"unicodeSetsFlag_syntax\", \"parse\"),\n    dotAllFlag: feat(\"dotAllFlag\"),\n    unicodePropertyEscapes: feat(\"unicodePropertyEscape\"),\n    namedGroups: feat(\"namedCaptureGroups\") || featDuplicateNamedGroups(),\n    onNamedGroup: () => {},\n  };\n}\n\nexport function canSkipRegexpu(\n  node: t.RegExpLiteral,\n  options: RegexpuOptions,\n): boolean {\n  const { flags, pattern } = node;\n\n  if (flags.includes(\"v\")) {\n    if (options.unicodeSetsFlag === \"transform\") return false;\n  }\n\n  if (flags.includes(\"u\")) {\n    if (options.unicodeFlag === \"transform\") return false;\n    if (\n      options.unicodePropertyEscapes === \"transform\" &&\n      /\\\\[pP]{/.test(pattern)\n    ) {\n      return false;\n    }\n  }\n\n  if (flags.includes(\"s\")) {\n    if (options.dotAllFlag === \"transform\") return false;\n  }\n\n  if (options.namedGroups === \"transform\" && /\\(\\?<(?![=!])/.test(pattern)) {\n    return false;\n  }\n\n  return true;\n}\n\nexport function transformFlags(regexpuOptions: RegexpuOptions, flags: string) {\n  if (regexpuOptions.unicodeSetsFlag === \"transform\") {\n    flags = flags.replace(\"v\", \"u\");\n  }\n  if (regexpuOptions.unicodeFlag === \"transform\") {\n    flags = flags.replace(\"u\", \"\");\n  }\n  if (regexpuOptions.dotAllFlag === \"transform\") {\n    flags = flags.replace(\"s\", \"\");\n  }\n  return flags;\n}\n"], "mappings": ";;;;;;;;;AACA;;AAIO,SAASA,sBAAT,CACLC,OADK,EAELC,WAFK,EAGW;EAGhB,MAAMC,IAAI,GAAG,CACXC,IADW,EAEXC,EAAyD,GAAG,WAFjD,KAGR;IACH,OAAO,IAAAC,oBAAA,EAAWJ,WAAX,EAAwBK,kBAAA,CAASH,IAAT,CAAxB,IAA0CC,EAA1C,GAA+C,KAAtD;EACD,CALD;;EAOA,MAAMG,wBAAwB,GAAG,MAA2B;IAC1D,IAAI,CAACL,IAAI,CAAC,6BAAD,CAAT,EAA0C,OAAO,KAAP;IAM1C,MAAMM,KAAK,GAAG,gBAAd;IACA,MAAMC,IAAI,GAAG,IAAIC,GAAJ,EAAb;;IACA,KAAK,IAAIC,KAAT,EAAiBA,KAAK,GAAGH,KAAK,CAACI,IAAN,CAAWZ,OAAX,CAAzB,EAA+CS,IAAI,CAACI,GAAL,CAASF,KAAK,CAAC,CAAD,CAAd,CAA/C,EAAmE;MACjE,IAAIF,IAAI,CAACK,GAAL,CAASH,KAAK,CAAC,CAAD,CAAd,CAAJ,EAAwB,OAAO,WAAP;IACzB;;IACD,OAAO,KAAP;EACD,CAbD;;EAeA,OAAO;IACLI,WAAW,EAAEb,IAAI,CAAC,aAAD,CADZ;IAELc,eAAe,EACbd,IAAI,CAAe,iBAAf,CAAJ,IACAA,IAAI,CAAe,wBAAf,EAAyC,OAAzC,CAJD;IAKLe,UAAU,EAAEf,IAAI,CAAC,YAAD,CALX;IAMLgB,sBAAsB,EAAEhB,IAAI,CAAC,uBAAD,CANvB;IAOLiB,WAAW,EAAEjB,IAAI,CAAC,oBAAD,CAAJ,IAA8BK,wBAAwB,EAP9D;IAQLa,YAAY,EAAE,MAAM,CAAE;EARjB,CAAP;AAUD;;AAEM,SAASC,cAAT,CACLC,IADK,EAELC,OAFK,EAGI;EACT,MAAM;IAAEC,KAAF;IAASxB;EAAT,IAAqBsB,IAA3B;;EAEA,IAAIE,KAAK,CAACC,QAAN,CAAe,GAAf,CAAJ,EAAyB;IACvB,IAAIF,OAAO,CAACP,eAAR,KAA4B,WAAhC,EAA6C,OAAO,KAAP;EAC9C;;EAED,IAAIQ,KAAK,CAACC,QAAN,CAAe,GAAf,CAAJ,EAAyB;IACvB,IAAIF,OAAO,CAACR,WAAR,KAAwB,WAA5B,EAAyC,OAAO,KAAP;;IACzC,IACEQ,OAAO,CAACL,sBAAR,KAAmC,WAAnC,IACA,UAAUQ,IAAV,CAAe1B,OAAf,CAFF,EAGE;MACA,OAAO,KAAP;IACD;EACF;;EAED,IAAIwB,KAAK,CAACC,QAAN,CAAe,GAAf,CAAJ,EAAyB;IACvB,IAAIF,OAAO,CAACN,UAAR,KAAuB,WAA3B,EAAwC,OAAO,KAAP;EACzC;;EAED,IAAIM,OAAO,CAACJ,WAAR,KAAwB,WAAxB,IAAuC,gBAAgBO,IAAhB,CAAqB1B,OAArB,CAA3C,EAA0E;IACxE,OAAO,KAAP;EACD;;EAED,OAAO,IAAP;AACD;;AAEM,SAAS2B,cAAT,CAAwBC,cAAxB,EAAwDJ,KAAxD,EAAuE;EAC5E,IAAII,cAAc,CAACZ,eAAf,KAAmC,WAAvC,EAAoD;IAClDQ,KAAK,GAAGA,KAAK,CAACK,OAAN,CAAc,GAAd,EAAmB,GAAnB,CAAR;EACD;;EACD,IAAID,cAAc,CAACb,WAAf,KAA+B,WAAnC,EAAgD;IAC9CS,KAAK,GAAGA,KAAK,CAACK,OAAN,CAAc,GAAd,EAAmB,EAAnB,CAAR;EACD;;EACD,IAAID,cAAc,CAACX,UAAf,KAA8B,WAAlC,EAA+C;IAC7CO,KAAK,GAAGA,KAAK,CAACK,OAAN,CAAc,GAAd,EAAmB,EAAnB,CAAR;EACD;;EACD,OAAOL,KAAP;AACD"}