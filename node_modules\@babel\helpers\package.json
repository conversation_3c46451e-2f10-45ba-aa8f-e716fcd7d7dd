{"name": "@babel/helpers", "version": "7.19.4", "description": "Collection of helper functions used by Babel transforms.", "author": "The Babel Team (https://babel.dev/team)", "homepage": "https://babel.dev/docs/en/next/babel-helpers", "license": "MIT", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helpers"}, "main": "./lib/index.js", "dependencies": {"@babel/template": "^7.18.10", "@babel/traverse": "^7.19.4", "@babel/types": "^7.19.4"}, "devDependencies": {"@babel/generator": "^7.19.4", "@babel/helper-plugin-test-runner": "^7.18.6", "@babel/parser": "^7.19.4", "regenerator-runtime": "^0.13.9", "terser": "^5.9.0"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}