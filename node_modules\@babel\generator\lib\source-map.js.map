{"version": 3, "names": ["SourceMap", "constructor", "opts", "code", "_map", "_rawMappings", "_sourceFileName", "_lastGenLine", "_lastSourceLine", "_lastSourceColumn", "map", "GenMapping", "sourceRoot", "sourceFileName", "replace", "undefined", "setSourceContent", "Object", "keys", "for<PERSON>ach", "get", "toEncodedMap", "getDecoded", "toDecodedMap", "getRawMappings", "allMappings", "mark", "generated", "line", "column", "identifierName", "filename", "maybeAddMapping", "name", "source", "original"], "sources": ["../src/source-map.ts"], "sourcesContent": ["import {\n  GenMapping,\n  maybeAddMapping,\n  setSourceContent,\n  allMappings,\n  toEncodedMap,\n  toDecodedMap,\n} from \"@jridgewell/gen-mapping\";\n\nimport type {\n  EncodedSourceMap,\n  DecodedSourceMap,\n  Mapping,\n} from \"@jridgewell/gen-mapping\";\n\n/**\n * Build a sourcemap.\n */\n\nexport default class SourceMap {\n  private _map: GenMapping;\n  private _rawMappings: Mapping[] | undefined;\n  private _sourceFileName: string | undefined;\n\n  // Any real line is > 0, so init to 0 is fine.\n  private _lastGenLine = 0;\n  private _lastSourceLine = 0;\n\n  // Source columns can be 0, but we ony check in unison with sourceLine, which\n  // inits to an impossible value. So init to 0 is fine.\n  private _lastSourceColumn = 0;\n\n  constructor(\n    opts: { sourceFileName?: string; sourceRoot?: string },\n    code: string | { [sourceFileName: string]: string },\n  ) {\n    const map = (this._map = new GenMapping({ sourceRoot: opts.sourceRoot }));\n    this._sourceFileName = opts.sourceFileName?.replace(/\\\\/g, \"/\");\n    this._rawMappings = undefined;\n\n    if (typeof code === \"string\") {\n      setSourceContent(map, this._sourceFileName, code);\n    } else if (typeof code === \"object\") {\n      Object.keys(code).forEach(sourceFileName => {\n        setSourceContent(\n          map,\n          sourceFileName.replace(/\\\\/g, \"/\"),\n          code[sourceFileName],\n        );\n      });\n    }\n  }\n\n  /**\n   * Get the sourcemap.\n   */\n  get(): EncodedSourceMap {\n    return toEncodedMap(this._map);\n  }\n\n  getDecoded(): DecodedSourceMap {\n    return toDecodedMap(this._map);\n  }\n\n  getRawMappings(): Mapping[] {\n    return (this._rawMappings ||= allMappings(this._map));\n  }\n\n  /**\n   * Mark the current generated position with a source position. May also be passed null line/column\n   * values to insert a mapping to nothing.\n   */\n\n  mark(\n    generated: { line: number; column: number },\n    line: number,\n    column: number,\n    identifierName?: string | null,\n    filename?: string | null,\n  ) {\n    this._rawMappings = undefined;\n\n    maybeAddMapping(this._map, {\n      name: identifierName,\n      generated,\n      source:\n        line == null\n          ? undefined\n          : filename?.replace(/\\\\/g, \"/\") || this._sourceFileName,\n      original:\n        line == null\n          ? undefined\n          : {\n              line: line,\n              column: column,\n            },\n    });\n  }\n}\n"], "mappings": ";;;;;;;AAAA;;AAmBe,MAAMA,SAAN,CAAgB;EAa7BC,WAAW,CACTC,IADS,EAETC,IAFS,EAGT;IAAA;;IAAA,KAfMC,IAeN;IAAA,KAdMC,YAcN;IAAA,KAbMC,eAaN;IAAA,KAVMC,YAUN,GAVqB,CAUrB;IAAA,KATMC,eASN,GATwB,CASxB;IAAA,KALMC,iBAKN,GAL0B,CAK1B;IACA,MAAMC,GAAG,GAAI,KAAKN,IAAL,GAAY,IAAIO,sBAAJ,CAAe;MAAEC,UAAU,EAAEV,IAAI,CAACU;IAAnB,CAAf,CAAzB;IACA,KAAKN,eAAL,2BAAuBJ,IAAI,CAACW,cAA5B,qBAAuB,qBAAqBC,OAArB,CAA6B,KAA7B,EAAoC,GAApC,CAAvB;IACA,KAAKT,YAAL,GAAoBU,SAApB;;IAEA,IAAI,OAAOZ,IAAP,KAAgB,QAApB,EAA8B;MAC5B,IAAAa,4BAAA,EAAiBN,GAAjB,EAAsB,KAAKJ,eAA3B,EAA4CH,IAA5C;IACD,CAFD,MAEO,IAAI,OAAOA,IAAP,KAAgB,QAApB,EAA8B;MACnCc,MAAM,CAACC,IAAP,CAAYf,IAAZ,EAAkBgB,OAAlB,CAA0BN,cAAc,IAAI;QAC1C,IAAAG,4BAAA,EACEN,GADF,EAEEG,cAAc,CAACC,OAAf,CAAuB,KAAvB,EAA8B,GAA9B,CAFF,EAGEX,IAAI,CAACU,cAAD,CAHN;MAKD,CAND;IAOD;EACF;;EAKDO,GAAG,GAAqB;IACtB,OAAO,IAAAC,wBAAA,EAAa,KAAKjB,IAAlB,CAAP;EACD;;EAEDkB,UAAU,GAAqB;IAC7B,OAAO,IAAAC,wBAAA,EAAa,KAAKnB,IAAlB,CAAP;EACD;;EAEDoB,cAAc,GAAc;IAC1B,OAAQ,KAAKnB,YAAb,KAAQ,KAAKA,YAAb,GAA8B,IAAAoB,uBAAA,EAAY,KAAKrB,IAAjB,CAA9B;EACD;;EAODsB,IAAI,CACFC,SADE,EAEFC,IAFE,EAGFC,MAHE,EAIFC,cAJE,EAKFC,QALE,EAMF;IACA,KAAK1B,YAAL,GAAoBU,SAApB;IAEA,IAAAiB,2BAAA,EAAgB,KAAK5B,IAArB,EAA2B;MACzB6B,IAAI,EAAEH,cADmB;MAEzBH,SAFyB;MAGzBO,MAAM,EACJN,IAAI,IAAI,IAAR,GACIb,SADJ,GAEI,CAAAgB,QAAQ,QAAR,YAAAA,QAAQ,CAAEjB,OAAV,CAAkB,KAAlB,EAAyB,GAAzB,MAAiC,KAAKR,eANnB;MAOzB6B,QAAQ,EACNP,IAAI,IAAI,IAAR,GACIb,SADJ,GAEI;QACEa,IAAI,EAAEA,IADR;QAEEC,MAAM,EAAEA;MAFV;IAVmB,CAA3B;EAeD;;AA9E4B"}