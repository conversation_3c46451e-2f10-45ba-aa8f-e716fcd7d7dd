"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/**\n * Auto Save on Leave Hook\n *\n * 简单逻辑：\n * 1. 用户选择离开/重新加载 = 不自动保存 + 清空快照\n * 2. 保存成功后 = 清空快照\n * 3. 笔记跳转时 = 自动保存\n * 4. 页面关闭/刷新时 = 弹窗询问（重新加载和离开都代表不保存）\n * 5. 非笔记跳转时 = 直接清空快照（不弹窗）\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n */ \n\n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        return  true && window.saveButtonStatus === \"save\";\n    }, []);\n    var autoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    var clearSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.clearSnapshots) {\n            try {\n                window.clearSnapshots();\n            } catch (error) {\n                console.error(\"清空快照失败:\", error);\n            }\n        }\n    }, []);\n    // 页面关闭/刷新时弹窗\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled || !hasUnsavedChanges()) return;\n        var message = \"您有未保存的更改。确定要离开吗？\";\n        event.returnValue = message;\n        // 用户选择离开或重新加载时清空快照\n        var handleUnload = function() {\n            clearSnapshots();\n            window.removeEventListener(\"unload\", handleUnload);\n        };\n        window.addEventListener(\"unload\", handleUnload);\n        return message;\n    }, [\n        enabled,\n        hasUnsavedChanges,\n        clearSnapshots\n    ]);\n    // 路由跳转时处理\n    var handleRouteChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function(url) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, function(_state) {\n                if (!enabled || isAutoSavingRef.current || !hasUnsavedChanges()) return [\n                    2\n                ];\n                // 检查是否是笔记跳转\n                if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_2__.isNoteLink)(url) || url === \"/\" || url.includes(\"?new\")) {\n                    // 笔记跳转：后台自动保存\n                    isAutoSavingRef.current = true;\n                    autoSave().finally(function() {\n                        isAutoSavingRef.current = false;\n                    });\n                    return [\n                        2\n                    ];\n                }\n                // 非笔记跳转：直接清空快照并跳转（不弹窗）\n                // 重新加载和离开都代表不保存\n                clearSnapshots();\n                return [\n                    2\n                ];\n            });\n        // 不阻止跳转，直接允许\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        hasUnsavedChanges,\n        autoSave,\n        clearSnapshots,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            return window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChange);\n        return function() {\n            return router.events.off(\"routeChangeStart\", handleRouteChange);\n        };\n    }, [\n        enabled,\n        handleRouteChange,\n        router.events\n    ]);\n    return {\n        hasUnsavedChanges: hasUnsavedChanges,\n        autoSave: autoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});