{"name": "@babel/helper-create-regexp-features-plugin", "version": "7.19.0", "author": "The Babel Team (https://babel.dev/team)", "license": "MIT", "description": "Compile ESNext Regular Expressions to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-create-regexp-features-plugin"}, "main": "./lib/index.js", "publishConfig": {"access": "public"}, "keywords": ["babel", "babel-plugin"], "dependencies": {"@babel/helper-annotate-as-pure": "^7.18.6", "regexpu-core": "^5.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.19.0", "@babel/helper-plugin-test-runner": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "type": "commonjs"}