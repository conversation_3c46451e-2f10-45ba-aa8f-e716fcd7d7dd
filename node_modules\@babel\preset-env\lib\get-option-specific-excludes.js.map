{"version": 3, "names": ["defaultExcludesForLooseMode", "loose"], "sources": ["../src/get-option-specific-excludes.ts"], "sourcesContent": ["const defaultExcludesForLooseMode = [\"transform-typeof-symbol\"];\n\nexport default function ({ loose }: { loose: boolean }): null | string[] {\n  return loose ? defaultExcludesForLooseMode : null;\n}\n"], "mappings": ";;;;;;AAAA,MAAMA,2BAA2B,GAAG,CAAC,yBAAD,CAApC;;AAEe,kBAAU;EAAEC;AAAF,CAAV,EAA0D;EACvE,OAAOA,KAAK,GAAGD,2BAAH,GAAiC,IAA7C;AACD"}