{"version": 3, "names": ["createAddInitializerMethod", "initializers", "decoratorFinishedRef", "addInitializer", "initializer", "assertNotFinished", "assertCallable", "push", "memberDec", "dec", "name", "desc", "kind", "isStatic", "isPrivate", "value", "kindStr", "ctx", "static", "private", "v", "get", "set", "call", "access", "fnName", "Error", "fn", "hint", "TypeError", "assertValidReturnValue", "type", "undefined", "init", "applyMemberDec", "ret", "base", "decInfo", "decs", "Object", "getOwnPropertyDescriptor", "newValue", "i", "length", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "applyMemberDecs", "Class", "decInfos", "protoInitializers", "staticInitializers", "existingProtoNonFields", "Map", "existingStaticNonFields", "Array", "isArray", "prototype", "existingNonFields", "existingKind", "pushInitializers", "applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs2203", "memberDecs"], "sources": ["../../src/helpers/applyDecs2203.js"], "sourcesContent": ["/* @minVersion 7.19.0 */\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction createAddInitializerMethod(initializers, decoratorFinishedRef) {\n  return function addInitializer(initializer) {\n    assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n    assertCallable(initializer, \"An initializer\");\n    initializers.push(initializer);\n  };\n}\n\nfunction memberDec(\n  dec,\n  name,\n  desc,\n  initializers,\n  kind,\n  isStatic,\n  isPrivate,\n  value\n) {\n  var kindStr;\n\n  switch (kind) {\n    case 1 /* ACCESSOR */:\n      kindStr = \"accessor\";\n      break;\n    case 2 /* METHOD */:\n      kindStr = \"method\";\n      break;\n    case 3 /* GETTER */:\n      kindStr = \"getter\";\n      break;\n    case 4 /* SETTER */:\n      kindStr = \"setter\";\n      break;\n    default:\n      kindStr = \"field\";\n  }\n\n  var ctx = {\n    kind: kindStr,\n    name: isPrivate ? \"#\" + name : name,\n    static: isStatic,\n    private: isPrivate,\n  };\n\n  var decoratorFinishedRef = { v: false };\n\n  if (kind !== 0 /* FIELD */) {\n    ctx.addInitializer = createAddInitializerMethod(\n      initializers,\n      decoratorFinishedRef\n    );\n  }\n\n  var get, set;\n  if (kind === 0 /* FIELD */) {\n    if (isPrivate) {\n      get = desc.get;\n      set = desc.set;\n    } else {\n      get = function () {\n        return this[name];\n      };\n      set = function (v) {\n        this[name] = v;\n      };\n    }\n  } else if (kind === 2 /* METHOD */) {\n    get = function () {\n      return desc.value;\n    };\n  } else {\n    // replace with values that will go through the final getter and setter\n    if (kind === 1 /* ACCESSOR */ || kind === 3 /* GETTER */) {\n      get = function () {\n        return desc.get.call(this);\n      };\n    }\n\n    if (kind === 1 /* ACCESSOR */ || kind === 4 /* SETTER */) {\n      set = function (v) {\n        desc.set.call(this, v);\n      };\n    }\n  }\n  ctx.access =\n    get && set ? { get: get, set: set } : get ? { get: get } : { set: set };\n\n  try {\n    return dec(value, ctx);\n  } finally {\n    decoratorFinishedRef.v = true;\n  }\n}\n\nfunction assertNotFinished(decoratorFinishedRef, fnName) {\n  if (decoratorFinishedRef.v) {\n    throw new Error(\n      \"attempted to call \" + fnName + \" after decoration was finished\"\n    );\n  }\n}\n\nfunction assertCallable(fn, hint) {\n  if (typeof fn !== \"function\") {\n    throw new TypeError(hint + \" must be a function\");\n  }\n}\n\nfunction assertValidReturnValue(kind, value) {\n  var type = typeof value;\n\n  if (kind === 1 /* ACCESSOR */) {\n    if (type !== \"object\" || value === null) {\n      throw new TypeError(\n        \"accessor decorators must return an object with get, set, or init properties or void 0\"\n      );\n    }\n    if (value.get !== undefined) {\n      assertCallable(value.get, \"accessor.get\");\n    }\n    if (value.set !== undefined) {\n      assertCallable(value.set, \"accessor.set\");\n    }\n    if (value.init !== undefined) {\n      assertCallable(value.init, \"accessor.init\");\n    }\n  } else if (type !== \"function\") {\n    var hint;\n    if (kind === 0 /* FIELD */) {\n      hint = \"field\";\n    } else if (kind === 10 /* CLASS */) {\n      hint = \"class\";\n    } else {\n      hint = \"method\";\n    }\n    throw new TypeError(hint + \" decorators must return a function or void 0\");\n  }\n}\n\nfunction applyMemberDec(\n  ret,\n  base,\n  decInfo,\n  name,\n  kind,\n  isStatic,\n  isPrivate,\n  initializers\n) {\n  var decs = decInfo[0];\n\n  var desc, init, value;\n\n  if (isPrivate) {\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      desc = {\n        get: decInfo[3],\n        set: decInfo[4],\n      };\n    } else if (kind === 3 /* GETTER */) {\n      desc = {\n        get: decInfo[3],\n      };\n    } else if (kind === 4 /* SETTER */) {\n      desc = {\n        set: decInfo[3],\n      };\n    } else {\n      desc = {\n        value: decInfo[3],\n      };\n    }\n  } else if (kind !== 0 /* FIELD */) {\n    desc = Object.getOwnPropertyDescriptor(base, name);\n  }\n\n  if (kind === 1 /* ACCESSOR */) {\n    value = {\n      get: desc.get,\n      set: desc.set,\n    };\n  } else if (kind === 2 /* METHOD */) {\n    value = desc.value;\n  } else if (kind === 3 /* GETTER */) {\n    value = desc.get;\n  } else if (kind === 4 /* SETTER */) {\n    value = desc.set;\n  }\n\n  var newValue, get, set;\n\n  if (typeof decs === \"function\") {\n    newValue = memberDec(\n      decs,\n      name,\n      desc,\n      initializers,\n      kind,\n      isStatic,\n      isPrivate,\n      value\n    );\n\n    if (newValue !== void 0) {\n      assertValidReturnValue(kind, newValue);\n\n      if (kind === 0 /* FIELD */) {\n        init = newValue;\n      } else if (kind === 1 /* ACCESSOR */) {\n        init = newValue.init;\n        get = newValue.get || value.get;\n        set = newValue.set || value.set;\n\n        value = { get: get, set: set };\n      } else {\n        value = newValue;\n      }\n    }\n  } else {\n    for (var i = decs.length - 1; i >= 0; i--) {\n      var dec = decs[i];\n\n      newValue = memberDec(\n        dec,\n        name,\n        desc,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value\n      );\n\n      if (newValue !== void 0) {\n        assertValidReturnValue(kind, newValue);\n        var newInit;\n\n        if (kind === 0 /* FIELD */) {\n          newInit = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          newInit = newValue.init;\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n\n        if (newInit !== void 0) {\n          if (init === void 0) {\n            init = newInit;\n          } else if (typeof init === \"function\") {\n            init = [init, newInit];\n          } else {\n            init.push(newInit);\n          }\n        }\n      }\n    }\n  }\n\n  if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n    if (init === void 0) {\n      // If the initializer was void 0, sub in a dummy initializer\n      init = function (instance, init) {\n        return init;\n      };\n    } else if (typeof init !== \"function\") {\n      var ownInitializers = init;\n\n      init = function (instance, init) {\n        var value = init;\n\n        for (var i = 0; i < ownInitializers.length; i++) {\n          value = ownInitializers[i].call(instance, value);\n        }\n\n        return value;\n      };\n    } else {\n      var originalInitializer = init;\n\n      init = function (instance, init) {\n        return originalInitializer.call(instance, init);\n      };\n    }\n\n    ret.push(init);\n  }\n\n  if (kind !== 0 /* FIELD */) {\n    if (kind === 1 /* ACCESSOR */) {\n      desc.get = value.get;\n      desc.set = value.set;\n    } else if (kind === 2 /* METHOD */) {\n      desc.value = value;\n    } else if (kind === 3 /* GETTER */) {\n      desc.get = value;\n    } else if (kind === 4 /* SETTER */) {\n      desc.set = value;\n    }\n\n    if (isPrivate) {\n      if (kind === 1 /* ACCESSOR */) {\n        ret.push(function (instance, args) {\n          return value.get.call(instance, args);\n        });\n        ret.push(function (instance, args) {\n          return value.set.call(instance, args);\n        });\n      } else if (kind === 2 /* METHOD */) {\n        ret.push(value);\n      } else {\n        ret.push(function (instance, args) {\n          return value.call(instance, args);\n        });\n      }\n    } else {\n      Object.defineProperty(base, name, desc);\n    }\n  }\n}\n\nfunction applyMemberDecs(ret, Class, decInfos) {\n  var protoInitializers;\n  var staticInitializers;\n\n  var existingProtoNonFields = new Map();\n  var existingStaticNonFields = new Map();\n\n  for (var i = 0; i < decInfos.length; i++) {\n    var decInfo = decInfos[i];\n\n    // skip computed property names\n    if (!Array.isArray(decInfo)) continue;\n\n    var kind = decInfo[1];\n    var name = decInfo[2];\n    var isPrivate = decInfo.length > 3;\n\n    var isStatic = kind >= 5; /* STATIC */\n    var base;\n    var initializers;\n\n    if (isStatic) {\n      base = Class;\n      kind = kind - 5 /* STATIC */;\n      // initialize staticInitializers when we see a non-field static member\n      if (kind !== 0 /* FIELD */) {\n        staticInitializers = staticInitializers || [];\n        initializers = staticInitializers;\n      }\n    } else {\n      base = Class.prototype;\n      // initialize protoInitializers when we see a non-field member\n      if (kind !== 0 /* FIELD */) {\n        protoInitializers = protoInitializers || [];\n        initializers = protoInitializers;\n      }\n    }\n\n    if (kind !== 0 /* FIELD */ && !isPrivate) {\n      var existingNonFields = isStatic\n        ? existingStaticNonFields\n        : existingProtoNonFields;\n\n      var existingKind = existingNonFields.get(name) || 0;\n\n      if (\n        existingKind === true ||\n        (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n        (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n      ) {\n        throw new Error(\n          \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n            name\n        );\n      } else if (!existingKind && kind > 2 /* METHOD */) {\n        existingNonFields.set(name, kind);\n      } else {\n        existingNonFields.set(name, true);\n      }\n    }\n\n    applyMemberDec(\n      ret,\n      base,\n      decInfo,\n      name,\n      kind,\n      isStatic,\n      isPrivate,\n      initializers\n    );\n  }\n\n  pushInitializers(ret, protoInitializers);\n  pushInitializers(ret, staticInitializers);\n}\n\nfunction pushInitializers(ret, initializers) {\n  if (initializers) {\n    ret.push(function (instance) {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(instance);\n      }\n      return instance;\n    });\n  }\n}\n\nfunction applyClassDecs(ret, targetClass, classDecs) {\n  if (classDecs.length > 0) {\n    var initializers = [];\n    var newClass = targetClass;\n    var name = targetClass.name;\n\n    for (var i = classDecs.length - 1; i >= 0; i--) {\n      var decoratorFinishedRef = { v: false };\n\n      try {\n        var nextNewClass = classDecs[i](newClass, {\n          kind: \"class\",\n          name: name,\n          addInitializer: createAddInitializerMethod(\n            initializers,\n            decoratorFinishedRef\n          ),\n        });\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n\n      if (nextNewClass !== undefined) {\n        assertValidReturnValue(10 /* CLASS */, nextNewClass);\n        newClass = nextNewClass;\n      }\n    }\n\n    ret.push(newClass, function () {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(newClass);\n      }\n    });\n  }\n}\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\nexport default function applyDecs2203(targetClass, memberDecs, classDecs) {\n  var ret = [];\n  applyMemberDecs(ret, targetClass, memberDecs);\n  applyClassDecs(ret, targetClass, classDecs);\n  return ret;\n}\n"], "mappings": ";;;;;;;AAoBA,SAASA,0BAAT,CAAoCC,YAApC,EAAkDC,oBAAlD,EAAwE;EACtE,OAAO,SAASC,cAAT,CAAwBC,WAAxB,EAAqC;IAC1CC,iBAAiB,CAACH,oBAAD,EAAuB,gBAAvB,CAAjB;IACAI,cAAc,CAACF,WAAD,EAAc,gBAAd,CAAd;IACAH,YAAY,CAACM,IAAb,CAAkBH,WAAlB;EACD,CAJD;AAKD;;AAED,SAASI,SAAT,CACEC,GADF,EAEEC,IAFF,EAGEC,IAHF,EAIEV,YAJF,EAKEW,IALF,EAMEC,QANF,EAOEC,SAPF,EAQEC,KARF,EASE;EACA,IAAIC,OAAJ;;EAEA,QAAQJ,IAAR;IACE,KAAK,CAAL;MACEI,OAAO,GAAG,UAAV;MACA;;IACF,KAAK,CAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF,KAAK,CAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF,KAAK,CAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF;MACEA,OAAO,GAAG,OAAV;EAdJ;;EAiBA,IAAIC,GAAG,GAAG;IACRL,IAAI,EAAEI,OADE;IAERN,IAAI,EAAEI,SAAS,GAAG,MAAMJ,IAAT,GAAgBA,IAFvB;IAGRQ,MAAM,EAAEL,QAHA;IAIRM,OAAO,EAAEL;EAJD,CAAV;EAOA,IAAIZ,oBAAoB,GAAG;IAAEkB,CAAC,EAAE;EAAL,CAA3B;;EAEA,IAAIR,IAAI,KAAK,CAAb,EAA4B;IAC1BK,GAAG,CAACd,cAAJ,GAAqBH,0BAA0B,CAC7CC,YAD6C,EAE7CC,oBAF6C,CAA/C;EAID;;EAED,IAAImB,GAAJ,EAASC,GAAT;;EACA,IAAIV,IAAI,KAAK,CAAb,EAA4B;IAC1B,IAAIE,SAAJ,EAAe;MACbO,GAAG,GAAGV,IAAI,CAACU,GAAX;MACAC,GAAG,GAAGX,IAAI,CAACW,GAAX;IACD,CAHD,MAGO;MACLD,GAAG,GAAG,YAAY;QAChB,OAAO,KAAKX,IAAL,CAAP;MACD,CAFD;;MAGAY,GAAG,GAAG,UAAUF,CAAV,EAAa;QACjB,KAAKV,IAAL,IAAaU,CAAb;MACD,CAFD;IAGD;EACF,CAZD,MAYO,IAAIR,IAAI,KAAK,CAAb,EAA6B;IAClCS,GAAG,GAAG,YAAY;MAChB,OAAOV,IAAI,CAACI,KAAZ;IACD,CAFD;EAGD,CAJM,MAIA;IAEL,IAAIH,IAAI,KAAK,CAAT,IAA6BA,IAAI,KAAK,CAA1C,EAA0D;MACxDS,GAAG,GAAG,YAAY;QAChB,OAAOV,IAAI,CAACU,GAAL,CAASE,IAAT,CAAc,IAAd,CAAP;MACD,CAFD;IAGD;;IAED,IAAIX,IAAI,KAAK,CAAT,IAA6BA,IAAI,KAAK,CAA1C,EAA0D;MACxDU,GAAG,GAAG,UAAUF,CAAV,EAAa;QACjBT,IAAI,CAACW,GAAL,CAASC,IAAT,CAAc,IAAd,EAAoBH,CAApB;MACD,CAFD;IAGD;EACF;;EACDH,GAAG,CAACO,MAAJ,GACEH,GAAG,IAAIC,GAAP,GAAa;IAAED,GAAG,EAAEA,GAAP;IAAYC,GAAG,EAAEA;EAAjB,CAAb,GAAsCD,GAAG,GAAG;IAAEA,GAAG,EAAEA;EAAP,CAAH,GAAkB;IAAEC,GAAG,EAAEA;EAAP,CAD7D;;EAGA,IAAI;IACF,OAAOb,GAAG,CAACM,KAAD,EAAQE,GAAR,CAAV;EACD,CAFD,SAEU;IACRf,oBAAoB,CAACkB,CAArB,GAAyB,IAAzB;EACD;AACF;;AAED,SAASf,iBAAT,CAA2BH,oBAA3B,EAAiDuB,MAAjD,EAAyD;EACvD,IAAIvB,oBAAoB,CAACkB,CAAzB,EAA4B;IAC1B,MAAM,IAAIM,KAAJ,CACJ,uBAAuBD,MAAvB,GAAgC,gCAD5B,CAAN;EAGD;AACF;;AAED,SAASnB,cAAT,CAAwBqB,EAAxB,EAA4BC,IAA5B,EAAkC;EAChC,IAAI,OAAOD,EAAP,KAAc,UAAlB,EAA8B;IAC5B,MAAM,IAAIE,SAAJ,CAAcD,IAAI,GAAG,qBAArB,CAAN;EACD;AACF;;AAED,SAASE,sBAAT,CAAgClB,IAAhC,EAAsCG,KAAtC,EAA6C;EAC3C,IAAIgB,IAAI,GAAG,OAAOhB,KAAlB;;EAEA,IAAIH,IAAI,KAAK,CAAb,EAA+B;IAC7B,IAAImB,IAAI,KAAK,QAAT,IAAqBhB,KAAK,KAAK,IAAnC,EAAyC;MACvC,MAAM,IAAIc,SAAJ,CACJ,uFADI,CAAN;IAGD;;IACD,IAAId,KAAK,CAACM,GAAN,KAAcW,SAAlB,EAA6B;MAC3B1B,cAAc,CAACS,KAAK,CAACM,GAAP,EAAY,cAAZ,CAAd;IACD;;IACD,IAAIN,KAAK,CAACO,GAAN,KAAcU,SAAlB,EAA6B;MAC3B1B,cAAc,CAACS,KAAK,CAACO,GAAP,EAAY,cAAZ,CAAd;IACD;;IACD,IAAIP,KAAK,CAACkB,IAAN,KAAeD,SAAnB,EAA8B;MAC5B1B,cAAc,CAACS,KAAK,CAACkB,IAAP,EAAa,eAAb,CAAd;IACD;EACF,CAfD,MAeO,IAAIF,IAAI,KAAK,UAAb,EAAyB;IAC9B,IAAIH,IAAJ;;IACA,IAAIhB,IAAI,KAAK,CAAb,EAA4B;MAC1BgB,IAAI,GAAG,OAAP;IACD,CAFD,MAEO,IAAIhB,IAAI,KAAK,EAAb,EAA6B;MAClCgB,IAAI,GAAG,OAAP;IACD,CAFM,MAEA;MACLA,IAAI,GAAG,QAAP;IACD;;IACD,MAAM,IAAIC,SAAJ,CAAcD,IAAI,GAAG,8CAArB,CAAN;EACD;AACF;;AAED,SAASM,cAAT,CACEC,GADF,EAEEC,IAFF,EAGEC,OAHF,EAIE3B,IAJF,EAKEE,IALF,EAMEC,QANF,EAOEC,SAPF,EAQEb,YARF,EASE;EACA,IAAIqC,IAAI,GAAGD,OAAO,CAAC,CAAD,CAAlB;EAEA,IAAI1B,IAAJ,EAAUsB,IAAV,EAAgBlB,KAAhB;;EAEA,IAAID,SAAJ,EAAe;IACb,IAAIF,IAAI,KAAK,CAAT,IAA0BA,IAAI,KAAK,CAAvC,EAAyD;MACvDD,IAAI,GAAG;QACLU,GAAG,EAAEgB,OAAO,CAAC,CAAD,CADP;QAELf,GAAG,EAAEe,OAAO,CAAC,CAAD;MAFP,CAAP;IAID,CALD,MAKO,IAAIzB,IAAI,KAAK,CAAb,EAA6B;MAClCD,IAAI,GAAG;QACLU,GAAG,EAAEgB,OAAO,CAAC,CAAD;MADP,CAAP;IAGD,CAJM,MAIA,IAAIzB,IAAI,KAAK,CAAb,EAA6B;MAClCD,IAAI,GAAG;QACLW,GAAG,EAAEe,OAAO,CAAC,CAAD;MADP,CAAP;IAGD,CAJM,MAIA;MACL1B,IAAI,GAAG;QACLI,KAAK,EAAEsB,OAAO,CAAC,CAAD;MADT,CAAP;IAGD;EACF,CAnBD,MAmBO,IAAIzB,IAAI,KAAK,CAAb,EAA4B;IACjCD,IAAI,GAAG4B,MAAM,CAACC,wBAAP,CAAgCJ,IAAhC,EAAsC1B,IAAtC,CAAP;EACD;;EAED,IAAIE,IAAI,KAAK,CAAb,EAA+B;IAC7BG,KAAK,GAAG;MACNM,GAAG,EAAEV,IAAI,CAACU,GADJ;MAENC,GAAG,EAAEX,IAAI,CAACW;IAFJ,CAAR;EAID,CALD,MAKO,IAAIV,IAAI,KAAK,CAAb,EAA6B;IAClCG,KAAK,GAAGJ,IAAI,CAACI,KAAb;EACD,CAFM,MAEA,IAAIH,IAAI,KAAK,CAAb,EAA6B;IAClCG,KAAK,GAAGJ,IAAI,CAACU,GAAb;EACD,CAFM,MAEA,IAAIT,IAAI,KAAK,CAAb,EAA6B;IAClCG,KAAK,GAAGJ,IAAI,CAACW,GAAb;EACD;;EAED,IAAImB,QAAJ,EAAcpB,GAAd,EAAmBC,GAAnB;;EAEA,IAAI,OAAOgB,IAAP,KAAgB,UAApB,EAAgC;IAC9BG,QAAQ,GAAGjC,SAAS,CAClB8B,IADkB,EAElB5B,IAFkB,EAGlBC,IAHkB,EAIlBV,YAJkB,EAKlBW,IALkB,EAMlBC,QANkB,EAOlBC,SAPkB,EAQlBC,KARkB,CAApB;;IAWA,IAAI0B,QAAQ,KAAK,KAAK,CAAtB,EAAyB;MACvBX,sBAAsB,CAAClB,IAAD,EAAO6B,QAAP,CAAtB;;MAEA,IAAI7B,IAAI,KAAK,CAAb,EAA4B;QAC1BqB,IAAI,GAAGQ,QAAP;MACD,CAFD,MAEO,IAAI7B,IAAI,KAAK,CAAb,EAA+B;QACpCqB,IAAI,GAAGQ,QAAQ,CAACR,IAAhB;QACAZ,GAAG,GAAGoB,QAAQ,CAACpB,GAAT,IAAgBN,KAAK,CAACM,GAA5B;QACAC,GAAG,GAAGmB,QAAQ,CAACnB,GAAT,IAAgBP,KAAK,CAACO,GAA5B;QAEAP,KAAK,GAAG;UAAEM,GAAG,EAAEA,GAAP;UAAYC,GAAG,EAAEA;QAAjB,CAAR;MACD,CANM,MAMA;QACLP,KAAK,GAAG0B,QAAR;MACD;IACF;EACF,CA3BD,MA2BO;IACL,KAAK,IAAIC,CAAC,GAAGJ,IAAI,CAACK,MAAL,GAAc,CAA3B,EAA8BD,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;MACzC,IAAIjC,GAAG,GAAG6B,IAAI,CAACI,CAAD,CAAd;MAEAD,QAAQ,GAAGjC,SAAS,CAClBC,GADkB,EAElBC,IAFkB,EAGlBC,IAHkB,EAIlBV,YAJkB,EAKlBW,IALkB,EAMlBC,QANkB,EAOlBC,SAPkB,EAQlBC,KARkB,CAApB;;MAWA,IAAI0B,QAAQ,KAAK,KAAK,CAAtB,EAAyB;QACvBX,sBAAsB,CAAClB,IAAD,EAAO6B,QAAP,CAAtB;QACA,IAAIG,OAAJ;;QAEA,IAAIhC,IAAI,KAAK,CAAb,EAA4B;UAC1BgC,OAAO,GAAGH,QAAV;QACD,CAFD,MAEO,IAAI7B,IAAI,KAAK,CAAb,EAA+B;UACpCgC,OAAO,GAAGH,QAAQ,CAACR,IAAnB;UACAZ,GAAG,GAAGoB,QAAQ,CAACpB,GAAT,IAAgBN,KAAK,CAACM,GAA5B;UACAC,GAAG,GAAGmB,QAAQ,CAACnB,GAAT,IAAgBP,KAAK,CAACO,GAA5B;UAEAP,KAAK,GAAG;YAAEM,GAAG,EAAEA,GAAP;YAAYC,GAAG,EAAEA;UAAjB,CAAR;QACD,CANM,MAMA;UACLP,KAAK,GAAG0B,QAAR;QACD;;QAED,IAAIG,OAAO,KAAK,KAAK,CAArB,EAAwB;UACtB,IAAIX,IAAI,KAAK,KAAK,CAAlB,EAAqB;YACnBA,IAAI,GAAGW,OAAP;UACD,CAFD,MAEO,IAAI,OAAOX,IAAP,KAAgB,UAApB,EAAgC;YACrCA,IAAI,GAAG,CAACA,IAAD,EAAOW,OAAP,CAAP;UACD,CAFM,MAEA;YACLX,IAAI,CAAC1B,IAAL,CAAUqC,OAAV;UACD;QACF;MACF;IACF;EACF;;EAED,IAAIhC,IAAI,KAAK,CAAT,IAA0BA,IAAI,KAAK,CAAvC,EAAyD;IACvD,IAAIqB,IAAI,KAAK,KAAK,CAAlB,EAAqB;MAEnBA,IAAI,GAAG,UAAUY,QAAV,EAAoBZ,IAApB,EAA0B;QAC/B,OAAOA,IAAP;MACD,CAFD;IAGD,CALD,MAKO,IAAI,OAAOA,IAAP,KAAgB,UAApB,EAAgC;MACrC,IAAIa,eAAe,GAAGb,IAAtB;;MAEAA,IAAI,GAAG,UAAUY,QAAV,EAAoBZ,IAApB,EAA0B;QAC/B,IAAIlB,KAAK,GAAGkB,IAAZ;;QAEA,KAAK,IAAIS,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGI,eAAe,CAACH,MAApC,EAA4CD,CAAC,EAA7C,EAAiD;UAC/C3B,KAAK,GAAG+B,eAAe,CAACJ,CAAD,CAAf,CAAmBnB,IAAnB,CAAwBsB,QAAxB,EAAkC9B,KAAlC,CAAR;QACD;;QAED,OAAOA,KAAP;MACD,CARD;IASD,CAZM,MAYA;MACL,IAAIgC,mBAAmB,GAAGd,IAA1B;;MAEAA,IAAI,GAAG,UAAUY,QAAV,EAAoBZ,IAApB,EAA0B;QAC/B,OAAOc,mBAAmB,CAACxB,IAApB,CAAyBsB,QAAzB,EAAmCZ,IAAnC,CAAP;MACD,CAFD;IAGD;;IAEDE,GAAG,CAAC5B,IAAJ,CAAS0B,IAAT;EACD;;EAED,IAAIrB,IAAI,KAAK,CAAb,EAA4B;IAC1B,IAAIA,IAAI,KAAK,CAAb,EAA+B;MAC7BD,IAAI,CAACU,GAAL,GAAWN,KAAK,CAACM,GAAjB;MACAV,IAAI,CAACW,GAAL,GAAWP,KAAK,CAACO,GAAjB;IACD,CAHD,MAGO,IAAIV,IAAI,KAAK,CAAb,EAA6B;MAClCD,IAAI,CAACI,KAAL,GAAaA,KAAb;IACD,CAFM,MAEA,IAAIH,IAAI,KAAK,CAAb,EAA6B;MAClCD,IAAI,CAACU,GAAL,GAAWN,KAAX;IACD,CAFM,MAEA,IAAIH,IAAI,KAAK,CAAb,EAA6B;MAClCD,IAAI,CAACW,GAAL,GAAWP,KAAX;IACD;;IAED,IAAID,SAAJ,EAAe;MACb,IAAIF,IAAI,KAAK,CAAb,EAA+B;QAC7BuB,GAAG,CAAC5B,IAAJ,CAAS,UAAUsC,QAAV,EAAoBG,IAApB,EAA0B;UACjC,OAAOjC,KAAK,CAACM,GAAN,CAAUE,IAAV,CAAesB,QAAf,EAAyBG,IAAzB,CAAP;QACD,CAFD;QAGAb,GAAG,CAAC5B,IAAJ,CAAS,UAAUsC,QAAV,EAAoBG,IAApB,EAA0B;UACjC,OAAOjC,KAAK,CAACO,GAAN,CAAUC,IAAV,CAAesB,QAAf,EAAyBG,IAAzB,CAAP;QACD,CAFD;MAGD,CAPD,MAOO,IAAIpC,IAAI,KAAK,CAAb,EAA6B;QAClCuB,GAAG,CAAC5B,IAAJ,CAASQ,KAAT;MACD,CAFM,MAEA;QACLoB,GAAG,CAAC5B,IAAJ,CAAS,UAAUsC,QAAV,EAAoBG,IAApB,EAA0B;UACjC,OAAOjC,KAAK,CAACQ,IAAN,CAAWsB,QAAX,EAAqBG,IAArB,CAAP;QACD,CAFD;MAGD;IACF,CAfD,MAeO;MACLT,MAAM,CAACU,cAAP,CAAsBb,IAAtB,EAA4B1B,IAA5B,EAAkCC,IAAlC;IACD;EACF;AACF;;AAED,SAASuC,eAAT,CAAyBf,GAAzB,EAA8BgB,KAA9B,EAAqCC,QAArC,EAA+C;EAC7C,IAAIC,iBAAJ;EACA,IAAIC,kBAAJ;EAEA,IAAIC,sBAAsB,GAAG,IAAIC,GAAJ,EAA7B;EACA,IAAIC,uBAAuB,GAAG,IAAID,GAAJ,EAA9B;;EAEA,KAAK,IAAId,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGU,QAAQ,CAACT,MAA7B,EAAqCD,CAAC,EAAtC,EAA0C;IACxC,IAAIL,OAAO,GAAGe,QAAQ,CAACV,CAAD,CAAtB;IAGA,IAAI,CAACgB,KAAK,CAACC,OAAN,CAActB,OAAd,CAAL,EAA6B;IAE7B,IAAIzB,IAAI,GAAGyB,OAAO,CAAC,CAAD,CAAlB;IACA,IAAI3B,IAAI,GAAG2B,OAAO,CAAC,CAAD,CAAlB;IACA,IAAIvB,SAAS,GAAGuB,OAAO,CAACM,MAAR,GAAiB,CAAjC;IAEA,IAAI9B,QAAQ,GAAGD,IAAI,IAAI,CAAvB;IACA,IAAIwB,IAAJ;IACA,IAAInC,YAAJ;;IAEA,IAAIY,QAAJ,EAAc;MACZuB,IAAI,GAAGe,KAAP;MACAvC,IAAI,GAAGA,IAAI,GAAG,CAAd;;MAEA,IAAIA,IAAI,KAAK,CAAb,EAA4B;QAC1B0C,kBAAkB,GAAGA,kBAAkB,IAAI,EAA3C;QACArD,YAAY,GAAGqD,kBAAf;MACD;IACF,CARD,MAQO;MACLlB,IAAI,GAAGe,KAAK,CAACS,SAAb;;MAEA,IAAIhD,IAAI,KAAK,CAAb,EAA4B;QAC1ByC,iBAAiB,GAAGA,iBAAiB,IAAI,EAAzC;QACApD,YAAY,GAAGoD,iBAAf;MACD;IACF;;IAED,IAAIzC,IAAI,KAAK,CAAT,IAA0B,CAACE,SAA/B,EAA0C;MACxC,IAAI+C,iBAAiB,GAAGhD,QAAQ,GAC5B4C,uBAD4B,GAE5BF,sBAFJ;MAIA,IAAIO,YAAY,GAAGD,iBAAiB,CAACxC,GAAlB,CAAsBX,IAAtB,KAA+B,CAAlD;;MAEA,IACEoD,YAAY,KAAK,IAAjB,IACCA,YAAY,KAAK,CAAjB,IAAmClD,IAAI,KAAK,CAD7C,IAECkD,YAAY,KAAK,CAAjB,IAAmClD,IAAI,KAAK,CAH/C,EAIE;QACA,MAAM,IAAIc,KAAJ,CACJ,0MACEhB,IAFE,CAAN;MAID,CATD,MASO,IAAI,CAACoD,YAAD,IAAiBlD,IAAI,GAAG,CAA5B,EAA4C;QACjDiD,iBAAiB,CAACvC,GAAlB,CAAsBZ,IAAtB,EAA4BE,IAA5B;MACD,CAFM,MAEA;QACLiD,iBAAiB,CAACvC,GAAlB,CAAsBZ,IAAtB,EAA4B,IAA5B;MACD;IACF;;IAEDwB,cAAc,CACZC,GADY,EAEZC,IAFY,EAGZC,OAHY,EAIZ3B,IAJY,EAKZE,IALY,EAMZC,QANY,EAOZC,SAPY,EAQZb,YARY,CAAd;EAUD;;EAED8D,gBAAgB,CAAC5B,GAAD,EAAMkB,iBAAN,CAAhB;EACAU,gBAAgB,CAAC5B,GAAD,EAAMmB,kBAAN,CAAhB;AACD;;AAED,SAASS,gBAAT,CAA0B5B,GAA1B,EAA+BlC,YAA/B,EAA6C;EAC3C,IAAIA,YAAJ,EAAkB;IAChBkC,GAAG,CAAC5B,IAAJ,CAAS,UAAUsC,QAAV,EAAoB;MAC3B,KAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzC,YAAY,CAAC0C,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;QAC5CzC,YAAY,CAACyC,CAAD,CAAZ,CAAgBnB,IAAhB,CAAqBsB,QAArB;MACD;;MACD,OAAOA,QAAP;IACD,CALD;EAMD;AACF;;AAED,SAASmB,cAAT,CAAwB7B,GAAxB,EAA6B8B,WAA7B,EAA0CC,SAA1C,EAAqD;EACnD,IAAIA,SAAS,CAACvB,MAAV,GAAmB,CAAvB,EAA0B;IACxB,IAAI1C,YAAY,GAAG,EAAnB;IACA,IAAIkE,QAAQ,GAAGF,WAAf;IACA,IAAIvD,IAAI,GAAGuD,WAAW,CAACvD,IAAvB;;IAEA,KAAK,IAAIgC,CAAC,GAAGwB,SAAS,CAACvB,MAAV,GAAmB,CAAhC,EAAmCD,CAAC,IAAI,CAAxC,EAA2CA,CAAC,EAA5C,EAAgD;MAC9C,IAAIxC,oBAAoB,GAAG;QAAEkB,CAAC,EAAE;MAAL,CAA3B;;MAEA,IAAI;QACF,IAAIgD,YAAY,GAAGF,SAAS,CAACxB,CAAD,CAAT,CAAayB,QAAb,EAAuB;UACxCvD,IAAI,EAAE,OADkC;UAExCF,IAAI,EAAEA,IAFkC;UAGxCP,cAAc,EAAEH,0BAA0B,CACxCC,YADwC,EAExCC,oBAFwC;QAHF,CAAvB,CAAnB;MAQD,CATD,SASU;QACRA,oBAAoB,CAACkB,CAArB,GAAyB,IAAzB;MACD;;MAED,IAAIgD,YAAY,KAAKpC,SAArB,EAAgC;QAC9BF,sBAAsB,CAAC,EAAD,EAAiBsC,YAAjB,CAAtB;QACAD,QAAQ,GAAGC,YAAX;MACD;IACF;;IAEDjC,GAAG,CAAC5B,IAAJ,CAAS4D,QAAT,EAAmB,YAAY;MAC7B,KAAK,IAAIzB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGzC,YAAY,CAAC0C,MAAjC,EAAyCD,CAAC,EAA1C,EAA8C;QAC5CzC,YAAY,CAACyC,CAAD,CAAZ,CAAgBnB,IAAhB,CAAqB4C,QAArB;MACD;IACF,CAJD;EAKD;AACF;;AAmJc,SAASE,aAAT,CAAuBJ,WAAvB,EAAoCK,UAApC,EAAgDJ,SAAhD,EAA2D;EACxE,IAAI/B,GAAG,GAAG,EAAV;EACAe,eAAe,CAACf,GAAD,EAAM8B,WAAN,EAAmBK,UAAnB,CAAf;EACAN,cAAc,CAAC7B,GAAD,EAAM8B,WAAN,EAAmBC,SAAnB,CAAd;EACA,OAAO/B,GAAP;AACD"}