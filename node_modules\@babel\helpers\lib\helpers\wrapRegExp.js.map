{"version": 3, "names": ["_wrapRegExp", "re", "groups", "BabelRegExp", "undefined", "_super", "RegExp", "prototype", "_groups", "WeakMap", "flags", "_this", "set", "get", "setPrototypeOf", "inherits", "exec", "str", "result", "call", "buildGroups", "Symbol", "replace", "substitution", "_", "name", "args", "arguments", "length", "slice", "push", "apply", "g", "Object", "keys", "reduce", "i", "k", "create"], "sources": ["../../src/helpers/wrapRegExp.js"], "sourcesContent": ["/* @minVersion 7.19.0 */\n\nimport setPrototypeOf from \"setPrototypeOf\";\nimport inherits from \"inherits\";\n\nexport default function _wrapRegExp() {\n  _wrapRegExp = function (re, groups) {\n    return new BabelRegExp(re, undefined, groups);\n  };\n\n  var _super = RegExp.prototype;\n  var _groups = new WeakMap();\n\n  function BabelRegExp(re, flags, groups) {\n    var _this = new RegExp(re, flags);\n    // if the regex is recreated with 'g' flag\n    _groups.set(_this, groups || _groups.get(re));\n    return setPrototypeOf(_this, BabelRegExp.prototype);\n  }\n  inherits(BabelRegExp, RegExp);\n\n  BabelRegExp.prototype.exec = function (str) {\n    var result = _super.exec.call(this, str);\n    if (result) result.groups = buildGroups(result, this);\n    return result;\n  };\n  BabelRegExp.prototype[Symbol.replace] = function (str, substitution) {\n    if (typeof substitution === \"string\") {\n      var groups = _groups.get(this);\n      return _super[Symbol.replace].call(\n        this,\n        str,\n        substitution.replace(/\\$<([^>]+)>/g, function (_, name) {\n          return \"$\" + groups[name];\n        })\n      );\n    } else if (typeof substitution === \"function\") {\n      var _this = this;\n      return _super[Symbol.replace].call(this, str, function () {\n        var args = arguments;\n        // Modern engines already pass result.groups returned by exec() as the last arg.\n        if (typeof args[args.length - 1] !== \"object\") {\n          args = [].slice.call(args);\n          args.push(buildGroups(args, _this));\n        }\n        return substitution.apply(this, args);\n      });\n    } else {\n      return _super[Symbol.replace].call(this, str, substitution);\n    }\n  };\n\n  function buildGroups(result, re) {\n    // NOTE: This function should return undefined if there are no groups,\n    // but in that case Babel doesn't add the wrapper anyway.\n\n    var g = _groups.get(re);\n    return Object.keys(g).reduce(function (groups, name) {\n      var i = g[name];\n      if (typeof i === \"number\") groups[name] = result[i];\n      else {\n        // i is an array of indexes\n        var k = 0;\n        // if no group matched, we stop at k = i.length - 1 and then\n        // we store result[i[i.length - 1]] which is undefined.\n        while (result[i[k]] === undefined && k + 1 < i.length) k++;\n        groups[name] = result[i[k]];\n      }\n      return groups;\n    }, Object.create(null));\n  }\n\n  return _wrapRegExp.apply(this, arguments);\n}\n"], "mappings": ";;;;;;;AAEA;;AACA;;AAEe,SAASA,WAAT,GAAuB;EACpC,kBAAAA,WAAW,GAAG,UAAUC,EAAV,EAAcC,MAAd,EAAsB;IAClC,OAAO,IAAIC,WAAJ,CAAgBF,EAAhB,EAAoBG,SAApB,EAA+BF,MAA/B,CAAP;EACD,CAFD;;EAIA,IAAIG,MAAM,GAAGC,MAAM,CAACC,SAApB;;EACA,IAAIC,OAAO,GAAG,IAAIC,OAAJ,EAAd;;EAEA,SAASN,WAAT,CAAqBF,EAArB,EAAyBS,KAAzB,EAAgCR,MAAhC,EAAwC;IACtC,IAAIS,KAAK,GAAG,IAAIL,MAAJ,CAAWL,EAAX,EAAeS,KAAf,CAAZ;;IAEAF,OAAO,CAACI,GAAR,CAAYD,KAAZ,EAAmBT,MAAM,IAAIM,OAAO,CAACK,GAAR,CAAYZ,EAAZ,CAA7B;;IACA,OAAOa,eAAc,CAACH,KAAD,EAAQR,WAAW,CAACI,SAApB,CAArB;EACD;;EACDQ,SAAQ,CAACZ,WAAD,EAAcG,MAAd,CAAR;;EAEAH,WAAW,CAACI,SAAZ,CAAsBS,IAAtB,GAA6B,UAAUC,GAAV,EAAe;IAC1C,IAAIC,MAAM,GAAGb,MAAM,CAACW,IAAP,CAAYG,IAAZ,CAAiB,IAAjB,EAAuBF,GAAvB,CAAb;;IACA,IAAIC,MAAJ,EAAYA,MAAM,CAAChB,MAAP,GAAgBkB,WAAW,CAACF,MAAD,EAAS,IAAT,CAA3B;IACZ,OAAOA,MAAP;EACD,CAJD;;EAKAf,WAAW,CAACI,SAAZ,CAAsBc,MAAM,CAACC,OAA7B,IAAwC,UAAUL,GAAV,EAAeM,YAAf,EAA6B;IACnE,IAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;MACpC,IAAIrB,MAAM,GAAGM,OAAO,CAACK,GAAR,CAAY,IAAZ,CAAb;;MACA,OAAOR,MAAM,CAACgB,MAAM,CAACC,OAAR,CAAN,CAAuBH,IAAvB,CACL,IADK,EAELF,GAFK,EAGLM,YAAY,CAACD,OAAb,CAAqB,cAArB,EAAqC,UAAUE,CAAV,EAAaC,IAAb,EAAmB;QACtD,OAAO,MAAMvB,MAAM,CAACuB,IAAD,CAAnB;MACD,CAFD,CAHK,CAAP;IAOD,CATD,MASO,IAAI,OAAOF,YAAP,KAAwB,UAA5B,EAAwC;MAC7C,IAAIZ,KAAK,GAAG,IAAZ;;MACA,OAAON,MAAM,CAACgB,MAAM,CAACC,OAAR,CAAN,CAAuBH,IAAvB,CAA4B,IAA5B,EAAkCF,GAAlC,EAAuC,YAAY;QACxD,IAAIS,IAAI,GAAGC,SAAX;;QAEA,IAAI,OAAOD,IAAI,CAACA,IAAI,CAACE,MAAL,GAAc,CAAf,CAAX,KAAiC,QAArC,EAA+C;UAC7CF,IAAI,GAAG,GAAGG,KAAH,CAASV,IAAT,CAAcO,IAAd,CAAP;UACAA,IAAI,CAACI,IAAL,CAAUV,WAAW,CAACM,IAAD,EAAOf,KAAP,CAArB;QACD;;QACD,OAAOY,YAAY,CAACQ,KAAb,CAAmB,IAAnB,EAAyBL,IAAzB,CAAP;MACD,CARM,CAAP;IASD,CAXM,MAWA;MACL,OAAOrB,MAAM,CAACgB,MAAM,CAACC,OAAR,CAAN,CAAuBH,IAAvB,CAA4B,IAA5B,EAAkCF,GAAlC,EAAuCM,YAAvC,CAAP;IACD;EACF,CAxBD;;EA0BA,SAASH,WAAT,CAAqBF,MAArB,EAA6BjB,EAA7B,EAAiC;IAI/B,IAAI+B,CAAC,GAAGxB,OAAO,CAACK,GAAR,CAAYZ,EAAZ,CAAR;;IACA,OAAOgC,MAAM,CAACC,IAAP,CAAYF,CAAZ,EAAeG,MAAf,CAAsB,UAAUjC,MAAV,EAAkBuB,IAAlB,EAAwB;MACnD,IAAIW,CAAC,GAAGJ,CAAC,CAACP,IAAD,CAAT;MACA,IAAI,OAAOW,CAAP,KAAa,QAAjB,EAA2BlC,MAAM,CAACuB,IAAD,CAAN,GAAeP,MAAM,CAACkB,CAAD,CAArB,CAA3B,KACK;QAEH,IAAIC,CAAC,GAAG,CAAR;;QAGA,OAAOnB,MAAM,CAACkB,CAAC,CAACC,CAAD,CAAF,CAAN,KAAiBjC,SAAjB,IAA8BiC,CAAC,GAAG,CAAJ,GAAQD,CAAC,CAACR,MAA/C,EAAuDS,CAAC;;QACxDnC,MAAM,CAACuB,IAAD,CAAN,GAAeP,MAAM,CAACkB,CAAC,CAACC,CAAD,CAAF,CAArB;MACD;MACD,OAAOnC,MAAP;IACD,CAZM,EAYJ+B,MAAM,CAACK,MAAP,CAAc,IAAd,CAZI,CAAP;EAaD;;EAED,OAAOtC,WAAW,CAAC+B,KAAZ,CAAkB,IAAlB,EAAwBJ,SAAxB,CAAP;AACD"}