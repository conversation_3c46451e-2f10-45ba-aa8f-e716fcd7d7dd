"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            var status = window.saveButtonStatus;\n            return status === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        // 只有 save 状态才弹窗\n        if (shouldAutoSave()) {\n            // 显示确认对话框\n            var message = \"您有未保存的更改。确定要离开吗？\";\n            event.returnValue = message;\n            // 使用延迟检测用户选择\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"，执行自动保存\n                performAutoSave();\n            }, 100);\n            return message;\n        }\n    // view 状态：直接允许离开，不弹窗\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 按照用户逻辑：只有 save 状态才处理路由变化\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, success, confirmed, error, confirmed1, confirmed2;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            9\n                        ];\n                        // 阻止路由跳转\n                        router.events.emit(\"routeChangeError\", new Error(\"Auto-saving before route change\"), url);\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                        if (!isNoteNavigation) return [\n                            3,\n                            6\n                        ];\n                        // 笔记跳转：直接自动保存，不弹窗\n                        isAutoSavingRef.current = true;\n                        _state.label = 1;\n                    case 1:\n                        _state.trys.push([\n                            1,\n                            3,\n                            4,\n                            5\n                        ]);\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 2:\n                        success = _state.sent();\n                        if (success) {\n                            // 自动保存成功，继续跳转\n                            router.push(url);\n                        } else {\n                            confirmed = window.confirm(\"自动保存失败。是否强制离开？\");\n                            if (confirmed) {\n                                router.push(url);\n                            }\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 3:\n                        error = _state.sent();\n                        console.error(\"自动保存出错:\", error);\n                        confirmed1 = window.confirm(\"自动保存出错。是否强制离开？\");\n                        if (confirmed1) {\n                            router.push(url);\n                        }\n                        return [\n                            3,\n                            5\n                        ];\n                    case 4:\n                        isAutoSavingRef.current = false;\n                        return [\n                            7\n                        ];\n                    case 5:\n                        return [\n                            3,\n                            9\n                        ];\n                    case 6:\n                        confirmed2 = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed2) return [\n                            3,\n                            7\n                        ];\n                        // 用户选择离开：弃用 IndexedDB 中的修改，直接跳转\n                        router.push(url);\n                        return [\n                            3,\n                            9\n                        ];\n                    case 7:\n                        // 用户选择取消：自动保存\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 8:\n                        _state.sent();\n                        _state.label = 9;\n                    case 9:\n                        return [\n                            2\n                        ];\n                }\n            });\n        // view 状态：不调用 useAutoSaveOnLeave，直接允许跳转\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});