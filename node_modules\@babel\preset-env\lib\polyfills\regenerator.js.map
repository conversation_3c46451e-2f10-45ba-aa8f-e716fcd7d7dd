{"version": 3, "names": ["isRegeneratorSource", "source", "visitor", "ImportDeclaration", "path", "getImportSource", "regeneratorImportExcluded", "remove", "Program", "get", "for<PERSON>ach", "bodyPath", "getRequireSource", "name", "pre", "post", "opts", "debug", "filename", "file", "process", "env", "BABEL_ENV", "replace", "console", "log"], "sources": ["../../src/polyfills/regenerator.ts"], "sourcesContent": ["import { getImportSource, getRequireSource } from \"./utils\";\nimport type { Visitor } from \"@babel/traverse\";\nimport type { PluginObject, PluginPass } from \"@babel/core\";\n\nfunction isRegeneratorSource(source: string) {\n  return (\n    source === \"regenerator-runtime/runtime\" ||\n    source === \"regenerator-runtime/runtime.js\"\n  );\n}\n\ntype State = {\n  regeneratorImportExcluded: boolean;\n};\n\nexport default function (): PluginObject<State & PluginPass> {\n  const visitor: Visitor<State & PluginPass> = {\n    ImportDeclaration(path) {\n      if (isRegeneratorSource(getImportSource(path))) {\n        this.regeneratorImportExcluded = true;\n        path.remove();\n      }\n    },\n    Program(path) {\n      path.get(\"body\").forEach(bodyPath => {\n        if (isRegeneratorSource(getRequireSource(bodyPath))) {\n          this.regeneratorImportExcluded = true;\n          bodyPath.remove();\n        }\n      });\n    },\n  };\n\n  return {\n    name: \"preset-env/remove-regenerator\",\n    visitor,\n    pre() {\n      this.regeneratorImportExcluded = false;\n    },\n    post() {\n      if (this.opts.debug && this.regeneratorImportExcluded) {\n        let filename = this.file.opts.filename;\n        // normalize filename to generate consistent preset-env test fixtures\n        if (process.env.BABEL_ENV === \"test\") {\n          filename = filename.replace(/\\\\/g, \"/\");\n        }\n        console.log(\n          `\\n[${filename}] Based on your targets, regenerator-runtime import excluded.`,\n        );\n      }\n    },\n  };\n}\n"], "mappings": ";;;;;;;AAAA;;AAIA,SAASA,mBAAT,CAA6BC,MAA7B,EAA6C;EAC3C,OACEA,MAAM,KAAK,6BAAX,IACAA,MAAM,KAAK,gCAFb;AAID;;AAMc,oBAA8C;EAC3D,MAAMC,OAAoC,GAAG;IAC3CC,iBAAiB,CAACC,IAAD,EAAO;MACtB,IAAIJ,mBAAmB,CAAC,IAAAK,sBAAA,EAAgBD,IAAhB,CAAD,CAAvB,EAAgD;QAC9C,KAAKE,yBAAL,GAAiC,IAAjC;QACAF,IAAI,CAACG,MAAL;MACD;IACF,CAN0C;;IAO3CC,OAAO,CAACJ,IAAD,EAAO;MACZA,IAAI,CAACK,GAAL,CAAS,MAAT,EAAiBC,OAAjB,CAAyBC,QAAQ,IAAI;QACnC,IAAIX,mBAAmB,CAAC,IAAAY,uBAAA,EAAiBD,QAAjB,CAAD,CAAvB,EAAqD;UACnD,KAAKL,yBAAL,GAAiC,IAAjC;UACAK,QAAQ,CAACJ,MAAT;QACD;MACF,CALD;IAMD;;EAd0C,CAA7C;EAiBA,OAAO;IACLM,IAAI,EAAE,+BADD;IAELX,OAFK;;IAGLY,GAAG,GAAG;MACJ,KAAKR,yBAAL,GAAiC,KAAjC;IACD,CALI;;IAMLS,IAAI,GAAG;MACL,IAAI,KAAKC,IAAL,CAAUC,KAAV,IAAmB,KAAKX,yBAA5B,EAAuD;QACrD,IAAIY,QAAQ,GAAG,KAAKC,IAAL,CAAUH,IAAV,CAAeE,QAA9B;;QAEA,IAAIE,OAAO,CAACC,GAAR,CAAYC,SAAZ,KAA0B,MAA9B,EAAsC;UACpCJ,QAAQ,GAAGA,QAAQ,CAACK,OAAT,CAAiB,KAAjB,EAAwB,GAAxB,CAAX;QACD;;QACDC,OAAO,CAACC,GAAR,CACG,MAAKP,QAAS,+DADjB;MAGD;IACF;;EAjBI,CAAP;AAmBD"}