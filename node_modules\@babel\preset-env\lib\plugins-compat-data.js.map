{"version": 3, "names": ["keys", "Object", "plugins", "filterAvailable", "originalPlugins", "pluginsBugfixes", "originalPluginsBugfixes", "overlappingPlugins", "originalOverlappingPlugins", "data", "result", "plugin", "hasOwnProperty", "call", "availablePlugins"], "sources": ["../src/plugins-compat-data.ts"], "sourcesContent": ["import originalPlugins from \"@babel/compat-data/plugins\";\nimport originalPluginsBugfixes from \"@babel/compat-data/plugin-bugfixes\";\nimport originalOverlappingPlugins from \"@babel/compat-data/overlapping-plugins\";\nimport availablePlugins from \"./available-plugins\";\n\nconst keys: <O extends object>(o: O) => (keyof O)[] = Object.keys;\n\nexport const plugins = filterAvailable(originalPlugins);\nexport const pluginsBugfixes = filterAvailable(originalPluginsBugfixes);\nexport const overlappingPlugins = filterAvailable(originalOverlappingPlugins);\n\nfunction filterAvailable<Data extends { [name: string]: unknown }>(\n  data: Data,\n): { [Name in keyof Data & keyof typeof availablePlugins]: Data[Name] } {\n  const result = {} as any;\n  for (const plugin of keys(data)) {\n    if (Object.hasOwnProperty.call(availablePlugins, plugin)) {\n      result[plugin] = data[plugin];\n    }\n  }\n  return result;\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AACA;;AAEA,MAAMA,IAA6C,GAAGC,MAAM,CAACD,IAA7D;AAEO,MAAME,OAAO,GAAGC,eAAe,CAACC,QAAD,CAA/B;;AACA,MAAMC,eAAe,GAAGF,eAAe,CAACG,eAAD,CAAvC;;AACA,MAAMC,kBAAkB,GAAGJ,eAAe,CAACK,mBAAD,CAA1C;;;AAEP,SAASL,eAAT,CACEM,IADF,EAEwE;EACtE,MAAMC,MAAM,GAAG,EAAf;;EACA,KAAK,MAAMC,MAAX,IAAqBX,IAAI,CAACS,IAAD,CAAzB,EAAiC;IAC/B,IAAIR,MAAM,CAACW,cAAP,CAAsBC,IAAtB,CAA2BC,yBAA3B,EAA6CH,MAA7C,CAAJ,EAA0D;MACxDD,MAAM,CAACC,MAAD,CAAN,GAAiBF,IAAI,CAACE,MAAD,CAArB;IACD;EACF;;EACD,OAAOD,MAAP;AACD"}