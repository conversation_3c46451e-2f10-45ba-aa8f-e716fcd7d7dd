# motea Environment Configuration - Supabase PostgreSQL
# Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>
# Modified and maintained by waycaan, 2025.

# ===========================================
# Application Configuration
# ===========================================
NODE_ENV=production
PORT=3000

# ===========================================
# Database Configuration - Supabase PostgreSQL
# ===========================================
# Supabase provides PostgreSQL with additional features like real-time subscriptions
# Good for applications that might need additional backend services

# Primary database connection (REQUIRED)
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Explicit provider setting (OPTIONAL - auto-detected from URL)
DB_PROVIDER=supabase

# Database table prefix (OPTIONAL)
# STORE_PREFIX=motea_

# ===========================================
# Supabase-specific Configuration (OPTIONAL)
# ===========================================
# These are optional if you only use direct PostgreSQL connection
# Required if you plan to use Supabase's additional features

# Supabase project URL
# SUPABASE_URL=https://xxx.supabase.co

# Supabase anonymous key (public key)
# SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase service role key (private key - use with caution)
# SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# ===========================================
# Supabase Configuration Tips
# ===========================================
# 1. SSL is required for Supabase connections
# 2. Use connection pooling for better performance
# 3. Consider using Supabase Edge Functions for serverless logic
# 4. Database includes built-in auth and real-time features

# ===========================================
# Authentication Configuration
# ===========================================
# Option 1: Set a password for the application
PASSWORD=your_secure_password_here

# Option 2: Disable password authentication (uncomment below)
# DISABLE_PASSWORD=true

# ===========================================
# Performance Configuration
# ===========================================
# Number of notes to preload (recommended: 10-20 for Supabase)
PRELOAD_NOTES_COUNT=15

# Logging level
LOG_LEVEL=info

# ===========================================
# Session Configuration
# ===========================================
# Session secret for secure cookies
SESSION_SECRET=your_session_secret_here

# Use secure cookies in production
COOKIE_SECURE=true

# ===========================================
# Optional Configuration
# ===========================================
# Base URL for the application
# BASE_URL=https://your-domain.com

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# Supabase Database Setup Instructions
# ===========================================
# 1. Sign up at https://supabase.com
# 2. Create a new project
# 3. Choose a region close to your users
# 4. Go to Settings > Database
# 5. Copy the connection string (URI format)
# 6. Replace the DATABASE_URL above with your connection string
# 7. The connection string format is:
#    postgresql://postgres:<EMAIL>:5432/postgres

# ===========================================
# Connection String Formats
# ===========================================
# Direct PostgreSQL connection (recommended for motea):
# postgresql://postgres:<EMAIL>:5432/postgres

# With SSL mode explicitly set:
# postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require

# Connection pooling (for high-traffic applications):
# postgresql://postgres:<EMAIL>:6543/postgres?pgbouncer=true

# ===========================================
# Deployment Notes
# ===========================================
# - Supabase includes connection pooling via PgBouncer
# - SSL is required and enabled by default
# - Free tier includes 500MB database storage
# - Supports real-time subscriptions if needed in future
# - Includes built-in authentication system (not used by motea)
# - Good for applications that might expand beyond note-taking
