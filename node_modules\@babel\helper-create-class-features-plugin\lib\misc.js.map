{"version": 3, "names": ["findBareSupers", "traverse", "visitors", "merge", "Super", "path", "node", "parentPath", "isCallExpression", "callee", "push", "environmentVisitor", "referenceVisitor", "skip", "ReferencedIdentifier", "scope", "hasOwnBinding", "name", "rename", "handleClassTDZ", "state", "classBinding", "getBinding", "classNameTDZError", "file", "addHelper", "throwNode", "t", "callExpression", "stringLiteral", "replaceWith", "sequenceExpression", "classFieldDefinitionEvaluationTDZVisitor", "injectInitialization", "constructor", "nodes", "renamer", "length", "isDerived", "superClass", "newConstructor", "classMethod", "identifier", "blockStatement", "params", "restElement", "body", "template", "statement", "ast", "get", "unshiftContainer", "bareSupers", "<PERSON><PERSON><PERSON><PERSON>", "bareSuper", "insertAfter", "map", "n", "cloneNode", "extractComputedKeys", "computedPaths", "declarations", "id", "computedPath", "computedKey", "isReferencedIdentifier", "computedNode", "isConstantExpression", "ident", "generateUidIdentifierBasedOnNode", "key", "kind", "expressionStatement", "assignmentExpression"], "sources": ["../src/misc.ts"], "sourcesContent": ["import { template, traverse, types as t } from \"@babel/core\";\nimport type { File } from \"@babel/core\";\nimport type { NodePath, Scope, Visitor, Binding } from \"@babel/traverse\";\nimport environmentVisitor from \"@babel/helper-environment-visitor\";\n\nconst findBareSupers = traverse.visitors.merge<NodePath<t.CallExpression>[]>([\n  {\n    Super(path) {\n      const { node, parentPath } = path;\n      if (parentPath.isCallExpression({ callee: node })) {\n        this.push(parentPath);\n      }\n    },\n  },\n  environmentVisitor,\n]);\n\nconst referenceVisitor: Visitor<{ scope: Scope }> = {\n  \"TSTypeAnnotation|TypeAnnotation\"(\n    path: NodePath<t.TSTypeAnnotation | t.TypeAnnotation>,\n  ) {\n    path.skip();\n  },\n\n  ReferencedIdentifier(path: NodePath<t.Identifier>, { scope }) {\n    if (scope.hasOwnBinding(path.node.name)) {\n      scope.rename(path.node.name);\n      path.skip();\n    }\n  },\n};\n\ntype HandleClassTDZState = {\n  classBinding: Binding;\n  file: File;\n};\n\nfunction handleClassTDZ(\n  path: NodePath<t.Identifier>,\n  state: HandleClassTDZState,\n) {\n  if (\n    state.classBinding &&\n    state.classBinding === path.scope.getBinding(path.node.name)\n  ) {\n    const classNameTDZError = state.file.addHelper(\"classNameTDZError\");\n    const throwNode = t.callExpression(classNameTDZError, [\n      t.stringLiteral(path.node.name),\n    ]);\n\n    path.replaceWith(t.sequenceExpression([throwNode, path.node]));\n    path.skip();\n  }\n}\n\nconst classFieldDefinitionEvaluationTDZVisitor: Visitor<HandleClassTDZState> = {\n  ReferencedIdentifier: handleClassTDZ,\n};\n\ninterface RenamerState {\n  scope: Scope;\n}\n\nexport function injectInitialization(\n  path: NodePath<t.Class>,\n  constructor: NodePath<t.ClassMethod> | undefined,\n  nodes: t.Statement[],\n  renamer?: (visitor: Visitor<RenamerState>, state: RenamerState) => void,\n) {\n  if (!nodes.length) return;\n\n  const isDerived = !!path.node.superClass;\n\n  if (!constructor) {\n    const newConstructor = t.classMethod(\n      \"constructor\",\n      t.identifier(\"constructor\"),\n      [],\n      t.blockStatement([]),\n    );\n\n    if (isDerived) {\n      newConstructor.params = [t.restElement(t.identifier(\"args\"))];\n      newConstructor.body.body.push(template.statement.ast`super(...args)`);\n    }\n\n    [constructor] = path\n      .get(\"body\")\n      .unshiftContainer(\"body\", newConstructor) as NodePath<t.ClassMethod>[];\n  }\n\n  if (renamer) {\n    renamer(referenceVisitor, { scope: constructor.scope });\n  }\n\n  if (isDerived) {\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    constructor.traverse(findBareSupers, bareSupers);\n    let isFirst = true;\n    for (const bareSuper of bareSupers) {\n      if (isFirst) {\n        bareSuper.insertAfter(nodes);\n        isFirst = false;\n      } else {\n        bareSuper.insertAfter(nodes.map(n => t.cloneNode(n)));\n      }\n    }\n  } else {\n    constructor.get(\"body\").unshiftContainer(\"body\", nodes);\n  }\n}\n\nexport function extractComputedKeys(\n  path: NodePath<t.Class>,\n  computedPaths: NodePath<t.ClassProperty | t.ClassMethod>[],\n  file: File,\n) {\n  const declarations: t.ExpressionStatement[] = [];\n  const state = {\n    classBinding: path.node.id && path.scope.getBinding(path.node.id.name),\n    file,\n  };\n  for (const computedPath of computedPaths) {\n    const computedKey = computedPath.get(\"key\");\n    if (computedKey.isReferencedIdentifier()) {\n      handleClassTDZ(computedKey, state);\n    } else {\n      computedKey.traverse(classFieldDefinitionEvaluationTDZVisitor, state);\n    }\n\n    const computedNode = computedPath.node;\n    // Make sure computed property names are only evaluated once (upon class definition)\n    // and in the right order in combination with static properties\n    if (!computedKey.isConstantExpression()) {\n      const ident = path.scope.generateUidIdentifierBasedOnNode(\n        computedNode.key,\n      );\n      // Declaring in the same block scope\n      // Ref: https://github.com/babel/babel/pull/10029/files#diff-fbbdd83e7a9c998721c1484529c2ce92\n      path.scope.push({\n        id: ident,\n        kind: \"let\",\n      });\n      declarations.push(\n        t.expressionStatement(\n          t.assignmentExpression(\"=\", t.cloneNode(ident), computedNode.key),\n        ),\n      );\n      computedNode.key = t.cloneNode(ident);\n    }\n  }\n\n  return declarations;\n}\n"], "mappings": ";;;;;;;;AAAA;;AAGA;;AAEA,MAAMA,cAAc,GAAGC,cAAA,CAASC,QAAT,CAAkBC,KAAlB,CAAsD,CAC3E;EACEC,KAAK,CAACC,IAAD,EAAO;IACV,MAAM;MAAEC,IAAF;MAAQC;IAAR,IAAuBF,IAA7B;;IACA,IAAIE,UAAU,CAACC,gBAAX,CAA4B;MAAEC,MAAM,EAAEH;IAAV,CAA5B,CAAJ,EAAmD;MACjD,KAAKI,IAAL,CAAUH,UAAV;IACD;EACF;;AANH,CAD2E,EAS3EI,iCAT2E,CAAtD,CAAvB;;AAYA,MAAMC,gBAA2C,GAAG;EAClD,kCACEP,IADF,EAEE;IACAA,IAAI,CAACQ,IAAL;EACD,CALiD;;EAOlDC,oBAAoB,CAACT,IAAD,EAA+B;IAAEU;EAAF,CAA/B,EAA0C;IAC5D,IAAIA,KAAK,CAACC,aAAN,CAAoBX,IAAI,CAACC,IAAL,CAAUW,IAA9B,CAAJ,EAAyC;MACvCF,KAAK,CAACG,MAAN,CAAab,IAAI,CAACC,IAAL,CAAUW,IAAvB;MACAZ,IAAI,CAACQ,IAAL;IACD;EACF;;AAZiD,CAApD;;AAoBA,SAASM,cAAT,CACEd,IADF,EAEEe,KAFF,EAGE;EACA,IACEA,KAAK,CAACC,YAAN,IACAD,KAAK,CAACC,YAAN,KAAuBhB,IAAI,CAACU,KAAL,CAAWO,UAAX,CAAsBjB,IAAI,CAACC,IAAL,CAAUW,IAAhC,CAFzB,EAGE;IACA,MAAMM,iBAAiB,GAAGH,KAAK,CAACI,IAAN,CAAWC,SAAX,CAAqB,mBAArB,CAA1B;;IACA,MAAMC,SAAS,GAAGC,WAAA,CAAEC,cAAF,CAAiBL,iBAAjB,EAAoC,CACpDI,WAAA,CAAEE,aAAF,CAAgBxB,IAAI,CAACC,IAAL,CAAUW,IAA1B,CADoD,CAApC,CAAlB;;IAIAZ,IAAI,CAACyB,WAAL,CAAiBH,WAAA,CAAEI,kBAAF,CAAqB,CAACL,SAAD,EAAYrB,IAAI,CAACC,IAAjB,CAArB,CAAjB;IACAD,IAAI,CAACQ,IAAL;EACD;AACF;;AAED,MAAMmB,wCAAsE,GAAG;EAC7ElB,oBAAoB,EAAEK;AADuD,CAA/E;;AAQO,SAASc,oBAAT,CACL5B,IADK,EAEL6B,WAFK,EAGLC,KAHK,EAILC,OAJK,EAKL;EACA,IAAI,CAACD,KAAK,CAACE,MAAX,EAAmB;EAEnB,MAAMC,SAAS,GAAG,CAAC,CAACjC,IAAI,CAACC,IAAL,CAAUiC,UAA9B;;EAEA,IAAI,CAACL,WAAL,EAAkB;IAChB,MAAMM,cAAc,GAAGb,WAAA,CAAEc,WAAF,CACrB,aADqB,EAErBd,WAAA,CAAEe,UAAF,CAAa,aAAb,CAFqB,EAGrB,EAHqB,EAIrBf,WAAA,CAAEgB,cAAF,CAAiB,EAAjB,CAJqB,CAAvB;;IAOA,IAAIL,SAAJ,EAAe;MACbE,cAAc,CAACI,MAAf,GAAwB,CAACjB,WAAA,CAAEkB,WAAF,CAAclB,WAAA,CAAEe,UAAF,CAAa,MAAb,CAAd,CAAD,CAAxB;MACAF,cAAc,CAACM,IAAf,CAAoBA,IAApB,CAAyBpC,IAAzB,CAA8BqC,cAAA,CAASC,SAAT,CAAmBC,GAAI,gBAArD;IACD;;IAED,CAACf,WAAD,IAAgB7B,IAAI,CACjB6C,GADa,CACT,MADS,EAEbC,gBAFa,CAEI,MAFJ,EAEYX,cAFZ,CAAhB;EAGD;;EAED,IAAIJ,OAAJ,EAAa;IACXA,OAAO,CAACxB,gBAAD,EAAmB;MAAEG,KAAK,EAAEmB,WAAW,CAACnB;IAArB,CAAnB,CAAP;EACD;;EAED,IAAIuB,SAAJ,EAAe;IACb,MAAMc,UAAwC,GAAG,EAAjD;IACAlB,WAAW,CAACjC,QAAZ,CAAqBD,cAArB,EAAqCoD,UAArC;IACA,IAAIC,OAAO,GAAG,IAAd;;IACA,KAAK,MAAMC,SAAX,IAAwBF,UAAxB,EAAoC;MAClC,IAAIC,OAAJ,EAAa;QACXC,SAAS,CAACC,WAAV,CAAsBpB,KAAtB;QACAkB,OAAO,GAAG,KAAV;MACD,CAHD,MAGO;QACLC,SAAS,CAACC,WAAV,CAAsBpB,KAAK,CAACqB,GAAN,CAAUC,CAAC,IAAI9B,WAAA,CAAE+B,SAAF,CAAYD,CAAZ,CAAf,CAAtB;MACD;IACF;EACF,CAZD,MAYO;IACLvB,WAAW,CAACgB,GAAZ,CAAgB,MAAhB,EAAwBC,gBAAxB,CAAyC,MAAzC,EAAiDhB,KAAjD;EACD;AACF;;AAEM,SAASwB,mBAAT,CACLtD,IADK,EAELuD,aAFK,EAGLpC,IAHK,EAIL;EACA,MAAMqC,YAAqC,GAAG,EAA9C;EACA,MAAMzC,KAAK,GAAG;IACZC,YAAY,EAAEhB,IAAI,CAACC,IAAL,CAAUwD,EAAV,IAAgBzD,IAAI,CAACU,KAAL,CAAWO,UAAX,CAAsBjB,IAAI,CAACC,IAAL,CAAUwD,EAAV,CAAa7C,IAAnC,CADlB;IAEZO;EAFY,CAAd;;EAIA,KAAK,MAAMuC,YAAX,IAA2BH,aAA3B,EAA0C;IACxC,MAAMI,WAAW,GAAGD,YAAY,CAACb,GAAb,CAAiB,KAAjB,CAApB;;IACA,IAAIc,WAAW,CAACC,sBAAZ,EAAJ,EAA0C;MACxC9C,cAAc,CAAC6C,WAAD,EAAc5C,KAAd,CAAd;IACD,CAFD,MAEO;MACL4C,WAAW,CAAC/D,QAAZ,CAAqB+B,wCAArB,EAA+DZ,KAA/D;IACD;;IAED,MAAM8C,YAAY,GAAGH,YAAY,CAACzD,IAAlC;;IAGA,IAAI,CAAC0D,WAAW,CAACG,oBAAZ,EAAL,EAAyC;MACvC,MAAMC,KAAK,GAAG/D,IAAI,CAACU,KAAL,CAAWsD,gCAAX,CACZH,YAAY,CAACI,GADD,CAAd;MAKAjE,IAAI,CAACU,KAAL,CAAWL,IAAX,CAAgB;QACdoD,EAAE,EAAEM,KADU;QAEdG,IAAI,EAAE;MAFQ,CAAhB;MAIAV,YAAY,CAACnD,IAAb,CACEiB,WAAA,CAAE6C,mBAAF,CACE7C,WAAA,CAAE8C,oBAAF,CAAuB,GAAvB,EAA4B9C,WAAA,CAAE+B,SAAF,CAAYU,KAAZ,CAA5B,EAAgDF,YAAY,CAACI,GAA7D,CADF,CADF;MAKAJ,YAAY,CAACI,GAAb,GAAmB3C,WAAA,CAAE+B,SAAF,CAAYU,KAAZ,CAAnB;IACD;EACF;;EAED,OAAOP,YAAP;AACD"}