"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/**\n * Auto Save on Leave Hook\n *\n * 简单逻辑：\n * 1. 用户选择离开 = 不自动保存 + 清空快照\n * 2. 保存成功后 = 清空快照\n * 3. 笔记跳转时 = 自动保存\n * 4. 页面关闭/刷新时 = 弹窗询问\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n */ \n\n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        return  true && window.saveButtonStatus === \"save\";\n    }, []);\n    var autoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    var clearSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.clearSnapshots) {\n            try {\n                window.clearSnapshots();\n            } catch (error) {\n                console.error(\"清空快照失败:\", error);\n            }\n        }\n    }, []);\n    // 页面关闭/刷新时弹窗\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled || !hasUnsavedChanges()) return;\n        var message = \"您有未保存的更改。确定要离开吗？\";\n        event.returnValue = message;\n        // 用户选择取消时自动保存\n        setTimeout(function() {\n            autoSave();\n        }, 100);\n        // 用户选择离开时清空快照\n        var handleUnload = function() {\n            clearSnapshots();\n            window.removeEventListener(\"unload\", handleUnload);\n        };\n        window.addEventListener(\"unload\", handleUnload);\n        return message;\n    }, [\n        enabled,\n        hasUnsavedChanges,\n        autoSave,\n        clearSnapshots\n    ]);\n    // 路由跳转时处理\n    var handleRouteChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function(url) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, function(_state) {\n                if (!enabled || isAutoSavingRef.current || !hasUnsavedChanges()) return [\n                    2\n                ];\n                // 检查是否是笔记跳转\n                if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_2__.isNoteLink)(url) || url === \"/\" || url.includes(\"?new\")) {\n                    // 笔记跳转：后台自动保存\n                    isAutoSavingRef.current = true;\n                    autoSave().finally(function() {\n                        isAutoSavingRef.current = false;\n                    });\n                    return [\n                        2\n                    ];\n                }\n                // 非笔记跳转：阻止跳转，清空快照，直接离开（不弹窗）\n                // 因为用户已经在浏览器原生弹窗中选择了\"离开\"或\"重新加载\"\n                router.events.emit(\"routeChangeError\", new Error(\"阻止跳转\"), url);\n                clearSnapshots();\n                router.push(url);\n                return [\n                    2\n                ];\n            });\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        hasUnsavedChanges,\n        autoSave,\n        clearSnapshots,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            return window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChange);\n        return function() {\n            return router.events.off(\"routeChangeStart\", handleRouteChange);\n        };\n    }, [\n        enabled,\n        handleRouteChange,\n        router.events\n    ]);\n    return {\n        hasUnsavedChanges: hasUnsavedChanges,\n        autoSave: autoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9ob29rcy91c2UtYXV0by1zYXZlLW9uLWxlYXZlLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7Ozs7Ozs7Ozs7O0NBV0MsR0FFRDs7QUFBdUQ7QUFDZjtBQUNNO0FBTTlDLElBQU1LLGtCQUFrQixHQUFHLFdBQTZDO1FBQTVDQyxPQUFrQyxvRUFBRyxFQUFFO0lBQy9ELGVBQTJCQSxPQUFPLENBQTFCQyxPQUFPLEVBQVBBLE9BQU8seUJBQUcsSUFBSTtJQUN0QixJQUFNQyxNQUFNLEdBQUdMLHNEQUFTLEVBQUU7SUFDMUIsSUFBTU0sZUFBZSxHQUFHUCw2Q0FBTSxDQUFDLEtBQUssQ0FBQztJQUVyQyxJQUFNUSxpQkFBaUIsR0FBR1Qsa0RBQVcsQ0FBQyxXQUFNO1FBQ3hDLE9BQU8sS0FBNkIsSUFDN0IsTUFBTyxDQUFTVyxnQkFBZ0IsS0FBSyxNQUFNLENBQUM7SUFDdkQsQ0FBQyxFQUFFLEVBQUUsQ0FBQztJQUVOLElBQU1DLFFBQVEsR0FBR1osa0RBQVcsZUFBQywrRkFBWTtZQUt4QmEsS0FBSzs7Ozt5QkFKZCxNQUE2QixJQUFJLE1BQU8sQ0FBU0Msa0JBQWtCLEdBQW5FOzs7c0JBQW1FOzs7Ozs7Ozs7b0JBRS9EOzt3QkFBTSxNQUFPLENBQVNBLGtCQUFrQixFQUFFO3NCQUFBOztvQkFBMUMsYUFBMEMsQ0FBQztvQkFDM0M7O3dCQUFPLElBQUk7c0JBQUM7O29CQUNQRCxLQUFLO29CQUNWOzt3QkFBTyxLQUFLO3NCQUFDOztvQkFHckI7O3dCQUFPLEtBQUs7c0JBQUM7OztJQUNqQixDQUFDLEdBQUUsRUFBRSxDQUFDO0lBRU4sSUFBTUUsY0FBYyxHQUFHZixrREFBVyxDQUFDLFdBQU07UUFDckMsSUFBSSxLQUE2QixJQUFJLE1BQU8sQ0FBU2UsY0FBYyxFQUFFO1lBQ2pFLElBQUk7Z0JBQ0EsTUFBTyxDQUFTQSxjQUFjLEVBQUUsQ0FBQztZQUNyQyxFQUFFLE9BQU9GLEtBQUssRUFBRTtnQkFDWkcsT0FBTyxDQUFDSCxLQUFLLENBQUMsU0FBUyxFQUFFQSxLQUFLLENBQUMsQ0FBQztZQUNwQyxDQUFDO1FBQ0wsQ0FBQztJQUNMLENBQUMsRUFBRSxFQUFFLENBQUM7SUFFTixhQUFhO0lBQ2IsSUFBTUksa0JBQWtCLEdBQUdqQixrREFBVyxDQUFDLFNBQUNrQixLQUF3QixFQUFLO1FBQ2pFLElBQUksQ0FBQ1osT0FBTyxJQUFJLENBQUNHLGlCQUFpQixFQUFFLEVBQUUsT0FBTztRQUU3QyxJQUFNVSxPQUFPLEdBQUcsa0JBQWtCO1FBQ2xDRCxLQUFLLENBQUNFLFdBQVcsR0FBR0QsT0FBTyxDQUFDO1FBRTVCLGNBQWM7UUFDZEUsVUFBVSxDQUFDLFdBQU07WUFDYlQsUUFBUSxFQUFFLENBQUM7UUFDZixDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7UUFFUixjQUFjO1FBQ2QsSUFBTVUsWUFBWSxHQUFHLFdBQU07WUFDdkJQLGNBQWMsRUFBRSxDQUFDO1lBQ2pCTCxNQUFNLENBQUNhLG1CQUFtQixDQUFDLFFBQVEsRUFBRUQsWUFBWSxDQUFDLENBQUM7UUFDdkQsQ0FBQztRQUNEWixNQUFNLENBQUNjLGdCQUFnQixDQUFDLFFBQVEsRUFBRUYsWUFBWSxDQUFDLENBQUM7UUFFaEQsT0FBT0gsT0FBTyxDQUFDO0lBQ25CLENBQUMsRUFBRTtRQUFDYixPQUFPO1FBQUVHLGlCQUFpQjtRQUFFRyxRQUFRO1FBQUVHLGNBQWM7S0FBQyxDQUFDO0lBRTFELFVBQVU7SUFDVixJQUFNVSxpQkFBaUIsR0FBR3pCLGtEQUFXO21CQUFDLDZGQUFPMEIsR0FBVyxFQUFLOztnQkFDekQsSUFBSSxDQUFDcEIsT0FBTyxJQUFJRSxlQUFlLENBQUNtQixPQUFPLElBQUksQ0FBQ2xCLGlCQUFpQixFQUFFLEVBQUU7O2tCQUFPO2dCQUV4RSxZQUFZO2dCQUNaLElBQUlOLDREQUFVLENBQUN1QixHQUFHLENBQUMsSUFBSUEsR0FBRyxLQUFLLEdBQUcsSUFBSUEsR0FBRyxDQUFDRSxRQUFRLENBQUMsTUFBTSxDQUFDLEVBQUU7b0JBQ3hELGNBQWM7b0JBQ2RwQixlQUFlLENBQUNtQixPQUFPLEdBQUcsSUFBSSxDQUFDO29CQUMvQmYsUUFBUSxFQUFFLENBQUNpQixPQUFPLENBQUMsV0FBTTt3QkFDckJyQixlQUFlLENBQUNtQixPQUFPLEdBQUcsS0FBSyxDQUFDO29CQUNwQyxDQUFDLENBQUMsQ0FBQztvQkFDSDs7c0JBQU87Z0JBQ1gsQ0FBQztnQkFFRCw0QkFBNEI7Z0JBQzVCLGdDQUFnQztnQkFDaENwQixNQUFNLENBQUN1QixNQUFNLENBQUNDLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxJQUFJQyxLQUFLLENBQUMsTUFBTSxDQUFDLEVBQUVOLEdBQUcsQ0FBQyxDQUFDO2dCQUMvRFgsY0FBYyxFQUFFLENBQUM7Z0JBQ2pCUixNQUFNLENBQUMwQixJQUFJLENBQUNQLEdBQUcsQ0FBQyxDQUFDOzs7OztRQUNyQixDQUFDO3dCQWxCNENBLEdBQVc7OztTQWtCckQ7UUFBQ3BCLE9BQU87UUFBRUcsaUJBQWlCO1FBQUVHLFFBQVE7UUFBRUcsY0FBYztRQUFFUixNQUFNO0tBQUMsQ0FBQztJQUdsRVIsZ0RBQVMsQ0FBQyxXQUFNO1FBQ1osSUFBSSxDQUFDTyxPQUFPLEVBQUUsT0FBTztRQUNyQkksTUFBTSxDQUFDYyxnQkFBZ0IsQ0FBQyxjQUFjLEVBQUVQLGtCQUFrQixDQUFDLENBQUM7UUFDNUQsT0FBTzttQkFBTVAsTUFBTSxDQUFDYSxtQkFBbUIsQ0FBQyxjQUFjLEVBQUVOLGtCQUFrQixDQUFDO1NBQUEsQ0FBQztJQUNoRixDQUFDLEVBQUU7UUFBQ1gsT0FBTztRQUFFVyxrQkFBa0I7S0FBQyxDQUFDLENBQUM7SUFFbENsQixnREFBUyxDQUFDLFdBQU07UUFDWixJQUFJLENBQUNPLE9BQU8sRUFBRSxPQUFPO1FBQ3JCQyxNQUFNLENBQUN1QixNQUFNLENBQUNJLEVBQUUsQ0FBQyxrQkFBa0IsRUFBRVQsaUJBQWlCLENBQUMsQ0FBQztRQUN4RCxPQUFPO21CQUFNbEIsTUFBTSxDQUFDdUIsTUFBTSxDQUFDSyxHQUFHLENBQUMsa0JBQWtCLEVBQUVWLGlCQUFpQixDQUFDO1NBQUEsQ0FBQztJQUMxRSxDQUFDLEVBQUU7UUFBQ25CLE9BQU87UUFBRW1CLGlCQUFpQjtRQUFFbEIsTUFBTSxDQUFDdUIsTUFBTTtLQUFDLENBQUMsQ0FBQztJQUVoRCxPQUFPO1FBQ0hyQixpQkFBaUIsRUFBakJBLGlCQUFpQjtRQUNqQkcsUUFBUSxFQUFSQSxRQUFRO0tBQ1gsQ0FBQztBQUNOLENBQUM7QUFFRCwrREFBZVIsa0JBQWtCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGlicy93ZWIvaG9va3MvdXNlLWF1dG8tc2F2ZS1vbi1sZWF2ZS50cz9iOTJiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQXV0byBTYXZlIG9uIExlYXZlIEhvb2tcbiAqXG4gKiDnroDljZXpgLvovpHvvJpcbiAqIDEuIOeUqOaIt+mAieaLqeemu+W8gCA9IOS4jeiHquWKqOS/neWtmCArIOa4heepuuW/q+eFp1xuICogMi4g5L+d5a2Y5oiQ5Yqf5ZCOID0g5riF56m65b+r54WnXG4gKiAzLiDnrJTorrDot7Povazml7YgPSDoh6rliqjkv53lrZhcbiAqIDQuIOmhtemdouWFs+mXrS/liLfmlrDml7YgPSDlvLnnqpfor6Lpl65cbiAqXG4gKiBDb3B5cmlnaHQgKGMpIDIwMjUgd2F5Y2FhblxuICogTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlXG4gKi9cblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHsgaXNOb3RlTGluayB9IGZyb20gJ2xpYnMvc2hhcmVkL25vdGUnO1xuXG5pbnRlcmZhY2UgVXNlQXV0b1NhdmVPbkxlYXZlT3B0aW9ucyB7XG4gICAgZW5hYmxlZD86IGJvb2xlYW47XG59XG5cbmNvbnN0IHVzZUF1dG9TYXZlT25MZWF2ZSA9IChvcHRpb25zOiBVc2VBdXRvU2F2ZU9uTGVhdmVPcHRpb25zID0ge30pID0+IHtcbiAgICBjb25zdCB7IGVuYWJsZWQgPSB0cnVlIH0gPSBvcHRpb25zO1xuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xuICAgIGNvbnN0IGlzQXV0b1NhdmluZ1JlZiA9IHVzZVJlZihmYWxzZSk7XG5cbiAgICBjb25zdCBoYXNVbnNhdmVkQ2hhbmdlcyA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgICAgcmV0dXJuIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmXG4gICAgICAgICAgICAgICAod2luZG93IGFzIGFueSkuc2F2ZUJ1dHRvblN0YXR1cyA9PT0gJ3NhdmUnO1xuICAgIH0sIFtdKTtcblxuICAgIGNvbnN0IGF1dG9TYXZlID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcgJiYgKHdpbmRvdyBhcyBhbnkpLnNhdmVCdXR0b25BdXRvU2F2ZSkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBhd2FpdCAod2luZG93IGFzIGFueSkuc2F2ZUJ1dHRvbkF1dG9TYXZlKCk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgfSwgW10pO1xuXG4gICAgY29uc3QgY2xlYXJTbmFwc2hvdHMgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAod2luZG93IGFzIGFueSkuY2xlYXJTbmFwc2hvdHMpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgKHdpbmRvdyBhcyBhbnkpLmNsZWFyU25hcHNob3RzKCk7XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+a4heepuuW/q+eFp+Wksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9LCBbXSk7XG5cbiAgICAvLyDpobXpnaLlhbPpl60v5Yi35paw5pe25by556qXXG4gICAgY29uc3QgaGFuZGxlQmVmb3JlVW5sb2FkID0gdXNlQ2FsbGJhY2soKGV2ZW50OiBCZWZvcmVVbmxvYWRFdmVudCkgPT4ge1xuICAgICAgICBpZiAoIWVuYWJsZWQgfHwgIWhhc1Vuc2F2ZWRDaGFuZ2VzKCkpIHJldHVybjtcblxuICAgICAgICBjb25zdCBtZXNzYWdlID0gJ+aCqOacieacquS/neWtmOeahOabtOaUueOAguehruWumuimgeemu+W8gOWQl++8nyc7XG4gICAgICAgIGV2ZW50LnJldHVyblZhbHVlID0gbWVzc2FnZTtcblxuICAgICAgICAvLyDnlKjmiLfpgInmi6nlj5bmtojml7boh6rliqjkv53lrZhcbiAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICBhdXRvU2F2ZSgpO1xuICAgICAgICB9LCAxMDApO1xuXG4gICAgICAgIC8vIOeUqOaIt+mAieaLqeemu+W8gOaXtua4heepuuW/q+eFp1xuICAgICAgICBjb25zdCBoYW5kbGVVbmxvYWQgPSAoKSA9PiB7XG4gICAgICAgICAgICBjbGVhclNuYXBzaG90cygpO1xuICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3VubG9hZCcsIGhhbmRsZVVubG9hZCk7XG4gICAgICAgIH07XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCd1bmxvYWQnLCBoYW5kbGVVbmxvYWQpO1xuXG4gICAgICAgIHJldHVybiBtZXNzYWdlO1xuICAgIH0sIFtlbmFibGVkLCBoYXNVbnNhdmVkQ2hhbmdlcywgYXV0b1NhdmUsIGNsZWFyU25hcHNob3RzXSk7XG5cbiAgICAvLyDot6/nlLHot7Povazml7blpITnkIZcbiAgICBjb25zdCBoYW5kbGVSb3V0ZUNoYW5nZSA9IHVzZUNhbGxiYWNrKGFzeW5jICh1cmw6IHN0cmluZykgPT4ge1xuICAgICAgICBpZiAoIWVuYWJsZWQgfHwgaXNBdXRvU2F2aW5nUmVmLmN1cnJlbnQgfHwgIWhhc1Vuc2F2ZWRDaGFuZ2VzKCkpIHJldHVybjtcblxuICAgICAgICAvLyDmo4Dmn6XmmK/lkKbmmK/nrJTorrDot7PovaxcbiAgICAgICAgaWYgKGlzTm90ZUxpbmsodXJsKSB8fCB1cmwgPT09ICcvJyB8fCB1cmwuaW5jbHVkZXMoJz9uZXcnKSkge1xuICAgICAgICAgICAgLy8g56yU6K6w6Lez6L2s77ya5ZCO5Y+w6Ieq5Yqo5L+d5a2YXG4gICAgICAgICAgICBpc0F1dG9TYXZpbmdSZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgICAgICBhdXRvU2F2ZSgpLmZpbmFsbHkoKCkgPT4ge1xuICAgICAgICAgICAgICAgIGlzQXV0b1NhdmluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIOmdnueslOiusOi3s+i9rO+8mumYu+atoui3s+i9rO+8jOa4heepuuW/q+eFp++8jOebtOaOpeemu+W8gO+8iOS4jeW8ueeql++8iVxuICAgICAgICAvLyDlm6DkuLrnlKjmiLflt7Lnu4/lnKjmtY/op4jlmajljp/nlJ/lvLnnqpfkuK3pgInmi6nkuoZcIuemu+W8gFwi5oiWXCLph43mlrDliqDovb1cIlxuICAgICAgICByb3V0ZXIuZXZlbnRzLmVtaXQoJ3JvdXRlQ2hhbmdlRXJyb3InLCBuZXcgRXJyb3IoJ+mYu+atoui3s+i9rCcpLCB1cmwpO1xuICAgICAgICBjbGVhclNuYXBzaG90cygpO1xuICAgICAgICByb3V0ZXIucHVzaCh1cmwpO1xuICAgIH0sIFtlbmFibGVkLCBoYXNVbnNhdmVkQ2hhbmdlcywgYXV0b1NhdmUsIGNsZWFyU25hcHNob3RzLCByb3V0ZXJdKTtcblxuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFlbmFibGVkKSByZXR1cm47XG4gICAgICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCBoYW5kbGVCZWZvcmVVbmxvYWQpO1xuICAgICAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZCk7XG4gICAgfSwgW2VuYWJsZWQsIGhhbmRsZUJlZm9yZVVubG9hZF0pO1xuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFlbmFibGVkKSByZXR1cm47XG4gICAgICAgIHJvdXRlci5ldmVudHMub24oJ3JvdXRlQ2hhbmdlU3RhcnQnLCBoYW5kbGVSb3V0ZUNoYW5nZSk7XG4gICAgICAgIHJldHVybiAoKSA9PiByb3V0ZXIuZXZlbnRzLm9mZigncm91dGVDaGFuZ2VTdGFydCcsIGhhbmRsZVJvdXRlQ2hhbmdlKTtcbiAgICB9LCBbZW5hYmxlZCwgaGFuZGxlUm91dGVDaGFuZ2UsIHJvdXRlci5ldmVudHNdKTtcblxuICAgIHJldHVybiB7XG4gICAgICAgIGhhc1Vuc2F2ZWRDaGFuZ2VzLFxuICAgICAgICBhdXRvU2F2ZSxcbiAgICB9O1xufTtcblxuZXhwb3J0IGRlZmF1bHQgdXNlQXV0b1NhdmVPbkxlYXZlO1xuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwidXNlUm91dGVyIiwiaXNOb3RlTGluayIsInVzZUF1dG9TYXZlT25MZWF2ZSIsIm9wdGlvbnMiLCJlbmFibGVkIiwicm91dGVyIiwiaXNBdXRvU2F2aW5nUmVmIiwiaGFzVW5zYXZlZENoYW5nZXMiLCJ3aW5kb3ciLCJzYXZlQnV0dG9uU3RhdHVzIiwiYXV0b1NhdmUiLCJlcnJvciIsInNhdmVCdXR0b25BdXRvU2F2ZSIsImNsZWFyU25hcHNob3RzIiwiY29uc29sZSIsImhhbmRsZUJlZm9yZVVubG9hZCIsImV2ZW50IiwibWVzc2FnZSIsInJldHVyblZhbHVlIiwic2V0VGltZW91dCIsImhhbmRsZVVubG9hZCIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJhZGRFdmVudExpc3RlbmVyIiwiaGFuZGxlUm91dGVDaGFuZ2UiLCJ1cmwiLCJjdXJyZW50IiwiaW5jbHVkZXMiLCJmaW5hbGx5IiwiZXZlbnRzIiwiZW1pdCIsIkVycm9yIiwicHVzaCIsIm9uIiwib2ZmIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});