# Motea Development Environment Configuration
# Database Configuration - Neon PostgreSQL
DATABASE_URL=postgres://neondb_owner:<EMAIL>/neondb?sslmode=require
DB_PROVIDER=neon

# Application Configuration
NODE_ENV=development
PORT=3000
HOSTNAME=localhost
BASE_URL=http://localhost:3000
COOKIE_SECURE=false

# Development Settings
NEXT_TELEMETRY_DISABLED=1
PRELOAD_NOTES_COUNT=5

# Authentication (for development)
PASSWORD=motea

# Store Configuration
STORE_PREFIX=

# Debug Settings (enable IME debugging)
DEBUG=true
