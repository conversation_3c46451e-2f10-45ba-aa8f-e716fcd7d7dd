{"version": 3, "names": ["isCallExpression", "isExpressionStatement", "isIdentifier", "isStringLiteral", "getImportSource", "node", "specifiers", "length", "source", "value", "getRequireSource", "expression", "callee", "name", "arguments", "isPolyfillSource"], "sources": ["../../src/polyfills/utils.ts"], "sourcesContent": ["import {\n  isCallExpression,\n  isExpressionStatement,\n  isIdentifier,\n  isStringLiteral,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nimport type { NodePath } from \"@babel/traverse\";\n\nexport function getImportSource({ node }: NodePath<t.ImportDeclaration>) {\n  if (node.specifiers.length === 0) return node.source.value;\n}\n\nexport function getRequireSource({ node }: NodePath) {\n  if (!isExpressionStatement(node)) return;\n  const { expression } = node;\n  if (\n    isCallExpression(expression) &&\n    isIdentifier(expression.callee) &&\n    expression.callee.name === \"require\" &&\n    expression.arguments.length === 1 &&\n    isStringLiteral(expression.arguments[0])\n  ) {\n    return expression.arguments[0].value;\n  }\n}\n\nexport function isPolyfillSource(source?: string | null): boolean {\n  return source === \"@babel/polyfill\" || source === \"core-js\";\n}\n"], "mappings": ";;;;;;;;;AAAA;;;EACEA,gB;EACAC,qB;EACAC,Y;EACAC;;;AAKK,SAASC,eAAT,CAAyB;EAAEC;AAAF,CAAzB,EAAkE;EACvE,IAAIA,IAAI,CAACC,UAAL,CAAgBC,MAAhB,KAA2B,CAA/B,EAAkC,OAAOF,IAAI,CAACG,MAAL,CAAYC,KAAnB;AACnC;;AAEM,SAASC,gBAAT,CAA0B;EAAEL;AAAF,CAA1B,EAA8C;EACnD,IAAI,CAACJ,qBAAqB,CAACI,IAAD,CAA1B,EAAkC;EAClC,MAAM;IAAEM;EAAF,IAAiBN,IAAvB;;EACA,IACEL,gBAAgB,CAACW,UAAD,CAAhB,IACAT,YAAY,CAACS,UAAU,CAACC,MAAZ,CADZ,IAEAD,UAAU,CAACC,MAAX,CAAkBC,IAAlB,KAA2B,SAF3B,IAGAF,UAAU,CAACG,SAAX,CAAqBP,MAArB,KAAgC,CAHhC,IAIAJ,eAAe,CAACQ,UAAU,CAACG,SAAX,CAAqB,CAArB,CAAD,CALjB,EAME;IACA,OAAOH,UAAU,CAACG,SAAX,CAAqB,CAArB,EAAwBL,KAA/B;EACD;AACF;;AAEM,SAASM,gBAAT,CAA0BP,MAA1B,EAA2D;EAChE,OAAOA,MAAM,KAAK,iBAAX,IAAgCA,MAAM,KAAK,SAAlD;AACD"}