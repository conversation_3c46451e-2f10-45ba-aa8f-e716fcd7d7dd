{"version": 3, "names": ["logPlugin", "item", "targetVersions", "list", "filteredList", "getInclusionReasons", "support", "startsWith", "proposalName", "slice", "Object", "prototype", "hasOwnProperty", "call", "compatData", "console", "log", "formattedTargets", "first", "target", "keys"], "sources": ["../src/debug.ts"], "sourcesContent": ["import {\n  getInclusionReasons,\n  type Targets,\n  type Target,\n} from \"@babel/helper-compilation-targets\";\nimport compatData from \"@babel/compat-data/plugins\";\n\n// Outputs a message that shows which target(s) caused an item to be included:\n// transform-foo { \"edge\":\"13\", \"firefox\":\"49\", \"ie\":\"10\" }\nexport const logPlugin = (\n  item: string,\n  targetVersions: Targets,\n  list: { [key: string]: Targets },\n) => {\n  const filteredList = getInclusionReasons(item, targetVersions, list);\n\n  const support = list[item];\n\n  // TODO(Babel 8): Remove this. It's needed to keep outputting proposal-\n  // in the debug log.\n  if (item.startsWith(\"transform-\")) {\n    const proposalName = `proposal-${item.slice(10)}`;\n    if (\n      proposalName === \"proposal-dynamic-import\" ||\n      Object.prototype.hasOwnProperty.call(compatData, proposalName)\n    ) {\n      item = proposalName;\n    }\n  }\n\n  if (!support) {\n    console.log(`  ${item}`);\n    return;\n  }\n\n  let formattedTargets = `{`;\n  let first = true;\n  for (const target of Object.keys(filteredList) as Target[]) {\n    if (!first) formattedTargets += `,`;\n    first = false;\n    formattedTargets += ` ${target}`;\n    if (support[target]) formattedTargets += ` < ${support[target]}`;\n  }\n  formattedTargets += ` }`;\n\n  console.log(`  ${item} ${formattedTargets}`);\n};\n"], "mappings": ";;;;;;;AAAA;;AAKA;;AAIO,MAAMA,SAAS,GAAG,CACvBC,IADuB,EAEvBC,cAFuB,EAGvBC,IAHuB,KAIpB;EACH,MAAMC,YAAY,GAAG,IAAAC,6CAAA,EAAoBJ,IAApB,EAA0BC,cAA1B,EAA0CC,IAA1C,CAArB;EAEA,MAAMG,OAAO,GAAGH,IAAI,CAACF,IAAD,CAApB;;EAIA,IAAIA,IAAI,CAACM,UAAL,CAAgB,YAAhB,CAAJ,EAAmC;IACjC,MAAMC,YAAY,GAAI,YAAWP,IAAI,CAACQ,KAAL,CAAW,EAAX,CAAe,EAAhD;;IACA,IACED,YAAY,KAAK,yBAAjB,IACAE,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCC,QAArC,EAAiDN,YAAjD,CAFF,EAGE;MACAP,IAAI,GAAGO,YAAP;IACD;EACF;;EAED,IAAI,CAACF,OAAL,EAAc;IACZS,OAAO,CAACC,GAAR,CAAa,KAAIf,IAAK,EAAtB;IACA;EACD;;EAED,IAAIgB,gBAAgB,GAAI,GAAxB;EACA,IAAIC,KAAK,GAAG,IAAZ;;EACA,KAAK,MAAMC,MAAX,IAAqBT,MAAM,CAACU,IAAP,CAAYhB,YAAZ,CAArB,EAA4D;IAC1D,IAAI,CAACc,KAAL,EAAYD,gBAAgB,IAAK,GAArB;IACZC,KAAK,GAAG,KAAR;IACAD,gBAAgB,IAAK,IAAGE,MAAO,EAA/B;IACA,IAAIb,OAAO,CAACa,MAAD,CAAX,EAAqBF,gBAAgB,IAAK,MAAKX,OAAO,CAACa,MAAD,CAAS,EAA1C;EACtB;;EACDF,gBAAgB,IAAK,IAArB;EAEAF,OAAO,CAACC,GAAR,CAAa,KAAIf,IAAK,IAAGgB,gBAAiB,EAA1C;AACD,CArCM"}