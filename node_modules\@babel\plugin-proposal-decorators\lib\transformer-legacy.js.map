{"version": 3, "names": ["buildClassDecorator", "template", "statement", "buildClassPrototype", "buildGetDescriptor", "buildGetObjectInitializer", "WARNING_CALLS", "WeakSet", "applyEnsureOrdering", "path", "decorators", "isClass", "get", "reduce", "acc", "prop", "concat", "node", "identDecorators", "filter", "decorator", "t", "isIdentifier", "expression", "length", "sequenceExpression", "map", "id", "scope", "generateDeclaredUidIdentifier", "assignmentExpression", "applyClassDecorators", "classPath", "hasClassDecorators", "name", "dec", "reverse", "CLASS_REF", "cloneNode", "DECORATOR", "INNER", "classNode", "applyMethodDecorators", "state", "hasMethodDecorators", "body", "applyTargetDecorators", "some", "applyObjectDecorators", "properties", "type", "decoratedProps", "exprs", "computed", "buildCodeFrameError", "property", "isLiteral", "key", "stringLiteral", "target", "static", "isClassProperty", "descriptor", "initializer", "value", "functionExpression", "blockStatement", "returnStatement", "nullLiteral", "callExpression", "addHelper", "thisExpression", "add", "push", "arrayExpression", "objectExpression", "objectProperty", "identifier", "booleanLiteral", "isObjectProperty", "TEMP", "TARGET", "PROPERTY", "decoratedClassToExpression", "ref", "generateUidIdentifier", "variableDeclaration", "variableDeclarator", "toExpression", "visitor", "ExportDefaultDeclaration", "decl", "isClassDeclaration", "replacement", "varDeclPath", "replaceWithMultiple", "exportNamedDeclaration", "exportSpecifier", "declarations", "registerDeclaration", "ClassDeclaration", "newPath", "replaceWith", "binding", "getOwnBinding", "ClassExpression", "decoratedClass", "ObjectExpression", "decoratedObject", "AssignmentExpression", "has", "right", "CallExpression", "arguments", "callee"], "sources": ["../src/transformer-legacy.ts"], "sourcesContent": ["// Fork of https://github.com/loganfsmyth/babel-plugin-proposal-decorators-legacy\n\nimport { template, types as t, type PluginPass } from \"@babel/core\";\nimport type { NodePath, Visitor } from \"@babel/traverse\";\n\nconst buildClassDecorator = template.statement(`\n  DECORATOR(CLASS_REF = INNER) || CLASS_REF;\n`) as (replacements: {\n  DECORATOR: t.Expression;\n  CLASS_REF: t.Identifier;\n  INNER: t.Expression;\n}) => t.ExpressionStatement;\n\nconst buildClassPrototype = template(`\n  CLASS_REF.prototype;\n`) as (replacements: { CLASS_REF: t.Identifier }) => t.ExpressionStatement;\n\nconst buildGetDescriptor = template(`\n    Object.getOwnPropertyDescriptor(TARGET, PROPERTY);\n`) as (replacements: {\n  TARGET: t.Expression;\n  PROPERTY: t.Literal;\n}) => t.ExpressionStatement;\n\nconst buildGetObjectInitializer = template(`\n    (TEMP = Object.getOwnPropertyDescriptor(TARGET, PROPERTY), (TEMP = TEMP ? TEMP.value : undefined), {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        initializer: function(){\n            return TEMP;\n        }\n    })\n`) as (replacements: {\n  TEMP: t.Identifier;\n  TARGET: t.Expression;\n  PROPERTY: t.Literal;\n}) => t.ExpressionStatement;\n\nconst WARNING_CALLS = new WeakSet();\n\n// legacy decorator does not support ClassAccessorProperty\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty;\n\n/**\n * If the decorator expressions are non-identifiers, hoist them to before the class so we can be sure\n * that they are evaluated in order.\n */\nfunction applyEnsureOrdering(\n  path: NodePath<t.ClassExpression | t.ObjectExpression>,\n) {\n  // TODO: This should probably also hoist computed properties.\n  const decorators: t.Decorator[] = (\n    path.isClass()\n      ? [\n          path,\n          ...(path.get(\"body.body\") as NodePath<ClassDecoratableElement>[]),\n        ]\n      : path.get(\"properties\")\n  ).reduce(\n    (\n      acc: t.Decorator[],\n      prop: NodePath<\n        t.ObjectMember | t.ClassExpression | ClassDecoratableElement\n      >,\n    ) => acc.concat(prop.node.decorators || []),\n    [],\n  );\n\n  const identDecorators = decorators.filter(\n    decorator => !t.isIdentifier(decorator.expression),\n  );\n  if (identDecorators.length === 0) return;\n\n  return t.sequenceExpression(\n    identDecorators\n      .map((decorator): t.Expression => {\n        const expression = decorator.expression;\n        const id = (decorator.expression =\n          path.scope.generateDeclaredUidIdentifier(\"dec\"));\n        return t.assignmentExpression(\"=\", id, expression);\n      })\n      .concat([path.node]),\n  );\n}\n\n/**\n * Given a class expression with class-level decorators, create a new expression\n * with the proper decorated behavior.\n */\nfunction applyClassDecorators(classPath: NodePath<t.ClassExpression>) {\n  if (!hasClassDecorators(classPath.node)) return;\n\n  const decorators = classPath.node.decorators || [];\n  classPath.node.decorators = null;\n\n  const name = classPath.scope.generateDeclaredUidIdentifier(\"class\");\n\n  return decorators\n    .map(dec => dec.expression)\n    .reverse()\n    .reduce(function (acc, decorator) {\n      return buildClassDecorator({\n        CLASS_REF: t.cloneNode(name),\n        DECORATOR: t.cloneNode(decorator),\n        INNER: acc,\n      }).expression;\n    }, classPath.node);\n}\n\nfunction hasClassDecorators(classNode: t.Class) {\n  return !!(classNode.decorators && classNode.decorators.length);\n}\n\n/**\n * Given a class expression with method-level decorators, create a new expression\n * with the proper decorated behavior.\n */\nfunction applyMethodDecorators(\n  path: NodePath<t.ClassExpression>,\n  state: PluginPass,\n) {\n  if (!hasMethodDecorators(path.node.body.body)) return;\n\n  return applyTargetDecorators(\n    path,\n    state,\n    // @ts-expect-error ClassAccessorProperty is not supported in legacy decorator\n    path.node.body.body,\n  );\n}\n\nfunction hasMethodDecorators(\n  body: t.ClassBody[\"body\"] | t.ObjectExpression[\"properties\"],\n) {\n  return body.some(\n    node =>\n      // @ts-expect-error decorators not in SpreadElement/StaticBlock\n      node.decorators?.length,\n  );\n}\n\n/**\n * Given an object expression with property decorators, create a new expression\n * with the proper decorated behavior.\n */\nfunction applyObjectDecorators(\n  path: NodePath<t.ObjectExpression>,\n  state: PluginPass,\n) {\n  if (!hasMethodDecorators(path.node.properties)) return;\n\n  return applyTargetDecorators(\n    path,\n    state,\n    path.node.properties.filter(\n      (prop): prop is t.ObjectMember => prop.type !== \"SpreadElement\",\n    ),\n  );\n}\n\n/**\n * A helper to pull out property decorators into a sequence expression.\n */\nfunction applyTargetDecorators(\n  path: NodePath<t.ClassExpression | t.ObjectExpression>,\n  state: PluginPass,\n  decoratedProps: (t.ObjectMember | ClassDecoratableElement)[],\n) {\n  const name = path.scope.generateDeclaredUidIdentifier(\n    path.isClass() ? \"class\" : \"obj\",\n  );\n\n  const exprs = decoratedProps.reduce(function (acc, node) {\n    let decorators: t.Decorator[] = [];\n    if (node.decorators != null) {\n      decorators = node.decorators;\n      node.decorators = null;\n    }\n\n    if (decorators.length === 0) return acc;\n\n    if (\n      // @ts-expect-error computed is not in ClassPrivateProperty\n      node.computed\n    ) {\n      throw path.buildCodeFrameError(\n        \"Computed method/property decorators are not yet supported.\",\n      );\n    }\n\n    const property: t.Literal = t.isLiteral(node.key)\n      ? node.key\n      : t.stringLiteral(\n          // @ts-expect-error: should we handle ClassPrivateProperty?\n          node.key.name,\n        );\n\n    const target =\n      path.isClass() && !(node as ClassDecoratableElement).static\n        ? buildClassPrototype({\n            CLASS_REF: name,\n          }).expression\n        : name;\n\n    if (t.isClassProperty(node, { static: false })) {\n      const descriptor = path.scope.generateDeclaredUidIdentifier(\"descriptor\");\n\n      const initializer = node.value\n        ? t.functionExpression(\n            null,\n            [],\n            t.blockStatement([t.returnStatement(node.value)]),\n          )\n        : t.nullLiteral();\n\n      node.value = t.callExpression(\n        state.addHelper(\"initializerWarningHelper\"),\n        [descriptor, t.thisExpression()],\n      );\n\n      WARNING_CALLS.add(node.value);\n\n      acc.push(\n        t.assignmentExpression(\n          \"=\",\n          t.cloneNode(descriptor),\n          t.callExpression(state.addHelper(\"applyDecoratedDescriptor\"), [\n            t.cloneNode(target),\n            t.cloneNode(property),\n            t.arrayExpression(\n              decorators.map(dec => t.cloneNode(dec.expression)),\n            ),\n            t.objectExpression([\n              t.objectProperty(\n                t.identifier(\"configurable\"),\n                t.booleanLiteral(true),\n              ),\n              t.objectProperty(\n                t.identifier(\"enumerable\"),\n                t.booleanLiteral(true),\n              ),\n              t.objectProperty(\n                t.identifier(\"writable\"),\n                t.booleanLiteral(true),\n              ),\n              t.objectProperty(t.identifier(\"initializer\"), initializer),\n            ]),\n          ]),\n        ),\n      );\n    } else {\n      acc.push(\n        t.callExpression(state.addHelper(\"applyDecoratedDescriptor\"), [\n          t.cloneNode(target),\n          t.cloneNode(property),\n          t.arrayExpression(decorators.map(dec => t.cloneNode(dec.expression))),\n          t.isObjectProperty(node) || t.isClassProperty(node, { static: true })\n            ? buildGetObjectInitializer({\n                TEMP: path.scope.generateDeclaredUidIdentifier(\"init\"),\n                TARGET: t.cloneNode(target),\n                PROPERTY: t.cloneNode(property),\n              }).expression\n            : buildGetDescriptor({\n                TARGET: t.cloneNode(target),\n                PROPERTY: t.cloneNode(property),\n              }).expression,\n          t.cloneNode(target),\n        ]),\n      );\n    }\n\n    return acc;\n  }, []);\n\n  return t.sequenceExpression([\n    t.assignmentExpression(\"=\", t.cloneNode(name), path.node),\n    t.sequenceExpression(exprs),\n    t.cloneNode(name),\n  ]);\n}\n\nfunction decoratedClassToExpression({ node, scope }: NodePath<t.Class>) {\n  if (!hasClassDecorators(node) && !hasMethodDecorators(node.body.body)) {\n    return;\n  }\n\n  const ref = node.id\n    ? t.cloneNode(node.id)\n    : scope.generateUidIdentifier(\"class\");\n\n  return t.variableDeclaration(\"let\", [\n    t.variableDeclarator(ref, t.toExpression(node)),\n  ]);\n}\n\nconst visitor: Visitor<PluginPass> = {\n  ExportDefaultDeclaration(path) {\n    const decl = path.get(\"declaration\");\n    if (!decl.isClassDeclaration()) return;\n\n    const replacement = decoratedClassToExpression(decl);\n    if (replacement) {\n      const [varDeclPath] = path.replaceWithMultiple([\n        replacement,\n        t.exportNamedDeclaration(null, [\n          t.exportSpecifier(\n            // @ts-expect-error todo(flow->ts) might be add more specific return type for decoratedClassToExpression\n            t.cloneNode(replacement.declarations[0].id),\n            t.identifier(\"default\"),\n          ),\n        ]),\n      ]);\n\n      if (!decl.node.id) {\n        path.scope.registerDeclaration(varDeclPath);\n      }\n    }\n  },\n  ClassDeclaration(path) {\n    const replacement = decoratedClassToExpression(path);\n    if (replacement) {\n      const [newPath] = path.replaceWith(replacement);\n\n      const decl = newPath.get(\"declarations.0\");\n      const id = decl.node.id as t.Identifier;\n\n      // TODO: Maybe add this logic to @babel/traverse\n      const binding = path.scope.getOwnBinding(id.name);\n      binding.identifier = id;\n      binding.path = decl;\n    }\n  },\n  ClassExpression(path, state) {\n    // Create a replacement for the class node if there is one. We do one pass to replace classes with\n    // class decorators, and a second pass to process method decorators.\n    const decoratedClass =\n      applyEnsureOrdering(path) ||\n      applyClassDecorators(path) ||\n      applyMethodDecorators(path, state);\n\n    if (decoratedClass) path.replaceWith(decoratedClass);\n  },\n  ObjectExpression(path, state) {\n    const decoratedObject =\n      applyEnsureOrdering(path) || applyObjectDecorators(path, state);\n\n    if (decoratedObject) path.replaceWith(decoratedObject);\n  },\n\n  AssignmentExpression(path, state) {\n    if (!WARNING_CALLS.has(path.node.right)) return;\n\n    path.replaceWith(\n      t.callExpression(state.addHelper(\"initializerDefineProperty\"), [\n        // @ts-expect-error todo(flow->ts) typesafe NodePath.get\n        t.cloneNode(path.get(\"left.object\").node),\n        t.stringLiteral(\n          // @ts-expect-error todo(flow->ts) typesafe NodePath.get\n          path.get(\"left.property\").node.name ||\n            // @ts-expect-error todo(flow->ts) typesafe NodePath.get\n            path.get(\"left.property\").node.value,\n        ),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"right.arguments\")[0].node),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"right.arguments\")[1].node),\n      ]),\n    );\n  },\n\n  CallExpression(path, state) {\n    if (path.node.arguments.length !== 3) return;\n    if (!WARNING_CALLS.has(path.node.arguments[2])) return;\n\n    // If the class properties plugin isn't enabled, this line will add an unused helper\n    // to the code. It's not ideal, but it's ok since the configuration is not valid anyway.\n    // @ts-expect-error todo(flow->ts) check that `callee` is Identifier\n    if (path.node.callee.name !== state.addHelper(\"defineProperty\").name) {\n      return;\n    }\n\n    path.replaceWith(\n      t.callExpression(state.addHelper(\"initializerDefineProperty\"), [\n        t.cloneNode(path.get(\"arguments\")[0].node),\n        t.cloneNode(path.get(\"arguments\")[1].node),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"arguments.2.arguments\")[0].node),\n        // @ts-expect-error todo(flow->ts)\n        t.cloneNode(path.get(\"arguments.2.arguments\")[1].node),\n      ]),\n    );\n  },\n};\n\nexport default visitor;\n"], "mappings": ";;;;;;;AAEA;;AAGA,MAAMA,mBAAmB,GAAGC,cAAA,CAASC,SAAT,CAAoB;AAChD;AACA,CAF4B,CAA5B;;AAQA,MAAMC,mBAAmB,GAAG,IAAAF,cAAA,EAAU;AACtC;AACA,CAF4B,CAA5B;AAIA,MAAMG,kBAAkB,GAAG,IAAAH,cAAA,EAAU;AACrC;AACA,CAF2B,CAA3B;AAOA,MAAMI,yBAAyB,GAAG,IAAAJ,cAAA,EAAU;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CATkC,CAAlC;AAeA,MAAMK,aAAa,GAAG,IAAIC,OAAJ,EAAtB;;AAaA,SAASC,mBAAT,CACEC,IADF,EAEE;EAEA,MAAMC,UAAyB,GAAG,CAChCD,IAAI,CAACE,OAAL,KACI,CACEF,IADF,EAEE,GAAIA,IAAI,CAACG,GAAL,CAAS,WAAT,CAFN,CADJ,GAKIH,IAAI,CAACG,GAAL,CAAS,YAAT,CAN4B,EAOhCC,MAPgC,CAQhC,CACEC,GADF,EAEEC,IAFF,KAKKD,GAAG,CAACE,MAAJ,CAAWD,IAAI,CAACE,IAAL,CAAUP,UAAV,IAAwB,EAAnC,CAb2B,EAchC,EAdgC,CAAlC;EAiBA,MAAMQ,eAAe,GAAGR,UAAU,CAACS,MAAX,CACtBC,SAAS,IAAI,CAACC,WAAA,CAAEC,YAAF,CAAeF,SAAS,CAACG,UAAzB,CADQ,CAAxB;EAGA,IAAIL,eAAe,CAACM,MAAhB,KAA2B,CAA/B,EAAkC;EAElC,OAAOH,WAAA,CAAEI,kBAAF,CACLP,eAAe,CACZQ,GADH,CACQN,SAAD,IAA6B;IAChC,MAAMG,UAAU,GAAGH,SAAS,CAACG,UAA7B;IACA,MAAMI,EAAE,GAAIP,SAAS,CAACG,UAAV,GACVd,IAAI,CAACmB,KAAL,CAAWC,6BAAX,CAAyC,KAAzC,CADF;IAEA,OAAOR,WAAA,CAAES,oBAAF,CAAuB,GAAvB,EAA4BH,EAA5B,EAAgCJ,UAAhC,CAAP;EACD,CANH,EAOGP,MAPH,CAOU,CAACP,IAAI,CAACQ,IAAN,CAPV,CADK,CAAP;AAUD;;AAMD,SAASc,oBAAT,CAA8BC,SAA9B,EAAsE;EACpE,IAAI,CAACC,kBAAkB,CAACD,SAAS,CAACf,IAAX,CAAvB,EAAyC;EAEzC,MAAMP,UAAU,GAAGsB,SAAS,CAACf,IAAV,CAAeP,UAAf,IAA6B,EAAhD;EACAsB,SAAS,CAACf,IAAV,CAAeP,UAAf,GAA4B,IAA5B;EAEA,MAAMwB,IAAI,GAAGF,SAAS,CAACJ,KAAV,CAAgBC,6BAAhB,CAA8C,OAA9C,CAAb;EAEA,OAAOnB,UAAU,CACdgB,GADI,CACAS,GAAG,IAAIA,GAAG,CAACZ,UADX,EAEJa,OAFI,GAGJvB,MAHI,CAGG,UAAUC,GAAV,EAAeM,SAAf,EAA0B;IAChC,OAAOpB,mBAAmB,CAAC;MACzBqC,SAAS,EAAEhB,WAAA,CAAEiB,SAAF,CAAYJ,IAAZ,CADc;MAEzBK,SAAS,EAAElB,WAAA,CAAEiB,SAAF,CAAYlB,SAAZ,CAFc;MAGzBoB,KAAK,EAAE1B;IAHkB,CAAD,CAAnB,CAIJS,UAJH;EAKD,CATI,EASFS,SAAS,CAACf,IATR,CAAP;AAUD;;AAED,SAASgB,kBAAT,CAA4BQ,SAA5B,EAAgD;EAC9C,OAAO,CAAC,EAAEA,SAAS,CAAC/B,UAAV,IAAwB+B,SAAS,CAAC/B,UAAV,CAAqBc,MAA/C,CAAR;AACD;;AAMD,SAASkB,qBAAT,CACEjC,IADF,EAEEkC,KAFF,EAGE;EACA,IAAI,CAACC,mBAAmB,CAACnC,IAAI,CAACQ,IAAL,CAAU4B,IAAV,CAAeA,IAAhB,CAAxB,EAA+C;EAE/C,OAAOC,qBAAqB,CAC1BrC,IAD0B,EAE1BkC,KAF0B,EAI1BlC,IAAI,CAACQ,IAAL,CAAU4B,IAAV,CAAeA,IAJW,CAA5B;AAMD;;AAED,SAASD,mBAAT,CACEC,IADF,EAEE;EACA,OAAOA,IAAI,CAACE,IAAL,CACL9B,IAAI;IAAA;;IAAA,2BAEFA,IAAI,CAACP,UAFH,qBAEF,iBAAiBc,MAFf;EAAA,CADC,CAAP;AAKD;;AAMD,SAASwB,qBAAT,CACEvC,IADF,EAEEkC,KAFF,EAGE;EACA,IAAI,CAACC,mBAAmB,CAACnC,IAAI,CAACQ,IAAL,CAAUgC,UAAX,CAAxB,EAAgD;EAEhD,OAAOH,qBAAqB,CAC1BrC,IAD0B,EAE1BkC,KAF0B,EAG1BlC,IAAI,CAACQ,IAAL,CAAUgC,UAAV,CAAqB9B,MAArB,CACGJ,IAAD,IAAkCA,IAAI,CAACmC,IAAL,KAAc,eADlD,CAH0B,CAA5B;AAOD;;AAKD,SAASJ,qBAAT,CACErC,IADF,EAEEkC,KAFF,EAGEQ,cAHF,EAIE;EACA,MAAMjB,IAAI,GAAGzB,IAAI,CAACmB,KAAL,CAAWC,6BAAX,CACXpB,IAAI,CAACE,OAAL,KAAiB,OAAjB,GAA2B,KADhB,CAAb;EAIA,MAAMyC,KAAK,GAAGD,cAAc,CAACtC,MAAf,CAAsB,UAAUC,GAAV,EAAeG,IAAf,EAAqB;IACvD,IAAIP,UAAyB,GAAG,EAAhC;;IACA,IAAIO,IAAI,CAACP,UAAL,IAAmB,IAAvB,EAA6B;MAC3BA,UAAU,GAAGO,IAAI,CAACP,UAAlB;MACAO,IAAI,CAACP,UAAL,GAAkB,IAAlB;IACD;;IAED,IAAIA,UAAU,CAACc,MAAX,KAAsB,CAA1B,EAA6B,OAAOV,GAAP;;IAE7B,IAEEG,IAAI,CAACoC,QAFP,EAGE;MACA,MAAM5C,IAAI,CAAC6C,mBAAL,CACJ,4DADI,CAAN;IAGD;;IAED,MAAMC,QAAmB,GAAGlC,WAAA,CAAEmC,SAAF,CAAYvC,IAAI,CAACwC,GAAjB,IACxBxC,IAAI,CAACwC,GADmB,GAExBpC,WAAA,CAAEqC,aAAF,CAEEzC,IAAI,CAACwC,GAAL,CAASvB,IAFX,CAFJ;IAOA,MAAMyB,MAAM,GACVlD,IAAI,CAACE,OAAL,MAAkB,CAAEM,IAAD,CAAkC2C,MAArD,GACIzD,mBAAmB,CAAC;MAClBkC,SAAS,EAAEH;IADO,CAAD,CAAnB,CAEGX,UAHP,GAIIW,IALN;;IAOA,IAAIb,WAAA,CAAEwC,eAAF,CAAkB5C,IAAlB,EAAwB;MAAE2C,MAAM,EAAE;IAAV,CAAxB,CAAJ,EAAgD;MAC9C,MAAME,UAAU,GAAGrD,IAAI,CAACmB,KAAL,CAAWC,6BAAX,CAAyC,YAAzC,CAAnB;MAEA,MAAMkC,WAAW,GAAG9C,IAAI,CAAC+C,KAAL,GAChB3C,WAAA,CAAE4C,kBAAF,CACE,IADF,EAEE,EAFF,EAGE5C,WAAA,CAAE6C,cAAF,CAAiB,CAAC7C,WAAA,CAAE8C,eAAF,CAAkBlD,IAAI,CAAC+C,KAAvB,CAAD,CAAjB,CAHF,CADgB,GAMhB3C,WAAA,CAAE+C,WAAF,EANJ;MAQAnD,IAAI,CAAC+C,KAAL,GAAa3C,WAAA,CAAEgD,cAAF,CACX1B,KAAK,CAAC2B,SAAN,CAAgB,0BAAhB,CADW,EAEX,CAACR,UAAD,EAAazC,WAAA,CAAEkD,cAAF,EAAb,CAFW,CAAb;MAKAjE,aAAa,CAACkE,GAAd,CAAkBvD,IAAI,CAAC+C,KAAvB;MAEAlD,GAAG,CAAC2D,IAAJ,CACEpD,WAAA,CAAES,oBAAF,CACE,GADF,EAEET,WAAA,CAAEiB,SAAF,CAAYwB,UAAZ,CAFF,EAGEzC,WAAA,CAAEgD,cAAF,CAAiB1B,KAAK,CAAC2B,SAAN,CAAgB,0BAAhB,CAAjB,EAA8D,CAC5DjD,WAAA,CAAEiB,SAAF,CAAYqB,MAAZ,CAD4D,EAE5DtC,WAAA,CAAEiB,SAAF,CAAYiB,QAAZ,CAF4D,EAG5DlC,WAAA,CAAEqD,eAAF,CACEhE,UAAU,CAACgB,GAAX,CAAeS,GAAG,IAAId,WAAA,CAAEiB,SAAF,CAAYH,GAAG,CAACZ,UAAhB,CAAtB,CADF,CAH4D,EAM5DF,WAAA,CAAEsD,gBAAF,CAAmB,CACjBtD,WAAA,CAAEuD,cAAF,CACEvD,WAAA,CAAEwD,UAAF,CAAa,cAAb,CADF,EAEExD,WAAA,CAAEyD,cAAF,CAAiB,IAAjB,CAFF,CADiB,EAKjBzD,WAAA,CAAEuD,cAAF,CACEvD,WAAA,CAAEwD,UAAF,CAAa,YAAb,CADF,EAEExD,WAAA,CAAEyD,cAAF,CAAiB,IAAjB,CAFF,CALiB,EASjBzD,WAAA,CAAEuD,cAAF,CACEvD,WAAA,CAAEwD,UAAF,CAAa,UAAb,CADF,EAEExD,WAAA,CAAEyD,cAAF,CAAiB,IAAjB,CAFF,CATiB,EAajBzD,WAAA,CAAEuD,cAAF,CAAiBvD,WAAA,CAAEwD,UAAF,CAAa,aAAb,CAAjB,EAA8Cd,WAA9C,CAbiB,CAAnB,CAN4D,CAA9D,CAHF,CADF;IA4BD,CA9CD,MA8CO;MACLjD,GAAG,CAAC2D,IAAJ,CACEpD,WAAA,CAAEgD,cAAF,CAAiB1B,KAAK,CAAC2B,SAAN,CAAgB,0BAAhB,CAAjB,EAA8D,CAC5DjD,WAAA,CAAEiB,SAAF,CAAYqB,MAAZ,CAD4D,EAE5DtC,WAAA,CAAEiB,SAAF,CAAYiB,QAAZ,CAF4D,EAG5DlC,WAAA,CAAEqD,eAAF,CAAkBhE,UAAU,CAACgB,GAAX,CAAeS,GAAG,IAAId,WAAA,CAAEiB,SAAF,CAAYH,GAAG,CAACZ,UAAhB,CAAtB,CAAlB,CAH4D,EAI5DF,WAAA,CAAE0D,gBAAF,CAAmB9D,IAAnB,KAA4BI,WAAA,CAAEwC,eAAF,CAAkB5C,IAAlB,EAAwB;QAAE2C,MAAM,EAAE;MAAV,CAAxB,CAA5B,GACIvD,yBAAyB,CAAC;QACxB2E,IAAI,EAAEvE,IAAI,CAACmB,KAAL,CAAWC,6BAAX,CAAyC,MAAzC,CADkB;QAExBoD,MAAM,EAAE5D,WAAA,CAAEiB,SAAF,CAAYqB,MAAZ,CAFgB;QAGxBuB,QAAQ,EAAE7D,WAAA,CAAEiB,SAAF,CAAYiB,QAAZ;MAHc,CAAD,CAAzB,CAIGhC,UALP,GAMInB,kBAAkB,CAAC;QACjB6E,MAAM,EAAE5D,WAAA,CAAEiB,SAAF,CAAYqB,MAAZ,CADS;QAEjBuB,QAAQ,EAAE7D,WAAA,CAAEiB,SAAF,CAAYiB,QAAZ;MAFO,CAAD,CAAlB,CAGGhC,UAbqD,EAc5DF,WAAA,CAAEiB,SAAF,CAAYqB,MAAZ,CAd4D,CAA9D,CADF;IAkBD;;IAED,OAAO7C,GAAP;EACD,CApGa,EAoGX,EApGW,CAAd;EAsGA,OAAOO,WAAA,CAAEI,kBAAF,CAAqB,CAC1BJ,WAAA,CAAES,oBAAF,CAAuB,GAAvB,EAA4BT,WAAA,CAAEiB,SAAF,CAAYJ,IAAZ,CAA5B,EAA+CzB,IAAI,CAACQ,IAApD,CAD0B,EAE1BI,WAAA,CAAEI,kBAAF,CAAqB2B,KAArB,CAF0B,EAG1B/B,WAAA,CAAEiB,SAAF,CAAYJ,IAAZ,CAH0B,CAArB,CAAP;AAKD;;AAED,SAASiD,0BAAT,CAAoC;EAAElE,IAAF;EAAQW;AAAR,CAApC,EAAwE;EACtE,IAAI,CAACK,kBAAkB,CAAChB,IAAD,CAAnB,IAA6B,CAAC2B,mBAAmB,CAAC3B,IAAI,CAAC4B,IAAL,CAAUA,IAAX,CAArD,EAAuE;IACrE;EACD;;EAED,MAAMuC,GAAG,GAAGnE,IAAI,CAACU,EAAL,GACRN,WAAA,CAAEiB,SAAF,CAAYrB,IAAI,CAACU,EAAjB,CADQ,GAERC,KAAK,CAACyD,qBAAN,CAA4B,OAA5B,CAFJ;EAIA,OAAOhE,WAAA,CAAEiE,mBAAF,CAAsB,KAAtB,EAA6B,CAClCjE,WAAA,CAAEkE,kBAAF,CAAqBH,GAArB,EAA0B/D,WAAA,CAAEmE,YAAF,CAAevE,IAAf,CAA1B,CADkC,CAA7B,CAAP;AAGD;;AAED,MAAMwE,OAA4B,GAAG;EACnCC,wBAAwB,CAACjF,IAAD,EAAO;IAC7B,MAAMkF,IAAI,GAAGlF,IAAI,CAACG,GAAL,CAAS,aAAT,CAAb;IACA,IAAI,CAAC+E,IAAI,CAACC,kBAAL,EAAL,EAAgC;IAEhC,MAAMC,WAAW,GAAGV,0BAA0B,CAACQ,IAAD,CAA9C;;IACA,IAAIE,WAAJ,EAAiB;MACf,MAAM,CAACC,WAAD,IAAgBrF,IAAI,CAACsF,mBAAL,CAAyB,CAC7CF,WAD6C,EAE7CxE,WAAA,CAAE2E,sBAAF,CAAyB,IAAzB,EAA+B,CAC7B3E,WAAA,CAAE4E,eAAF,CAEE5E,WAAA,CAAEiB,SAAF,CAAYuD,WAAW,CAACK,YAAZ,CAAyB,CAAzB,EAA4BvE,EAAxC,CAFF,EAGEN,WAAA,CAAEwD,UAAF,CAAa,SAAb,CAHF,CAD6B,CAA/B,CAF6C,CAAzB,CAAtB;;MAWA,IAAI,CAACc,IAAI,CAAC1E,IAAL,CAAUU,EAAf,EAAmB;QACjBlB,IAAI,CAACmB,KAAL,CAAWuE,mBAAX,CAA+BL,WAA/B;MACD;IACF;EACF,CAtBkC;;EAuBnCM,gBAAgB,CAAC3F,IAAD,EAAO;IACrB,MAAMoF,WAAW,GAAGV,0BAA0B,CAAC1E,IAAD,CAA9C;;IACA,IAAIoF,WAAJ,EAAiB;MACf,MAAM,CAACQ,OAAD,IAAY5F,IAAI,CAAC6F,WAAL,CAAiBT,WAAjB,CAAlB;MAEA,MAAMF,IAAI,GAAGU,OAAO,CAACzF,GAAR,CAAY,gBAAZ,CAAb;MACA,MAAMe,EAAE,GAAGgE,IAAI,CAAC1E,IAAL,CAAUU,EAArB;MAGA,MAAM4E,OAAO,GAAG9F,IAAI,CAACmB,KAAL,CAAW4E,aAAX,CAAyB7E,EAAE,CAACO,IAA5B,CAAhB;MACAqE,OAAO,CAAC1B,UAAR,GAAqBlD,EAArB;MACA4E,OAAO,CAAC9F,IAAR,GAAekF,IAAf;IACD;EACF,CApCkC;;EAqCnCc,eAAe,CAAChG,IAAD,EAAOkC,KAAP,EAAc;IAG3B,MAAM+D,cAAc,GAClBlG,mBAAmB,CAACC,IAAD,CAAnB,IACAsB,oBAAoB,CAACtB,IAAD,CADpB,IAEAiC,qBAAqB,CAACjC,IAAD,EAAOkC,KAAP,CAHvB;IAKA,IAAI+D,cAAJ,EAAoBjG,IAAI,CAAC6F,WAAL,CAAiBI,cAAjB;EACrB,CA9CkC;;EA+CnCC,gBAAgB,CAAClG,IAAD,EAAOkC,KAAP,EAAc;IAC5B,MAAMiE,eAAe,GACnBpG,mBAAmB,CAACC,IAAD,CAAnB,IAA6BuC,qBAAqB,CAACvC,IAAD,EAAOkC,KAAP,CADpD;IAGA,IAAIiE,eAAJ,EAAqBnG,IAAI,CAAC6F,WAAL,CAAiBM,eAAjB;EACtB,CApDkC;;EAsDnCC,oBAAoB,CAACpG,IAAD,EAAOkC,KAAP,EAAc;IAChC,IAAI,CAACrC,aAAa,CAACwG,GAAd,CAAkBrG,IAAI,CAACQ,IAAL,CAAU8F,KAA5B,CAAL,EAAyC;IAEzCtG,IAAI,CAAC6F,WAAL,CACEjF,WAAA,CAAEgD,cAAF,CAAiB1B,KAAK,CAAC2B,SAAN,CAAgB,2BAAhB,CAAjB,EAA+D,CAE7DjD,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,aAAT,EAAwBK,IAApC,CAF6D,EAG7DI,WAAA,CAAEqC,aAAF,CAEEjD,IAAI,CAACG,GAAL,CAAS,eAAT,EAA0BK,IAA1B,CAA+BiB,IAA/B,IAEEzB,IAAI,CAACG,GAAL,CAAS,eAAT,EAA0BK,IAA1B,CAA+B+C,KAJnC,CAH6D,EAU7D3C,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,iBAAT,EAA4B,CAA5B,EAA+BK,IAA3C,CAV6D,EAY7DI,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,iBAAT,EAA4B,CAA5B,EAA+BK,IAA3C,CAZ6D,CAA/D,CADF;EAgBD,CAzEkC;;EA2EnC+F,cAAc,CAACvG,IAAD,EAAOkC,KAAP,EAAc;IAC1B,IAAIlC,IAAI,CAACQ,IAAL,CAAUgG,SAAV,CAAoBzF,MAApB,KAA+B,CAAnC,EAAsC;IACtC,IAAI,CAAClB,aAAa,CAACwG,GAAd,CAAkBrG,IAAI,CAACQ,IAAL,CAAUgG,SAAV,CAAoB,CAApB,CAAlB,CAAL,EAAgD;;IAKhD,IAAIxG,IAAI,CAACQ,IAAL,CAAUiG,MAAV,CAAiBhF,IAAjB,KAA0BS,KAAK,CAAC2B,SAAN,CAAgB,gBAAhB,EAAkCpC,IAAhE,EAAsE;MACpE;IACD;;IAEDzB,IAAI,CAAC6F,WAAL,CACEjF,WAAA,CAAEgD,cAAF,CAAiB1B,KAAK,CAAC2B,SAAN,CAAgB,2BAAhB,CAAjB,EAA+D,CAC7DjD,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,WAAT,EAAsB,CAAtB,EAAyBK,IAArC,CAD6D,EAE7DI,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,WAAT,EAAsB,CAAtB,EAAyBK,IAArC,CAF6D,EAI7DI,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,uBAAT,EAAkC,CAAlC,EAAqCK,IAAjD,CAJ6D,EAM7DI,WAAA,CAAEiB,SAAF,CAAY7B,IAAI,CAACG,GAAL,CAAS,uBAAT,EAAkC,CAAlC,EAAqCK,IAAjD,CAN6D,CAA/D,CADF;EAUD;;AAhGkC,CAArC;eAmGewE,O"}