{"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "defineProperty", "getOwnPropertyDescriptors", "defineProperties"], "sources": ["../../src/helpers/objectSpread2.js"], "sourcesContent": ["/* @minVersion 7.5.0 */\n\nimport defineProperty from \"defineProperty\";\n\n// This function is different to \"Reflect.ownKeys\". The enumerableOnly\n// filters on symbol properties only. Returned string properties are always\n// enumerable. It is good to use in objectSpread.\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\n\nexport default function _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(\n          target,\n          key,\n          Object.getOwnPropertyDescriptor(source, key)\n        );\n      });\n    }\n  }\n  return target;\n}\n"], "mappings": ";;;;;;;AAEA;;AAMA,SAASA,OAAT,CAAiBC,MAAjB,EAAyBC,cAAzB,EAAyC;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAP,CAAYF,MAAZ,CAAX;;EACA,IAAIG,MAAM,CAACC,qBAAX,EAAkC;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAP,CAA6BJ,MAA7B,CAAd;;IACA,IAAIC,cAAJ,EAAoB;MAClBI,OAAO,GAAGA,OAAO,CAACC,MAAR,CAAe,UAAUC,GAAV,EAAe;QACtC,OAAOJ,MAAM,CAACK,wBAAP,CAAgCR,MAAhC,EAAwCO,GAAxC,EAA6CE,UAApD;MACD,CAFS,CAAV;IAGD;;IACDP,IAAI,CAACQ,IAAL,CAAUC,KAAV,CAAgBT,IAAhB,EAAsBG,OAAtB;EACD;;EACD,OAAOH,IAAP;AACD;;AAEc,SAASU,cAAT,CAAwBC,MAAxB,EAAgC;EAC7C,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGC,SAAS,CAACC,MAA9B,EAAsCF,CAAC,EAAvC,EAA2C;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAD,CAAT,IAAgB,IAAhB,GAAuBC,SAAS,CAACD,CAAD,CAAhC,GAAsC,EAAnD;;IACA,IAAIA,CAAC,GAAG,CAAR,EAAW;MACTf,OAAO,CAACI,MAAM,CAACc,MAAD,CAAP,EAAiB,IAAjB,CAAP,CAA8BC,OAA9B,CAAsC,UAAUC,GAAV,EAAe;QACnDC,eAAc,CAACP,MAAD,EAASM,GAAT,EAAcF,MAAM,CAACE,GAAD,CAApB,CAAd;MACD,CAFD;IAGD,CAJD,MAIO,IAAIhB,MAAM,CAACkB,yBAAX,EAAsC;MAC3ClB,MAAM,CAACmB,gBAAP,CAAwBT,MAAxB,EAAgCV,MAAM,CAACkB,yBAAP,CAAiCJ,MAAjC,CAAhC;IACD,CAFM,MAEA;MACLlB,OAAO,CAACI,MAAM,CAACc,MAAD,CAAP,CAAP,CAAwBC,OAAxB,CAAgC,UAAUC,GAAV,EAAe;QAC7ChB,MAAM,CAACiB,cAAP,CACEP,MADF,EAEEM,GAFF,EAGEhB,MAAM,CAACK,wBAAP,CAAgCS,MAAhC,EAAwCE,GAAxC,CAHF;MAKD,CAND;IAOD;EACF;;EACD,OAAON,MAAP;AACD"}