"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/**\n * Auto Save on Leave Hook\n *\n * 简单逻辑：\n * 1. 用户选择离开 = 不自动保存 + 清空快照\n * 2. 保存成功后 = 清空快照\n * 3. 笔记跳转时 = 自动保存\n * 4. 页面关闭/刷新时 = 弹窗询问\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n */ \n\n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var hasUnsavedChanges = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        return  true && window.saveButtonStatus === \"save\";\n    }, []);\n    var autoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    var clearSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.clearSnapshots) {\n            try {\n                window.clearSnapshots();\n            } catch (error) {\n                console.error(\"清空快照失败:\", error);\n            }\n        }\n    }, []);\n    // 页面关闭/刷新时弹窗\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled || !hasUnsavedChanges()) return;\n        var message = \"您有未保存的更改。确定要离开吗？\";\n        event.returnValue = message;\n        // 用户选择取消时自动保存\n        setTimeout(function() {\n            autoSave();\n        }, 100);\n        // 用户选择离开或重新加载时清空快照\n        var handleUnload = function() {\n            clearSnapshots();\n            window.removeEventListener(\"unload\", handleUnload);\n        };\n        window.addEventListener(\"unload\", handleUnload);\n        return message;\n    }, [\n        enabled,\n        hasUnsavedChanges,\n        autoSave,\n        clearSnapshots\n    ]);\n    // 路由跳转时处理\n    var handleRouteChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(function(url) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(this, function(_state) {\n                if (!enabled || isAutoSavingRef.current || !hasUnsavedChanges()) return [\n                    2\n                ];\n                // 检查是否是笔记跳转\n                if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_2__.isNoteLink)(url) || url === \"/\" || url.includes(\"?new\")) {\n                    // 笔记跳转：后台自动保存\n                    isAutoSavingRef.current = true;\n                    autoSave().finally(function() {\n                        isAutoSavingRef.current = false;\n                    });\n                    return [\n                        2\n                    ];\n                }\n                // 非笔记跳转：直接清空快照并跳转（不弹窗）\n                // 重新加载和离开都代表不保存\n                clearSnapshots();\n                return [\n                    2\n                ];\n            });\n        // 不阻止跳转，直接允许\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        hasUnsavedChanges,\n        autoSave,\n        clearSnapshots,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            return window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChange);\n        return function() {\n            return router.events.off(\"routeChangeStart\", handleRouteChange);\n        };\n    }, [\n        enabled,\n        handleRouteChange,\n        router.events\n    ]);\n    return {\n        hasUnsavedChanges: hasUnsavedChanges,\n        autoSave: autoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});