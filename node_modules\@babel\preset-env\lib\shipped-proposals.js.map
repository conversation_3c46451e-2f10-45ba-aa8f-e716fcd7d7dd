{"version": 3, "names": ["proposalPlugins", "Set", "proposalSyntaxPlugins", "pluginSyntaxObject", "pluginSyntaxEntries", "Object", "keys", "map", "key", "pluginSyntaxMap", "Map"], "sources": ["../src/shipped-proposals.ts"], "sourcesContent": ["// TODO(Babel 8): Remove this file\n/* eslint sort-keys: \"error\" */\n// These mappings represent the transform plugins that have been\n// shipped by browsers, and are enabled by the `shippedProposals` option.\n\nconst proposalPlugins = new Set<string>();\n\n// proposal syntax plugins enabled by the `shippedProposals` option.\n// Unlike proposalPlugins above, they are independent of compiler targets.\nconst proposalSyntaxPlugins = [\"syntax-import-assertions\"] as const;\n\n// use intermediary object to enforce alphabetical key order\nconst pluginSyntaxObject = {\n  \"transform-async-generator-functions\": \"syntax-async-generators\",\n  \"transform-class-properties\": \"syntax-class-properties\",\n  \"transform-class-static-block\": \"syntax-class-static-block\",\n  \"transform-json-strings\": \"syntax-json-strings\",\n  \"transform-nullish-coalescing-operator\": \"syntax-nullish-coalescing-operator\",\n  \"transform-numeric-separator\": \"syntax-numeric-separator\",\n  \"transform-object-rest-spread\": \"syntax-object-rest-spread\",\n  \"transform-optional-catch-binding\": \"syntax-optional-catch-binding\",\n  \"transform-optional-chaining\": \"syntax-optional-chaining\",\n  // note: we don't have syntax-private-methods\n  \"transform-private-methods\": \"syntax-class-properties\",\n  \"transform-private-property-in-object\": \"syntax-private-property-in-object\",\n  \"transform-unicode-property-regex\": null as null,\n} as const;\n\nconst pluginSyntaxEntries = Object.keys(pluginSyntaxObject).map<\n  [string, string | null]\n>(function (key) {\n  return [\n    key,\n    // @ts-expect-error key has been guarded\n    pluginSyntaxObject[key],\n  ];\n});\n\nconst pluginSyntaxMap = new Map(pluginSyntaxEntries);\n\nexport { proposalPlugins, proposalSyntaxPlugins, pluginSyntaxMap };\n"], "mappings": ";;;;;;AAKA,MAAMA,eAAe,GAAG,IAAIC,GAAJ,EAAxB;;AAIA,MAAMC,qBAAqB,GAAG,CAAC,0BAAD,CAA9B;;AAGA,MAAMC,kBAAkB,GAAG;EACzB,uCAAuC,yBADd;EAEzB,8BAA8B,yBAFL;EAGzB,gCAAgC,2BAHP;EAIzB,0BAA0B,qBAJD;EAKzB,yCAAyC,oCALhB;EAMzB,+BAA+B,0BANN;EAOzB,gCAAgC,2BAPP;EAQzB,oCAAoC,+BARX;EASzB,+BAA+B,0BATN;EAWzB,6BAA6B,yBAXJ;EAYzB,wCAAwC,mCAZf;EAazB,oCAAoC;AAbX,CAA3B;AAgBA,MAAMC,mBAAmB,GAAGC,MAAM,CAACC,IAAP,CAAYH,kBAAZ,EAAgCI,GAAhC,CAE1B,UAAUC,GAAV,EAAe;EACf,OAAO,CACLA,GADK,EAGLL,kBAAkB,CAACK,GAAD,CAHb,CAAP;AAKD,CAR2B,CAA5B;AAUA,MAAMC,eAAe,GAAG,IAAIC,GAAJ,CAAQN,mBAAR,CAAxB"}