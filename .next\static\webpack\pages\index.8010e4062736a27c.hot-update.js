"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_define_property_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/src/_define_property.mjs */ \"./node_modules/@swc/helpers/src/_define_property.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_type_of.mjs */ \"./node_modules/@swc/helpers/src/_type_of.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var ref, ref1, ref2, ref3, snapshotJsonContent;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                noteId: note === null || note === void 0 ? void 0 : note.id,\n                noteContent: (note === null || note === void 0 ? void 0 : (ref = note.content) === null || ref === void 0 ? void 0 : ref.substring(0, 100)) + \"...\",\n                noteContentLength: (note === null || note === void 0 ? void 0 : (ref1 = note.content) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n            });\n            if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                // 新建笔记：快照为空值\n                console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n                return [\n                    2\n                ];\n            }\n            try {\n                ;\n                snapshotJsonContent = \"\";\n                console.log(\"\\uD83D\\uDD27 快照内容来源分析:\", {\n                    noteContent: ((ref2 = note.content) === null || ref2 === void 0 ? void 0 : ref2.substring(0, 100)) + \"...\",\n                    noteContentLength: ((ref3 = note.content) === null || ref3 === void 0 ? void 0 : ref3.length) || 0,\n                    noteContentType: (0,_swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(note.content)\n                });\n                // 直接使用 note 对象的内容作为快照\n                if (note.content !== undefined && note.content !== null) {\n                    snapshotJsonContent = note.content;\n                    console.log(\"\\uD83D\\uDD27 使用 note 对象内容作为快照\");\n                } else {\n                    snapshotJsonContent = \"\";\n                    console.log(\"\\uD83D\\uDD27 note 内容为空，设置空快照\");\n                }\n                console.log(\"\\uD83D\\uDD27 快照初始化完成:\", {\n                    noteId: note.id,\n                    hasContent: !!snapshotJsonContent,\n                    contentLength: snapshotJsonContent.length,\n                    isJson: snapshotJsonContent.startsWith(\"{\"),\n                    source: (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) ? \"cache\" : note.content ? \"note\" : \"empty\",\n                    contentPreview: snapshotJsonContent.substring(0, 100) + \"...\"\n                });\n                // 🔧 关键：设置快照和当前编辑器内容\n                setNoteSnapshot(snapshotJsonContent);\n                setCurrentEditorContent(snapshotJsonContent);\n            } catch (error) {\n                console.error(\"JSON快照初始化失败:\", error);\n                // 失败时设置为空快照\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n            }\n            return [\n                2\n            ];\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 当笔记ID变化或内容加载完成时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        // 🔧 修复：只有当 note 存在、有 ID 且内容已加载时才初始化\n        // 避免在内容未完全加载时就初始化快照\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            var ref;\n            console.log(\"\\uD83D\\uDD27 触发快照初始化条件:\", {\n                noteId: note.id,\n                hasContent: note.content !== undefined,\n                contentLength: ((ref = note.content) === null || ref === void 0 ? void 0 : ref.length) || 0\n            });\n            initializeSnapshot();\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        console.log(\"\\uD83D\\uDD27 清空所有快照\");\n        setNoteSnapshot(null);\n        setCurrentEditorContent(\"\");\n        // 🔧 修复：清空后重新初始化快照，确保编辑器显示正确内容\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            setTimeout(function() {\n                initializeSnapshot();\n            }, 0);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 关键修复：保存成功后更新快照为当前内容，而不是清空\n                    // 这样可以保持编辑器状态一致，避免内容丢失\n                    setNoteSnapshot(currentEditorContent);\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB,\n        clearAllSnapshots\n    ]);\n    return(// 🔧 简化：像 ceta 版本一样直接返回 note 对象\n    (0,_swc_helpers_src_define_property_mjs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])({\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note\n    }, \"note\", note));\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_13__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});