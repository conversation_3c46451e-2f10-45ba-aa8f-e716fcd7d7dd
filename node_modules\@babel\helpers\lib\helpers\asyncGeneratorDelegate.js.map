{"version": 3, "names": ["_asyncGeneratorDelegate", "inner", "iter", "waiting", "pump", "key", "value", "Promise", "resolve", "done", "OverloadYield", "Symbol", "iterator", "next", "throw", "return"], "sources": ["../../src/helpers/asyncGeneratorDelegate.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nimport <PERSON><PERSON><PERSON><PERSON> from \"OverloadYield\";\n\nexport default function _asyncGeneratorDelegate(inner) {\n  var iter = {},\n    // See the comment in AsyncGenerator to understand what this is.\n    waiting = false;\n\n  function pump(key, value) {\n    waiting = true;\n    value = new Promise(function (resolve) {\n      resolve(inner[key](value));\n    });\n    return {\n      done: false,\n      value: new OverloadYield(value, /* kind: delegate */ 1),\n    };\n  }\n\n  iter[(typeof Symbol !== \"undefined\" && Symbol.iterator) || \"@@iterator\"] =\n    function () {\n      return this;\n    };\n\n  iter.next = function (value) {\n    if (waiting) {\n      waiting = false;\n      return value;\n    }\n    return pump(\"next\", value);\n  };\n\n  if (typeof inner.throw === \"function\") {\n    iter.throw = function (value) {\n      if (waiting) {\n        waiting = false;\n        throw value;\n      }\n      return pump(\"throw\", value);\n    };\n  }\n\n  if (typeof inner.return === \"function\") {\n    iter.return = function (value) {\n      if (waiting) {\n        waiting = false;\n        return value;\n      }\n      return pump(\"return\", value);\n    };\n  }\n\n  return iter;\n}\n"], "mappings": ";;;;;;;AAEA;;AAEe,SAASA,uBAAT,CAAiCC,KAAjC,EAAwC;EACrD,IAAIC,IAAI,GAAG,EAAX;EAAA,IAEEC,OAAO,GAAG,KAFZ;;EAIA,SAASC,IAAT,CAAcC,GAAd,EAAmBC,KAAnB,EAA0B;IACxBH,OAAO,GAAG,IAAV;IACAG,KAAK,GAAG,IAAIC,OAAJ,CAAY,UAAUC,OAAV,EAAmB;MACrCA,OAAO,CAACP,KAAK,CAACI,GAAD,CAAL,CAAWC,KAAX,CAAD,CAAP;IACD,CAFO,CAAR;IAGA,OAAO;MACLG,IAAI,EAAE,KADD;MAELH,KAAK,EAAE,IAAII,cAAJ,CAAkBJ,KAAlB,EAA8C,CAA9C;IAFF,CAAP;EAID;;EAEDJ,IAAI,CAAE,OAAOS,MAAP,KAAkB,WAAlB,IAAiCA,MAAM,CAACC,QAAzC,IAAsD,YAAvD,CAAJ,GACE,YAAY;IACV,OAAO,IAAP;EACD,CAHH;;EAKAV,IAAI,CAACW,IAAL,GAAY,UAAUP,KAAV,EAAiB;IAC3B,IAAIH,OAAJ,EAAa;MACXA,OAAO,GAAG,KAAV;MACA,OAAOG,KAAP;IACD;;IACD,OAAOF,IAAI,CAAC,MAAD,EAASE,KAAT,CAAX;EACD,CAND;;EAQA,IAAI,OAAOL,KAAK,CAACa,KAAb,KAAuB,UAA3B,EAAuC;IACrCZ,IAAI,CAACY,KAAL,GAAa,UAAUR,KAAV,EAAiB;MAC5B,IAAIH,OAAJ,EAAa;QACXA,OAAO,GAAG,KAAV;QACA,MAAMG,KAAN;MACD;;MACD,OAAOF,IAAI,CAAC,OAAD,EAAUE,KAAV,CAAX;IACD,CAND;EAOD;;EAED,IAAI,OAAOL,KAAK,CAACc,MAAb,KAAwB,UAA5B,EAAwC;IACtCb,IAAI,CAACa,MAAL,GAAc,UAAUT,KAAV,EAAiB;MAC7B,IAAIH,OAAJ,EAAa;QACXA,OAAO,GAAG,KAAV;QACA,OAAOG,KAAP;MACD;;MACD,OAAOF,IAAI,CAAC,QAAD,EAAWE,KAAX,CAAX;IACD,CAND;EAOD;;EAED,OAAOJ,IAAP;AACD"}