"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            var status = window.saveButtonStatus;\n            return status === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.clearSnapshots) {\n            try {\n                window.clearSnapshots();\n            } catch (error) {\n                console.error(\"清空快照失败:\", error);\n            }\n        }\n    }, []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        // 只有 save 状态才弹窗\n        if (shouldAutoSave()) {\n            // 显示确认对话框\n            var message = \"您有未保存的更改。确定要离开吗？\";\n            event.returnValue = message;\n            // 🔧 优化：使用延迟检测用户选择\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"，执行自动保存\n                performAutoSave();\n            }, 100);\n            // 🔧 新增：如果用户选择\"离开\"，页面会立即卸载，在 unload 事件中清空快照\n            var handleUnload = function() {\n                clearAllSnapshots();\n                window.removeEventListener(\"unload\", handleUnload);\n            };\n            window.addEventListener(\"unload\", handleUnload);\n            return message;\n        }\n    // view 状态：直接允许离开，不弹窗\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 路由变化处理 - 严格按照用户要求\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, confirmed;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            4\n                        ];\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                        if (!isNoteNavigation) return [\n                            3,\n                            1\n                        ];\n                        // 笔记跳转：自动保存，不弹窗，不阻止跳转\n                        isAutoSavingRef.current = true;\n                        // 在后台执行自动保存，不阻止路由跳转\n                        performAutoSave().finally(function() {\n                            isAutoSavingRef.current = false;\n                        });\n                        // 直接允许跳转，不阻止\n                        return [\n                            2\n                        ];\n                    case 1:\n                        // 非笔记跳转：阻止跳转，弹窗询问\n                        router.events.emit(\"routeChangeError\", new Error(\"User confirmation required\"), url);\n                        confirmed = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed) return [\n                            3,\n                            2\n                        ];\n                        // 用户选择离开：不保存，直接跳转\n                        router.push(url);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        // 用户选择取消：自动保存\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 3:\n                        _state.sent();\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        // view 状态：直接允许跳转，不做任何处理\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});