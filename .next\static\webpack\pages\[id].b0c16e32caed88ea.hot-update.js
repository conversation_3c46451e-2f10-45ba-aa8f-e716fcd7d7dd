"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_type_of.mjs */ \"./node_modules/@swc/helpers/src/_type_of.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var ref, ref1, ref2, ref3, snapshotJsonContent;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                noteId: note === null || note === void 0 ? void 0 : note.id,\n                noteContent: (note === null || note === void 0 ? void 0 : (ref = note.content) === null || ref === void 0 ? void 0 : ref.substring(0, 100)) + \"...\",\n                noteContentLength: (note === null || note === void 0 ? void 0 : (ref1 = note.content) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n            });\n            if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                // 新建笔记：快照为空值\n                console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n                return [\n                    2\n                ];\n            }\n            try {\n                ;\n                snapshotJsonContent = \"\";\n                console.log(\"\\uD83D\\uDD27 快照内容来源分析:\", {\n                    noteContent: ((ref2 = note.content) === null || ref2 === void 0 ? void 0 : ref2.substring(0, 100)) + \"...\",\n                    noteContentLength: ((ref3 = note.content) === null || ref3 === void 0 ? void 0 : ref3.length) || 0,\n                    noteContentType: (0,_swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(note.content)\n                });\n                // 直接使用 note 对象的内容作为快照\n                if (note.content !== undefined && note.content !== null) {\n                    snapshotJsonContent = note.content;\n                    console.log(\"\\uD83D\\uDD27 使用 note 对象内容作为快照\");\n                } else {\n                    snapshotJsonContent = \"\";\n                    console.log(\"\\uD83D\\uDD27 note 内容为空，设置空快照\");\n                }\n                console.log(\"\\uD83D\\uDD27 快照初始化完成:\", {\n                    noteId: note.id,\n                    hasContent: !!snapshotJsonContent,\n                    contentLength: snapshotJsonContent.length,\n                    isJson: snapshotJsonContent.startsWith(\"{\"),\n                    source: (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) ? \"cache\" : note.content ? \"note\" : \"empty\",\n                    contentPreview: snapshotJsonContent.substring(0, 100) + \"...\"\n                });\n                // 🔧 关键：设置快照和当前编辑器内容\n                setNoteSnapshot(snapshotJsonContent);\n                setCurrentEditorContent(snapshotJsonContent);\n            } catch (error) {\n                console.error(\"JSON快照初始化失败:\", error);\n                // 失败时设置为空快照\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n            }\n            return [\n                2\n            ];\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 当笔记ID变化或内容加载完成时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        // 🔧 修复：只有当 note 存在、有 ID 且内容已加载时才初始化\n        // 避免在内容未完全加载时就初始化快照\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            var ref;\n            console.log(\"\\uD83D\\uDD27 触发快照初始化条件:\", {\n                noteId: note.id,\n                hasContent: note.content !== undefined,\n                contentLength: ((ref = note.content) === null || ref === void 0 ? void 0 : ref.length) || 0\n            });\n            initializeSnapshot();\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        console.log(\"\\uD83D\\uDD27 清空所有快照\");\n        setNoteSnapshot(null);\n        setCurrentEditorContent(\"\");\n        // 🔧 修复：清空后重新初始化快照，确保编辑器显示正确内容\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            setTimeout(function() {\n                initializeSnapshot();\n            }, 0);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 关键修复：保存成功后清空所有快照\n                    clearAllSnapshots();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB,\n        clearAllSnapshots\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note,\n        // 🔧 新增：快照对比相关功能\n        getEditorState: getEditorState,\n        saveCurrentContent: saveCurrentContent,\n        compareWithSnapshot: compareWithSnapshot,\n        clearAllSnapshots: clearAllSnapshots,\n        // 🔧 关键修复：返回当前编辑器内容作为编辑器的 value\n        editorNote: (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, note), {\n            content: currentEditorContent\n        })\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_12__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvci50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7OztBQUE0QztBQUNKO0FBT3pCO0FBRTBDO0FBQ0w7QUFHUjtBQUNJO0FBR25CO0FBRzdCLElBQU1XLE9BQU8sR0FBRyxNQUFNO0FBRXRCLElBQU1DLGdCQUFnQixHQUFHLFNBQUNDLFFBQW9CLEVBQUs7SUFDL0MsZ0VBQWdFO0lBQ2hFLElBQUlDLElBQUksR0FBR0QsUUFBUTtJQUNuQixJQUFJRSxtQkFBbUIsRUFBT0MsVUFBVSxFQUFPQyxVQUFVO0lBRXpELElBQUk7UUFDQSxJQUFNQyxTQUFTLEdBQUdsQix3RUFBc0IsRUFBRTtRQUMxQ2UsbUJBQW1CLEdBQUdHLFNBQVMsQ0FBQ0gsbUJBQW1CLENBQUM7UUFDcERDLFVBQVUsR0FBR0UsU0FBUyxDQUFDRixVQUFVLENBQUM7UUFDbENDLFVBQVUsR0FBR0MsU0FBUyxDQUFDRCxVQUFVLENBQUM7UUFFbEMscURBQXFEO1FBQ3JELElBQUksQ0FBQ0gsSUFBSSxFQUFFO1lBQ1BBLElBQUksR0FBR0ksU0FBUyxDQUFDSixJQUFJLENBQUM7UUFDMUIsQ0FBQztJQUNMLEVBQUUsT0FBT00sS0FBSyxFQUFFO1FBQ1osbUVBQW1FO1FBQ25FQyxPQUFPLENBQUNDLElBQUksQ0FBQyxvRUFBb0UsQ0FBQyxDQUFDO1FBQ25GUCxtQkFBbUIsaUJBQUc7O2dCQUFZUTs7b0JBQUFBLFNBQVM7a0JBQUE7O1NBQUEsRUFBQztRQUM1Q1AsVUFBVSxpQkFBRzs7Z0JBQVlPOztvQkFBQUEsU0FBUztrQkFBQTs7U0FBQSxFQUFDO1FBQ25DTixVQUFVLGlCQUFHOztnQkFBWU07O29CQUFBQSxTQUFTO2tCQUFBOztTQUFBLEVBQUM7SUFDdkMsQ0FBQztJQUVELElBQU1DLE1BQU0sR0FBR3ZCLHNEQUFTLEVBQUU7SUFDMUIsSUFBTXdCLEtBQUssR0FBR2xCLGtFQUFRLEVBQUU7SUFDeEIsSUFBTW1CLFFBQVEsR0FBR3RCLDZDQUFNLENBQW1CLElBQUksQ0FBQztJQUUvQyxjQUFjO0lBQ2QsSUFBd0NELEdBQTZCLEdBQTdCQSwrQ0FBUSxDQUFnQixJQUFJLENBQUMsRUFBOUR3QixZQUFZLEdBQXFCeEIsR0FBNkIsR0FBbEQsRUFBRXlCLGVBQWUsR0FBSXpCLEdBQTZCLEdBQWpDO0lBQ3BDLElBQXdEQSxJQUFvQixHQUFwQkEsK0NBQVEsQ0FBUyxFQUFFLENBQUMsRUFBckUwQixvQkFBb0IsR0FBNkIxQixJQUFvQixHQUFqRCxFQUFFMkIsdUJBQXVCLEdBQUkzQixJQUFvQixHQUF4QjtJQUVwRCxxQ0FBcUM7SUFDckMsSUFBTTRCLGVBQWUsR0FBRzdCLGtEQUFXO21CQUMvQiw2RkFBTzhCLElBQXdCLEVBQUs7Z0JBSTFCQyxZQUFZLEVBQ1pDLFFBQVEsRUFFUkMsV0FBVzs7Ozt3QkFOakIsSUFBSSxDQUFDckIsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7OzBCQUFPO3dCQUdEOzs0QkFBTTVCLG1FQUFpQixDQUFDTSxJQUFJLENBQUNzQixFQUFFLENBQUM7MEJBQUE7O3dCQUEvQ0gsWUFBWSxHQUFHLGFBQWdDO3dCQUMvQ0MsUUFBUSxHQUFHRCxZQUFZLElBQUluQixJQUFJLENBQUM7d0JBRWhDcUIsV0FBVyxHQUFHLG1GQUFLRCxRQUFRLEVBQUtGLElBQUksQ0FBRSxDQUFDO3dCQUU3Qzs7NEJBQU14QixtRUFBaUIsQ0FBQ00sSUFBSSxDQUFDc0IsRUFBRSxFQUFFRCxXQUFXLENBQUM7MEJBQUE7O3dCQUE3QyxhQUE2QyxDQUFDOzs7Ozs7UUFDbEQsQ0FBQzt3QkFWTUgsSUFBd0I7OztTQVcvQjtRQUFDbEIsSUFBSTtLQUFDLENBQ1Q7SUFFRCxJQUFNeUIsWUFBWSxHQUFHckMsa0RBQVcsZUFDNUIsK0ZBQVk7WUFHRnNDLEtBQUssRUFHREMsU0FBUyxFQUNUQyxVQUFVLEVBR05DLFFBQVEsRUFLUkMsSUFBSSxFQUdBQyxPQUFPLEVBUVhWLFdBQVcsRUFRaEJmLEtBQUs7Ozs7b0JBakNkLElBQUksQ0FBQ04sQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7O3dCQUFPLEtBQUs7c0JBQUM7b0JBRXRCSSxLQUFLLEdBQUc5QiwyQ0FBRyxDQUFDYyxNQUFNLENBQUNzQixLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7OztvQkFHakI7O3dCQUFNdEMsbUVBQWlCLENBQUNNLElBQUksQ0FBQ3NCLEVBQUUsQ0FBQztzQkFBQTs7b0JBQTVDSyxTQUFTLEdBQUcsYUFBZ0M7b0JBQzVDQyxVQUFVLEdBQUdELFNBQVMsSUFBSTNCLElBQUksQ0FBQzt5QkFFakMwQixLQUFLLEVBQUxBOzs7c0JBQUs7b0JBQ0NHLFFBQVEsR0FBRyx5S0FDVkQsVUFBVTt3QkFDYkssR0FBRyxFQUFFLE1BQU8sQ0FBQ0QsS0FBSyxDQUFDQyxHQUFHLElBQWVwQyxPQUFPO3NCQUMvQyxDQUFDO29CQUVXOzt3QkFBTU0sVUFBVSxDQUFDMEIsUUFBUSxDQUFDO3NCQUFBOztvQkFBakNDLElBQUksR0FBRyxhQUEwQjt5QkFFbkNBLElBQUksRUFBSkE7OztzQkFBSTtvQkFDRUMsT0FBTyxHQUFHLEdBQUUsQ0FBVSxPQUFSRCxJQUFJLENBQUNSLEVBQUUsQ0FBRSxDQUFDO3lCQUMxQlosQ0FBQUEsTUFBTSxDQUFDd0IsTUFBTSxLQUFLSCxPQUFPLEdBQXpCckI7OztzQkFBeUI7b0JBQ3pCOzt3QkFBTUEsTUFBTSxDQUFDeUIsT0FBTyxDQUFDSixPQUFPLEVBQUV0QixTQUFTLEVBQUU7NEJBQUUyQixPQUFPLEVBQUUsSUFBSTt5QkFBRSxDQUFDO3NCQUFBOztvQkFBM0QsYUFBMkQsQ0FBQzs7O29CQUVoRXpCLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxTQUFTLENBQUMsQ0FBQztvQkFDekM7O3dCQUFPLElBQUk7c0JBQUM7Ozs7Ozs7b0JBR0k7O3dCQUFNVCxVQUFVLENBQUMwQixVQUFVLENBQUM7c0JBQUE7O29CQUExQ1AsV0FBVyxHQUFHLGFBQTRCO3lCQUU1Q0EsV0FBVyxFQUFYQTs7O3NCQUFXO29CQUNYOzt3QkFBTTNCLG1FQUFpQixDQUFDMkIsV0FBVyxDQUFDQyxFQUFFLEVBQUVELFdBQVcsQ0FBQztzQkFBQTs7b0JBQXBELGFBQW9ELENBQUM7b0JBQ3JEVixLQUFLLENBQUMsd0JBQXdCLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQzNDOzt3QkFBTyxJQUFJO3NCQUFDOzs7Ozs7O29CQUdmTCxLQUFLO29CQUNWSyxLQUFLLENBQUMsK0JBQStCLEVBQUUsT0FBTyxDQUFDLENBQUM7b0JBQ2hEOzt3QkFBTyxLQUFLO3NCQUFDOztvQkFHakI7O3dCQUFPLEtBQUs7c0JBQUM7OztJQUNqQixDQUFDLEdBQ0Q7UUFBQ1gsSUFBSTtRQUFFVSxNQUFNO1FBQUVQLFVBQVU7UUFBRUQsVUFBVTtRQUFFUyxLQUFLO0tBQUMsQ0FDaEQ7SUFFRCxJQUFNMEIsWUFBWSxHQUFHakQsa0RBQVc7bUJBQzVCLDZGQUFPa0QsS0FBYSxFQUFLO2dCQUdmQyxNQUFNOzs7O3dCQUZaLElBQUksQ0FBQ3RDLG1CQUFtQixFQUFFOzs0QkFBTyxFQUFFOzBCQUFDO3dCQUVyQjs7NEJBQU1BLG1CQUFtQixDQUFDcUMsS0FBSyxDQUFDOzBCQUFBOzt3QkFBekNDLE1BQU0sR0FBRyxhQUFnQzt3QkFDL0MsSUFBSUEsTUFBTSxhQUFOQSxNQUFNLFdBQUksR0FBVkEsS0FBQUEsQ0FBVSxHQUFWQSxNQUFNLENBQUVqQixFQUFFLEVBQUU7NEJBQ1o7O2dDQUFRLEdBQUMsQ0FBWSxPQUFWaUIsTUFBTSxDQUFDakIsRUFBRSxDQUFFOzhCQUFDO3dCQUMzQixDQUFDO3dCQUNEOzs0QkFBTyxFQUFFOzBCQUFDOzs7UUFDZCxDQUFDO3dCQVJNZ0IsS0FBYTs7O1NBU3BCO1FBQUNyQyxtQkFBbUI7S0FBQyxDQUN4QjtJQUVELElBQU11QyxZQUFZLEdBQUdwRCxrREFBVzttQkFDNUIsNkZBQU9xRCxJQUFZLEVBQUs7O2dCQUNwQjs7O2tCQUFVOztRQUNkLENBQUM7d0JBRk1BLElBQVk7OztTQUduQixFQUFFLENBQ0w7SUFFRCxJQUFNQyxXQUFXLEdBQUd0RCxrREFBVyxDQUMzQixTQUFDdUQsSUFBWSxFQUFFQyxLQUFzQixFQUFLO1FBQ3RDLElBQUlwRCw0REFBVSxDQUFDbUQsSUFBSSxDQUFDLEVBQUU7WUFDbEJDLEtBQUssQ0FBQ0MsY0FBYyxFQUFFLENBQUM7WUFDdkJuQyxNQUFNLENBQUNvQyxJQUFJLENBQUNILElBQUksQ0FBQyxDQUFDO1FBQ3RCLE9BQU87WUFDSEksTUFBTSxDQUFDQyxJQUFJLENBQUNMLElBQUksRUFBRSxRQUFRLEVBQUUscUJBQXFCLENBQUMsQ0FBQztRQUN2RCxDQUFDO0lBQ0wsQ0FBQyxFQUNEO1FBQUNqQyxNQUFNO0tBQUMsQ0FDWDtJQUVELElBQU11QyxhQUFhLEdBQUc3RCxrREFBVzttQkFDN0IsNkZBQU84RCxLQUFXLEVBQUVDLEdBQVksRUFBSzs7Z0JBQ2pDLGlEQUFpRDtnQkFDakR4QyxLQUFLLENBQUMsK0NBQStDLEVBQUUsT0FBTyxDQUFDLENBQUM7Z0JBQ2hFLE1BQU0sSUFBSXlDLEtBQUssQ0FBQywrQkFBK0IsQ0FBQyxDQUFDOztRQUNyRCxDQUFDO3dCQUpNRixLQUFXLEVBQUVDLEdBQVk7OztTQUtoQztRQUFDeEMsS0FBSztLQUFDLENBQ1Y7SUFFRCxJQUFNMEMsV0FBVyxHQUFHakUsa0RBQVcsQ0FBQyxTQUFDd0QsS0FBc0IsRUFBSztRQUN4RCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDLEVBQUUsRUFBRSxDQUFDO0lBRU4sSUFBa0N2RCxJQUEyQixHQUEzQkEsK0NBQVEsRUFBbUIsRUFBdERpRSxTQUFTLEdBQWtCakUsSUFBMkIsR0FBN0MsRUFBRWtFLFlBQVksR0FBSWxFLElBQTJCLEdBQS9CO0lBRTlCLElBQU1tRSxZQUFZLEdBQUdwRSxrREFBVyxlQUFDLCtGQUFZO1lBRW5DcUUsU0FBUzs7OztvQkFEZmxELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQzFELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxDQUFDLENBQUM7b0JBQ2hCbUMsU0FBUyxLQUFzQixDQUFDO29CQUN0QyxJQUFJLENBQUN6RCxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsR0FBRTs7d0JBQU9tQyxTQUFTO3NCQUFDO29CQUNoQ0YsWUFBWSxJQUFJLENBQUM7b0JBQ2pCOzt3QkFBTTdELG1FQUFpQixDQUFzQixTQUFDa0UsS0FBSyxFQUFLO2dDQUNoREEsR0FBYTs0QkFBakIsSUFBSUEsQ0FBQUEsR0FBYSxHQUFiQSxLQUFLLENBQUNDLE9BQU8sY0FBYkQsR0FBYSxXQUFVLEdBQXZCQSxLQUFBQSxDQUF1QixHQUF2QkEsR0FBYSxDQUFFRSxRQUFRLENBQUM5RCxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsS0FBSSxFQUFFLENBQUMsRUFBRTtnQ0FDekNtQyxTQUFTLENBQUNYLElBQUksQ0FBQ2MsS0FBSyxDQUFDLENBQUM7NEJBQzFCLENBQUM7d0JBQ0wsQ0FBQyxDQUFDO3NCQUFBOztvQkFKRixhQUlFLENBQUM7b0JBQ0hMLFlBQVksQ0FBQ0UsU0FBUyxDQUFDLENBQUM7Ozs7OztJQUM1QixDQUFDLEdBQUU7UUFBQ3pELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtLQUFDLENBQUM7SUFFZCw0QkFBNEI7SUFDNUIsSUFBTXlDLGtCQUFrQixHQUFHM0Usa0RBQVcsZUFBQywrRkFBWTtZQUc5QlksR0FBYSxFQUNQQSxJQUFhLEVBaUJmQSxJQUFZLEVBQ05BLElBQVksRUFKL0JnRSxtQkFBbUI7O1lBakIzQnpELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx1QkFBWSxFQUFFO2dCQUN0Qk8sTUFBTSxFQUFFakUsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFO2dCQUNoQjRDLFdBQVcsRUFBRWxFLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsQ0FBQUEsR0FBYSxHQUFiQSxJQUFJLENBQUVtRSxPQUFPLGNBQWJuRSxHQUFhLGNBQWJBLEtBQUFBLENBQWEsR0FBYkEsR0FBYSxDQUFFb0UsU0FBUyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBRyxLQUFLO2dCQUNyREMsaUJBQWlCLEVBQUVyRSxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBUyxHQUFiQSxLQUFBQSxDQUFhLEdBQWJBLENBQUFBLElBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTyxjQUFibkUsSUFBYSxjQUFiQSxLQUFBQSxDQUFhLEdBQWJBLElBQWEsQ0FBRXNFLE1BQU0sQ0FBUixJQUFZLENBQUM7YUFDaEQsQ0FBQyxDQUFDO1lBRUgsSUFBSSxDQUFDdEUsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7Z0JBQ1gsYUFBYTtnQkFDYmYsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLHlCQUFjLENBQUMsQ0FBQztnQkFDNUI1QyxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBQ3RCRSx1QkFBdUIsQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDNUI7O2tCQUFPO1lBQ1gsQ0FBQztZQUVELElBQUk7O2dCQUdJZ0QsbUJBQW1CLEdBQUcsRUFBRSxDQUFDO2dCQUU3QnpELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx3QkFBYSxFQUFFO29CQUN2QlEsV0FBVyxFQUFFbEUsQ0FBQUEsQ0FBQUEsSUFBWSxHQUFaQSxJQUFJLENBQUNtRSxPQUFPLGNBQVpuRSxJQUFZLFdBQVcsR0FBdkJBLEtBQUFBLENBQXVCLEdBQXZCQSxJQUFZLENBQUVvRSxTQUFTLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFHLEtBQUs7b0JBQ3BEQyxpQkFBaUIsRUFBRXJFLENBQUFBLENBQUFBLElBQVksR0FBWkEsSUFBSSxDQUFDbUUsT0FBTyxjQUFabkUsSUFBWSxXQUFRLEdBQXBCQSxLQUFBQSxDQUFvQixHQUFwQkEsSUFBWSxDQUFFc0UsTUFBTSxLQUFJLENBQUM7b0JBQzVDQyxlQUFlLEVBQUUseUVBQW1CLENBQVp2RSxJQUFJLENBQUNtRSxPQUFPO2lCQUN2QyxDQUFDLENBQUM7Z0JBRUgsc0JBQXNCO2dCQUN0QixJQUFJbkUsSUFBSSxDQUFDbUUsT0FBTyxLQUFLMUQsU0FBUyxJQUFJVCxJQUFJLENBQUNtRSxPQUFPLEtBQUssSUFBSSxFQUFFO29CQUNyREgsbUJBQW1CLEdBQUdoRSxJQUFJLENBQUNtRSxPQUFPLENBQUM7b0JBQ25DNUQsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLCtCQUFvQixDQUFDLENBQUM7Z0JBQ3RDLE9BQU87b0JBQ0hNLG1CQUFtQixHQUFHLEVBQUUsQ0FBQztvQkFDekJ6RCxPQUFPLENBQUNtRCxHQUFHLENBQUMsOEJBQW1CLENBQUMsQ0FBQztnQkFDckMsQ0FBQztnQkFFRG5ELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx1QkFBWSxFQUFFO29CQUN0Qk8sTUFBTSxFQUFFakUsSUFBSSxDQUFDc0IsRUFBRTtvQkFDZmtELFVBQVUsRUFBRSxDQUFDLENBQUNSLG1CQUFtQjtvQkFDakNTLGFBQWEsRUFBRVQsbUJBQW1CLENBQUNNLE1BQU07b0JBQ3pDSSxNQUFNLEVBQUVWLG1CQUFtQixDQUFDVyxVQUFVLENBQUMsR0FBRyxDQUFDO29CQUMzQ0MsTUFBTSxFQUFFQyxDQUFBQSxVQUFVLGFBQVZBLFVBQVUsV0FBUyxHQUFuQkEsS0FBQUEsQ0FBbUIsR0FBbkJBLFVBQVUsQ0FBRVYsT0FBTyxJQUFHLE9BQU8sR0FBSW5FLElBQUksQ0FBQ21FLE9BQU8sR0FBRyxNQUFNLEdBQUcsT0FBTztvQkFDeEVXLGNBQWMsRUFBRWQsbUJBQW1CLENBQUNJLFNBQVMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEdBQUcsS0FBSztpQkFDaEUsQ0FBQyxDQUFDO2dCQUVILG9CQUFvQjtnQkFDcEJ0RCxlQUFlLENBQUNrRCxtQkFBbUIsQ0FBQyxDQUFDO2dCQUNyQ2hELHVCQUF1QixDQUFDZ0QsbUJBQW1CLENBQUMsQ0FBQztZQUNqRCxFQUFFLE9BQU8xRCxLQUFLLEVBQUU7Z0JBQ1pDLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLGNBQWMsRUFBRUEsS0FBSyxDQUFDLENBQUM7Z0JBQ3JDLFlBQVk7Z0JBQ1pRLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQztnQkFDdEJFLHVCQUF1QixDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ2hDLENBQUM7Ozs7O0lBQ0wsQ0FBQyxHQUFFO1FBQUNoQixJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUU7UUFBRXRCLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTztLQUFDLENBQUM7SUFFN0IsdUJBQXVCO0lBQ3ZCNUUsZ0RBQVMsQ0FBQyxXQUFNO1FBQ1osb0NBQW9DO1FBQ3BDLG9CQUFvQjtRQUNwQixJQUFJUyxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsS0FBSXRCLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTyxNQUFLMUQsU0FBUyxFQUFFO2dCQUl0QlQsR0FBWTtZQUgvQk8sT0FBTyxDQUFDbUQsR0FBRyxDQUFDLHlCQUFjLEVBQUU7Z0JBQ3hCTyxNQUFNLEVBQUVqRSxJQUFJLENBQUNzQixFQUFFO2dCQUNma0QsVUFBVSxFQUFFeEUsSUFBSSxDQUFDbUUsT0FBTyxLQUFLMUQsU0FBUztnQkFDdENnRSxhQUFhLEVBQUV6RSxDQUFBQSxDQUFBQSxHQUFZLEdBQVpBLElBQUksQ0FBQ21FLE9BQU8sY0FBWm5FLEdBQVksV0FBUSxHQUFwQkEsS0FBQUEsQ0FBb0IsR0FBcEJBLEdBQVksQ0FBRXNFLE1BQU0sS0FBSSxDQUFDO2FBQzNDLENBQUMsQ0FBQztZQUNIUCxrQkFBa0IsRUFBRSxDQUFDO1FBQ3pCLENBQUM7SUFDTCxDQUFDLEVBQUU7UUFBQy9ELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtRQUFFdEIsSUFBSSxhQUFKQSxJQUFJLFdBQVMsR0FBYkEsS0FBQUEsQ0FBYSxHQUFiQSxJQUFJLENBQUVtRSxPQUFPO1FBQUVKLGtCQUFrQjtLQUFDLENBQUMsQ0FBQztJQUVsRCxzQ0FBc0M7SUFDdEMsSUFBTWdCLGNBQWMsR0FBRzNGLGtEQUFXLENBQzlCLFNBQUM0RixRQUFzQixFQUFXO1FBQzlCLElBQU1DLFdBQVcsR0FBR0QsUUFBUSxFQUFFO1FBRTlCLG9DQUFvQztRQUNwQ2hFLHVCQUF1QixDQUFDaUUsV0FBVyxDQUFDLENBQUM7SUFDekMsQ0FBQyxFQUNELEVBQUUsQ0FDTDtJQUVELGdEQUFnRDtJQUNoRCxJQUFNQyxhQUFhLEdBQUc5RixrREFBVyxDQUM3QixTQUFDa0QsS0FBYSxFQUFXO1lBQ3JCckIsR0FHRTtRQUhGQSxDQUFBQSxHQUdFLEdBSEZBLGVBQWUsQ0FBQztZQUNacUIsS0FBSyxFQUFMQSxLQUFLO1lBQ0w2QyxVQUFVLEVBQUUsSUFBSUMsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRTtTQUN2QyxDQUFDLGNBSEZwRSxHQUdFLFdBQU8sR0FIVEEsS0FBQUEsQ0FHUyxHQUhUQSxHQUdFLENBQUVxRSxLQUFLLENBQUMsU0FBQ0MsQ0FBQzttQkFBS2hGLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLDRDQUE0QyxFQUFFaUYsQ0FBQyxDQUFDO1NBQUEsQ0FBQyxDQUFDO0lBQ3JGLENBQUMsRUFDRDtRQUFDdEUsZUFBZTtLQUFDLENBQ3BCO0lBRUQsa0NBQWtDO0lBQ2xDLElBQU11RSxtQkFBbUIsR0FBR3BHLGtEQUFXLENBQUMsV0FBZTtRQUNuRCxpQ0FBaUM7UUFDakMsSUFBSXlCLFlBQVksS0FBSyxJQUFJLEVBQUU7WUFDdkIsT0FBT0Usb0JBQW9CLENBQUMwRSxJQUFJLEVBQUUsS0FBSyxFQUFFLENBQUM7UUFDOUMsQ0FBQztRQUVELDBCQUEwQjtRQUMxQixJQUFNQyxVQUFVLEdBQUczRSxvQkFBb0IsS0FBS0YsWUFBWTtRQUN4RCxPQUFPNkUsVUFBVSxDQUFDO0lBQ3RCLENBQUMsRUFBRTtRQUFDN0UsWUFBWTtRQUFFRSxvQkFBb0I7S0FBQyxDQUFDO0lBRXhDLGlDQUFpQztJQUNqQyxJQUFNNEUsY0FBYyxHQUFHdkcsa0RBQVcsQ0FBQyxXQUFNO1FBQ3JDLE9BQU87WUFDSHNHLFVBQVUsRUFBRUYsbUJBQW1CLEVBQUU7WUFDakNJLGNBQWMsRUFBRTdFLG9CQUFvQjtZQUNwQzhFLFFBQVEsRUFBRWhGLFlBQVk7WUFDdEJpRixTQUFTLEVBQUVqRixZQUFZLEtBQUssSUFBSTtTQUNuQyxDQUFDO0lBQ04sQ0FBQyxFQUFFO1FBQUMyRSxtQkFBbUI7UUFBRXpFLG9CQUFvQjtRQUFFRixZQUFZO0tBQUMsQ0FBQztJQUU3RCxpQkFBaUI7SUFDakIsSUFBTWtGLGlCQUFpQixHQUFHM0csa0RBQVcsQ0FBQyxXQUFNO1FBQ3hDbUIsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLHFCQUFVLENBQUMsQ0FBQztRQUN4QjVDLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUN0QkUsdUJBQXVCLENBQUMsRUFBRSxDQUFDLENBQUM7UUFFNUIsOEJBQThCO1FBQzlCLElBQUloQixDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsS0FBSXRCLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTyxNQUFLMUQsU0FBUyxFQUFFO1lBQ3pDdUYsVUFBVSxDQUFDLFdBQU07Z0JBQ2JqQyxrQkFBa0IsRUFBRSxDQUFDO1lBQ3pCLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNWLENBQUM7SUFDTCxDQUFDLEVBQUU7UUFBQy9ELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtRQUFFdEIsSUFBSSxhQUFKQSxJQUFJLFdBQVMsR0FBYkEsS0FBQUEsQ0FBYSxHQUFiQSxJQUFJLENBQUVtRSxPQUFPO1FBQUVKLGtCQUFrQjtLQUFDLENBQUM7SUFFakQsNENBQTRDO0lBQzVDLElBQU1rQyxrQkFBa0IsR0FBRzdHLGtEQUFXLGVBQUMsK0ZBQThCO1lBS3pEa0QsS0FBSyxFQUlDNEQsVUFBVSxFQW9CZjVGLEtBQUs7Ozs7b0JBNUJkLElBQUksQ0FBQ04sQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7O3dCQUFPLEtBQUs7c0JBQUM7Ozs7Ozs7OztvQkFLeEIsSUFBSXRCLElBQUksYUFBSkEsSUFBSSxXQUFhLEdBQWpCQSxLQUFBQSxDQUFpQixHQUFqQkEsSUFBSSxDQUFFbUcsV0FBVyxFQUFFO3dCQUNuQjdELEtBQUssR0FBR3RDLElBQUksQ0FBQ3NDLEtBQUssQ0FBQztvQkFDdkIsT0FBTzt3QkFDRzRELFVBQVUsR0FBR0UsUUFBUSxDQUFDQyxhQUFhLENBQUMsYUFBYSxDQUFDLENBQXdCO3dCQUNoRixJQUFJSCxVQUFVLElBQUlBLFVBQVUsQ0FBQ3RDLEtBQUssRUFBRTs0QkFDaEN0QixLQUFLLEdBQUc0RCxVQUFVLENBQUN0QyxLQUFLLENBQUM2QixJQUFJLEVBQUUsQ0FBQzt3QkFDcEMsT0FBTzs0QkFDSCx1QkFBdUI7NEJBQ3ZCbkQsS0FBSyxHQUFHdEMsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQU8sR0FBWEEsS0FBQUEsQ0FBVyxHQUFYQSxJQUFJLENBQUVzQyxLQUFLLEtBQUksVUFBVSxDQUFDO3dCQUN0QyxDQUFDO29CQUNMLENBQUM7b0JBRUQscUJBQXFCO29CQUNyQjs7d0JBQU1yQixlQUFlLENBQUM7NEJBQ2xCa0QsT0FBTyxFQUFFcEQsb0JBQW9COzRCQUM3QnVCLEtBQUssRUFBTEEsS0FBSzs0QkFDTDZDLFVBQVUsRUFBRSxJQUFJQyxJQUFJLEVBQUUsQ0FBQ0MsV0FBVyxFQUFFO3lCQUN2QyxDQUFDO3NCQUFBOztvQkFKRixhQUlFLENBQUM7b0JBRUgscUJBQXFCO29CQUNyQlUsaUJBQWlCLEVBQUUsQ0FBQztvQkFFcEI7O3dCQUFPLElBQUk7c0JBQUM7O29CQUNQekYsS0FBSztvQkFDVkMsT0FBTyxDQUFDRCxLQUFLLENBQUMsa0NBQXVCLEVBQUVBLEtBQUssQ0FBQyxDQUFDO29CQUM5Qzs7d0JBQU8sS0FBSztzQkFBQzs7Ozs7OztJQUVyQixDQUFDLEdBQUU7UUFBQ04sSUFBSTtRQUFFZSxvQkFBb0I7UUFBRUUsZUFBZTtRQUFFOEUsaUJBQWlCO0tBQUMsQ0FBQztJQUVwRSxPQUFPO1FBQ0gxRCxZQUFZLEVBQVpBLFlBQVk7UUFDWkcsWUFBWSxFQUFaQSxZQUFZO1FBQ1pFLFdBQVcsRUFBWEEsV0FBVztRQUNYTyxhQUFhLEVBQWJBLGFBQWE7UUFDYkksV0FBVyxFQUFYQSxXQUFXO1FBQ1hHLFlBQVksRUFBWkEsWUFBWTtRQUNadUIsY0FBYyxFQUFkQSxjQUFjO1FBQ2RHLGFBQWEsRUFBYkEsYUFBYTtRQUNiakUsZUFBZSxFQUFmQSxlQUFlO1FBQ2ZRLFlBQVksRUFBWkEsWUFBWTtRQUNaNkIsU0FBUyxFQUFUQSxTQUFTO1FBQ1QxQyxRQUFRLEVBQVJBLFFBQVE7UUFDUlosSUFBSSxFQUFKQSxJQUFJO1FBQ0osZ0JBQWdCO1FBQ2hCMkYsY0FBYyxFQUFkQSxjQUFjO1FBQ2RNLGtCQUFrQixFQUFsQkEsa0JBQWtCO1FBQ2xCVCxtQkFBbUIsRUFBbkJBLG1CQUFtQjtRQUNuQk8saUJBQWlCLEVBQWpCQSxpQkFBaUI7UUFDakIsK0JBQStCO1FBQy9CTyxVQUFVLEVBQUUseUtBQ0x0RyxJQUFJO1lBQ1BtRSxPQUFPLEVBQUVwRCxvQkFBb0I7VUFDaEM7S0FDSixDQUFDO0FBQ04sQ0FBQztBQUVELElBQU13RixrQkFBa0IsR0FBRzVHLCtEQUFlLENBQUNHLGdCQUFnQixDQUFDO0FBRTVELCtEQUFleUcsa0JBQWtCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGlicy93ZWIvc3RhdGUvbGV4aWNhbC1lZGl0b3IudHM/NDNmOCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTm90ZVN0YXRlIGZyb20gJ2xpYnMvd2ViL3N0YXRlL25vdGUnO1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9yb3V0ZXInO1xuaW1wb3J0IHtcbiAgICB1c2VDYWxsYmFjayxcbiAgICBNb3VzZUV2ZW50IGFzIFJlYWN0TW91c2VFdmVudCxcbiAgICB1c2VTdGF0ZSxcbiAgICB1c2VSZWYsXG4gICAgdXNlRWZmZWN0LFxufSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBzZWFyY2hOb3RlLCBzZWFyY2hSYW5nZVRleHQgfSBmcm9tICdsaWJzL3dlYi91dGlscy9zZWFyY2gnO1xuaW1wb3J0IHsgaXNOb3RlTGluaywgTm90ZU1vZGVsIH0gZnJvbSAnbGlicy9zaGFyZWQvbm90ZSc7XG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ2xpYnMvd2ViL2hvb2tzL3VzZS10b2FzdCc7XG5pbXBvcnQgUG9ydGFsU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvcG9ydGFsJztcbmltcG9ydCB7IE5vdGVDYWNoZUl0ZW0gfSBmcm9tICdsaWJzL3dlYi9jYWNoZSc7XG5pbXBvcnQgbm90ZUNhY2hlIGZyb20gJ2xpYnMvd2ViL2NhY2hlL25vdGUnO1xuaW1wb3J0IHsgY3JlYXRlQ29udGFpbmVyIH0gZnJvbSAndW5zdGF0ZWQtbmV4dCc7XG5pbXBvcnQgeyBMZXhpY2FsRWRpdG9yUmVmIH0gZnJvbSAnY29tcG9uZW50cy9lZGl0b3IvbGV4aWNhbC1lZGl0b3InO1xuaW1wb3J0IFVJU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvdWknO1xuaW1wb3J0IHsgaGFzIH0gZnJvbSAnbG9kYXNoJztcblxuXG5jb25zdCBST09UX0lEID0gJ3Jvb3QnO1xuXG5jb25zdCB1c2VMZXhpY2FsRWRpdG9yID0gKGluaXROb3RlPzogTm90ZU1vZGVsKSA9PiB7XG4gICAgLy8gVXNlIGluaXROb3RlIGlmIHByb3ZpZGVkLCBvdGhlcndpc2UgdHJ5IHRvIGdldCBmcm9tIE5vdGVTdGF0ZVxuICAgIGxldCBub3RlID0gaW5pdE5vdGU7XG4gICAgbGV0IGNyZWF0ZU5vdGVXaXRoVGl0bGU6IGFueSwgdXBkYXRlTm90ZTogYW55LCBjcmVhdGVOb3RlOiBhbnk7XG5cbiAgICB0cnkge1xuICAgICAgICBjb25zdCBub3RlU3RhdGUgPSBOb3RlU3RhdGUudXNlQ29udGFpbmVyKCk7XG4gICAgICAgIGNyZWF0ZU5vdGVXaXRoVGl0bGUgPSBub3RlU3RhdGUuY3JlYXRlTm90ZVdpdGhUaXRsZTtcbiAgICAgICAgdXBkYXRlTm90ZSA9IG5vdGVTdGF0ZS51cGRhdGVOb3RlO1xuICAgICAgICBjcmVhdGVOb3RlID0gbm90ZVN0YXRlLmNyZWF0ZU5vdGU7XG5cbiAgICAgICAgLy8gT25seSB1c2Ugbm90ZVN0YXRlLm5vdGUgaWYgbm8gaW5pdE5vdGUgaXMgcHJvdmlkZWRcbiAgICAgICAgaWYgKCFub3RlKSB7XG4gICAgICAgICAgICBub3RlID0gbm90ZVN0YXRlLm5vdGU7XG4gICAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBJZiBOb3RlU3RhdGUgaXMgbm90IGF2YWlsYWJsZSwgd2UnbGwgd29yayB3aXRoIGp1c3QgdGhlIGluaXROb3RlXG4gICAgICAgIGNvbnNvbGUud2FybignTm90ZVN0YXRlIG5vdCBhdmFpbGFibGUgaW4gTGV4aWNhbEVkaXRvclN0YXRlLCB1c2luZyBpbml0Tm90ZSBvbmx5Jyk7XG4gICAgICAgIGNyZWF0ZU5vdGVXaXRoVGl0bGUgPSBhc3luYyAoKSA9PiB1bmRlZmluZWQ7XG4gICAgICAgIHVwZGF0ZU5vdGUgPSBhc3luYyAoKSA9PiB1bmRlZmluZWQ7XG4gICAgICAgIGNyZWF0ZU5vdGUgPSBhc3luYyAoKSA9PiB1bmRlZmluZWQ7XG4gICAgfVxuXG4gICAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gICAgY29uc3QgdG9hc3QgPSB1c2VUb2FzdCgpO1xuICAgIGNvbnN0IGVkaXRvckVsID0gdXNlUmVmPExleGljYWxFZGl0b3JSZWY+KG51bGwpO1xuXG4gICAgLy8g8J+UpyDmlrDlop7vvJrlv6vnhafnirbmgIHnrqHnkIZcbiAgICBjb25zdCBbbm90ZVNuYXBzaG90LCBzZXROb3RlU25hcHNob3RdID0gdXNlU3RhdGU8c3RyaW5nIHwgbnVsbD4obnVsbCk7XG4gICAgY29uc3QgW2N1cnJlbnRFZGl0b3JDb250ZW50LCBzZXRDdXJyZW50RWRpdG9yQ29udGVudF0gPSB1c2VTdGF0ZTxzdHJpbmc+KCcnKTtcblxuICAgIC8vIE1hbnVhbCBzYXZlIGZ1bmN0aW9uIGZvciBJbmRleGVkREJcbiAgICBjb25zdCBzYXZlVG9JbmRleGVkREIgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKGRhdGE6IFBhcnRpYWw8Tm90ZU1vZGVsPikgPT4ge1xuICAgICAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuO1xuXG4gICAgICAgICAgICAvLyDku44gSW5kZXhlZERCIOiOt+WPluacgOaWsOaVsOaNruS9nOS4uuWfuuehgO+8jOmBv+WFjeimhuebluW3suS/neWtmOeahOaVsOaNrlxuICAgICAgICAgICAgY29uc3QgZXhpc3RpbmdOb3RlID0gYXdhaXQgbm90ZUNhY2hlLmdldEl0ZW0obm90ZS5pZCk7XG4gICAgICAgICAgICBjb25zdCBiYXNlTm90ZSA9IGV4aXN0aW5nTm90ZSB8fCBub3RlO1xuXG4gICAgICAgICAgICBjb25zdCB1cGRhdGVkTm90ZSA9IHsgLi4uYmFzZU5vdGUsIC4uLmRhdGEgfTtcblxuICAgICAgICAgICAgYXdhaXQgbm90ZUNhY2hlLnNldEl0ZW0obm90ZS5pZCwgdXBkYXRlZE5vdGUpO1xuICAgICAgICB9LFxuICAgICAgICBbbm90ZV1cbiAgICApO1xuXG4gICAgY29uc3Qgc3luY1RvU2VydmVyID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jICgpID0+IHtcbiAgICAgICAgICAgIGlmICghbm90ZT8uaWQpIHJldHVybiBmYWxzZTtcblxuICAgICAgICAgICAgY29uc3QgaXNOZXcgPSBoYXMocm91dGVyLnF1ZXJ5LCAnbmV3Jyk7XG5cbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgY29uc3QgbG9jYWxOb3RlID0gYXdhaXQgbm90ZUNhY2hlLmdldEl0ZW0obm90ZS5pZCk7XG4gICAgICAgICAgICAgICAgY29uc3Qgbm90ZVRvU2F2ZSA9IGxvY2FsTm90ZSB8fCBub3RlO1xuXG4gICAgICAgICAgICAgICAgaWYgKGlzTmV3KSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vdGVEYXRhID0ge1xuICAgICAgICAgICAgICAgICAgICAgICAgLi4ubm90ZVRvU2F2ZSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHBpZDogKHJvdXRlci5xdWVyeS5waWQgYXMgc3RyaW5nKSB8fCBST09UX0lEXG4gICAgICAgICAgICAgICAgICAgIH07XG5cbiAgICAgICAgICAgICAgICAgICAgY29uc3QgaXRlbSA9IGF3YWl0IGNyZWF0ZU5vdGUobm90ZURhdGEpO1xuXG4gICAgICAgICAgICAgICAgICAgIGlmIChpdGVtKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCBub3RlVXJsID0gYC8ke2l0ZW0uaWR9YDtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmIChyb3V0ZXIuYXNQYXRoICE9PSBub3RlVXJsKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgYXdhaXQgcm91dGVyLnJlcGxhY2Uobm90ZVVybCwgdW5kZWZpbmVkLCB7IHNoYWxsb3c6IHRydWUgfSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCgnTm90ZSBzYXZlZCB0byBzZXJ2ZXInLCAnc3VjY2VzcycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCB1cGRhdGVkTm90ZSA9IGF3YWl0IHVwZGF0ZU5vdGUobm90ZVRvU2F2ZSk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKHVwZGF0ZWROb3RlKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCBub3RlQ2FjaGUuc2V0SXRlbSh1cGRhdGVkTm90ZS5pZCwgdXBkYXRlZE5vdGUpO1xuICAgICAgICAgICAgICAgICAgICAgICAgdG9hc3QoJ05vdGUgdXBkYXRlZCBvbiBzZXJ2ZXInLCAnc3VjY2VzcycpO1xuICAgICAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgICAgIHRvYXN0KCdGYWlsZWQgdG8gc2F2ZSBub3RlIHRvIHNlcnZlcicsICdlcnJvcicpO1xuICAgICAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICB9LFxuICAgICAgICBbbm90ZSwgcm91dGVyLCBjcmVhdGVOb3RlLCB1cGRhdGVOb3RlLCB0b2FzdF1cbiAgICApO1xuXG4gICAgY29uc3Qgb25DcmVhdGVMaW5rID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jICh0aXRsZTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICBpZiAoIWNyZWF0ZU5vdGVXaXRoVGl0bGUpIHJldHVybiAnJztcblxuICAgICAgICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY3JlYXRlTm90ZVdpdGhUaXRsZSh0aXRsZSk7XG4gICAgICAgICAgICBpZiAocmVzdWx0Py5pZCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBgLyR7cmVzdWx0LmlkfWA7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gJyc7XG4gICAgICAgIH0sXG4gICAgICAgIFtjcmVhdGVOb3RlV2l0aFRpdGxlXVxuICAgICk7XG5cbiAgICBjb25zdCBvblNlYXJjaExpbmsgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKHRlcm06IHN0cmluZykgPT4ge1xuICAgICAgICAgICAgcmV0dXJuIFtdO1xuICAgICAgICB9LFxuICAgICAgICBbXVxuICAgICk7XG5cbiAgICBjb25zdCBvbkNsaWNrTGluayA9IHVzZUNhbGxiYWNrKFxuICAgICAgICAoaHJlZjogc3RyaW5nLCBldmVudDogUmVhY3RNb3VzZUV2ZW50KSA9PiB7XG4gICAgICAgICAgICBpZiAoaXNOb3RlTGluayhocmVmKSkge1xuICAgICAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgICAgICAgcm91dGVyLnB1c2goaHJlZik7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHdpbmRvdy5vcGVuKGhyZWYsICdfYmxhbmsnLCAnbm9vcGVuZXIsbm9yZWZlcnJlcicpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9LFxuICAgICAgICBbcm91dGVyXVxuICAgICk7XG5cbiAgICBjb25zdCBvblVwbG9hZEltYWdlID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIGFzeW5jIChfZmlsZTogRmlsZSwgX2lkPzogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICAvLyBJbWFnZSB1cGxvYWQgaXMgZGlzYWJsZWQgaW4gUG9zdGdyZVNRTCB2ZXJzaW9uXG4gICAgICAgICAgICB0b2FzdCgnSW1hZ2UgdXBsb2FkIGlzIG5vdCBzdXBwb3J0ZWQgaW4gdGhpcyB2ZXJzaW9uJywgJ2Vycm9yJyk7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoJ0ltYWdlIHVwbG9hZCBpcyBub3Qgc3VwcG9ydGVkJyk7XG4gICAgICAgIH0sXG4gICAgICAgIFt0b2FzdF1cbiAgICApO1xuXG4gICAgY29uc3Qgb25Ib3ZlckxpbmsgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IFJlYWN0TW91c2VFdmVudCkgPT4ge1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9LCBbXSk7XG5cbiAgICBjb25zdCBbYmFja2xpbmtzLCBzZXRCYWNrTGlua3NdID0gdXNlU3RhdGU8Tm90ZUNhY2hlSXRlbVtdPigpO1xuXG4gICAgY29uc3QgZ2V0QmFja0xpbmtzID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhub3RlPy5pZCk7XG4gICAgICAgIGNvbnN0IGxpbmtOb3RlczogTm90ZUNhY2hlSXRlbVtdID0gW107XG4gICAgICAgIGlmICghbm90ZT8uaWQpIHJldHVybiBsaW5rTm90ZXM7XG4gICAgICAgIHNldEJhY2tMaW5rcyhbXSk7XG4gICAgICAgIGF3YWl0IG5vdGVDYWNoZS5pdGVyYXRlPE5vdGVDYWNoZUl0ZW0sIHZvaWQ+KCh2YWx1ZSkgPT4ge1xuICAgICAgICAgICAgaWYgKHZhbHVlLmxpbmtJZHM/LmluY2x1ZGVzKG5vdGU/LmlkIHx8ICcnKSkge1xuICAgICAgICAgICAgICAgIGxpbmtOb3Rlcy5wdXNoKHZhbHVlKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHNldEJhY2tMaW5rcyhsaW5rTm90ZXMpO1xuICAgIH0sIFtub3RlPy5pZF0pO1xuXG4gICAgLy8g8J+UpyDlv6vnhafliJ3lp4vljJbpgLvovpEgLSDmiZPlvIDnrJTorrDml7borr7nva5KU09O5b+r54WnXG4gICAgY29uc3QgaW5pdGlhbGl6ZVNuYXBzaG90ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDlvIDlp4vliJ3lp4vljJblv6vnhac6Jywge1xuICAgICAgICAgICAgbm90ZUlkOiBub3RlPy5pZCxcbiAgICAgICAgICAgIG5vdGVDb250ZW50OiBub3RlPy5jb250ZW50Py5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nLFxuICAgICAgICAgICAgbm90ZUNvbnRlbnRMZW5ndGg6IG5vdGU/LmNvbnRlbnQ/Lmxlbmd0aCB8fCAwXG4gICAgICAgIH0pO1xuXG4gICAgICAgIGlmICghbm90ZT8uaWQpIHtcbiAgICAgICAgICAgIC8vIOaWsOW7uueslOiusO+8muW/q+eFp+S4uuepuuWAvFxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg5paw5bu656yU6K6w77yM6K6+572u56m65b+r54WnJyk7XG4gICAgICAgICAgICBzZXROb3RlU25hcHNob3QobnVsbCk7XG4gICAgICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudCgnJyk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cblxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgLy8g8J+UpyDkv67lpI3vvJrkvJjlhYjkvb/nlKjlvZPliY0gbm90ZSDlr7nosaHnmoTlhoXlrrnvvIzlm6DkuLrlroPmmK/mnIDmlrDnmoRcbiAgICAgICAgICAgIC8vIG5vdGUg5a+56LGh5p2l6IeqIE5vdGVTdGF0Ze+8jOW3sue7j+WkhOeQhuS6hue8k+WtmOWSjOacjeWKoeWZqOaVsOaNrueahOWQjOatpVxuICAgICAgICAgICAgbGV0IHNuYXBzaG90SnNvbkNvbnRlbnQgPSAnJztcblxuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg5b+r54Wn5YaF5a655p2l5rqQ5YiG5p6QOicsIHtcbiAgICAgICAgICAgICAgICBub3RlQ29udGVudDogbm90ZS5jb250ZW50Py5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nLFxuICAgICAgICAgICAgICAgIG5vdGVDb250ZW50TGVuZ3RoOiBub3RlLmNvbnRlbnQ/Lmxlbmd0aCB8fCAwLFxuICAgICAgICAgICAgICAgIG5vdGVDb250ZW50VHlwZTogdHlwZW9mIG5vdGUuY29udGVudFxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIC8vIOebtOaOpeS9v+eUqCBub3RlIOWvueixoeeahOWGheWuueS9nOS4uuW/q+eFp1xuICAgICAgICAgICAgaWYgKG5vdGUuY29udGVudCAhPT0gdW5kZWZpbmVkICYmIG5vdGUuY29udGVudCAhPT0gbnVsbCkge1xuICAgICAgICAgICAgICAgIHNuYXBzaG90SnNvbkNvbnRlbnQgPSBub3RlLmNvbnRlbnQ7XG4gICAgICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg5L2/55SoIG5vdGUg5a+56LGh5YaF5a655L2c5Li65b+r54WnJyk7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIHNuYXBzaG90SnNvbkNvbnRlbnQgPSAnJztcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyBub3RlIOWGheWuueS4uuepuu+8jOiuvue9ruepuuW/q+eFpycpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDlv6vnhafliJ3lp4vljJblrozmiJA6Jywge1xuICAgICAgICAgICAgICAgIG5vdGVJZDogbm90ZS5pZCxcbiAgICAgICAgICAgICAgICBoYXNDb250ZW50OiAhIXNuYXBzaG90SnNvbkNvbnRlbnQsXG4gICAgICAgICAgICAgICAgY29udGVudExlbmd0aDogc25hcHNob3RKc29uQ29udGVudC5sZW5ndGgsXG4gICAgICAgICAgICAgICAgaXNKc29uOiBzbmFwc2hvdEpzb25Db250ZW50LnN0YXJ0c1dpdGgoJ3snKSxcbiAgICAgICAgICAgICAgICBzb3VyY2U6IGNhY2hlZE5vdGU/LmNvbnRlbnQgPyAnY2FjaGUnIDogKG5vdGUuY29udGVudCA/ICdub3RlJyA6ICdlbXB0eScpLFxuICAgICAgICAgICAgICAgIGNvbnRlbnRQcmV2aWV3OiBzbmFwc2hvdEpzb25Db250ZW50LnN1YnN0cmluZygwLCAxMDApICsgJy4uLidcbiAgICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgICAvLyDwn5SnIOWFs+mUru+8muiuvue9ruW/q+eFp+WSjOW9k+WJjee8lui+keWZqOWGheWuuVxuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KHNuYXBzaG90SnNvbkNvbnRlbnQpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoc25hcHNob3RKc29uQ29udGVudCk7XG4gICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKCdKU09O5b+r54Wn5Yid5aeL5YyW5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgICAgIC8vIOWksei0peaXtuiuvue9ruS4uuepuuW/q+eFp1xuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoJycpO1xuICAgICAgICB9XG4gICAgfSwgW25vdGU/LmlkLCBub3RlPy5jb250ZW50XSk7XG5cbiAgICAvLyDlvZPnrJTorrBJROWPmOWMluaIluWGheWuueWKoOi9veWujOaIkOaXtuWIneWni+WMluW/q+eFp1xuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIC8vIPCflKcg5L+u5aSN77ya5Y+q5pyJ5b2TIG5vdGUg5a2Y5Zyo44CB5pyJIElEIOS4lOWGheWuueW3suWKoOi9veaXtuaJjeWIneWni+WMllxuICAgICAgICAvLyDpgb/lhY3lnKjlhoXlrrnmnKrlrozlhajliqDovb3ml7blsLHliJ3lp4vljJblv6vnhadcbiAgICAgICAgaWYgKG5vdGU/LmlkICYmIG5vdGU/LmNvbnRlbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coJ/CflKcg6Kem5Y+R5b+r54Wn5Yid5aeL5YyW5p2h5Lu2OicsIHtcbiAgICAgICAgICAgICAgICBub3RlSWQ6IG5vdGUuaWQsXG4gICAgICAgICAgICAgICAgaGFzQ29udGVudDogbm90ZS5jb250ZW50ICE9PSB1bmRlZmluZWQsXG4gICAgICAgICAgICAgICAgY29udGVudExlbmd0aDogbm90ZS5jb250ZW50Py5sZW5ndGggfHwgMFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpbml0aWFsaXplU25hcHNob3QoKTtcbiAgICAgICAgfVxuICAgIH0sIFtub3RlPy5pZCwgbm90ZT8uY29udGVudCwgaW5pdGlhbGl6ZVNuYXBzaG90XSk7XG5cbiAgICAvLyDnroDljJbnmoQgb25DaGFuZ2Ug5aSE55CGIC0g5Y+q5pu05paw5b2T5YmN57yW6L6R5Zmo5YaF5a6577yM5LiN5YGa5YW25LuW5pON5L2cXG4gICAgY29uc3Qgb25FZGl0b3JDaGFuZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgKGdldFZhbHVlOiAoKSA9PiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGpzb25Db250ZW50ID0gZ2V0VmFsdWUoKTtcblxuICAgICAgICAgICAgLy8g5Y+q5pu05paw5b2T5YmN57yW6L6R5Zmo5YaF5a6554q25oCB77yM5YW25LuW6YC76L6R5Lqk57uZIFNhdmVCdXR0b24g5aSE55CGXG4gICAgICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudChqc29uQ29udGVudCk7XG4gICAgICAgIH0sXG4gICAgICAgIFtdXG4gICAgKTtcblxuICAgIC8vIEZ1bmN0aW9uIHRvIGhhbmRsZSB0aXRsZSBjaGFuZ2VzIHNwZWNpZmljYWxseVxuICAgIGNvbnN0IG9uVGl0bGVDaGFuZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgKHRpdGxlOiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgICAgICAgICAgIHNhdmVUb0luZGV4ZWREQih7XG4gICAgICAgICAgICAgICAgdGl0bGUsXG4gICAgICAgICAgICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9KT8uY2F0Y2goKHYpID0+IGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHdoaWxzdCBzYXZpbmcgdGl0bGUgdG8gSW5kZXhlZERCOiAlTycsIHYpKTtcbiAgICAgICAgfSxcbiAgICAgICAgW3NhdmVUb0luZGV4ZWREQl1cbiAgICApO1xuXG4gICAgLy8g8J+UpyDkv67lpI3vvJpKU09O5b+r54Wn5a+55q+U5Yqf6IO9IC0g5L6bU2F2ZUJ1dHRvbuS9v+eUqFxuICAgIGNvbnN0IGNvbXBhcmVXaXRoU25hcHNob3QgPSB1c2VDYWxsYmFjaygoKTogYm9vbGVhbiA9PiB7XG4gICAgICAgIC8vIOWmguaenOaYr+aWsOW7uueslOiusO+8iOW/q+eFp+S4um51bGzvvInvvIzku7vkvZVKU09O5YaF5a656YO9566X5L2c5Y+Y5YyWXG4gICAgICAgIGlmIChub3RlU25hcHNob3QgPT09IG51bGwpIHtcbiAgICAgICAgICAgIHJldHVybiBjdXJyZW50RWRpdG9yQ29udGVudC50cmltKCkgIT09ICcnO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5bey5a2Y5Zyo56yU6K6w77ya5q+U6L6D5b2T5YmNSlNPTuWGheWuueS4jkpTT07lv6vnhadcbiAgICAgICAgY29uc3QgaGFzQ2hhbmdlcyA9IGN1cnJlbnRFZGl0b3JDb250ZW50ICE9PSBub3RlU25hcHNob3Q7XG4gICAgICAgIHJldHVybiBoYXNDaGFuZ2VzO1xuICAgIH0sIFtub3RlU25hcHNob3QsIGN1cnJlbnRFZGl0b3JDb250ZW50XSk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8muiOt+WPluW9k+WJjee8lui+keWZqOeKtuaAgSAtIOS+m1NhdmVCdXR0b27kvb/nlKhcbiAgICBjb25zdCBnZXRFZGl0b3JTdGF0ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc0NoYW5nZXM6IGNvbXBhcmVXaXRoU25hcHNob3QoKSxcbiAgICAgICAgICAgIGN1cnJlbnRDb250ZW50OiBjdXJyZW50RWRpdG9yQ29udGVudCxcbiAgICAgICAgICAgIHNuYXBzaG90OiBub3RlU25hcHNob3QsXG4gICAgICAgICAgICBpc05ld05vdGU6IG5vdGVTbmFwc2hvdCA9PT0gbnVsbFxuICAgICAgICB9O1xuICAgIH0sIFtjb21wYXJlV2l0aFNuYXBzaG90LCBjdXJyZW50RWRpdG9yQ29udGVudCwgbm90ZVNuYXBzaG90XSk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8mua4heepuuaJgOacieW/q+eFp+eahOWHveaVsFxuICAgIGNvbnN0IGNsZWFyQWxsU25hcHNob3RzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDmuIXnqbrmiYDmnInlv6vnhacnKTtcbiAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudCgnJyk7XG5cbiAgICAgICAgLy8g8J+UpyDkv67lpI3vvJrmuIXnqbrlkI7ph43mlrDliJ3lp4vljJblv6vnhafvvIznoa7kv53nvJbovpHlmajmmL7npLrmraPnoa7lhoXlrrlcbiAgICAgICAgaWYgKG5vdGU/LmlkICYmIG5vdGU/LmNvbnRlbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgaW5pdGlhbGl6ZVNuYXBzaG90KCk7XG4gICAgICAgICAgICB9LCAwKTtcbiAgICAgICAgfVxuICAgIH0sIFtub3RlPy5pZCwgbm90ZT8uY29udGVudCwgaW5pdGlhbGl6ZVNuYXBzaG90XSk7XG5cbiAgICAvLyDwn5SnIOS/ruWkje+8muS/neWtmOW9k+WJjUpTT07lhoXlrrnliLBJbmRleGVkREIgLSDkvptTYXZlQnV0dG9u6LCD55SoXG4gICAgY29uc3Qgc2F2ZUN1cnJlbnRDb250ZW50ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICAgICAgICBpZiAoIW5vdGU/LmlkKSByZXR1cm4gZmFsc2U7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIOiOt+WPluagh+mimFxuICAgICAgICAgICAgbGV0IHRpdGxlOiBzdHJpbmc7XG4gICAgICAgICAgICBpZiAobm90ZT8uaXNEYWlseU5vdGUpIHtcbiAgICAgICAgICAgICAgICB0aXRsZSA9IG5vdGUudGl0bGU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRpdGxlSW5wdXQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdoMSB0ZXh0YXJlYScpIGFzIEhUTUxUZXh0QXJlYUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgaWYgKHRpdGxlSW5wdXQgJiYgdGl0bGVJbnB1dC52YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICB0aXRsZSA9IHRpdGxlSW5wdXQudmFsdWUudHJpbSgpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOWvueS6jkpTT07moLzlvI/vvIzkvb/nlKjnjrDmnInmoIfpopjmiJbpu5jorqTmoIfpophcbiAgICAgICAgICAgICAgICAgICAgdGl0bGUgPSBub3RlPy50aXRsZSB8fCAnVW50aXRsZWQnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8g5L+d5a2YSlNPTuWGheWuueWIsEluZGV4ZWREQlxuICAgICAgICAgICAgYXdhaXQgc2F2ZVRvSW5kZXhlZERCKHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiBjdXJyZW50RWRpdG9yQ29udGVudCwgLy8g6L+Z6YeM5pivSlNPTuagvOW8j1xuICAgICAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIC8vIPCflKcg5YWz6ZSu5L+u5aSN77ya5L+d5a2Y5oiQ5Yqf5ZCO5riF56m65omA5pyJ5b+r54WnXG4gICAgICAgICAgICBjbGVhckFsbFNuYXBzaG90cygpO1xuXG4gICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ/CflKcg5L+d5a2YSlNPTuWIsEluZGV4ZWREQuWksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH1cbiAgICB9LCBbbm90ZSwgY3VycmVudEVkaXRvckNvbnRlbnQsIHNhdmVUb0luZGV4ZWREQiwgY2xlYXJBbGxTbmFwc2hvdHNdKTtcblxuICAgIHJldHVybiB7XG4gICAgICAgIG9uQ3JlYXRlTGluayxcbiAgICAgICAgb25TZWFyY2hMaW5rLFxuICAgICAgICBvbkNsaWNrTGluayxcbiAgICAgICAgb25VcGxvYWRJbWFnZSxcbiAgICAgICAgb25Ib3ZlckxpbmssXG4gICAgICAgIGdldEJhY2tMaW5rcyxcbiAgICAgICAgb25FZGl0b3JDaGFuZ2UsXG4gICAgICAgIG9uVGl0bGVDaGFuZ2UsXG4gICAgICAgIHNhdmVUb0luZGV4ZWREQixcbiAgICAgICAgc3luY1RvU2VydmVyLFxuICAgICAgICBiYWNrbGlua3MsXG4gICAgICAgIGVkaXRvckVsLFxuICAgICAgICBub3RlLFxuICAgICAgICAvLyDwn5SnIOaWsOWinu+8muW/q+eFp+WvueavlOebuOWFs+WKn+iDvVxuICAgICAgICBnZXRFZGl0b3JTdGF0ZSxcbiAgICAgICAgc2F2ZUN1cnJlbnRDb250ZW50LFxuICAgICAgICBjb21wYXJlV2l0aFNuYXBzaG90LFxuICAgICAgICBjbGVhckFsbFNuYXBzaG90cywgLy8g8J+UpyDmlrDlop7vvJrmuIXnqbrlv6vnhaflip/og71cbiAgICAgICAgLy8g8J+UpyDlhbPplK7kv67lpI3vvJrov5Tlm57lvZPliY3nvJbovpHlmajlhoXlrrnkvZzkuLrnvJbovpHlmajnmoQgdmFsdWVcbiAgICAgICAgZWRpdG9yTm90ZToge1xuICAgICAgICAgICAgLi4ubm90ZSxcbiAgICAgICAgICAgIGNvbnRlbnQ6IGN1cnJlbnRFZGl0b3JDb250ZW50XG4gICAgICAgIH0sXG4gICAgfTtcbn07XG5cbmNvbnN0IExleGljYWxFZGl0b3JTdGF0ZSA9IGNyZWF0ZUNvbnRhaW5lcih1c2VMZXhpY2FsRWRpdG9yKTtcblxuZXhwb3J0IGRlZmF1bHQgTGV4aWNhbEVkaXRvclN0YXRlO1xuIl0sIm5hbWVzIjpbIk5vdGVTdGF0ZSIsInVzZVJvdXRlciIsInVzZUNhbGxiYWNrIiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJpc05vdGVMaW5rIiwidXNlVG9hc3QiLCJub3RlQ2FjaGUiLCJjcmVhdGVDb250YWluZXIiLCJoYXMiLCJST09UX0lEIiwidXNlTGV4aWNhbEVkaXRvciIsImluaXROb3RlIiwibm90ZSIsImNyZWF0ZU5vdGVXaXRoVGl0bGUiLCJ1cGRhdGVOb3RlIiwiY3JlYXRlTm90ZSIsIm5vdGVTdGF0ZSIsInVzZUNvbnRhaW5lciIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJ1bmRlZmluZWQiLCJyb3V0ZXIiLCJ0b2FzdCIsImVkaXRvckVsIiwibm90ZVNuYXBzaG90Iiwic2V0Tm90ZVNuYXBzaG90IiwiY3VycmVudEVkaXRvckNvbnRlbnQiLCJzZXRDdXJyZW50RWRpdG9yQ29udGVudCIsInNhdmVUb0luZGV4ZWREQiIsImRhdGEiLCJleGlzdGluZ05vdGUiLCJiYXNlTm90ZSIsInVwZGF0ZWROb3RlIiwiaWQiLCJnZXRJdGVtIiwic2V0SXRlbSIsInN5bmNUb1NlcnZlciIsImlzTmV3IiwibG9jYWxOb3RlIiwibm90ZVRvU2F2ZSIsIm5vdGVEYXRhIiwiaXRlbSIsIm5vdGVVcmwiLCJxdWVyeSIsInBpZCIsImFzUGF0aCIsInJlcGxhY2UiLCJzaGFsbG93Iiwib25DcmVhdGVMaW5rIiwidGl0bGUiLCJyZXN1bHQiLCJvblNlYXJjaExpbmsiLCJ0ZXJtIiwib25DbGlja0xpbmsiLCJocmVmIiwiZXZlbnQiLCJwcmV2ZW50RGVmYXVsdCIsInB1c2giLCJ3aW5kb3ciLCJvcGVuIiwib25VcGxvYWRJbWFnZSIsIl9maWxlIiwiX2lkIiwiRXJyb3IiLCJvbkhvdmVyTGluayIsImJhY2tsaW5rcyIsInNldEJhY2tMaW5rcyIsImdldEJhY2tMaW5rcyIsImxpbmtOb3RlcyIsImxvZyIsIml0ZXJhdGUiLCJ2YWx1ZSIsImxpbmtJZHMiLCJpbmNsdWRlcyIsImluaXRpYWxpemVTbmFwc2hvdCIsInNuYXBzaG90SnNvbkNvbnRlbnQiLCJub3RlSWQiLCJub3RlQ29udGVudCIsImNvbnRlbnQiLCJzdWJzdHJpbmciLCJub3RlQ29udGVudExlbmd0aCIsImxlbmd0aCIsIm5vdGVDb250ZW50VHlwZSIsImhhc0NvbnRlbnQiLCJjb250ZW50TGVuZ3RoIiwiaXNKc29uIiwic3RhcnRzV2l0aCIsInNvdXJjZSIsImNhY2hlZE5vdGUiLCJjb250ZW50UHJldmlldyIsIm9uRWRpdG9yQ2hhbmdlIiwiZ2V0VmFsdWUiLCJqc29uQ29udGVudCIsIm9uVGl0bGVDaGFuZ2UiLCJ1cGRhdGVkX2F0IiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiY2F0Y2giLCJ2IiwiY29tcGFyZVdpdGhTbmFwc2hvdCIsInRyaW0iLCJoYXNDaGFuZ2VzIiwiZ2V0RWRpdG9yU3RhdGUiLCJjdXJyZW50Q29udGVudCIsInNuYXBzaG90IiwiaXNOZXdOb3RlIiwiY2xlYXJBbGxTbmFwc2hvdHMiLCJzZXRUaW1lb3V0Iiwic2F2ZUN1cnJlbnRDb250ZW50IiwidGl0bGVJbnB1dCIsImlzRGFpbHlOb3RlIiwiZG9jdW1lbnQiLCJxdWVyeVNlbGVjdG9yIiwiZWRpdG9yTm90ZSIsIkxleGljYWxFZGl0b3JTdGF0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});