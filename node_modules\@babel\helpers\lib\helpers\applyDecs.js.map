{"version": 3, "names": ["old_createMetadataMethodsForProperty", "metadataMap", "kind", "property", "decoratorFinishedRef", "getMetadata", "key", "old_assertNotFinished", "old_assertMetadataKey", "metadataForKey", "pub", "public", "priv", "private", "get", "Object", "hasOwnProperty", "call", "constructor", "setMetadata", "value", "Map", "set", "old_convertMetadataMapToFinal", "obj", "parentMetadataMap", "Symbol", "metadata", "for", "metadataKeys", "getOwnPropertySymbols", "length", "i", "metaForKey", "parentMetaForKey", "parentPub", "setPrototypeOf", "privArr", "Array", "from", "values", "parentPriv", "concat", "old_createAddInitializerMethod", "initializers", "addInitializer", "initializer", "old_assertCallable", "push", "old_memberDec", "dec", "name", "desc", "isStatic", "isPrivate", "kindStr", "ctx", "v", "metadataKind", "metadataName", "access", "assign", "fnName", "Error", "TypeError", "fn", "hint", "old_assertValidReturnValue", "type", "undefined", "init", "old_getInit", "console", "warn", "old_applyMemberDec", "ret", "base", "decInfo", "decs", "getOwnPropertyDescriptor", "newValue", "newInit", "instance", "ownInitializers", "originalInitializer", "args", "defineProperty", "old_applyMemberDecs", "Class", "protoMetadataMap", "staticMetadataMap", "decInfos", "protoInitializers", "staticInitializers", "existingProtoNonFields", "existingStaticNonFields", "isArray", "prototype", "existingNonFields", "existingKind", "old_pushInitializers", "old_applyClassDecs", "targetClass", "classDecs", "newClass", "nextNewClass", "applyDecs", "memberDecs"], "sources": ["../../src/helpers/applyDecs.js"], "sourcesContent": ["/* @minVersion 7.17.8 */\n\n/**\n * NOTE: This is an old version of the helper, used for 2021-12 decorators.\n * Updates should be done in applyDecs2203.js.\n */\n\n/**\n  Enums are used in this file, but not assigned to vars to avoid non-hoistable values\n\n  CONSTRUCTOR = 0;\n  PUBLIC = 1;\n  PRIVATE = 2;\n\n  FIELD = 0;\n  ACCESSOR = 1;\n  METHOD = 2;\n  GETTER = 3;\n  SETTER = 4;\n\n  STATIC = 5;\n\n  CLASS = 10; // only used in assertValidReturnValue\n*/\n\nfunction old_createMetadataMethodsForProperty(\n  metadataMap,\n  kind,\n  property,\n  decoratorFinishedRef\n) {\n  return {\n    getMetadata: function (key) {\n      old_assertNotFinished(decoratorFinishedRef, \"getMetadata\");\n      old_assertMetadataKey(key);\n\n      var metadataForKey = metadataMap[key];\n\n      if (metadataForKey === void 0) return void 0;\n\n      if (kind === 1 /* PUBLIC */) {\n        var pub = metadataForKey.public;\n        if (pub !== void 0) {\n          return pub[property];\n        }\n      } else if (kind === 2 /* PRIVATE */) {\n        var priv = metadataForKey.private;\n        if (priv !== void 0) {\n          return priv.get(property);\n        }\n      } else if (Object.hasOwnProperty.call(metadataForKey, \"constructor\")) {\n        return metadataForKey.constructor;\n      }\n    },\n    setMetadata: function (key, value) {\n      old_assertNotFinished(decoratorFinishedRef, \"setMetadata\");\n      old_assertMetadataKey(key);\n\n      var metadataForKey = metadataMap[key];\n\n      if (metadataForKey === void 0) {\n        metadataForKey = metadataMap[key] = {};\n      }\n\n      if (kind === 1 /* PUBLIC */) {\n        var pub = metadataForKey.public;\n\n        if (pub === void 0) {\n          pub = metadataForKey.public = {};\n        }\n\n        pub[property] = value;\n      } else if (kind === 2 /* PRIVATE */) {\n        var priv = metadataForKey.priv;\n\n        if (priv === void 0) {\n          priv = metadataForKey.private = new Map();\n        }\n\n        priv.set(property, value);\n      } else {\n        metadataForKey.constructor = value;\n      }\n    },\n  };\n}\n\nfunction old_convertMetadataMapToFinal(obj, metadataMap) {\n  var parentMetadataMap = obj[Symbol.metadata || Symbol.for(\"Symbol.metadata\")];\n  var metadataKeys = Object.getOwnPropertySymbols(metadataMap);\n\n  if (metadataKeys.length === 0) return;\n\n  for (var i = 0; i < metadataKeys.length; i++) {\n    var key = metadataKeys[i];\n    var metaForKey = metadataMap[key];\n    var parentMetaForKey = parentMetadataMap ? parentMetadataMap[key] : null;\n\n    var pub = metaForKey.public;\n    var parentPub = parentMetaForKey ? parentMetaForKey.public : null;\n\n    if (pub && parentPub) {\n      Object.setPrototypeOf(pub, parentPub);\n    }\n\n    var priv = metaForKey.private;\n\n    if (priv) {\n      var privArr = Array.from(priv.values());\n      var parentPriv = parentMetaForKey ? parentMetaForKey.private : null;\n\n      if (parentPriv) {\n        privArr = privArr.concat(parentPriv);\n      }\n\n      metaForKey.private = privArr;\n    }\n\n    if (parentMetaForKey) {\n      Object.setPrototypeOf(metaForKey, parentMetaForKey);\n    }\n  }\n\n  if (parentMetadataMap) {\n    Object.setPrototypeOf(metadataMap, parentMetadataMap);\n  }\n\n  obj[Symbol.metadata || Symbol.for(\"Symbol.metadata\")] = metadataMap;\n}\n\nfunction old_createAddInitializerMethod(initializers, decoratorFinishedRef) {\n  return function addInitializer(initializer) {\n    old_assertNotFinished(decoratorFinishedRef, \"addInitializer\");\n    old_assertCallable(initializer, \"An initializer\");\n    initializers.push(initializer);\n  };\n}\n\nfunction old_memberDec(\n  dec,\n  name,\n  desc,\n  metadataMap,\n  initializers,\n  kind,\n  isStatic,\n  isPrivate,\n  value\n) {\n  var kindStr;\n\n  switch (kind) {\n    case 1 /* ACCESSOR */:\n      kindStr = \"accessor\";\n      break;\n    case 2 /* METHOD */:\n      kindStr = \"method\";\n      break;\n    case 3 /* GETTER */:\n      kindStr = \"getter\";\n      break;\n    case 4 /* SETTER */:\n      kindStr = \"setter\";\n      break;\n    default:\n      kindStr = \"field\";\n  }\n\n  var ctx = {\n    kind: kindStr,\n    name: isPrivate ? \"#\" + name : name,\n    isStatic: isStatic,\n    isPrivate: isPrivate,\n  };\n\n  var decoratorFinishedRef = { v: false };\n\n  if (kind !== 0 /* FIELD */) {\n    ctx.addInitializer = old_createAddInitializerMethod(\n      initializers,\n      decoratorFinishedRef\n    );\n  }\n\n  var metadataKind, metadataName;\n\n  if (isPrivate) {\n    metadataKind = 2 /* PRIVATE */;\n    metadataName = Symbol(name);\n\n    var access = {};\n\n    if (kind === 0 /* FIELD */) {\n      access.get = desc.get;\n      access.set = desc.set;\n    } else if (kind === 2 /* METHOD */) {\n      access.get = function () {\n        return desc.value;\n      };\n    } else {\n      // replace with values that will go through the final getter and setter\n      if (kind === 1 /* ACCESSOR */ || kind === 3 /* GETTER */) {\n        access.get = function () {\n          return desc.get.call(this);\n        };\n      }\n\n      if (kind === 1 /* ACCESSOR */ || kind === 4 /* SETTER */) {\n        access.set = function (v) {\n          desc.set.call(this, v);\n        };\n      }\n    }\n\n    ctx.access = access;\n  } else {\n    metadataKind = 1 /* PUBLIC */;\n    metadataName = name;\n  }\n\n  try {\n    return dec(\n      value,\n      Object.assign(\n        ctx,\n        old_createMetadataMethodsForProperty(\n          metadataMap,\n          metadataKind,\n          metadataName,\n          decoratorFinishedRef\n        )\n      )\n    );\n  } finally {\n    decoratorFinishedRef.v = true;\n  }\n}\n\nfunction old_assertNotFinished(decoratorFinishedRef, fnName) {\n  if (decoratorFinishedRef.v) {\n    throw new Error(\n      \"attempted to call \" + fnName + \" after decoration was finished\"\n    );\n  }\n}\n\nfunction old_assertMetadataKey(key) {\n  if (typeof key !== \"symbol\") {\n    throw new TypeError(\"Metadata keys must be symbols, received: \" + key);\n  }\n}\n\nfunction old_assertCallable(fn, hint) {\n  if (typeof fn !== \"function\") {\n    throw new TypeError(hint + \" must be a function\");\n  }\n}\n\nfunction old_assertValidReturnValue(kind, value) {\n  var type = typeof value;\n\n  if (kind === 1 /* ACCESSOR */) {\n    if (type !== \"object\" || value === null) {\n      throw new TypeError(\n        \"accessor decorators must return an object with get, set, or init properties or void 0\"\n      );\n    }\n    if (value.get !== undefined) {\n      old_assertCallable(value.get, \"accessor.get\");\n    }\n    if (value.set !== undefined) {\n      old_assertCallable(value.set, \"accessor.set\");\n    }\n    if (value.init !== undefined) {\n      old_assertCallable(value.init, \"accessor.init\");\n    }\n    if (value.initializer !== undefined) {\n      old_assertCallable(value.initializer, \"accessor.initializer\");\n    }\n  } else if (type !== \"function\") {\n    var hint;\n    if (kind === 0 /* FIELD */) {\n      hint = \"field\";\n    } else if (kind === 10 /* CLASS */) {\n      hint = \"class\";\n    } else {\n      hint = \"method\";\n    }\n    throw new TypeError(hint + \" decorators must return a function or void 0\");\n  }\n}\n\nfunction old_getInit(desc) {\n  var initializer;\n  if (\n    (initializer = desc.init) == null &&\n    (initializer = desc.initializer) &&\n    typeof console !== \"undefined\"\n  ) {\n    console.warn(\".initializer has been renamed to .init as of March 2022\");\n  }\n  return initializer;\n}\n\nfunction old_applyMemberDec(\n  ret,\n  base,\n  decInfo,\n  name,\n  kind,\n  isStatic,\n  isPrivate,\n  metadataMap,\n  initializers\n) {\n  var decs = decInfo[0];\n\n  var desc, initializer, value;\n\n  if (isPrivate) {\n    if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n      desc = {\n        get: decInfo[3],\n        set: decInfo[4],\n      };\n    } else if (kind === 3 /* GETTER */) {\n      desc = {\n        get: decInfo[3],\n      };\n    } else if (kind === 4 /* SETTER */) {\n      desc = {\n        set: decInfo[3],\n      };\n    } else {\n      desc = {\n        value: decInfo[3],\n      };\n    }\n  } else if (kind !== 0 /* FIELD */) {\n    desc = Object.getOwnPropertyDescriptor(base, name);\n  }\n\n  if (kind === 1 /* ACCESSOR */) {\n    value = {\n      get: desc.get,\n      set: desc.set,\n    };\n  } else if (kind === 2 /* METHOD */) {\n    value = desc.value;\n  } else if (kind === 3 /* GETTER */) {\n    value = desc.get;\n  } else if (kind === 4 /* SETTER */) {\n    value = desc.set;\n  }\n\n  var newValue, get, set;\n\n  if (typeof decs === \"function\") {\n    newValue = old_memberDec(\n      decs,\n      name,\n      desc,\n      metadataMap,\n      initializers,\n      kind,\n      isStatic,\n      isPrivate,\n      value\n    );\n\n    if (newValue !== void 0) {\n      old_assertValidReturnValue(kind, newValue);\n\n      if (kind === 0 /* FIELD */) {\n        initializer = newValue;\n      } else if (kind === 1 /* ACCESSOR */) {\n        initializer = old_getInit(newValue);\n        get = newValue.get || value.get;\n        set = newValue.set || value.set;\n\n        value = { get: get, set: set };\n      } else {\n        value = newValue;\n      }\n    }\n  } else {\n    for (var i = decs.length - 1; i >= 0; i--) {\n      var dec = decs[i];\n\n      newValue = old_memberDec(\n        dec,\n        name,\n        desc,\n        metadataMap,\n        initializers,\n        kind,\n        isStatic,\n        isPrivate,\n        value\n      );\n\n      if (newValue !== void 0) {\n        old_assertValidReturnValue(kind, newValue);\n        var newInit;\n\n        if (kind === 0 /* FIELD */) {\n          newInit = newValue;\n        } else if (kind === 1 /* ACCESSOR */) {\n          newInit = old_getInit(newValue);\n          get = newValue.get || value.get;\n          set = newValue.set || value.set;\n\n          value = { get: get, set: set };\n        } else {\n          value = newValue;\n        }\n\n        if (newInit !== void 0) {\n          if (initializer === void 0) {\n            initializer = newInit;\n          } else if (typeof initializer === \"function\") {\n            initializer = [initializer, newInit];\n          } else {\n            initializer.push(newInit);\n          }\n        }\n      }\n    }\n  }\n\n  if (kind === 0 /* FIELD */ || kind === 1 /* ACCESSOR */) {\n    if (initializer === void 0) {\n      // If the initializer was void 0, sub in a dummy initializer\n      initializer = function (instance, init) {\n        return init;\n      };\n    } else if (typeof initializer !== \"function\") {\n      var ownInitializers = initializer;\n\n      initializer = function (instance, init) {\n        var value = init;\n\n        for (var i = 0; i < ownInitializers.length; i++) {\n          value = ownInitializers[i].call(instance, value);\n        }\n\n        return value;\n      };\n    } else {\n      var originalInitializer = initializer;\n\n      initializer = function (instance, init) {\n        return originalInitializer.call(instance, init);\n      };\n    }\n\n    ret.push(initializer);\n  }\n\n  if (kind !== 0 /* FIELD */) {\n    if (kind === 1 /* ACCESSOR */) {\n      desc.get = value.get;\n      desc.set = value.set;\n    } else if (kind === 2 /* METHOD */) {\n      desc.value = value;\n    } else if (kind === 3 /* GETTER */) {\n      desc.get = value;\n    } else if (kind === 4 /* SETTER */) {\n      desc.set = value;\n    }\n\n    if (isPrivate) {\n      if (kind === 1 /* ACCESSOR */) {\n        ret.push(function (instance, args) {\n          return value.get.call(instance, args);\n        });\n        ret.push(function (instance, args) {\n          return value.set.call(instance, args);\n        });\n      } else if (kind === 2 /* METHOD */) {\n        ret.push(value);\n      } else {\n        ret.push(function (instance, args) {\n          return value.call(instance, args);\n        });\n      }\n    } else {\n      Object.defineProperty(base, name, desc);\n    }\n  }\n}\n\nfunction old_applyMemberDecs(\n  ret,\n  Class,\n  protoMetadataMap,\n  staticMetadataMap,\n  decInfos\n) {\n  var protoInitializers;\n  var staticInitializers;\n\n  var existingProtoNonFields = new Map();\n  var existingStaticNonFields = new Map();\n\n  for (var i = 0; i < decInfos.length; i++) {\n    var decInfo = decInfos[i];\n\n    // skip computed property names\n    if (!Array.isArray(decInfo)) continue;\n\n    var kind = decInfo[1];\n    var name = decInfo[2];\n    var isPrivate = decInfo.length > 3;\n\n    var isStatic = kind >= 5; /* STATIC */\n    var base;\n    var metadataMap;\n    var initializers;\n\n    if (isStatic) {\n      base = Class;\n      metadataMap = staticMetadataMap;\n      kind = kind - 5 /* STATIC */;\n      // initialize staticInitializers when we see a non-field static member\n      if (kind !== 0 /* FIELD */) {\n        staticInitializers = staticInitializers || [];\n        initializers = staticInitializers;\n      }\n    } else {\n      base = Class.prototype;\n      metadataMap = protoMetadataMap;\n      // initialize protoInitializers when we see a non-field member\n      if (kind !== 0 /* FIELD */) {\n        protoInitializers = protoInitializers || [];\n        initializers = protoInitializers;\n      }\n    }\n\n    if (kind !== 0 /* FIELD */ && !isPrivate) {\n      var existingNonFields = isStatic\n        ? existingStaticNonFields\n        : existingProtoNonFields;\n\n      var existingKind = existingNonFields.get(name) || 0;\n\n      if (\n        existingKind === true ||\n        (existingKind === 3 /* GETTER */ && kind !== 4) /* SETTER */ ||\n        (existingKind === 4 /* SETTER */ && kind !== 3) /* GETTER */\n      ) {\n        throw new Error(\n          \"Attempted to decorate a public method/accessor that has the same name as a previously decorated public method/accessor. This is not currently supported by the decorators plugin. Property name was: \" +\n            name\n        );\n      } else if (!existingKind && kind > 2 /* METHOD */) {\n        existingNonFields.set(name, kind);\n      } else {\n        existingNonFields.set(name, true);\n      }\n    }\n\n    old_applyMemberDec(\n      ret,\n      base,\n      decInfo,\n      name,\n      kind,\n      isStatic,\n      isPrivate,\n      metadataMap,\n      initializers\n    );\n  }\n\n  old_pushInitializers(ret, protoInitializers);\n  old_pushInitializers(ret, staticInitializers);\n}\n\nfunction old_pushInitializers(ret, initializers) {\n  if (initializers) {\n    ret.push(function (instance) {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(instance);\n      }\n      return instance;\n    });\n  }\n}\n\nfunction old_applyClassDecs(ret, targetClass, metadataMap, classDecs) {\n  if (classDecs.length > 0) {\n    var initializers = [];\n    var newClass = targetClass;\n    var name = targetClass.name;\n\n    for (var i = classDecs.length - 1; i >= 0; i--) {\n      var decoratorFinishedRef = { v: false };\n\n      try {\n        var ctx = Object.assign(\n          {\n            kind: \"class\",\n            name: name,\n            addInitializer: old_createAddInitializerMethod(\n              initializers,\n              decoratorFinishedRef\n            ),\n          },\n          old_createMetadataMethodsForProperty(\n            metadataMap,\n            0 /* CONSTRUCTOR */,\n            name,\n            decoratorFinishedRef\n          )\n        );\n        var nextNewClass = classDecs[i](newClass, ctx);\n      } finally {\n        decoratorFinishedRef.v = true;\n      }\n\n      if (nextNewClass !== undefined) {\n        old_assertValidReturnValue(10 /* CLASS */, nextNewClass);\n        newClass = nextNewClass;\n      }\n    }\n\n    ret.push(newClass, function () {\n      for (var i = 0; i < initializers.length; i++) {\n        initializers[i].call(newClass);\n      }\n    });\n  }\n}\n\n/**\n  Basic usage:\n\n  applyDecs(\n    Class,\n    [\n      // member decorators\n      [\n        dec,                // dec or array of decs\n        0,                  // kind of value being decorated\n        'prop',             // name of public prop on class containing the value being decorated,\n        '#p',               // the name of the private property (if is private, void 0 otherwise),\n      ]\n    ],\n    [\n      // class decorators\n      dec1, dec2\n    ]\n  )\n  ```\n\n  Fully transpiled example:\n\n  ```js\n  @dec\n  class Class {\n    @dec\n    a = 123;\n\n    @dec\n    #a = 123;\n\n    @dec\n    @dec2\n    accessor b = 123;\n\n    @dec\n    accessor #b = 123;\n\n    @dec\n    c() { console.log('c'); }\n\n    @dec\n    #c() { console.log('privC'); }\n\n    @dec\n    get d() { console.log('d'); }\n\n    @dec\n    get #d() { console.log('privD'); }\n\n    @dec\n    set e(v) { console.log('e'); }\n\n    @dec\n    set #e(v) { console.log('privE'); }\n  }\n\n\n  // becomes\n  let initializeInstance;\n  let initializeClass;\n\n  let initA;\n  let initPrivA;\n\n  let initB;\n  let initPrivB, getPrivB, setPrivB;\n\n  let privC;\n  let privD;\n  let privE;\n\n  let Class;\n  class _Class {\n    static {\n      let ret = applyDecs(\n        this,\n        [\n          [dec, 0, 'a'],\n          [dec, 0, 'a', (i) => i.#a, (i, v) => i.#a = v],\n          [[dec, dec2], 1, 'b'],\n          [dec, 1, 'b', (i) => i.#privBData, (i, v) => i.#privBData = v],\n          [dec, 2, 'c'],\n          [dec, 2, 'c', () => console.log('privC')],\n          [dec, 3, 'd'],\n          [dec, 3, 'd', () => console.log('privD')],\n          [dec, 4, 'e'],\n          [dec, 4, 'e', () => console.log('privE')],\n        ],\n        [\n          dec\n        ]\n      )\n\n      initA = ret[0];\n\n      initPrivA = ret[1];\n\n      initB = ret[2];\n\n      initPrivB = ret[3];\n      getPrivB = ret[4];\n      setPrivB = ret[5];\n\n      privC = ret[6];\n\n      privD = ret[7];\n\n      privE = ret[8];\n\n      initializeInstance = ret[9];\n\n      Class = ret[10]\n\n      initializeClass = ret[11];\n    }\n\n    a = (initializeInstance(this), initA(this, 123));\n\n    #a = initPrivA(this, 123);\n\n    #bData = initB(this, 123);\n    get b() { return this.#bData }\n    set b(v) { this.#bData = v }\n\n    #privBData = initPrivB(this, 123);\n    get #b() { return getPrivB(this); }\n    set #b(v) { setPrivB(this, v); }\n\n    c() { console.log('c'); }\n\n    #c(...args) { return privC(this, ...args) }\n\n    get d() { console.log('d'); }\n\n    get #d() { return privD(this); }\n\n    set e(v) { console.log('e'); }\n\n    set #e(v) { privE(this, v); }\n  }\n\n  initializeClass(Class);\n */\nexport default function applyDecs(targetClass, memberDecs, classDecs) {\n  var ret = [];\n  var staticMetadataMap = {};\n\n  var protoMetadataMap = {};\n\n  old_applyMemberDecs(\n    ret,\n    targetClass,\n    protoMetadataMap,\n    staticMetadataMap,\n    memberDecs\n  );\n\n  old_convertMetadataMapToFinal(targetClass.prototype, protoMetadataMap);\n\n  old_applyClassDecs(ret, targetClass, staticMetadataMap, classDecs);\n\n  old_convertMetadataMapToFinal(targetClass, staticMetadataMap);\n\n  return ret;\n}\n"], "mappings": ";;;;;;;AAyBA,SAASA,oCAAT,CACEC,WADF,EAEEC,IAFF,EAGEC,QAHF,EAIEC,oBAJF,EAKE;EACA,OAAO;IACLC,WAAW,EAAE,UAAUC,GAAV,EAAe;MAC1BC,qBAAqB,CAACH,oBAAD,EAAuB,aAAvB,CAArB;MACAI,qBAAqB,CAACF,GAAD,CAArB;MAEA,IAAIG,cAAc,GAAGR,WAAW,CAACK,GAAD,CAAhC;MAEA,IAAIG,cAAc,KAAK,KAAK,CAA5B,EAA+B,OAAO,KAAK,CAAZ;;MAE/B,IAAIP,IAAI,KAAK,CAAb,EAA6B;QAC3B,IAAIQ,GAAG,GAAGD,cAAc,CAACE,MAAzB;;QACA,IAAID,GAAG,KAAK,KAAK,CAAjB,EAAoB;UAClB,OAAOA,GAAG,CAACP,QAAD,CAAV;QACD;MACF,CALD,MAKO,IAAID,IAAI,KAAK,CAAb,EAA8B;QACnC,IAAIU,IAAI,GAAGH,cAAc,CAACI,OAA1B;;QACA,IAAID,IAAI,KAAK,KAAK,CAAlB,EAAqB;UACnB,OAAOA,IAAI,CAACE,GAAL,CAASX,QAAT,CAAP;QACD;MACF,CALM,MAKA,IAAIY,MAAM,CAACC,cAAP,CAAsBC,IAAtB,CAA2BR,cAA3B,EAA2C,aAA3C,CAAJ,EAA+D;QACpE,OAAOA,cAAc,CAACS,WAAtB;MACD;IACF,CAtBI;IAuBLC,WAAW,EAAE,UAAUb,GAAV,EAAec,KAAf,EAAsB;MACjCb,qBAAqB,CAACH,oBAAD,EAAuB,aAAvB,CAArB;MACAI,qBAAqB,CAACF,GAAD,CAArB;MAEA,IAAIG,cAAc,GAAGR,WAAW,CAACK,GAAD,CAAhC;;MAEA,IAAIG,cAAc,KAAK,KAAK,CAA5B,EAA+B;QAC7BA,cAAc,GAAGR,WAAW,CAACK,GAAD,CAAX,GAAmB,EAApC;MACD;;MAED,IAAIJ,IAAI,KAAK,CAAb,EAA6B;QAC3B,IAAIQ,GAAG,GAAGD,cAAc,CAACE,MAAzB;;QAEA,IAAID,GAAG,KAAK,KAAK,CAAjB,EAAoB;UAClBA,GAAG,GAAGD,cAAc,CAACE,MAAf,GAAwB,EAA9B;QACD;;QAEDD,GAAG,CAACP,QAAD,CAAH,GAAgBiB,KAAhB;MACD,CARD,MAQO,IAAIlB,IAAI,KAAK,CAAb,EAA8B;QACnC,IAAIU,IAAI,GAAGH,cAAc,CAACG,IAA1B;;QAEA,IAAIA,IAAI,KAAK,KAAK,CAAlB,EAAqB;UACnBA,IAAI,GAAGH,cAAc,CAACI,OAAf,GAAyB,IAAIQ,GAAJ,EAAhC;QACD;;QAEDT,IAAI,CAACU,GAAL,CAASnB,QAAT,EAAmBiB,KAAnB;MACD,CARM,MAQA;QACLX,cAAc,CAACS,WAAf,GAA6BE,KAA7B;MACD;IACF;EApDI,CAAP;AAsDD;;AAED,SAASG,6BAAT,CAAuCC,GAAvC,EAA4CvB,WAA5C,EAAyD;EACvD,IAAIwB,iBAAiB,GAAGD,GAAG,CAACE,MAAM,CAACC,QAAP,IAAmBD,MAAM,CAACE,GAAP,CAAW,iBAAX,CAApB,CAA3B;EACA,IAAIC,YAAY,GAAGd,MAAM,CAACe,qBAAP,CAA6B7B,WAA7B,CAAnB;EAEA,IAAI4B,YAAY,CAACE,MAAb,KAAwB,CAA5B,EAA+B;;EAE/B,KAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,YAAY,CAACE,MAAjC,EAAyCC,CAAC,EAA1C,EAA8C;IAC5C,IAAI1B,GAAG,GAAGuB,YAAY,CAACG,CAAD,CAAtB;IACA,IAAIC,UAAU,GAAGhC,WAAW,CAACK,GAAD,CAA5B;IACA,IAAI4B,gBAAgB,GAAGT,iBAAiB,GAAGA,iBAAiB,CAACnB,GAAD,CAApB,GAA4B,IAApE;IAEA,IAAII,GAAG,GAAGuB,UAAU,CAACtB,MAArB;IACA,IAAIwB,SAAS,GAAGD,gBAAgB,GAAGA,gBAAgB,CAACvB,MAApB,GAA6B,IAA7D;;IAEA,IAAID,GAAG,IAAIyB,SAAX,EAAsB;MACpBpB,MAAM,CAACqB,cAAP,CAAsB1B,GAAtB,EAA2ByB,SAA3B;IACD;;IAED,IAAIvB,IAAI,GAAGqB,UAAU,CAACpB,OAAtB;;IAEA,IAAID,IAAJ,EAAU;MACR,IAAIyB,OAAO,GAAGC,KAAK,CAACC,IAAN,CAAW3B,IAAI,CAAC4B,MAAL,EAAX,CAAd;MACA,IAAIC,UAAU,GAAGP,gBAAgB,GAAGA,gBAAgB,CAACrB,OAApB,GAA8B,IAA/D;;MAEA,IAAI4B,UAAJ,EAAgB;QACdJ,OAAO,GAAGA,OAAO,CAACK,MAAR,CAAeD,UAAf,CAAV;MACD;;MAEDR,UAAU,CAACpB,OAAX,GAAqBwB,OAArB;IACD;;IAED,IAAIH,gBAAJ,EAAsB;MACpBnB,MAAM,CAACqB,cAAP,CAAsBH,UAAtB,EAAkCC,gBAAlC;IACD;EACF;;EAED,IAAIT,iBAAJ,EAAuB;IACrBV,MAAM,CAACqB,cAAP,CAAsBnC,WAAtB,EAAmCwB,iBAAnC;EACD;;EAEDD,GAAG,CAACE,MAAM,CAACC,QAAP,IAAmBD,MAAM,CAACE,GAAP,CAAW,iBAAX,CAApB,CAAH,GAAwD3B,WAAxD;AACD;;AAED,SAAS0C,8BAAT,CAAwCC,YAAxC,EAAsDxC,oBAAtD,EAA4E;EAC1E,OAAO,SAASyC,cAAT,CAAwBC,WAAxB,EAAqC;IAC1CvC,qBAAqB,CAACH,oBAAD,EAAuB,gBAAvB,CAArB;IACA2C,kBAAkB,CAACD,WAAD,EAAc,gBAAd,CAAlB;IACAF,YAAY,CAACI,IAAb,CAAkBF,WAAlB;EACD,CAJD;AAKD;;AAED,SAASG,aAAT,CACEC,GADF,EAEEC,IAFF,EAGEC,IAHF,EAIEnD,WAJF,EAKE2C,YALF,EAME1C,IANF,EAOEmD,QAPF,EAQEC,SARF,EASElC,KATF,EAUE;EACA,IAAImC,OAAJ;;EAEA,QAAQrD,IAAR;IACE,KAAK,CAAL;MACEqD,OAAO,GAAG,UAAV;MACA;;IACF,KAAK,CAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF,KAAK,CAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF,KAAK,CAAL;MACEA,OAAO,GAAG,QAAV;MACA;;IACF;MACEA,OAAO,GAAG,OAAV;EAdJ;;EAiBA,IAAIC,GAAG,GAAG;IACRtD,IAAI,EAAEqD,OADE;IAERJ,IAAI,EAAEG,SAAS,GAAG,MAAMH,IAAT,GAAgBA,IAFvB;IAGRE,QAAQ,EAAEA,QAHF;IAIRC,SAAS,EAAEA;EAJH,CAAV;EAOA,IAAIlD,oBAAoB,GAAG;IAAEqD,CAAC,EAAE;EAAL,CAA3B;;EAEA,IAAIvD,IAAI,KAAK,CAAb,EAA4B;IAC1BsD,GAAG,CAACX,cAAJ,GAAqBF,8BAA8B,CACjDC,YADiD,EAEjDxC,oBAFiD,CAAnD;EAID;;EAED,IAAIsD,YAAJ,EAAkBC,YAAlB;;EAEA,IAAIL,SAAJ,EAAe;IACbI,YAAY,GAAG,CAAf;IACAC,YAAY,GAAGjC,MAAM,CAACyB,IAAD,CAArB;IAEA,IAAIS,MAAM,GAAG,EAAb;;IAEA,IAAI1D,IAAI,KAAK,CAAb,EAA4B;MAC1B0D,MAAM,CAAC9C,GAAP,GAAasC,IAAI,CAACtC,GAAlB;MACA8C,MAAM,CAACtC,GAAP,GAAa8B,IAAI,CAAC9B,GAAlB;IACD,CAHD,MAGO,IAAIpB,IAAI,KAAK,CAAb,EAA6B;MAClC0D,MAAM,CAAC9C,GAAP,GAAa,YAAY;QACvB,OAAOsC,IAAI,CAAChC,KAAZ;MACD,CAFD;IAGD,CAJM,MAIA;MAEL,IAAIlB,IAAI,KAAK,CAAT,IAA6BA,IAAI,KAAK,CAA1C,EAA0D;QACxD0D,MAAM,CAAC9C,GAAP,GAAa,YAAY;UACvB,OAAOsC,IAAI,CAACtC,GAAL,CAASG,IAAT,CAAc,IAAd,CAAP;QACD,CAFD;MAGD;;MAED,IAAIf,IAAI,KAAK,CAAT,IAA6BA,IAAI,KAAK,CAA1C,EAA0D;QACxD0D,MAAM,CAACtC,GAAP,GAAa,UAAUmC,CAAV,EAAa;UACxBL,IAAI,CAAC9B,GAAL,CAASL,IAAT,CAAc,IAAd,EAAoBwC,CAApB;QACD,CAFD;MAGD;IACF;;IAEDD,GAAG,CAACI,MAAJ,GAAaA,MAAb;EACD,CA7BD,MA6BO;IACLF,YAAY,GAAG,CAAf;IACAC,YAAY,GAAGR,IAAf;EACD;;EAED,IAAI;IACF,OAAOD,GAAG,CACR9B,KADQ,EAERL,MAAM,CAAC8C,MAAP,CACEL,GADF,EAEExD,oCAAoC,CAClCC,WADkC,EAElCyD,YAFkC,EAGlCC,YAHkC,EAIlCvD,oBAJkC,CAFtC,CAFQ,CAAV;EAYD,CAbD,SAaU;IACRA,oBAAoB,CAACqD,CAArB,GAAyB,IAAzB;EACD;AACF;;AAED,SAASlD,qBAAT,CAA+BH,oBAA/B,EAAqD0D,MAArD,EAA6D;EAC3D,IAAI1D,oBAAoB,CAACqD,CAAzB,EAA4B;IAC1B,MAAM,IAAIM,KAAJ,CACJ,uBAAuBD,MAAvB,GAAgC,gCAD5B,CAAN;EAGD;AACF;;AAED,SAAStD,qBAAT,CAA+BF,GAA/B,EAAoC;EAClC,IAAI,OAAOA,GAAP,KAAe,QAAnB,EAA6B;IAC3B,MAAM,IAAI0D,SAAJ,CAAc,8CAA8C1D,GAA5D,CAAN;EACD;AACF;;AAED,SAASyC,kBAAT,CAA4BkB,EAA5B,EAAgCC,IAAhC,EAAsC;EACpC,IAAI,OAAOD,EAAP,KAAc,UAAlB,EAA8B;IAC5B,MAAM,IAAID,SAAJ,CAAcE,IAAI,GAAG,qBAArB,CAAN;EACD;AACF;;AAED,SAASC,0BAAT,CAAoCjE,IAApC,EAA0CkB,KAA1C,EAAiD;EAC/C,IAAIgD,IAAI,GAAG,OAAOhD,KAAlB;;EAEA,IAAIlB,IAAI,KAAK,CAAb,EAA+B;IAC7B,IAAIkE,IAAI,KAAK,QAAT,IAAqBhD,KAAK,KAAK,IAAnC,EAAyC;MACvC,MAAM,IAAI4C,SAAJ,CACJ,uFADI,CAAN;IAGD;;IACD,IAAI5C,KAAK,CAACN,GAAN,KAAcuD,SAAlB,EAA6B;MAC3BtB,kBAAkB,CAAC3B,KAAK,CAACN,GAAP,EAAY,cAAZ,CAAlB;IACD;;IACD,IAAIM,KAAK,CAACE,GAAN,KAAc+C,SAAlB,EAA6B;MAC3BtB,kBAAkB,CAAC3B,KAAK,CAACE,GAAP,EAAY,cAAZ,CAAlB;IACD;;IACD,IAAIF,KAAK,CAACkD,IAAN,KAAeD,SAAnB,EAA8B;MAC5BtB,kBAAkB,CAAC3B,KAAK,CAACkD,IAAP,EAAa,eAAb,CAAlB;IACD;;IACD,IAAIlD,KAAK,CAAC0B,WAAN,KAAsBuB,SAA1B,EAAqC;MACnCtB,kBAAkB,CAAC3B,KAAK,CAAC0B,WAAP,EAAoB,sBAApB,CAAlB;IACD;EACF,CAlBD,MAkBO,IAAIsB,IAAI,KAAK,UAAb,EAAyB;IAC9B,IAAIF,IAAJ;;IACA,IAAIhE,IAAI,KAAK,CAAb,EAA4B;MAC1BgE,IAAI,GAAG,OAAP;IACD,CAFD,MAEO,IAAIhE,IAAI,KAAK,EAAb,EAA6B;MAClCgE,IAAI,GAAG,OAAP;IACD,CAFM,MAEA;MACLA,IAAI,GAAG,QAAP;IACD;;IACD,MAAM,IAAIF,SAAJ,CAAcE,IAAI,GAAG,8CAArB,CAAN;EACD;AACF;;AAED,SAASK,WAAT,CAAqBnB,IAArB,EAA2B;EACzB,IAAIN,WAAJ;;EACA,IACE,CAACA,WAAW,GAAGM,IAAI,CAACkB,IAApB,KAA6B,IAA7B,KACCxB,WAAW,GAAGM,IAAI,CAACN,WADpB,KAEA,OAAO0B,OAAP,KAAmB,WAHrB,EAIE;IACAA,OAAO,CAACC,IAAR,CAAa,yDAAb;EACD;;EACD,OAAO3B,WAAP;AACD;;AAED,SAAS4B,kBAAT,CACEC,GADF,EAEEC,IAFF,EAGEC,OAHF,EAIE1B,IAJF,EAKEjD,IALF,EAMEmD,QANF,EAOEC,SAPF,EAQErD,WARF,EASE2C,YATF,EAUE;EACA,IAAIkC,IAAI,GAAGD,OAAO,CAAC,CAAD,CAAlB;EAEA,IAAIzB,IAAJ,EAAUN,WAAV,EAAuB1B,KAAvB;;EAEA,IAAIkC,SAAJ,EAAe;IACb,IAAIpD,IAAI,KAAK,CAAT,IAA0BA,IAAI,KAAK,CAAvC,EAAyD;MACvDkD,IAAI,GAAG;QACLtC,GAAG,EAAE+D,OAAO,CAAC,CAAD,CADP;QAELvD,GAAG,EAAEuD,OAAO,CAAC,CAAD;MAFP,CAAP;IAID,CALD,MAKO,IAAI3E,IAAI,KAAK,CAAb,EAA6B;MAClCkD,IAAI,GAAG;QACLtC,GAAG,EAAE+D,OAAO,CAAC,CAAD;MADP,CAAP;IAGD,CAJM,MAIA,IAAI3E,IAAI,KAAK,CAAb,EAA6B;MAClCkD,IAAI,GAAG;QACL9B,GAAG,EAAEuD,OAAO,CAAC,CAAD;MADP,CAAP;IAGD,CAJM,MAIA;MACLzB,IAAI,GAAG;QACLhC,KAAK,EAAEyD,OAAO,CAAC,CAAD;MADT,CAAP;IAGD;EACF,CAnBD,MAmBO,IAAI3E,IAAI,KAAK,CAAb,EAA4B;IACjCkD,IAAI,GAAGrC,MAAM,CAACgE,wBAAP,CAAgCH,IAAhC,EAAsCzB,IAAtC,CAAP;EACD;;EAED,IAAIjD,IAAI,KAAK,CAAb,EAA+B;IAC7BkB,KAAK,GAAG;MACNN,GAAG,EAAEsC,IAAI,CAACtC,GADJ;MAENQ,GAAG,EAAE8B,IAAI,CAAC9B;IAFJ,CAAR;EAID,CALD,MAKO,IAAIpB,IAAI,KAAK,CAAb,EAA6B;IAClCkB,KAAK,GAAGgC,IAAI,CAAChC,KAAb;EACD,CAFM,MAEA,IAAIlB,IAAI,KAAK,CAAb,EAA6B;IAClCkB,KAAK,GAAGgC,IAAI,CAACtC,GAAb;EACD,CAFM,MAEA,IAAIZ,IAAI,KAAK,CAAb,EAA6B;IAClCkB,KAAK,GAAGgC,IAAI,CAAC9B,GAAb;EACD;;EAED,IAAI0D,QAAJ,EAAclE,GAAd,EAAmBQ,GAAnB;;EAEA,IAAI,OAAOwD,IAAP,KAAgB,UAApB,EAAgC;IAC9BE,QAAQ,GAAG/B,aAAa,CACtB6B,IADsB,EAEtB3B,IAFsB,EAGtBC,IAHsB,EAItBnD,WAJsB,EAKtB2C,YALsB,EAMtB1C,IANsB,EAOtBmD,QAPsB,EAQtBC,SARsB,EAStBlC,KATsB,CAAxB;;IAYA,IAAI4D,QAAQ,KAAK,KAAK,CAAtB,EAAyB;MACvBb,0BAA0B,CAACjE,IAAD,EAAO8E,QAAP,CAA1B;;MAEA,IAAI9E,IAAI,KAAK,CAAb,EAA4B;QAC1B4C,WAAW,GAAGkC,QAAd;MACD,CAFD,MAEO,IAAI9E,IAAI,KAAK,CAAb,EAA+B;QACpC4C,WAAW,GAAGyB,WAAW,CAACS,QAAD,CAAzB;QACAlE,GAAG,GAAGkE,QAAQ,CAAClE,GAAT,IAAgBM,KAAK,CAACN,GAA5B;QACAQ,GAAG,GAAG0D,QAAQ,CAAC1D,GAAT,IAAgBF,KAAK,CAACE,GAA5B;QAEAF,KAAK,GAAG;UAAEN,GAAG,EAAEA,GAAP;UAAYQ,GAAG,EAAEA;QAAjB,CAAR;MACD,CANM,MAMA;QACLF,KAAK,GAAG4D,QAAR;MACD;IACF;EACF,CA5BD,MA4BO;IACL,KAAK,IAAIhD,CAAC,GAAG8C,IAAI,CAAC/C,MAAL,GAAc,CAA3B,EAA8BC,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;MACzC,IAAIkB,GAAG,GAAG4B,IAAI,CAAC9C,CAAD,CAAd;MAEAgD,QAAQ,GAAG/B,aAAa,CACtBC,GADsB,EAEtBC,IAFsB,EAGtBC,IAHsB,EAItBnD,WAJsB,EAKtB2C,YALsB,EAMtB1C,IANsB,EAOtBmD,QAPsB,EAQtBC,SARsB,EAStBlC,KATsB,CAAxB;;MAYA,IAAI4D,QAAQ,KAAK,KAAK,CAAtB,EAAyB;QACvBb,0BAA0B,CAACjE,IAAD,EAAO8E,QAAP,CAA1B;QACA,IAAIC,OAAJ;;QAEA,IAAI/E,IAAI,KAAK,CAAb,EAA4B;UAC1B+E,OAAO,GAAGD,QAAV;QACD,CAFD,MAEO,IAAI9E,IAAI,KAAK,CAAb,EAA+B;UACpC+E,OAAO,GAAGV,WAAW,CAACS,QAAD,CAArB;UACAlE,GAAG,GAAGkE,QAAQ,CAAClE,GAAT,IAAgBM,KAAK,CAACN,GAA5B;UACAQ,GAAG,GAAG0D,QAAQ,CAAC1D,GAAT,IAAgBF,KAAK,CAACE,GAA5B;UAEAF,KAAK,GAAG;YAAEN,GAAG,EAAEA,GAAP;YAAYQ,GAAG,EAAEA;UAAjB,CAAR;QACD,CANM,MAMA;UACLF,KAAK,GAAG4D,QAAR;QACD;;QAED,IAAIC,OAAO,KAAK,KAAK,CAArB,EAAwB;UACtB,IAAInC,WAAW,KAAK,KAAK,CAAzB,EAA4B;YAC1BA,WAAW,GAAGmC,OAAd;UACD,CAFD,MAEO,IAAI,OAAOnC,WAAP,KAAuB,UAA3B,EAAuC;YAC5CA,WAAW,GAAG,CAACA,WAAD,EAAcmC,OAAd,CAAd;UACD,CAFM,MAEA;YACLnC,WAAW,CAACE,IAAZ,CAAiBiC,OAAjB;UACD;QACF;MACF;IACF;EACF;;EAED,IAAI/E,IAAI,KAAK,CAAT,IAA0BA,IAAI,KAAK,CAAvC,EAAyD;IACvD,IAAI4C,WAAW,KAAK,KAAK,CAAzB,EAA4B;MAE1BA,WAAW,GAAG,UAAUoC,QAAV,EAAoBZ,IAApB,EAA0B;QACtC,OAAOA,IAAP;MACD,CAFD;IAGD,CALD,MAKO,IAAI,OAAOxB,WAAP,KAAuB,UAA3B,EAAuC;MAC5C,IAAIqC,eAAe,GAAGrC,WAAtB;;MAEAA,WAAW,GAAG,UAAUoC,QAAV,EAAoBZ,IAApB,EAA0B;QACtC,IAAIlD,KAAK,GAAGkD,IAAZ;;QAEA,KAAK,IAAItC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGmD,eAAe,CAACpD,MAApC,EAA4CC,CAAC,EAA7C,EAAiD;UAC/CZ,KAAK,GAAG+D,eAAe,CAACnD,CAAD,CAAf,CAAmBf,IAAnB,CAAwBiE,QAAxB,EAAkC9D,KAAlC,CAAR;QACD;;QAED,OAAOA,KAAP;MACD,CARD;IASD,CAZM,MAYA;MACL,IAAIgE,mBAAmB,GAAGtC,WAA1B;;MAEAA,WAAW,GAAG,UAAUoC,QAAV,EAAoBZ,IAApB,EAA0B;QACtC,OAAOc,mBAAmB,CAACnE,IAApB,CAAyBiE,QAAzB,EAAmCZ,IAAnC,CAAP;MACD,CAFD;IAGD;;IAEDK,GAAG,CAAC3B,IAAJ,CAASF,WAAT;EACD;;EAED,IAAI5C,IAAI,KAAK,CAAb,EAA4B;IAC1B,IAAIA,IAAI,KAAK,CAAb,EAA+B;MAC7BkD,IAAI,CAACtC,GAAL,GAAWM,KAAK,CAACN,GAAjB;MACAsC,IAAI,CAAC9B,GAAL,GAAWF,KAAK,CAACE,GAAjB;IACD,CAHD,MAGO,IAAIpB,IAAI,KAAK,CAAb,EAA6B;MAClCkD,IAAI,CAAChC,KAAL,GAAaA,KAAb;IACD,CAFM,MAEA,IAAIlB,IAAI,KAAK,CAAb,EAA6B;MAClCkD,IAAI,CAACtC,GAAL,GAAWM,KAAX;IACD,CAFM,MAEA,IAAIlB,IAAI,KAAK,CAAb,EAA6B;MAClCkD,IAAI,CAAC9B,GAAL,GAAWF,KAAX;IACD;;IAED,IAAIkC,SAAJ,EAAe;MACb,IAAIpD,IAAI,KAAK,CAAb,EAA+B;QAC7ByE,GAAG,CAAC3B,IAAJ,CAAS,UAAUkC,QAAV,EAAoBG,IAApB,EAA0B;UACjC,OAAOjE,KAAK,CAACN,GAAN,CAAUG,IAAV,CAAeiE,QAAf,EAAyBG,IAAzB,CAAP;QACD,CAFD;QAGAV,GAAG,CAAC3B,IAAJ,CAAS,UAAUkC,QAAV,EAAoBG,IAApB,EAA0B;UACjC,OAAOjE,KAAK,CAACE,GAAN,CAAUL,IAAV,CAAeiE,QAAf,EAAyBG,IAAzB,CAAP;QACD,CAFD;MAGD,CAPD,MAOO,IAAInF,IAAI,KAAK,CAAb,EAA6B;QAClCyE,GAAG,CAAC3B,IAAJ,CAAS5B,KAAT;MACD,CAFM,MAEA;QACLuD,GAAG,CAAC3B,IAAJ,CAAS,UAAUkC,QAAV,EAAoBG,IAApB,EAA0B;UACjC,OAAOjE,KAAK,CAACH,IAAN,CAAWiE,QAAX,EAAqBG,IAArB,CAAP;QACD,CAFD;MAGD;IACF,CAfD,MAeO;MACLtE,MAAM,CAACuE,cAAP,CAAsBV,IAAtB,EAA4BzB,IAA5B,EAAkCC,IAAlC;IACD;EACF;AACF;;AAED,SAASmC,mBAAT,CACEZ,GADF,EAEEa,KAFF,EAGEC,gBAHF,EAIEC,iBAJF,EAKEC,QALF,EAME;EACA,IAAIC,iBAAJ;EACA,IAAIC,kBAAJ;EAEA,IAAIC,sBAAsB,GAAG,IAAIzE,GAAJ,EAA7B;EACA,IAAI0E,uBAAuB,GAAG,IAAI1E,GAAJ,EAA9B;;EAEA,KAAK,IAAIW,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG2D,QAAQ,CAAC5D,MAA7B,EAAqCC,CAAC,EAAtC,EAA0C;IACxC,IAAI6C,OAAO,GAAGc,QAAQ,CAAC3D,CAAD,CAAtB;IAGA,IAAI,CAACM,KAAK,CAAC0D,OAAN,CAAcnB,OAAd,CAAL,EAA6B;IAE7B,IAAI3E,IAAI,GAAG2E,OAAO,CAAC,CAAD,CAAlB;IACA,IAAI1B,IAAI,GAAG0B,OAAO,CAAC,CAAD,CAAlB;IACA,IAAIvB,SAAS,GAAGuB,OAAO,CAAC9C,MAAR,GAAiB,CAAjC;IAEA,IAAIsB,QAAQ,GAAGnD,IAAI,IAAI,CAAvB;IACA,IAAI0E,IAAJ;IACA,IAAI3E,WAAJ;IACA,IAAI2C,YAAJ;;IAEA,IAAIS,QAAJ,EAAc;MACZuB,IAAI,GAAGY,KAAP;MACAvF,WAAW,GAAGyF,iBAAd;MACAxF,IAAI,GAAGA,IAAI,GAAG,CAAd;;MAEA,IAAIA,IAAI,KAAK,CAAb,EAA4B;QAC1B2F,kBAAkB,GAAGA,kBAAkB,IAAI,EAA3C;QACAjD,YAAY,GAAGiD,kBAAf;MACD;IACF,CATD,MASO;MACLjB,IAAI,GAAGY,KAAK,CAACS,SAAb;MACAhG,WAAW,GAAGwF,gBAAd;;MAEA,IAAIvF,IAAI,KAAK,CAAb,EAA4B;QAC1B0F,iBAAiB,GAAGA,iBAAiB,IAAI,EAAzC;QACAhD,YAAY,GAAGgD,iBAAf;MACD;IACF;;IAED,IAAI1F,IAAI,KAAK,CAAT,IAA0B,CAACoD,SAA/B,EAA0C;MACxC,IAAI4C,iBAAiB,GAAG7C,QAAQ,GAC5B0C,uBAD4B,GAE5BD,sBAFJ;MAIA,IAAIK,YAAY,GAAGD,iBAAiB,CAACpF,GAAlB,CAAsBqC,IAAtB,KAA+B,CAAlD;;MAEA,IACEgD,YAAY,KAAK,IAAjB,IACCA,YAAY,KAAK,CAAjB,IAAmCjG,IAAI,KAAK,CAD7C,IAECiG,YAAY,KAAK,CAAjB,IAAmCjG,IAAI,KAAK,CAH/C,EAIE;QACA,MAAM,IAAI6D,KAAJ,CACJ,0MACEZ,IAFE,CAAN;MAID,CATD,MASO,IAAI,CAACgD,YAAD,IAAiBjG,IAAI,GAAG,CAA5B,EAA4C;QACjDgG,iBAAiB,CAAC5E,GAAlB,CAAsB6B,IAAtB,EAA4BjD,IAA5B;MACD,CAFM,MAEA;QACLgG,iBAAiB,CAAC5E,GAAlB,CAAsB6B,IAAtB,EAA4B,IAA5B;MACD;IACF;;IAEDuB,kBAAkB,CAChBC,GADgB,EAEhBC,IAFgB,EAGhBC,OAHgB,EAIhB1B,IAJgB,EAKhBjD,IALgB,EAMhBmD,QANgB,EAOhBC,SAPgB,EAQhBrD,WARgB,EAShB2C,YATgB,CAAlB;EAWD;;EAEDwD,oBAAoB,CAACzB,GAAD,EAAMiB,iBAAN,CAApB;EACAQ,oBAAoB,CAACzB,GAAD,EAAMkB,kBAAN,CAApB;AACD;;AAED,SAASO,oBAAT,CAA8BzB,GAA9B,EAAmC/B,YAAnC,EAAiD;EAC/C,IAAIA,YAAJ,EAAkB;IAChB+B,GAAG,CAAC3B,IAAJ,CAAS,UAAUkC,QAAV,EAAoB;MAC3B,KAAK,IAAIlD,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGY,YAAY,CAACb,MAAjC,EAAyCC,CAAC,EAA1C,EAA8C;QAC5CY,YAAY,CAACZ,CAAD,CAAZ,CAAgBf,IAAhB,CAAqBiE,QAArB;MACD;;MACD,OAAOA,QAAP;IACD,CALD;EAMD;AACF;;AAED,SAASmB,kBAAT,CAA4B1B,GAA5B,EAAiC2B,WAAjC,EAA8CrG,WAA9C,EAA2DsG,SAA3D,EAAsE;EACpE,IAAIA,SAAS,CAACxE,MAAV,GAAmB,CAAvB,EAA0B;IACxB,IAAIa,YAAY,GAAG,EAAnB;IACA,IAAI4D,QAAQ,GAAGF,WAAf;IACA,IAAInD,IAAI,GAAGmD,WAAW,CAACnD,IAAvB;;IAEA,KAAK,IAAInB,CAAC,GAAGuE,SAAS,CAACxE,MAAV,GAAmB,CAAhC,EAAmCC,CAAC,IAAI,CAAxC,EAA2CA,CAAC,EAA5C,EAAgD;MAC9C,IAAI5B,oBAAoB,GAAG;QAAEqD,CAAC,EAAE;MAAL,CAA3B;;MAEA,IAAI;QACF,IAAID,GAAG,GAAGzC,MAAM,CAAC8C,MAAP,CACR;UACE3D,IAAI,EAAE,OADR;UAEEiD,IAAI,EAAEA,IAFR;UAGEN,cAAc,EAAEF,8BAA8B,CAC5CC,YAD4C,EAE5CxC,oBAF4C;QAHhD,CADQ,EASRJ,oCAAoC,CAClCC,WADkC,EAElC,CAFkC,EAGlCkD,IAHkC,EAIlC/C,oBAJkC,CAT5B,CAAV;QAgBA,IAAIqG,YAAY,GAAGF,SAAS,CAACvE,CAAD,CAAT,CAAawE,QAAb,EAAuBhD,GAAvB,CAAnB;MACD,CAlBD,SAkBU;QACRpD,oBAAoB,CAACqD,CAArB,GAAyB,IAAzB;MACD;;MAED,IAAIgD,YAAY,KAAKpC,SAArB,EAAgC;QAC9BF,0BAA0B,CAAC,EAAD,EAAiBsC,YAAjB,CAA1B;QACAD,QAAQ,GAAGC,YAAX;MACD;IACF;;IAED9B,GAAG,CAAC3B,IAAJ,CAASwD,QAAT,EAAmB,YAAY;MAC7B,KAAK,IAAIxE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGY,YAAY,CAACb,MAAjC,EAAyCC,CAAC,EAA1C,EAA8C;QAC5CY,YAAY,CAACZ,CAAD,CAAZ,CAAgBf,IAAhB,CAAqBuF,QAArB;MACD;IACF,CAJD;EAKD;AACF;;AAmJc,SAASE,SAAT,CAAmBJ,WAAnB,EAAgCK,UAAhC,EAA4CJ,SAA5C,EAAuD;EACpE,IAAI5B,GAAG,GAAG,EAAV;EACA,IAAIe,iBAAiB,GAAG,EAAxB;EAEA,IAAID,gBAAgB,GAAG,EAAvB;EAEAF,mBAAmB,CACjBZ,GADiB,EAEjB2B,WAFiB,EAGjBb,gBAHiB,EAIjBC,iBAJiB,EAKjBiB,UALiB,CAAnB;EAQApF,6BAA6B,CAAC+E,WAAW,CAACL,SAAb,EAAwBR,gBAAxB,CAA7B;EAEAY,kBAAkB,CAAC1B,GAAD,EAAM2B,WAAN,EAAmBZ,iBAAnB,EAAsCa,SAAtC,CAAlB;EAEAhF,6BAA6B,CAAC+E,WAAD,EAAcZ,iBAAd,CAA7B;EAEA,OAAOf,GAAP;AACD"}