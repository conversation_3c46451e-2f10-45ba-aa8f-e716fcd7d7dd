"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            var status = window.saveButtonStatus;\n            return status === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        // 🔧 关键：只有 save 状态才弹窗，view 状态直接允许离开\n        if (shouldAutoSave()) {\n            // 显示确认对话框\n            var message = \"您有未保存的更改。确定要离开吗？\";\n            event.returnValue = message;\n            // 🔧 优化：使用延迟检测用户选择\n            // 如果用户选择\"离开\"，页面会立即卸载，setTimeout不会执行\n            // 如果用户选择\"取消\"，setTimeout会在用户回到页面后执行自动保存\n            setTimeout(function() {\n                performAutoSave();\n            }, 100);\n            return message;\n        }\n    // 🔧 view 状态：直接允许离开，不弹窗，不调用 useAutoSaveOnLeave\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 🔧 路由变化处理 - 严格按照用户要求的逻辑\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, confirmed;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            4\n                        ];\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                        if (!isNoteNavigation) return [\n                            3,\n                            1\n                        ];\n                        // 🔧 笔记跳转：自动保存，不弹窗，允许正常跳转\n                        isAutoSavingRef.current = true;\n                        // 在后台执行自动保存，不阻止路由跳转\n                        performAutoSave().finally(function() {\n                            isAutoSavingRef.current = false;\n                        });\n                        // 直接允许跳转，不阻止\n                        return [\n                            2\n                        ];\n                    case 1:\n                        // 🔧 非笔记跳转：阻止跳转，弹窗询问\n                        router.events.emit(\"routeChangeError\", new Error(\"User confirmation required\"), url);\n                        confirmed = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed) return [\n                            3,\n                            2\n                        ];\n                        // 🔧 用户选择离开：弃用在indexDB中的修改JSON，直接离开页面\n                        router.push(url);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        // 🔧 用户选择取消：自动保存\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 3:\n                        _state.sent();\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        // 🔧 view 状态：直接允许跳转，不做任何处理，不调用 useAutoSaveOnLeave\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9ob29rcy91c2UtYXV0by1zYXZlLW9uLWxlYXZlLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Ozs7Ozs7O0NBZUMsR0FFRDs7QUFBdUQ7QUFDZjtBQU14QyxJQUFNSSxrQkFBa0IsR0FBRyxXQUE2QztRQUE1Q0MsT0FBa0Msb0VBQUcsRUFBRTtJQUMvRCxlQUEyQkEsT0FBTyxDQUExQkMsT0FBTyxFQUFQQSxPQUFPLHlCQUFHLElBQUk7SUFDdEIsSUFBTUMsTUFBTSxHQUFHSixzREFBUyxFQUFFO0lBQzFCLElBQU1LLGVBQWUsR0FBR04sNkNBQU0sQ0FBQyxLQUFLLENBQUM7SUFHckMsSUFBTU8sY0FBYyxHQUFHUixrREFBVyxDQUFDLFdBQU07UUFDckMsSUFBSSxLQUE2QixJQUFJLE1BQU8sQ0FBU1UsZ0JBQWdCLEVBQUU7WUFDbkUsSUFBTUMsTUFBTSxHQUFHLE1BQU8sQ0FBU0QsZ0JBQWdCO1lBQy9DLE9BQU9DLE1BQU0sS0FBSyxNQUFNLENBQUM7UUFDN0IsQ0FBQztRQUNELE9BQU8sS0FBSyxDQUFDO0lBQ2pCLENBQUMsRUFBRSxFQUFFLENBQUM7SUFFTixJQUFNQyxlQUFlLEdBQUdaLGtEQUFXLGVBQUMsK0ZBQVk7WUFLL0JhLEtBQUs7Ozs7eUJBSmQsTUFBNkIsSUFBSSxNQUFPLENBQVNDLGtCQUFrQixHQUFuRTs7O3NCQUFtRTs7Ozs7Ozs7O29CQUUvRDs7d0JBQU0sTUFBTyxDQUFTQSxrQkFBa0IsRUFBRTtzQkFBQTs7b0JBQTFDLGFBQTBDLENBQUM7b0JBQzNDOzt3QkFBTyxJQUFJO3NCQUFDOztvQkFDUEQsS0FBSztvQkFDVjs7d0JBQU8sS0FBSztzQkFBQzs7b0JBR3JCOzt3QkFBTyxLQUFLO3NCQUFDOzs7SUFDakIsQ0FBQyxHQUFFLEVBQUUsQ0FBQztJQUVOLHFCQUFxQjtJQUNyQixJQUFNRSxrQkFBa0IsR0FBR2Ysa0RBQVcsQ0FBQyxTQUFDZ0IsS0FBd0IsRUFBSztRQUNqRSxJQUFJLENBQUNYLE9BQU8sRUFBRSxPQUFPO1FBRXJCLG1DQUFtQztRQUNuQyxJQUFJRyxjQUFjLEVBQUUsRUFBRTtZQUNsQixVQUFVO1lBQ1YsSUFBTVMsT0FBTyxHQUFHLGtCQUFrQjtZQUNsQ0QsS0FBSyxDQUFDRSxXQUFXLEdBQUdELE9BQU8sQ0FBQztZQUU1QixrQkFBa0I7WUFDbEIsb0NBQW9DO1lBQ3BDLHVDQUF1QztZQUN2Q0UsVUFBVSxDQUFDLFdBQU07Z0JBQ2JQLGVBQWUsRUFBRSxDQUFDO1lBQ3RCLENBQUMsRUFBRSxHQUFHLENBQUMsQ0FBQztZQUVSLE9BQU9LLE9BQU8sQ0FBQztRQUNuQixDQUFDO0lBQ0QsOENBQThDO0lBQ2xELENBQUMsRUFBRTtRQUFDWixPQUFPO1FBQUVHLGNBQWM7UUFBRUksZUFBZTtLQUFDLENBQUM7SUFFOUMseUJBQXlCO0lBQ3pCLElBQU1RLHNCQUFzQixHQUFHcEIsa0RBQVc7bUJBQUMsNkZBQU9xQixHQUFXLEVBQUs7Z0JBTXBEQyxnQkFBZ0IsRUFpQlpDLFNBQVM7Ozs7d0JBdEJ2QixJQUFJLENBQUNsQixPQUFPLElBQUlFLGVBQWUsQ0FBQ2lCLE9BQU8sRUFBRTs7MEJBQU87NkJBRzVDaEIsY0FBYyxFQUFFLEVBQWhCQTs7OzBCQUFnQjt3QkFFVmMsZ0JBQWdCLEdBQUdELEdBQUcsQ0FBQ0ksS0FBSyw0QkFBNEIsSUFBSUosR0FBRyxLQUFLLEdBQUcsSUFBSUEsR0FBRyxDQUFDSyxRQUFRLENBQUMsTUFBTSxDQUFDLENBQUM7NkJBRWxHSixnQkFBZ0IsRUFBaEJBOzs7MEJBQWdCO3dCQUNoQix5QkFBeUI7d0JBQ3pCZixlQUFlLENBQUNpQixPQUFPLEdBQUcsSUFBSSxDQUFDO3dCQUUvQixvQkFBb0I7d0JBQ3BCWixlQUFlLEVBQUUsQ0FBQ2UsT0FBTyxDQUFDLFdBQU07NEJBQzVCcEIsZUFBZSxDQUFDaUIsT0FBTyxHQUFHLEtBQUssQ0FBQzt3QkFDcEMsQ0FBQyxDQUFDLENBQUM7d0JBRUgsYUFBYTt3QkFDYjs7MEJBQU87O3dCQUVQLG9CQUFvQjt3QkFDcEJsQixNQUFNLENBQUNzQixNQUFNLENBQUNDLElBQUksQ0FBQyxrQkFBa0IsRUFBRSxJQUFJQyxLQUFLLENBQUMsNEJBQTRCLENBQUMsRUFBRVQsR0FBRyxDQUFDLENBQUM7d0JBRS9FRSxTQUFTLEdBQUdkLE1BQU0sQ0FBQ3NCLE9BQU8sQ0FBQyxrQkFBa0IsQ0FBQyxDQUFDOzZCQUNqRFIsU0FBUyxFQUFUQTs7OzBCQUFTO3dCQUNULHFDQUFxQzt3QkFDckNqQixNQUFNLENBQUMwQixJQUFJLENBQUNYLEdBQUcsQ0FBQyxDQUFDOzs7Ozs7d0JBRWpCLGdCQUFnQjt3QkFDaEI7OzRCQUFNVCxlQUFlLEVBQUU7MEJBQUE7O3dCQUF2QixhQUF1QixDQUFDOzs7Ozs7OztRQUlwQyxpREFBaUQ7UUFDckQsQ0FBQzt3QkFsQ2lEUyxHQUFXOzs7U0FrQzFEO1FBQUNoQixPQUFPO1FBQUVHLGNBQWM7UUFBRUksZUFBZTtRQUFFTixNQUFNO0tBQUMsQ0FBQztJQUl0RFAsZ0RBQVMsQ0FBQyxXQUFNO1FBQ1osSUFBSSxDQUFDTSxPQUFPLEVBQUUsT0FBTztRQUVyQkksTUFBTSxDQUFDd0IsZ0JBQWdCLENBQUMsY0FBYyxFQUFFbEIsa0JBQWtCLENBQUMsQ0FBQztRQUU1RCxPQUFPLFdBQU07WUFDVE4sTUFBTSxDQUFDeUIsbUJBQW1CLENBQUMsY0FBYyxFQUFFbkIsa0JBQWtCLENBQUMsQ0FBQztRQUNuRSxDQUFDLENBQUM7SUFDTixDQUFDLEVBQUU7UUFBQ1YsT0FBTztRQUFFVSxrQkFBa0I7S0FBQyxDQUFDLENBQUM7SUFFbENoQixnREFBUyxDQUFDLFdBQU07UUFDWixJQUFJLENBQUNNLE9BQU8sRUFBRSxPQUFPO1FBRXJCQyxNQUFNLENBQUNzQixNQUFNLENBQUNPLEVBQUUsQ0FBQyxrQkFBa0IsRUFBRWYsc0JBQXNCLENBQUMsQ0FBQztRQUU3RCxPQUFPLFdBQU07WUFDVGQsTUFBTSxDQUFDc0IsTUFBTSxDQUFDUSxHQUFHLENBQUMsa0JBQWtCLEVBQUVoQixzQkFBc0IsQ0FBQyxDQUFDO1FBQ2xFLENBQUMsQ0FBQztJQUNOLENBQUMsRUFBRTtRQUFDZixPQUFPO1FBQUVlLHNCQUFzQjtRQUFFZCxNQUFNLENBQUNzQixNQUFNO0tBQUMsQ0FBQyxDQUFDO0lBRXJELE9BQU87UUFDSHBCLGNBQWMsRUFBZEEsY0FBYztRQUNkSSxlQUFlLEVBQWZBLGVBQWU7S0FDbEIsQ0FBQztBQUNOLENBQUM7QUFFRCwrREFBZVQsa0JBQWtCLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGlicy93ZWIvaG9va3MvdXNlLWF1dG8tc2F2ZS1vbi1sZWF2ZS50cz9iOTJiIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQXV0byBTYXZlIG9uIExlYXZlIEhvb2tcbiAqXG4gKiBDb3B5cmlnaHQgKGMpIDIwMjUgd2F5Y2FhblxuICogTGljZW5zZWQgdW5kZXIgdGhlIE1JVCBMaWNlbnNlXG4gKlxuICogUGVybWlzc2lvbiBpcyBoZXJlYnkgZ3JhbnRlZCwgZnJlZSBvZiBjaGFyZ2UsIHRvIGFueSBwZXJzb24gb2J0YWluaW5nIGEgY29weVxuICogb2YgdGhpcyBzb2Z0d2FyZSBhbmQgYXNzb2NpYXRlZCBkb2N1bWVudGF0aW9uIGZpbGVzICh0aGUgXCJTb2Z0d2FyZVwiKSwgdG8gZGVhbFxuICogaW4gdGhlIFNvZnR3YXJlIHdpdGhvdXQgcmVzdHJpY3Rpb24sIGluY2x1ZGluZyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0c1xuICogdG8gdXNlLCBjb3B5LCBtb2RpZnksIG1lcmdlLCBwdWJsaXNoLCBkaXN0cmlidXRlLCBzdWJsaWNlbnNlLCBhbmQvb3Igc2VsbFxuICogY29waWVzIG9mIHRoZSBTb2Z0d2FyZSwgYW5kIHRvIHBlcm1pdCBwZXJzb25zIHRvIHdob20gdGhlIFNvZnR3YXJlIGlzXG4gKiBmdXJuaXNoZWQgdG8gZG8gc28sIHN1YmplY3QgdG8gdGhlIGZvbGxvd2luZyBjb25kaXRpb25zOlxuICpcbiAqIFRoZSBhYm92ZSBjb3B5cmlnaHQgbm90aWNlIGFuZCB0aGlzIHBlcm1pc3Npb24gbm90aWNlIHNoYWxsIGJlIGluY2x1ZGVkIGluIGFsbFxuICogY29waWVzIG9yIHN1YnN0YW50aWFsIHBvcnRpb25zIG9mIHRoZSBTb2Z0d2FyZS5cbiAqL1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZUNhbGxiYWNrLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XG5cbmludGVyZmFjZSBVc2VBdXRvU2F2ZU9uTGVhdmVPcHRpb25zIHtcbiAgICBlbmFibGVkPzogYm9vbGVhbjtcbn1cblxuY29uc3QgdXNlQXV0b1NhdmVPbkxlYXZlID0gKG9wdGlvbnM6IFVzZUF1dG9TYXZlT25MZWF2ZU9wdGlvbnMgPSB7fSkgPT4ge1xuICAgIGNvbnN0IHsgZW5hYmxlZCA9IHRydWUgfSA9IG9wdGlvbnM7XG4gICAgY29uc3Qgcm91dGVyID0gdXNlUm91dGVyKCk7XG4gICAgY29uc3QgaXNBdXRvU2F2aW5nUmVmID0gdXNlUmVmKGZhbHNlKTtcblxuXG4gICAgY29uc3Qgc2hvdWxkQXV0b1NhdmUgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiAod2luZG93IGFzIGFueSkuc2F2ZUJ1dHRvblN0YXR1cykge1xuICAgICAgICAgICAgY29uc3Qgc3RhdHVzID0gKHdpbmRvdyBhcyBhbnkpLnNhdmVCdXR0b25TdGF0dXM7XG4gICAgICAgICAgICByZXR1cm4gc3RhdHVzID09PSAnc2F2ZSc7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0sIFtdKTtcblxuICAgIGNvbnN0IHBlcmZvcm1BdXRvU2F2ZSA9IHVzZUNhbGxiYWNrKGFzeW5jICgpID0+IHtcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmICh3aW5kb3cgYXMgYW55KS5zYXZlQnV0dG9uQXV0b1NhdmUpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgYXdhaXQgKHdpbmRvdyBhcyBhbnkpLnNhdmVCdXR0b25BdXRvU2F2ZSgpO1xuICAgICAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH0sIFtdKTtcblxuICAgIC8vIOmhtemdouWFs+mXrS/liLfmlrDlpITnkIYgLSDlvLnnqpfmj5DnpLrmnLrliLZcbiAgICBjb25zdCBoYW5kbGVCZWZvcmVVbmxvYWQgPSB1c2VDYWxsYmFjaygoZXZlbnQ6IEJlZm9yZVVubG9hZEV2ZW50KSA9PiB7XG4gICAgICAgIGlmICghZW5hYmxlZCkgcmV0dXJuO1xuXG4gICAgICAgIC8vIPCflKcg5YWz6ZSu77ya5Y+q5pyJIHNhdmUg54q25oCB5omN5by556qX77yMdmlldyDnirbmgIHnm7TmjqXlhYHorrjnprvlvIBcbiAgICAgICAgaWYgKHNob3VsZEF1dG9TYXZlKCkpIHtcbiAgICAgICAgICAgIC8vIOaYvuekuuehruiupOWvueivneahhlxuICAgICAgICAgICAgY29uc3QgbWVzc2FnZSA9ICfmgqjmnInmnKrkv53lrZjnmoTmm7TmlLnjgILnoa7lrpropoHnprvlvIDlkJfvvJ8nO1xuICAgICAgICAgICAgZXZlbnQucmV0dXJuVmFsdWUgPSBtZXNzYWdlO1xuXG4gICAgICAgICAgICAvLyDwn5SnIOS8mOWMlu+8muS9v+eUqOW7tui/n+ajgOa1i+eUqOaIt+mAieaLqVxuICAgICAgICAgICAgLy8g5aaC5p6c55So5oi36YCJ5oupXCLnprvlvIBcIu+8jOmhtemdouS8mueri+WNs+WNuOi9ve+8jHNldFRpbWVvdXTkuI3kvJrmiafooYxcbiAgICAgICAgICAgIC8vIOWmguaenOeUqOaIt+mAieaLqVwi5Y+W5raIXCLvvIxzZXRUaW1lb3V05Lya5Zyo55So5oi35Zue5Yiw6aG16Z2i5ZCO5omn6KGM6Ieq5Yqo5L+d5a2YXG4gICAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHtcbiAgICAgICAgICAgICAgICBwZXJmb3JtQXV0b1NhdmUoKTtcbiAgICAgICAgICAgIH0sIDEwMCk7XG5cbiAgICAgICAgICAgIHJldHVybiBtZXNzYWdlO1xuICAgICAgICB9XG4gICAgICAgIC8vIPCflKcgdmlldyDnirbmgIHvvJrnm7TmjqXlhYHorrjnprvlvIDvvIzkuI3lvLnnqpfvvIzkuI3osIPnlKggdXNlQXV0b1NhdmVPbkxlYXZlXG4gICAgfSwgW2VuYWJsZWQsIHNob3VsZEF1dG9TYXZlLCBwZXJmb3JtQXV0b1NhdmVdKTtcblxuICAgIC8vIPCflKcg6Lev55Sx5Y+Y5YyW5aSE55CGIC0g5Lil5qC85oyJ54Wn55So5oi36KaB5rGC55qE6YC76L6RXG4gICAgY29uc3QgaGFuZGxlUm91dGVDaGFuZ2VTdGFydCA9IHVzZUNhbGxiYWNrKGFzeW5jICh1cmw6IHN0cmluZykgPT4ge1xuICAgICAgICBpZiAoIWVuYWJsZWQgfHwgaXNBdXRvU2F2aW5nUmVmLmN1cnJlbnQpIHJldHVybjtcblxuICAgICAgICAvLyDwn5SnIOWFs+mUru+8muWPquaciSBzYXZlIOeKtuaAgeaJjeWkhOeQhu+8jHZpZXcg54q25oCB55u05o6l5YWB6K646Lez6L2sXG4gICAgICAgIGlmIChzaG91bGRBdXRvU2F2ZSgpKSB7XG4gICAgICAgICAgICAvLyDwn5SnIOajgOafpeaYr+WQpuaYr+eslOiusOi3s+i9rO+8iOeslOiusElE5Y+R55Sf5Y+Y5Yqo77yJXG4gICAgICAgICAgICBjb25zdCBpc05vdGVOYXZpZ2F0aW9uID0gdXJsLm1hdGNoKC9eXFwvW2EtekEtWjAtOS1dKyhcXD8uKik/JC8pIHx8IHVybCA9PT0gJy8nIHx8IHVybC5pbmNsdWRlcygnP25ldycpO1xuXG4gICAgICAgICAgICBpZiAoaXNOb3RlTmF2aWdhdGlvbikge1xuICAgICAgICAgICAgICAgIC8vIPCflKcg56yU6K6w6Lez6L2s77ya6Ieq5Yqo5L+d5a2Y77yM5LiN5by556qX77yM5YWB6K645q2j5bi46Lez6L2sXG4gICAgICAgICAgICAgICAgaXNBdXRvU2F2aW5nUmVmLmN1cnJlbnQgPSB0cnVlO1xuXG4gICAgICAgICAgICAgICAgLy8g5Zyo5ZCO5Y+w5omn6KGM6Ieq5Yqo5L+d5a2Y77yM5LiN6Zi75q2i6Lev55Sx6Lez6L2sXG4gICAgICAgICAgICAgICAgcGVyZm9ybUF1dG9TYXZlKCkuZmluYWxseSgoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIGlzQXV0b1NhdmluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgICAgICAvLyDnm7TmjqXlhYHorrjot7PovazvvIzkuI3pmLvmraJcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIC8vIPCflKcg6Z2e56yU6K6w6Lez6L2s77ya6Zi75q2i6Lez6L2s77yM5by556qX6K+i6ZeuXG4gICAgICAgICAgICAgICAgcm91dGVyLmV2ZW50cy5lbWl0KCdyb3V0ZUNoYW5nZUVycm9yJywgbmV3IEVycm9yKCdVc2VyIGNvbmZpcm1hdGlvbiByZXF1aXJlZCcpLCB1cmwpO1xuXG4gICAgICAgICAgICAgICAgY29uc3QgY29uZmlybWVkID0gd2luZG93LmNvbmZpcm0oJ+aCqOacieacquS/neWtmOeahOabtOaUueOAguehruWumuimgeemu+W8gOWQl++8nycpO1xuICAgICAgICAgICAgICAgIGlmIChjb25maXJtZWQpIHtcbiAgICAgICAgICAgICAgICAgICAgLy8g8J+UpyDnlKjmiLfpgInmi6nnprvlvIDvvJrlvIPnlKjlnKhpbmRleERC5Lit55qE5L+u5pS5SlNPTu+8jOebtOaOpeemu+W8gOmhtemdolxuICAgICAgICAgICAgICAgICAgICByb3V0ZXIucHVzaCh1cmwpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIPCflKcg55So5oi36YCJ5oup5Y+W5raI77ya6Ieq5Yqo5L+d5a2YXG4gICAgICAgICAgICAgICAgICAgIGF3YWl0IHBlcmZvcm1BdXRvU2F2ZSgpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyDwn5SnIHZpZXcg54q25oCB77ya55u05o6l5YWB6K646Lez6L2s77yM5LiN5YGa5Lu75L2V5aSE55CG77yM5LiN6LCD55SoIHVzZUF1dG9TYXZlT25MZWF2ZVxuICAgIH0sIFtlbmFibGVkLCBzaG91bGRBdXRvU2F2ZSwgcGVyZm9ybUF1dG9TYXZlLCByb3V0ZXJdKTtcblxuXG5cbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAoIWVuYWJsZWQpIHJldHVybjtcblxuICAgICAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcignYmVmb3JldW5sb2FkJywgaGFuZGxlQmVmb3JlVW5sb2FkKTtcblxuICAgICAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZCk7XG4gICAgICAgIH07XG4gICAgfSwgW2VuYWJsZWQsIGhhbmRsZUJlZm9yZVVubG9hZF0pO1xuXG4gICAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICAgICAgaWYgKCFlbmFibGVkKSByZXR1cm47XG5cbiAgICAgICAgcm91dGVyLmV2ZW50cy5vbigncm91dGVDaGFuZ2VTdGFydCcsIGhhbmRsZVJvdXRlQ2hhbmdlU3RhcnQpO1xuXG4gICAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgICAgICByb3V0ZXIuZXZlbnRzLm9mZigncm91dGVDaGFuZ2VTdGFydCcsIGhhbmRsZVJvdXRlQ2hhbmdlU3RhcnQpO1xuICAgICAgICB9O1xuICAgIH0sIFtlbmFibGVkLCBoYW5kbGVSb3V0ZUNoYW5nZVN0YXJ0LCByb3V0ZXIuZXZlbnRzXSk7XG5cbiAgICByZXR1cm4ge1xuICAgICAgICBzaG91bGRBdXRvU2F2ZSxcbiAgICAgICAgcGVyZm9ybUF1dG9TYXZlLFxuICAgIH07XG59O1xuXG5leHBvcnQgZGVmYXVsdCB1c2VBdXRvU2F2ZU9uTGVhdmU7XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlQ2FsbGJhY2siLCJ1c2VSZWYiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRvU2F2ZU9uTGVhdmUiLCJvcHRpb25zIiwiZW5hYmxlZCIsInJvdXRlciIsImlzQXV0b1NhdmluZ1JlZiIsInNob3VsZEF1dG9TYXZlIiwid2luZG93Iiwic2F2ZUJ1dHRvblN0YXR1cyIsInN0YXR1cyIsInBlcmZvcm1BdXRvU2F2ZSIsImVycm9yIiwic2F2ZUJ1dHRvbkF1dG9TYXZlIiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwiZXZlbnQiLCJtZXNzYWdlIiwicmV0dXJuVmFsdWUiLCJzZXRUaW1lb3V0IiwiaGFuZGxlUm91dGVDaGFuZ2VTdGFydCIsInVybCIsImlzTm90ZU5hdmlnYXRpb24iLCJjb25maXJtZWQiLCJjdXJyZW50IiwibWF0Y2giLCJpbmNsdWRlcyIsImZpbmFsbHkiLCJldmVudHMiLCJlbWl0IiwiRXJyb3IiLCJjb25maXJtIiwicHVzaCIsImFkZEV2ZW50TGlzdGVuZXIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwib24iLCJvZmYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});