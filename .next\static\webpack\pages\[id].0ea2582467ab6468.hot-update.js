"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/hooks/use-auto-save-on-leave.ts":
/*!**************************************************!*\
  !*** ./libs/web/hooks/use-auto-save-on-leave.ts ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Auto Save on Leave Hook\n *\n * 🔧 优化后的逻辑：\n * 1. 用户选择离开 = 不触发自动保存 + 清空所有快照\n * 2. 保存成功后 = 清空所有快照\n * 3. 快照对比机制由 SaveButton 负责，通过 JSON-to-JSON 对比判断笔记状态\n * 4. view 状态：不调用任何自动保存逻辑\n * 5. save 状态：根据离开类型采用不同策略\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n */ \n\n\n\nvar useAutoSaveOnLeave = function() {\n    var options = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    var _enabled = options.enabled, enabled = _enabled === void 0 ? true : _enabled;\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var isAutoSavingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var shouldAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.saveButtonStatus) {\n            var status = window.saveButtonStatus;\n            return status === \"save\";\n        }\n        return false;\n    }, []);\n    var performAutoSave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function() {\n        var error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!( true && window.saveButtonAutoSave)) return [\n                        3,\n                        4\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    return [\n                        4,\n                        window.saveButtonAutoSave()\n                    ];\n                case 2:\n                    _state.sent();\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), []);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        if ( true && window.clearSnapshots) {\n            try {\n                window.clearSnapshots();\n            } catch (error) {\n                console.error(\"清空快照失败:\", error);\n            }\n        }\n    }, []);\n    // 页面关闭/刷新处理 - 弹窗提示机制\n    var handleBeforeUnload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(event) {\n        if (!enabled) return;\n        // 只有 save 状态才弹窗\n        if (shouldAutoSave()) {\n            // 显示确认对话框\n            var message = \"您有未保存的更改。确定要离开吗？\";\n            event.returnValue = message;\n            // 🔧 优化：使用延迟检测用户选择\n            setTimeout(function() {\n                // 如果能执行到这里，说明用户选择了\"取消\"，执行自动保存\n                performAutoSave();\n            }, 100);\n            // 🔧 新增：如果用户选择\"离开\"，页面会立即卸载，在 unload 事件中清空快照\n            var handleUnload = function() {\n                clearAllSnapshots();\n                window.removeEventListener(\"unload\", handleUnload);\n            };\n            window.addEventListener(\"unload\", handleUnload);\n            return message;\n        }\n    // view 状态：直接允许离开，不弹窗\n    }, [\n        enabled,\n        shouldAutoSave,\n        performAutoSave\n    ]);\n    // 路由变化处理 - 严格按照用户要求\n    var handleRouteChangeStart = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(function(url) {\n            var isNoteNavigation, confirmed;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!enabled || isAutoSavingRef.current) return [\n                            2\n                        ];\n                        if (!shouldAutoSave()) return [\n                            3,\n                            4\n                        ];\n                        isNoteNavigation = url.match(/^\\/[a-zA-Z0-9-]+(\\?.*)?$/) || url === \"/\" || url.includes(\"?new\");\n                        if (!isNoteNavigation) return [\n                            3,\n                            1\n                        ];\n                        // 笔记跳转：自动保存，不弹窗，不阻止跳转\n                        isAutoSavingRef.current = true;\n                        // 在后台执行自动保存，不阻止路由跳转\n                        performAutoSave().finally(function() {\n                            isAutoSavingRef.current = false;\n                        });\n                        // 直接允许跳转，不阻止\n                        return [\n                            2\n                        ];\n                    case 1:\n                        // 非笔记跳转：阻止跳转，弹窗询问\n                        router.events.emit(\"routeChangeError\", new Error(\"User confirmation required\"), url);\n                        confirmed = window.confirm(\"您有未保存的更改。确定要离开吗？\");\n                        if (!confirmed) return [\n                            3,\n                            2\n                        ];\n                        // 🔧 用户选择离开：不触发自动保存，清空所有快照，直接跳转\n                        clearAllSnapshots();\n                        router.push(url);\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        // 🔧 用户选择取消：自动保存\n                        return [\n                            4,\n                            performAutoSave()\n                        ];\n                    case 3:\n                        _state.sent();\n                        _state.label = 4;\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        // view 状态：直接允许跳转，不做任何处理\n        });\n        return function(url) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        enabled,\n        shouldAutoSave,\n        performAutoSave,\n        router\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        window.addEventListener(\"beforeunload\", handleBeforeUnload);\n        return function() {\n            window.removeEventListener(\"beforeunload\", handleBeforeUnload);\n        };\n    }, [\n        enabled,\n        handleBeforeUnload\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function() {\n        if (!enabled) return;\n        router.events.on(\"routeChangeStart\", handleRouteChangeStart);\n        return function() {\n            router.events.off(\"routeChangeStart\", handleRouteChangeStart);\n        };\n    }, [\n        enabled,\n        handleRouteChangeStart,\n        router.events\n    ]);\n    return {\n        shouldAutoSave: shouldAutoSave,\n        performAutoSave: performAutoSave\n    };\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (useAutoSaveOnLeave);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/hooks/use-auto-save-on-leave.ts\n"));

/***/ })

});