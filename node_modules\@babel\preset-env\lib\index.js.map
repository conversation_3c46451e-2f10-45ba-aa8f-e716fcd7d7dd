{"version": 3, "names": ["pluginCoreJS2", "_pluginCoreJS2", "default", "pluginCoreJS3", "_pluginCoreJS3", "pluginRegenerator", "_pluginRegenerator", "isPluginRequired", "targets", "support", "isRequired", "compatData", "filterStageFromList", "list", "stageList", "Object", "keys", "reduce", "result", "item", "has", "pluginLists", "withProposals", "withoutBugfixes", "pluginsList", "withBugfixes", "assign", "pluginsBugfixesList", "withoutProposals", "proposalPlugins", "getPluginList", "proposals", "bugfixes", "getPlugin", "pluginName", "plugin", "availablePlugins", "Error", "transformIncludesAndExcludes", "opts", "opt", "target", "match", "add", "all", "plugins", "Set", "builtIns", "getModulesPluginNames", "modules", "transformations", "shouldTransformESM", "shouldTransformDynamicImport", "shouldTransformExportNamespaceFrom", "shouldParseTopLevelAwait", "modulesPluginNames", "push", "console", "warn", "getPolyfillPlugins", "useBuiltIns", "corejs", "polyfillTargets", "include", "exclude", "shippedProposals", "regenerator", "debug", "polyfillPlugins", "pluginOptions", "method", "version", "toString", "undefined", "major", "legacyBabelPolyfillPlugin", "usage", "deprecated", "removeRegeneratorEntryPlugin", "getLocalTargets", "optionsTargets", "ignoreBrowserslistConfig", "config<PERSON><PERSON>", "browserslistEnv", "<PERSON><PERSON><PERSON><PERSON>", "browsers", "getTargets", "supportsStaticESM", "caller", "supportsDynamicImport", "supportsExportNamespaceFrom", "supportsTopLevelAwait", "declarePreset", "api", "assertVersion", "babelTargets", "optionsExclude", "forceAllTransforms", "optionsInclude", "loose", "spec", "normalizeOptions", "semver", "lt", "hasUglifyTarget", "uglify", "transformTargets", "shouldSkipExportNamespaceFrom", "includes", "excludes", "moduleTransformations", "pluginNames", "filterItems", "getOptionSpecificExcludesFor", "pluginSyntaxMap", "removeUnnecessaryItems", "overlappingPlugins", "removeUnsupportedItems", "addProposalSyntaxPlugins", "proposalSyntaxPlugins", "pluginUseBuiltIns", "Array", "from", "map", "concat", "log", "JSON", "stringify", "prettifyTargets", "for<PERSON>ach", "logPlugin"], "sources": ["../src/index.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport { logPlugin } from \"./debug\";\nimport getOptionSpecificExcludesFor from \"./get-option-specific-excludes\";\nimport {\n  addProposalSyntaxPlugins,\n  removeUnnecessaryItems,\n  removeUnsupportedItems,\n} from \"./filter-items\";\nimport moduleTransformations from \"./module-transformations\";\nimport normalizeOptions from \"./normalize-options\";\nimport {\n  pluginSyntaxMap,\n  proposalPlugins,\n  proposalSyntaxPlugins,\n} from \"./shipped-proposals\";\nimport {\n  plugins as pluginsList,\n  pluginsBugfixes as pluginsBugfixesList,\n  overlappingPlugins,\n} from \"./plugins-compat-data\";\n\nimport removeRegeneratorEntryPlugin from \"./polyfills/regenerator\";\nimport legacyBabelPolyfillPlugin from \"./polyfills/babel-polyfill\";\n\nimport type { CallerMetadata } from \"@babel/core\";\n\nimport _pluginCoreJS2 from \"babel-plugin-polyfill-corejs2\";\nimport _pluginCoreJS3 from \"babel-plugin-polyfill-corejs3\";\nimport _pluginRegenerator from \"babel-plugin-polyfill-regenerator\";\nconst pluginCoreJS2 = _pluginCoreJS2.default || _pluginCoreJS2;\nconst pluginCoreJS3 = _pluginCoreJS3.default || _pluginCoreJS3;\nconst pluginRegenerator = _pluginRegenerator.default || _pluginRegenerator;\n\nimport getTargets, {\n  prettifyTargets,\n  filterItems,\n  isRequired,\n} from \"@babel/helper-compilation-targets\";\nimport type { Targets, InputTargets } from \"@babel/helper-compilation-targets\";\nimport availablePlugins from \"./available-plugins\";\nimport { declarePreset } from \"@babel/helper-plugin-utils\";\n\ntype ModuleTransformationsType =\n  typeof import(\"./module-transformations\").default;\nimport type { BuiltInsOption, ModuleOption, Options } from \"./types\";\n\n// TODO: Remove in Babel 8\nexport function isPluginRequired(targets: Targets, support: Targets) {\n  return isRequired(\"fake-name\", targets, {\n    compatData: { \"fake-name\": support },\n  });\n}\n\nfunction filterStageFromList(\n  list: { [feature: string]: Targets },\n  stageList: Set<string>,\n) {\n  return Object.keys(list).reduce((result, item) => {\n    if (!stageList.has(item)) {\n      // @ts-expect-error todo: refine result types\n      result[item] = list[item];\n    }\n\n    return result;\n  }, {});\n}\n\nconst pluginLists = {\n  withProposals: {\n    withoutBugfixes: pluginsList,\n    withBugfixes: Object.assign({}, pluginsList, pluginsBugfixesList),\n  },\n  withoutProposals: {\n    withoutBugfixes: filterStageFromList(pluginsList, proposalPlugins),\n    withBugfixes: filterStageFromList(\n      Object.assign({}, pluginsList, pluginsBugfixesList),\n      proposalPlugins,\n    ),\n  },\n};\n\nfunction getPluginList(proposals: boolean, bugfixes: boolean) {\n  if (proposals) {\n    if (bugfixes) return pluginLists.withProposals.withBugfixes;\n    else return pluginLists.withProposals.withoutBugfixes;\n  } else {\n    if (bugfixes) return pluginLists.withoutProposals.withBugfixes;\n    else return pluginLists.withoutProposals.withoutBugfixes;\n  }\n}\n\nconst getPlugin = (pluginName: string) => {\n  const plugin =\n    // @ts-expect-error plugin name is constructed from available plugin list\n    availablePlugins[pluginName]();\n\n  if (!plugin) {\n    throw new Error(\n      `Could not find plugin \"${pluginName}\". Ensure there is an entry in ./available-plugins.js for it.`,\n    );\n  }\n\n  return plugin;\n};\n\nexport const transformIncludesAndExcludes = (opts: Array<string>): any => {\n  return opts.reduce(\n    (result, opt) => {\n      const target = opt.match(/^(es|es6|es7|esnext|web)\\./)\n        ? \"builtIns\"\n        : \"plugins\";\n      result[target].add(opt);\n      return result;\n    },\n    {\n      all: opts,\n      plugins: new Set(),\n      builtIns: new Set(),\n    },\n  );\n};\n\nexport const getModulesPluginNames = ({\n  modules,\n  transformations,\n  shouldTransformESM,\n  shouldTransformDynamicImport,\n  shouldTransformExportNamespaceFrom,\n  shouldParseTopLevelAwait,\n}: {\n  modules: ModuleOption;\n  transformations: ModuleTransformationsType;\n  shouldTransformESM: boolean;\n  shouldTransformDynamicImport: boolean;\n  shouldTransformExportNamespaceFrom: boolean;\n  shouldParseTopLevelAwait: boolean;\n}) => {\n  const modulesPluginNames = [];\n  if (modules !== false && transformations[modules]) {\n    if (shouldTransformESM) {\n      modulesPluginNames.push(transformations[modules]);\n    }\n\n    if (\n      shouldTransformDynamicImport &&\n      shouldTransformESM &&\n      modules !== \"umd\"\n    ) {\n      modulesPluginNames.push(\"transform-dynamic-import\");\n    } else {\n      if (shouldTransformDynamicImport) {\n        console.warn(\n          \"Dynamic import can only be supported when transforming ES modules\" +\n            \" to AMD, CommonJS or SystemJS. Only the parser plugin will be enabled.\",\n        );\n      }\n      modulesPluginNames.push(\"syntax-dynamic-import\");\n    }\n  } else {\n    modulesPluginNames.push(\"syntax-dynamic-import\");\n  }\n\n  if (shouldTransformExportNamespaceFrom) {\n    modulesPluginNames.push(\"transform-export-namespace-from\");\n  } else {\n    modulesPluginNames.push(\"syntax-export-namespace-from\");\n  }\n\n  if (shouldParseTopLevelAwait) {\n    modulesPluginNames.push(\"syntax-top-level-await\");\n  }\n\n  return modulesPluginNames;\n};\n\nexport const getPolyfillPlugins = ({\n  useBuiltIns,\n  corejs,\n  polyfillTargets,\n  include,\n  exclude,\n  proposals,\n  shippedProposals,\n  regenerator,\n  debug,\n}: {\n  useBuiltIns: BuiltInsOption;\n  corejs: SemVer | null | false;\n  polyfillTargets: Targets;\n  include: Set<string>;\n  exclude: Set<string>;\n  proposals: boolean;\n  shippedProposals: boolean;\n  regenerator: boolean;\n  debug: boolean;\n}) => {\n  const polyfillPlugins = [];\n  if (useBuiltIns === \"usage\" || useBuiltIns === \"entry\") {\n    const pluginOptions = {\n      method: `${useBuiltIns}-global`,\n      version: corejs ? corejs.toString() : undefined,\n      targets: polyfillTargets,\n      include,\n      exclude,\n      proposals,\n      shippedProposals,\n      debug,\n    };\n\n    if (corejs) {\n      if (useBuiltIns === \"usage\") {\n        if (corejs.major === 2) {\n          polyfillPlugins.push(\n            [pluginCoreJS2, pluginOptions],\n            [legacyBabelPolyfillPlugin, { usage: true }],\n          );\n        } else {\n          polyfillPlugins.push(\n            [pluginCoreJS3, pluginOptions],\n            [legacyBabelPolyfillPlugin, { usage: true, deprecated: true }],\n          );\n        }\n        if (regenerator) {\n          polyfillPlugins.push([\n            pluginRegenerator,\n            { method: \"usage-global\", debug },\n          ]);\n        }\n      } else {\n        if (corejs.major === 2) {\n          polyfillPlugins.push(\n            [legacyBabelPolyfillPlugin, { regenerator }],\n            [pluginCoreJS2, pluginOptions],\n          );\n        } else {\n          polyfillPlugins.push(\n            [pluginCoreJS3, pluginOptions],\n            [legacyBabelPolyfillPlugin, { deprecated: true }],\n          );\n          if (!regenerator) {\n            polyfillPlugins.push([removeRegeneratorEntryPlugin, pluginOptions]);\n          }\n        }\n      }\n    }\n  }\n  return polyfillPlugins;\n};\n\nfunction getLocalTargets(\n  optionsTargets: Options[\"targets\"],\n  ignoreBrowserslistConfig: boolean,\n  configPath: string,\n  browserslistEnv: string,\n) {\n  if (optionsTargets?.esmodules && optionsTargets.browsers) {\n    console.warn(`\n@babel/preset-env: esmodules and browsers targets have been specified together.\n\\`browsers\\` target, \\`${optionsTargets.browsers.toString()}\\` will be ignored.\n`);\n  }\n\n  return getTargets(optionsTargets as InputTargets, {\n    ignoreBrowserslistConfig,\n    configPath,\n    browserslistEnv,\n  });\n}\n\nfunction supportsStaticESM(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsStaticESM is not defined in CallerMetadata\n  return !!caller?.supportsStaticESM;\n}\n\nfunction supportsDynamicImport(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsDynamicImport is not defined in CallerMetadata\n  return !!caller?.supportsDynamicImport;\n}\n\nfunction supportsExportNamespaceFrom(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsExportNamespaceFrom is not defined in CallerMetadata\n  return !!caller?.supportsExportNamespaceFrom;\n}\n\nfunction supportsTopLevelAwait(caller: CallerMetadata | undefined) {\n  // @ts-expect-error supportsTopLevelAwait is not defined in CallerMetadata\n  return !!caller?.supportsTopLevelAwait;\n}\n\nexport default declarePreset((api, opts: Options) => {\n  api.assertVersion(7);\n\n  const babelTargets = api.targets();\n\n  const {\n    bugfixes,\n    configPath,\n    debug,\n    exclude: optionsExclude,\n    forceAllTransforms,\n    ignoreBrowserslistConfig,\n    include: optionsInclude,\n    loose,\n    modules,\n    shippedProposals,\n    spec,\n    targets: optionsTargets,\n    useBuiltIns,\n    corejs: { version: corejs, proposals },\n    browserslistEnv,\n  } = normalizeOptions(opts);\n\n  let targets = babelTargets;\n\n  if (\n    // @babel/core < 7.13.0 doesn't load targets (api.targets() always\n    // returns {} thanks to @babel/helper-plugin-utils), so we always want\n    // to fallback to the old targets behavior in this case.\n    semver.lt(api.version, \"7.13.0\") ||\n    // If any browserslist-related option is specified, fallback to the old\n    // behavior of not using the targets specified in the top-level options.\n    opts.targets ||\n    opts.configPath ||\n    opts.browserslistEnv ||\n    opts.ignoreBrowserslistConfig\n  ) {\n    if (!process.env.BABEL_8_BREAKING) {\n      // eslint-disable-next-line no-var\n      var hasUglifyTarget = false;\n\n      if (optionsTargets?.uglify) {\n        hasUglifyTarget = true;\n        delete optionsTargets.uglify;\n\n        console.warn(`\nThe uglify target has been deprecated. Set the top level\noption \\`forceAllTransforms: true\\` instead.\n`);\n      }\n    }\n\n    targets = getLocalTargets(\n      optionsTargets,\n      ignoreBrowserslistConfig,\n      configPath,\n      browserslistEnv,\n    );\n  }\n\n  const transformTargets = (\n    process.env.BABEL_8_BREAKING\n      ? forceAllTransforms\n      : forceAllTransforms || hasUglifyTarget\n  )\n    ? ({} as Targets)\n    : targets;\n\n  const include = transformIncludesAndExcludes(optionsInclude);\n  const exclude = transformIncludesAndExcludes(optionsExclude);\n\n  const compatData = getPluginList(shippedProposals, bugfixes);\n  const shouldSkipExportNamespaceFrom =\n    (modules === \"auto\" && api.caller?.(supportsExportNamespaceFrom)) ||\n    (modules === false &&\n      !isRequired(\"transform-export-namespace-from\", transformTargets, {\n        compatData,\n        includes: include.plugins,\n        excludes: exclude.plugins,\n      }));\n  const modulesPluginNames = getModulesPluginNames({\n    modules,\n    transformations: moduleTransformations,\n    // TODO: Remove the 'api.caller' check eventually. Just here to prevent\n    // unnecessary breakage in the short term for users on older betas/RCs.\n    shouldTransformESM: modules !== \"auto\" || !api.caller?.(supportsStaticESM),\n    shouldTransformDynamicImport:\n      modules !== \"auto\" || !api.caller?.(supportsDynamicImport),\n    shouldTransformExportNamespaceFrom: !shouldSkipExportNamespaceFrom,\n    shouldParseTopLevelAwait:\n      !api.caller || (api.caller(supportsTopLevelAwait) as boolean),\n  });\n\n  const pluginNames = filterItems(\n    compatData,\n    include.plugins,\n    exclude.plugins,\n    transformTargets,\n    modulesPluginNames,\n    getOptionSpecificExcludesFor({ loose }),\n    pluginSyntaxMap,\n  );\n  removeUnnecessaryItems(pluginNames, overlappingPlugins);\n  removeUnsupportedItems(pluginNames, api.version);\n  if (shippedProposals) {\n    addProposalSyntaxPlugins(pluginNames, proposalSyntaxPlugins);\n  }\n\n  const polyfillPlugins = getPolyfillPlugins({\n    useBuiltIns,\n    corejs,\n    polyfillTargets: targets,\n    include: include.builtIns,\n    exclude: exclude.builtIns,\n    proposals,\n    shippedProposals,\n    regenerator: pluginNames.has(\"transform-regenerator\"),\n    debug,\n  });\n\n  const pluginUseBuiltIns = useBuiltIns !== false;\n  const plugins = Array.from(pluginNames)\n    .map(pluginName => {\n      if (\n        pluginName === \"transform-class-properties\" ||\n        pluginName === \"transform-private-methods\" ||\n        pluginName === \"transform-private-property-in-object\"\n      ) {\n        return [\n          getPlugin(pluginName),\n          {\n            loose: loose\n              ? \"#__internal__@babel/preset-env__prefer-true-but-false-is-ok-if-it-prevents-an-error\"\n              : \"#__internal__@babel/preset-env__prefer-false-but-true-is-ok-if-it-prevents-an-error\",\n          },\n        ];\n      }\n      return [\n        getPlugin(pluginName),\n        { spec, loose, useBuiltIns: pluginUseBuiltIns },\n      ];\n    })\n    .concat(polyfillPlugins);\n\n  if (debug) {\n    console.log(\"@babel/preset-env: `DEBUG` option\");\n    console.log(\"\\nUsing targets:\");\n    console.log(JSON.stringify(prettifyTargets(targets), null, 2));\n    console.log(`\\nUsing modules transform: ${modules.toString()}`);\n    console.log(\"\\nUsing plugins:\");\n    pluginNames.forEach(pluginName => {\n      logPlugin(pluginName, targets, compatData);\n    });\n\n    if (!useBuiltIns) {\n      console.log(\n        \"\\nUsing polyfills: No polyfills were added, since the `useBuiltIns` option was not set.\",\n      );\n    }\n  }\n\n  return { plugins };\n});\n"], "mappings": ";;;;;;;;;AAAA;;AACA;;AACA;;AACA;;AAKA;;AACA;;AACA;;AAKA;;AAMA;;AACA;;AAIA;;AACA;;AACA;;AAKA;;AAMA;;AACA;;AAXA,MAAMA,aAAa,GAAGC,0BAAc,CAACC,OAAf,IAA0BD,0BAAhD;AACA,MAAME,aAAa,GAAGC,2BAAc,CAACF,OAAf,IAA0BE,2BAAhD;AACA,MAAMC,iBAAiB,GAAGC,+BAAkB,CAACJ,OAAnB,IAA8BI,+BAAxD;;AAgBO,SAASC,gBAAT,CAA0BC,OAA1B,EAA4CC,OAA5C,EAA8D;EACnE,OAAO,IAAAC,oCAAA,EAAW,WAAX,EAAwBF,OAAxB,EAAiC;IACtCG,UAAU,EAAE;MAAE,aAAaF;IAAf;EAD0B,CAAjC,CAAP;AAGD;;AAED,SAASG,mBAAT,CACEC,IADF,EAEEC,SAFF,EAGE;EACA,OAAOC,MAAM,CAACC,IAAP,CAAYH,IAAZ,EAAkBI,MAAlB,CAAyB,CAACC,MAAD,EAASC,IAAT,KAAkB;IAChD,IAAI,CAACL,SAAS,CAACM,GAAV,CAAcD,IAAd,CAAL,EAA0B;MAExBD,MAAM,CAACC,IAAD,CAAN,GAAeN,IAAI,CAACM,IAAD,CAAnB;IACD;;IAED,OAAOD,MAAP;EACD,CAPM,EAOJ,EAPI,CAAP;AAQD;;AAED,MAAMG,WAAW,GAAG;EAClBC,aAAa,EAAE;IACbC,eAAe,EAAEC,0BADJ;IAEbC,YAAY,EAAEV,MAAM,CAACW,MAAP,CAAc,EAAd,EAAkBF,0BAAlB,EAA+BG,kCAA/B;EAFD,CADG;EAKlBC,gBAAgB,EAAE;IAChBL,eAAe,EAAEX,mBAAmB,CAACY,0BAAD,EAAcK,iCAAd,CADpB;IAEhBJ,YAAY,EAAEb,mBAAmB,CAC/BG,MAAM,CAACW,MAAP,CAAc,EAAd,EAAkBF,0BAAlB,EAA+BG,kCAA/B,CAD+B,EAE/BE,iCAF+B;EAFjB;AALA,CAApB;;AAcA,SAASC,aAAT,CAAuBC,SAAvB,EAA2CC,QAA3C,EAA8D;EAC5D,IAAID,SAAJ,EAAe;IACb,IAAIC,QAAJ,EAAc,OAAOX,WAAW,CAACC,aAAZ,CAA0BG,YAAjC,CAAd,KACK,OAAOJ,WAAW,CAACC,aAAZ,CAA0BC,eAAjC;EACN,CAHD,MAGO;IACL,IAAIS,QAAJ,EAAc,OAAOX,WAAW,CAACO,gBAAZ,CAA6BH,YAApC,CAAd,KACK,OAAOJ,WAAW,CAACO,gBAAZ,CAA6BL,eAApC;EACN;AACF;;AAED,MAAMU,SAAS,GAAIC,UAAD,IAAwB;EACxC,MAAMC,MAAM,GAEVC,yBAAA,CAAiBF,UAAjB,GAFF;;EAIA,IAAI,CAACC,MAAL,EAAa;IACX,MAAM,IAAIE,KAAJ,CACH,0BAAyBH,UAAW,+DADjC,CAAN;EAGD;;EAED,OAAOC,MAAP;AACD,CAZD;;AAcO,MAAMG,4BAA4B,GAAIC,IAAD,IAA8B;EACxE,OAAOA,IAAI,CAACtB,MAAL,CACL,CAACC,MAAD,EAASsB,GAAT,KAAiB;IACf,MAAMC,MAAM,GAAGD,GAAG,CAACE,KAAJ,CAAU,4BAAV,IACX,UADW,GAEX,SAFJ;IAGAxB,MAAM,CAACuB,MAAD,CAAN,CAAeE,GAAf,CAAmBH,GAAnB;IACA,OAAOtB,MAAP;EACD,CAPI,EAQL;IACE0B,GAAG,EAAEL,IADP;IAEEM,OAAO,EAAE,IAAIC,GAAJ,EAFX;IAGEC,QAAQ,EAAE,IAAID,GAAJ;EAHZ,CARK,CAAP;AAcD,CAfM;;;;AAiBA,MAAME,qBAAqB,GAAG,CAAC;EACpCC,OADoC;EAEpCC,eAFoC;EAGpCC,kBAHoC;EAIpCC,4BAJoC;EAKpCC,kCALoC;EAMpCC;AANoC,CAAD,KAc/B;EACJ,MAAMC,kBAAkB,GAAG,EAA3B;;EACA,IAAIN,OAAO,KAAK,KAAZ,IAAqBC,eAAe,CAACD,OAAD,CAAxC,EAAmD;IACjD,IAAIE,kBAAJ,EAAwB;MACtBI,kBAAkB,CAACC,IAAnB,CAAwBN,eAAe,CAACD,OAAD,CAAvC;IACD;;IAED,IACEG,4BAA4B,IAC5BD,kBADA,IAEAF,OAAO,KAAK,KAHd,EAIE;MACAM,kBAAkB,CAACC,IAAnB,CAAwB,0BAAxB;IACD,CAND,MAMO;MACL,IAAIJ,4BAAJ,EAAkC;QAChCK,OAAO,CAACC,IAAR,CACE,sEACE,wEAFJ;MAID;;MACDH,kBAAkB,CAACC,IAAnB,CAAwB,uBAAxB;IACD;EACF,CApBD,MAoBO;IACLD,kBAAkB,CAACC,IAAnB,CAAwB,uBAAxB;EACD;;EAED,IAAIH,kCAAJ,EAAwC;IACtCE,kBAAkB,CAACC,IAAnB,CAAwB,iCAAxB;EACD,CAFD,MAEO;IACLD,kBAAkB,CAACC,IAAnB,CAAwB,8BAAxB;EACD;;EAED,IAAIF,wBAAJ,EAA8B;IAC5BC,kBAAkB,CAACC,IAAnB,CAAwB,wBAAxB;EACD;;EAED,OAAOD,kBAAP;AACD,CAnDM;;;;AAqDA,MAAMI,kBAAkB,GAAG,CAAC;EACjCC,WADiC;EAEjCC,MAFiC;EAGjCC,eAHiC;EAIjCC,OAJiC;EAKjCC,OALiC;EAMjCjC,SANiC;EAOjCkC,gBAPiC;EAQjCC,WARiC;EASjCC;AATiC,CAAD,KAoB5B;EACJ,MAAMC,eAAe,GAAG,EAAxB;;EACA,IAAIR,WAAW,KAAK,OAAhB,IAA2BA,WAAW,KAAK,OAA/C,EAAwD;IACtD,MAAMS,aAAa,GAAG;MACpBC,MAAM,EAAG,GAAEV,WAAY,SADH;MAEpBW,OAAO,EAAEV,MAAM,GAAGA,MAAM,CAACW,QAAP,EAAH,GAAuBC,SAFlB;MAGpBjE,OAAO,EAAEsD,eAHW;MAIpBC,OAJoB;MAKpBC,OALoB;MAMpBjC,SANoB;MAOpBkC,gBAPoB;MAQpBE;IARoB,CAAtB;;IAWA,IAAIN,MAAJ,EAAY;MACV,IAAID,WAAW,KAAK,OAApB,EAA6B;QAC3B,IAAIC,MAAM,CAACa,KAAP,KAAiB,CAArB,EAAwB;UACtBN,eAAe,CAACZ,IAAhB,CACE,CAACxD,aAAD,EAAgBqE,aAAhB,CADF,EAEE,CAACM,sBAAD,EAA4B;YAAEC,KAAK,EAAE;UAAT,CAA5B,CAFF;QAID,CALD,MAKO;UACLR,eAAe,CAACZ,IAAhB,CACE,CAACrD,aAAD,EAAgBkE,aAAhB,CADF,EAEE,CAACM,sBAAD,EAA4B;YAAEC,KAAK,EAAE,IAAT;YAAeC,UAAU,EAAE;UAA3B,CAA5B,CAFF;QAID;;QACD,IAAIX,WAAJ,EAAiB;UACfE,eAAe,CAACZ,IAAhB,CAAqB,CACnBnD,iBADmB,EAEnB;YAAEiE,MAAM,EAAE,cAAV;YAA0BH;UAA1B,CAFmB,CAArB;QAID;MACF,CAlBD,MAkBO;QACL,IAAIN,MAAM,CAACa,KAAP,KAAiB,CAArB,EAAwB;UACtBN,eAAe,CAACZ,IAAhB,CACE,CAACmB,sBAAD,EAA4B;YAAET;UAAF,CAA5B,CADF,EAEE,CAAClE,aAAD,EAAgBqE,aAAhB,CAFF;QAID,CALD,MAKO;UACLD,eAAe,CAACZ,IAAhB,CACE,CAACrD,aAAD,EAAgBkE,aAAhB,CADF,EAEE,CAACM,sBAAD,EAA4B;YAAEE,UAAU,EAAE;UAAd,CAA5B,CAFF;;UAIA,IAAI,CAACX,WAAL,EAAkB;YAChBE,eAAe,CAACZ,IAAhB,CAAqB,CAACsB,oBAAD,EAA+BT,aAA/B,CAArB;UACD;QACF;MACF;IACF;EACF;;EACD,OAAOD,eAAP;AACD,CAxEM;;;;AA0EP,SAASW,eAAT,CACEC,cADF,EAEEC,wBAFF,EAGEC,UAHF,EAIEC,eAJF,EAKE;EACA,IAAIH,cAAc,QAAd,IAAAA,cAAc,CAAEI,SAAhB,IAA6BJ,cAAc,CAACK,QAAhD,EAA0D;IACxD5B,OAAO,CAACC,IAAR,CAAc;AAClB;AACA,yBAAyBsB,cAAc,CAACK,QAAf,CAAwBb,QAAxB,EAAmC;AAC5D,CAHI;EAID;;EAED,OAAO,IAAAc,iCAAA,EAAWN,cAAX,EAA2C;IAChDC,wBADgD;IAEhDC,UAFgD;IAGhDC;EAHgD,CAA3C,CAAP;AAKD;;AAED,SAASI,iBAAT,CAA2BC,MAA3B,EAA+D;EAE7D,OAAO,CAAC,EAACA,MAAD,YAACA,MAAM,CAAED,iBAAT,CAAR;AACD;;AAED,SAASE,qBAAT,CAA+BD,MAA/B,EAAmE;EAEjE,OAAO,CAAC,EAACA,MAAD,YAACA,MAAM,CAAEC,qBAAT,CAAR;AACD;;AAED,SAASC,2BAAT,CAAqCF,MAArC,EAAyE;EAEvE,OAAO,CAAC,EAACA,MAAD,YAACA,MAAM,CAAEE,2BAAT,CAAR;AACD;;AAED,SAASC,qBAAT,CAA+BH,MAA/B,EAAmE;EAEjE,OAAO,CAAC,EAACA,MAAD,YAACA,MAAM,CAAEG,qBAAT,CAAR;AACD;;eAEc,IAAAC,gCAAA,EAAc,CAACC,GAAD,EAAMtD,IAAN,KAAwB;EACnDsD,GAAG,CAACC,aAAJ,CAAkB,CAAlB;EAEA,MAAMC,YAAY,GAAGF,GAAG,CAACrF,OAAJ,EAArB;EAEA,MAAM;IACJwB,QADI;IAEJkD,UAFI;IAGJf,KAHI;IAIJH,OAAO,EAAEgC,cAJL;IAKJC,kBALI;IAMJhB,wBANI;IAOJlB,OAAO,EAAEmC,cAPL;IAQJC,KARI;IASJlD,OATI;IAUJgB,gBAVI;IAWJmC,IAXI;IAYJ5F,OAAO,EAAEwE,cAZL;IAaJpB,WAbI;IAcJC,MAAM,EAAE;MAAEU,OAAO,EAAEV,MAAX;MAAmB9B;IAAnB,CAdJ;IAeJoD;EAfI,IAgBF,IAAAkB,yBAAA,EAAiB9D,IAAjB,CAhBJ;EAkBA,IAAI/B,OAAO,GAAGuF,YAAd;;EAEA,IAIEO,OAAM,CAACC,EAAP,CAAUV,GAAG,CAACtB,OAAd,EAAuB,QAAvB,KAGAhC,IAAI,CAAC/B,OAHL,IAIA+B,IAAI,CAAC2C,UAJL,IAKA3C,IAAI,CAAC4C,eALL,IAMA5C,IAAI,CAAC0C,wBAVP,EAWE;IACmC;MAEjC,IAAIuB,eAAe,GAAG,KAAtB;;MAEA,IAAIxB,cAAJ,YAAIA,cAAc,CAAEyB,MAApB,EAA4B;QAC1BD,eAAe,GAAG,IAAlB;QACA,OAAOxB,cAAc,CAACyB,MAAtB;QAEAhD,OAAO,CAACC,IAAR,CAAc;AACtB;AACA;AACA,CAHQ;MAID;IACF;IAEDlD,OAAO,GAAGuE,eAAe,CACvBC,cADuB,EAEvBC,wBAFuB,EAGvBC,UAHuB,EAIvBC,eAJuB,CAAzB;EAMD;;EAED,MAAMuB,gBAAgB,GAGhBT,kBAAkB,IAAIO,eAHH,GAKpB,EALoB,GAMrBhG,OANJ;EAQA,MAAMuD,OAAO,GAAGzB,4BAA4B,CAAC4D,cAAD,CAA5C;EACA,MAAMlC,OAAO,GAAG1B,4BAA4B,CAAC0D,cAAD,CAA5C;EAEA,MAAMrF,UAAU,GAAGmB,aAAa,CAACmC,gBAAD,EAAmBjC,QAAnB,CAAhC;EACA,MAAM2E,6BAA6B,GAChC1D,OAAO,KAAK,MAAZ,KAAsB4C,GAAG,CAACL,MAA1B,oBAAsBK,GAAG,CAACL,MAAJ,CAAaE,2BAAb,CAAtB,CAAD,IACCzC,OAAO,KAAK,KAAZ,IACC,CAAC,IAAAvC,oCAAA,EAAW,iCAAX,EAA8CgG,gBAA9C,EAAgE;IAC/D/F,UAD+D;IAE/DiG,QAAQ,EAAE7C,OAAO,CAAClB,OAF6C;IAG/DgE,QAAQ,EAAE7C,OAAO,CAACnB;EAH6C,CAAhE,CAHL;EAQA,MAAMU,kBAAkB,GAAGP,qBAAqB,CAAC;IAC/CC,OAD+C;IAE/CC,eAAe,EAAE4D,8BAF8B;IAK/C3D,kBAAkB,EAAEF,OAAO,KAAK,MAAZ,IAAsB,EAAC4C,GAAG,CAACL,MAAL,YAACK,GAAG,CAACL,MAAJ,CAAaD,iBAAb,CAAD,CALK;IAM/CnC,4BAA4B,EAC1BH,OAAO,KAAK,MAAZ,IAAsB,EAAC4C,GAAG,CAACL,MAAL,YAACK,GAAG,CAACL,MAAJ,CAAaC,qBAAb,CAAD,CAPuB;IAQ/CpC,kCAAkC,EAAE,CAACsD,6BARU;IAS/CrD,wBAAwB,EACtB,CAACuC,GAAG,CAACL,MAAL,IAAgBK,GAAG,CAACL,MAAJ,CAAWG,qBAAX;EAV6B,CAAD,CAAhD;EAaA,MAAMoB,WAAW,GAAG,IAAAC,qCAAA,EAClBrG,UADkB,EAElBoD,OAAO,CAAClB,OAFU,EAGlBmB,OAAO,CAACnB,OAHU,EAIlB6D,gBAJkB,EAKlBnD,kBALkB,EAMlB,IAAA0D,kCAAA,EAA6B;IAAEd;EAAF,CAA7B,CANkB,EAOlBe,iCAPkB,CAApB;EASA,IAAAC,mCAAA,EAAuBJ,WAAvB,EAAoCK,qCAApC;EACA,IAAAC,mCAAA,EAAuBN,WAAvB,EAAoClB,GAAG,CAACtB,OAAxC;;EACA,IAAIN,gBAAJ,EAAsB;IACpB,IAAAqD,qCAAA,EAAyBP,WAAzB,EAAsCQ,uCAAtC;EACD;;EAED,MAAMnD,eAAe,GAAGT,kBAAkB,CAAC;IACzCC,WADyC;IAEzCC,MAFyC;IAGzCC,eAAe,EAAEtD,OAHwB;IAIzCuD,OAAO,EAAEA,OAAO,CAAChB,QAJwB;IAKzCiB,OAAO,EAAEA,OAAO,CAACjB,QALwB;IAMzChB,SANyC;IAOzCkC,gBAPyC;IAQzCC,WAAW,EAAE6C,WAAW,CAAC3F,GAAZ,CAAgB,uBAAhB,CAR4B;IASzC+C;EATyC,CAAD,CAA1C;EAYA,MAAMqD,iBAAiB,GAAG5D,WAAW,KAAK,KAA1C;EACA,MAAMf,OAAO,GAAG4E,KAAK,CAACC,IAAN,CAAWX,WAAX,EACbY,GADa,CACTzF,UAAU,IAAI;IACjB,IACEA,UAAU,KAAK,4BAAf,IACAA,UAAU,KAAK,2BADf,IAEAA,UAAU,KAAK,sCAHjB,EAIE;MACA,OAAO,CACLD,SAAS,CAACC,UAAD,CADJ,EAEL;QACEiE,KAAK,EAAEA,KAAK,GACR,qFADQ,GAER;MAHN,CAFK,CAAP;IAQD;;IACD,OAAO,CACLlE,SAAS,CAACC,UAAD,CADJ,EAEL;MAAEkE,IAAF;MAAQD,KAAR;MAAevC,WAAW,EAAE4D;IAA5B,CAFK,CAAP;EAID,CApBa,EAqBbI,MArBa,CAqBNxD,eArBM,CAAhB;;EAuBA,IAAID,KAAJ,EAAW;IACTV,OAAO,CAACoE,GAAR,CAAY,mCAAZ;IACApE,OAAO,CAACoE,GAAR,CAAY,kBAAZ;IACApE,OAAO,CAACoE,GAAR,CAAYC,IAAI,CAACC,SAAL,CAAe,IAAAC,yCAAA,EAAgBxH,OAAhB,CAAf,EAAyC,IAAzC,EAA+C,CAA/C,CAAZ;IACAiD,OAAO,CAACoE,GAAR,CAAa,8BAA6B5E,OAAO,CAACuB,QAAR,EAAmB,EAA7D;IACAf,OAAO,CAACoE,GAAR,CAAY,kBAAZ;IACAd,WAAW,CAACkB,OAAZ,CAAoB/F,UAAU,IAAI;MAChC,IAAAgG,gBAAA,EAAUhG,UAAV,EAAsB1B,OAAtB,EAA+BG,UAA/B;IACD,CAFD;;IAIA,IAAI,CAACiD,WAAL,EAAkB;MAChBH,OAAO,CAACoE,GAAR,CACE,yFADF;IAGD;EACF;;EAED,OAAO;IAAEhF;EAAF,CAAP;AACD,CAlKc,C"}