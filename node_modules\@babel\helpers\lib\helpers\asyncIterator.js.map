{"version": 3, "names": ["_asyncIterator", "iterable", "method", "async", "sync", "retry", "Symbol", "asyncIterator", "iterator", "call", "AsyncFromSyncIterator", "TypeError", "s", "n", "next", "prototype", "AsyncFromSyncIteratorContinuation", "apply", "arguments", "return", "value", "ret", "undefined", "Promise", "resolve", "done", "throw", "thr", "reject", "r", "Object", "then"], "sources": ["../../src/helpers/asyncIterator.js"], "sourcesContent": ["/* @minVersion 7.15.9 */\n\nexport default function _asyncIterator(iterable) {\n  var method,\n    async,\n    sync,\n    retry = 2;\n\n  if (typeof Symbol !== \"undefined\") {\n    async = Symbol.asyncIterator;\n    sync = Symbol.iterator;\n  }\n\n  while (retry--) {\n    if (async && (method = iterable[async]) != null) {\n      return method.call(iterable);\n    }\n    if (sync && (method = iterable[sync]) != null) {\n      return new AsyncFromSyncIterator(method.call(iterable));\n    }\n\n    async = \"@@asyncIterator\";\n    sync = \"@@iterator\";\n  }\n\n  throw new TypeError(\"Object is not async iterable\");\n}\n\nfunction AsyncFromSyncIterator(s) {\n  AsyncFromSyncIterator = function (s) {\n    this.s = s;\n    this.n = s.next;\n  };\n  AsyncFromSyncIterator.prototype = {\n    /* SyncIterator */ s: null,\n    /* SyncIterator.[[Next]] */ n: null,\n    next: function () {\n      return AsyncFromSyncIteratorContinuation(this.n.apply(this.s, arguments));\n    },\n    return: function (value) {\n      var ret = this.s.return;\n      if (ret === undefined) {\n        return Promise.resolve({ value: value, done: true });\n      }\n      return AsyncFromSyncIteratorContinuation(ret.apply(this.s, arguments));\n    },\n    throw: function (value) {\n      var thr = this.s.return;\n      if (thr === undefined) return Promise.reject(value);\n      return AsyncFromSyncIteratorContinuation(thr.apply(this.s, arguments));\n    },\n  };\n\n  function AsyncFromSyncIteratorContinuation(r) {\n    // This step is _before_ calling AsyncFromSyncIteratorContinuation in the spec.\n    if (Object(r) !== r) {\n      return Promise.reject(new TypeError(r + \" is not an object.\"));\n    }\n\n    var done = r.done;\n    return Promise.resolve(r.value).then(function (value) {\n      return { value: value, done: done };\n    });\n  }\n\n  return new AsyncFromSyncIterator(s);\n}\n"], "mappings": ";;;;;;;AAEe,SAASA,cAAT,CAAwBC,QAAxB,EAAkC;EAC/C,IAAIC,MAAJ;EAAA,IACEC,KADF;EAAA,IAEEC,IAFF;EAAA,IAGEC,KAAK,GAAG,CAHV;;EAKA,IAAI,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;IACjCH,KAAK,GAAGG,MAAM,CAACC,aAAf;IACAH,IAAI,GAAGE,MAAM,CAACE,QAAd;EACD;;EAED,OAAOH,KAAK,EAAZ,EAAgB;IACd,IAAIF,KAAK,IAAI,CAACD,MAAM,GAAGD,QAAQ,CAACE,KAAD,CAAlB,KAA8B,IAA3C,EAAiD;MAC/C,OAAOD,MAAM,CAACO,IAAP,CAAYR,QAAZ,CAAP;IACD;;IACD,IAAIG,IAAI,IAAI,CAACF,MAAM,GAAGD,QAAQ,CAACG,IAAD,CAAlB,KAA6B,IAAzC,EAA+C;MAC7C,OAAO,IAAIM,qBAAJ,CAA0BR,MAAM,CAACO,IAAP,CAAYR,QAAZ,CAA1B,CAAP;IACD;;IAEDE,KAAK,GAAG,iBAAR;IACAC,IAAI,GAAG,YAAP;EACD;;EAED,MAAM,IAAIO,SAAJ,CAAc,8BAAd,CAAN;AACD;;AAED,SAASD,qBAAT,CAA+BE,CAA/B,EAAkC;EAChCF,qBAAqB,GAAG,UAAUE,CAAV,EAAa;IACnC,KAAKA,CAAL,GAASA,CAAT;IACA,KAAKC,CAAL,GAASD,CAAC,CAACE,IAAX;EACD,CAHD;;EAIAJ,qBAAqB,CAACK,SAAtB,GAAkC;IACbH,CAAC,EAAE,IADU;IAEJC,CAAC,EAAE,IAFC;IAGhCC,IAAI,EAAE,YAAY;MAChB,OAAOE,iCAAiC,CAAC,KAAKH,CAAL,CAAOI,KAAP,CAAa,KAAKL,CAAlB,EAAqBM,SAArB,CAAD,CAAxC;IACD,CAL+B;IAMhCC,MAAM,EAAE,UAAUC,KAAV,EAAiB;MACvB,IAAIC,GAAG,GAAG,KAAKT,CAAL,CAAOO,MAAjB;;MACA,IAAIE,GAAG,KAAKC,SAAZ,EAAuB;QACrB,OAAOC,OAAO,CAACC,OAAR,CAAgB;UAAEJ,KAAK,EAAEA,KAAT;UAAgBK,IAAI,EAAE;QAAtB,CAAhB,CAAP;MACD;;MACD,OAAOT,iCAAiC,CAACK,GAAG,CAACJ,KAAJ,CAAU,KAAKL,CAAf,EAAkBM,SAAlB,CAAD,CAAxC;IACD,CAZ+B;IAahCQ,KAAK,EAAE,UAAUN,KAAV,EAAiB;MACtB,IAAIO,GAAG,GAAG,KAAKf,CAAL,CAAOO,MAAjB;MACA,IAAIQ,GAAG,KAAKL,SAAZ,EAAuB,OAAOC,OAAO,CAACK,MAAR,CAAeR,KAAf,CAAP;MACvB,OAAOJ,iCAAiC,CAACW,GAAG,CAACV,KAAJ,CAAU,KAAKL,CAAf,EAAkBM,SAAlB,CAAD,CAAxC;IACD;EAjB+B,CAAlC;;EAoBA,SAASF,iCAAT,CAA2Ca,CAA3C,EAA8C;IAE5C,IAAIC,MAAM,CAACD,CAAD,CAAN,KAAcA,CAAlB,EAAqB;MACnB,OAAON,OAAO,CAACK,MAAR,CAAe,IAAIjB,SAAJ,CAAckB,CAAC,GAAG,oBAAlB,CAAf,CAAP;IACD;;IAED,IAAIJ,IAAI,GAAGI,CAAC,CAACJ,IAAb;IACA,OAAOF,OAAO,CAACC,OAAR,CAAgBK,CAAC,CAACT,KAAlB,EAAyBW,IAAzB,CAA8B,UAAUX,KAAV,EAAiB;MACpD,OAAO;QAAEA,KAAK,EAAEA,KAAT;QAAgBK,IAAI,EAAEA;MAAtB,CAAP;IACD,CAFM,CAAP;EAGD;;EAED,OAAO,IAAIf,qBAAJ,CAA0BE,CAA1B,CAAP;AACD"}