{"version": 3, "names": ["TopLevelOptions", "bugfixes", "config<PERSON><PERSON>", "corejs", "debug", "exclude", "forceAllTransforms", "ignoreBrowserslistConfig", "include", "loose", "modules", "shippedProposals", "spec", "targets", "useBuiltIns", "browserslistEnv", "ModulesOption", "false", "auto", "amd", "commonjs", "cjs", "systemjs", "umd", "UseBuiltInsOption", "entry", "usage"], "sources": ["../src/options.ts"], "sourcesContent": ["export const TopLevelOptions = {\n  bugfixes: \"bugfixes\",\n  configPath: \"configPath\",\n  corejs: \"corejs\",\n  debug: \"debug\",\n  exclude: \"exclude\",\n  forceAllTransforms: \"forceAllTransforms\",\n  ignoreBrowserslistConfig: \"ignoreBrowserslistConfig\",\n  include: \"include\",\n  loose: \"loose\",\n  modules: \"modules\",\n  shippedProposals: \"shippedProposals\",\n  spec: \"spec\",\n  targets: \"targets\",\n  useBuiltIns: \"useBuiltIns\",\n  browserslistEnv: \"browserslistEnv\",\n} as const;\n\nexport const ModulesOption = {\n  false: false,\n  auto: \"auto\",\n  amd: \"amd\",\n  commonjs: \"commonjs\",\n  cjs: \"cjs\",\n  systemjs: \"systemjs\",\n  umd: \"umd\",\n} as const;\n\nexport const UseBuiltInsOption = {\n  false: false,\n  entry: \"entry\",\n  usage: \"usage\",\n} as const;\n"], "mappings": ";;;;;;AAAO,MAAMA,eAAe,GAAG;EAC7BC,QAAQ,EAAE,UADmB;EAE7BC,UAAU,EAAE,YAFiB;EAG7BC,MAAM,EAAE,QAHqB;EAI7BC,KAAK,EAAE,OAJsB;EAK7BC,OAAO,EAAE,SALoB;EAM7BC,kBAAkB,EAAE,oBANS;EAO7BC,wBAAwB,EAAE,0BAPG;EAQ7BC,OAAO,EAAE,SARoB;EAS7BC,KAAK,EAAE,OATsB;EAU7BC,OAAO,EAAE,SAVoB;EAW7BC,gBAAgB,EAAE,kBAXW;EAY7BC,IAAI,EAAE,MAZuB;EAa7BC,OAAO,EAAE,SAboB;EAc7BC,WAAW,EAAE,aAdgB;EAe7BC,eAAe,EAAE;AAfY,CAAxB;;AAkBA,MAAMC,aAAa,GAAG;EAC3BC,KAAK,EAAE,KADoB;EAE3BC,IAAI,EAAE,MAFqB;EAG3BC,GAAG,EAAE,KAHsB;EAI3BC,QAAQ,EAAE,UAJiB;EAK3BC,GAAG,EAAE,KALsB;EAM3BC,QAAQ,EAAE,UANiB;EAO3BC,GAAG,EAAE;AAPsB,CAAtB;;AAUA,MAAMC,iBAAiB,GAAG;EAC/BP,KAAK,EAAE,KADwB;EAE/BQ,KAAK,EAAE,OAFwB;EAG/BC,KAAK,EAAE;AAHwB,CAA1B"}