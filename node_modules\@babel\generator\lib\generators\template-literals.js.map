{"version": 3, "names": ["TaggedTemplateExpression", "node", "print", "tag", "typeParameters", "quasi", "TemplateElement", "parent", "<PERSON><PERSON><PERSON><PERSON>", "quasis", "isLast", "length", "value", "raw", "token", "TemplateLiteral", "i", "expressions"], "sources": ["../../src/generators/template-literals.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function TaggedTemplateExpression(\n  this: Printer,\n  node: t.TaggedTemplateExpression,\n) {\n  this.print(node.tag, node);\n  this.print(node.typeParameters, node); // TS\n  this.print(node.quasi, node);\n}\n\nexport function TemplateElement(\n  this: Printer,\n  node: t.TemplateElement,\n  parent: t.TemplateLiteral,\n) {\n  const isFirst = parent.quasis[0] === node;\n  const isLast = parent.quasis[parent.quasis.length - 1] === node;\n\n  const value = (isFirst ? \"`\" : \"}\") + node.value.raw + (isLast ? \"`\" : \"${\");\n\n  this.token(value, true);\n}\n\nexport function TemplateLiteral(this: Printer, node: t.TemplateLiteral) {\n  const quasis = node.quasis;\n\n  for (let i = 0; i < quasis.length; i++) {\n    this.print(quasis[i], node);\n\n    if (i + 1 < quasis.length) {\n      this.print(node.expressions[i], node);\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;AAGO,SAASA,wBAAT,CAELC,IAFK,EAGL;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACE,GAAhB,EAAqBF,IAArB;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACG,cAAhB,EAAgCH,IAAhC;EACA,KAAKC,KAAL,CAAWD,IAAI,CAACI,KAAhB,EAAuBJ,IAAvB;AACD;;AAEM,SAASK,eAAT,CAELL,IAFK,EAGLM,MAHK,EAIL;EACA,MAAMC,OAAO,GAAGD,MAAM,CAACE,MAAP,CAAc,CAAd,MAAqBR,IAArC;EACA,MAAMS,MAAM,GAAGH,MAAM,CAACE,MAAP,CAAcF,MAAM,CAACE,MAAP,CAAcE,MAAd,GAAuB,CAArC,MAA4CV,IAA3D;EAEA,MAAMW,KAAK,GAAG,CAACJ,OAAO,GAAG,GAAH,GAAS,GAAjB,IAAwBP,IAAI,CAACW,KAAL,CAAWC,GAAnC,IAA0CH,MAAM,GAAG,GAAH,GAAS,IAAzD,CAAd;EAEA,KAAKI,KAAL,CAAWF,KAAX,EAAkB,IAAlB;AACD;;AAEM,SAASG,eAAT,CAAwCd,IAAxC,EAAiE;EACtE,MAAMQ,MAAM,GAAGR,IAAI,CAACQ,MAApB;;EAEA,KAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGP,MAAM,CAACE,MAA3B,EAAmCK,CAAC,EAApC,EAAwC;IACtC,KAAKd,KAAL,CAAWO,MAAM,CAACO,CAAD,CAAjB,EAAsBf,IAAtB;;IAEA,IAAIe,CAAC,GAAG,CAAJ,GAAQP,MAAM,CAACE,MAAnB,EAA2B;MACzB,KAAKT,KAAL,CAAWD,IAAI,CAACgB,WAAL,CAAiBD,CAAjB,CAAX,EAAgCf,IAAhC;IACD;EACF;AACF"}