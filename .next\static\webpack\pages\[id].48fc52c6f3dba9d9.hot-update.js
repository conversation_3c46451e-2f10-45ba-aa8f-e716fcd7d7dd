"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/note.ts":
/*!********************************!*\
  !*** ./libs/web/state/note.ts ***!
  \********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var libs_web_state_tree__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/web/state/tree */ \"./libs/web/state/tree.ts\");\n/* harmony import */ var libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/shared/meta */ \"./libs/shared/meta.ts\");\n/* harmony import */ var _api_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../api/note */ \"./libs/web/api/note.ts\");\n/* harmony import */ var _cache_note__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar useNote = function(initData) {\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initData), note = ref[0], setNote = ref[1];\n    var ref1 = (0,_api_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), find = ref1.find, abortFindNote = ref1.abort;\n    var ref2 = (0,_api_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), create = ref2.create, createError = ref2.error;\n    var ref3 = (0,_api_note__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(), mutate = ref3.mutate, loading = ref3.loading, abort = ref3.abort;\n    var ref4 = libs_web_state_tree__WEBPACK_IMPORTED_MODULE_1__[\"default\"].useContainer(), addItem = ref4.addItem, removeItem = ref4.removeItem, mutateItem = ref4.mutateItem, genNewId = ref4.genNewId;\n    var toast = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_5__.useToast)();\n    var fetchNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id) {\n            var cache, result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getItem(id)\n                        ];\n                    case 1:\n                        cache = _state.sent();\n                        if (cache) {\n                            setNote(cache);\n                        }\n                        return [\n                            4,\n                            find(id)\n                        ];\n                    case 2:\n                        result = _state.sent();\n                        if (!result) {\n                            return [\n                                2\n                            ];\n                        }\n                        // 🔧 修复：对于 JSON 编辑器，空内容应该是空字符串，不是 '\\n'\n                        result.content = result.content || \"\";\n                        setNote(result);\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setItem(id, result)\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            2,\n                            result\n                        ];\n                }\n            });\n        });\n        return function(id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        find\n    ]);\n    var removeNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id) {\n            var payload;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        payload = {\n                            deleted: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_DELETED.DELETED\n                        };\n                        setNote(function(prev) {\n                            if ((prev === null || prev === void 0 ? void 0 : prev.id) === id) {\n                                return (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, prev, payload);\n                            }\n                            return prev;\n                        });\n                        return [\n                            4,\n                            mutate(id, payload)\n                        ];\n                    case 1:\n                        _state.sent();\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].mutateItem(id, payload)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            4,\n                            removeItem(id)\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        mutate,\n        removeItem\n    ]);\n    var mutateNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id, payload) {\n            var note, diff;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getItem(id)\n                        ];\n                    case 1:\n                        note = _state.sent();\n                        if (!note) {\n                            // todo\n                            console.error(\"mutate note error\");\n                            return [\n                                2\n                            ];\n                        }\n                        diff = {};\n                        (0,lodash__WEBPACK_IMPORTED_MODULE_6__.map)(payload, function(value, key) {\n                            if (note[key] !== value) {\n                                diff[key] = value;\n                            }\n                        });\n                        if ((0,lodash__WEBPACK_IMPORTED_MODULE_6__.isEmpty)(diff)) {\n                            return [\n                                2\n                            ];\n                        }\n                        setNote(function(prev) {\n                            if ((prev === null || prev === void 0 ? void 0 : prev.id) === id) {\n                                return (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, prev, payload);\n                            }\n                            return prev;\n                        });\n                        return [\n                            4,\n                            mutate(id, payload)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].mutateItem(id, payload)\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            4,\n                            mutateItem(id, {\n                                data: (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, note, payload)\n                            })\n                        ];\n                    case 4:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(id, payload) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        mutate,\n        mutateItem\n    ]);\n    var createNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(body) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        return [\n                            4,\n                            create(body)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (!result) {\n                            toast(createError, \"error\");\n                            return [\n                                2\n                            ];\n                        }\n                        result.content = result.content || \"\\n\";\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setItem(result.id, result)\n                        ];\n                    case 2:\n                        _state.sent();\n                        setNote(result);\n                        addItem(result);\n                        return [\n                            2,\n                            result\n                        ];\n                }\n            });\n        });\n        return function(body) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        create,\n        addItem,\n        toast,\n        createError\n    ]);\n    var createNoteWithTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var id, result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        id = genNewId();\n                        return [\n                            4,\n                            create({\n                                id: id,\n                                title: title\n                            })\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (!result) {\n                            return [\n                                2\n                            ];\n                        }\n                        result.content = result.content || \"\\n\";\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].setItem(result.id, result)\n                        ];\n                    case 2:\n                        _state.sent();\n                        addItem(result);\n                        return [\n                            2,\n                            {\n                                id: id\n                            }\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        addItem,\n        create,\n        genNewId\n    ]);\n    var updateNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var localNote, noteToUpdate, updateData, newNote, result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        abort();\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                            toast(\"Not found id\", \"error\");\n                            return [\n                                2\n                            ];\n                        }\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        localNote = _state.sent();\n                        noteToUpdate = localNote || note;\n                        updateData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, data), {\n                            content: data.content || noteToUpdate.content\n                        });\n                        newNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToUpdate, data);\n                        setNote(newNote);\n                        return [\n                            4,\n                            mutateItem(newNote.id, {\n                                data: newNote\n                            })\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            4,\n                            mutate(note.id, updateData)\n                        ];\n                    case 3:\n                        result = _state.sent();\n                        return [\n                            4,\n                            _cache_note__WEBPACK_IMPORTED_MODULE_4__[\"default\"].mutateItem(note.id, updateData)\n                        ];\n                    case 4:\n                        _state.sent();\n                        return [\n                            2,\n                            result\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        abort,\n        toast,\n        note,\n        mutate,\n        mutateItem\n    ]);\n    var initNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function(note) {\n        setNote((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n            deleted: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_DELETED.NORMAL,\n            shared: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_SHARED.PRIVATE,\n            pinned: libs_shared_meta__WEBPACK_IMPORTED_MODULE_2__.NOTE_PINNED.UNPINNED,\n            editorsize: null,\n            id: \"-1\",\n            title: \"\"\n        }, note));\n    }, []);\n    var findOrCreateNote = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(id, note) {\n            var data, e;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        _state.trys.push([\n                            0,\n                            2,\n                            ,\n                            4\n                        ]);\n                        return [\n                            4,\n                            fetchNote(id)\n                        ];\n                    case 1:\n                        data = _state.sent();\n                        if (!data) {\n                            throw data;\n                        }\n                        return [\n                            3,\n                            4\n                        ];\n                    case 2:\n                        e = _state.sent();\n                        return [\n                            4,\n                            createNote((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({\n                                id: id\n                            }, note))\n                        ];\n                    case 3:\n                        _state.sent();\n                        return [\n                            3,\n                            4\n                        ];\n                    case 4:\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(id, note) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNote,\n        fetchNote\n    ]);\n    return {\n        note: note,\n        fetchNote: fetchNote,\n        abortFindNote: abortFindNote,\n        createNote: createNote,\n        findOrCreateNote: findOrCreateNote,\n        createNoteWithTitle: createNoteWithTitle,\n        updateNote: updateNote,\n        removeNote: removeNote,\n        mutateNote: mutateNote,\n        initNote: initNote,\n        loading: loading\n    };\n};\nvar NoteState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useNote);\n/* harmony default export */ __webpack_exports__[\"default\"] = (NoteState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/note.ts\n"));

/***/ })

});