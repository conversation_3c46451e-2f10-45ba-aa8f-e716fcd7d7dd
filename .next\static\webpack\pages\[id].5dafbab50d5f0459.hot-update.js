"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/container/lexical-edit-container.tsx":
/*!*********************************************************!*\
  !*** ./components/container/lexical-edit-container.tsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var components_editor_lexical_main_editor__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! components/editor/lexical-main-editor */ \"./components/editor/lexical-main-editor.tsx\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var libs_web_state_tree__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! libs/web/state/tree */ \"./libs/web/state/tree.ts\");\n/* harmony import */ var libs_web_state_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! libs/web/state/ui */ \"./libs/web/state/ui.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_hooks_use_auto_save_on_leave__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! libs/web/hooks/use-auto-save-on-leave */ \"./libs/web/hooks/use-auto-save-on-leave.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var components_note_nav__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! components/note-nav */ \"./components/note-nav.tsx\");\n/* harmony import */ var components_editor_delete_alert__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! components/editor/delete-alert */ \"./components/editor/delete-alert.tsx\");\n/**\n * Lexical Edit Container Component\n * Migrated from TipTap to Lexical\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar LexicalEditContainer = function() {\n    _s();\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    var _query = router.query, id = _query.id, pid = _query.pid;\n    var isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_3__.has)(router.query, \"new\");\n    var ref = libs_web_state_note__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer(), initNote = ref.initNote, findOrCreateNote = ref.findOrCreateNote, fetchNote = ref.fetchNote;\n    var loadNoteOnDemand = libs_web_state_tree__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useContainer().loadNoteOnDemand;\n    var ref1 = libs_web_state_ui__WEBPACK_IMPORTED_MODULE_8__[\"default\"].useContainer(), settings = ref1.settings.settings;\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast)();\n    (0,libs_web_hooks_use_auto_save_on_leave__WEBPACK_IMPORTED_MODULE_10__[\"default\"])({\n        enabled: true\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var initializeEditor = function() {\n            var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_14__[\"default\"])(function() {\n                var dailyDate, cachedNote, error;\n                return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(this, function(_state) {\n                    switch(_state.label){\n                        case 0:\n                            if (!id || Array.isArray(id)) return [\n                                2\n                            ];\n                            if (!isNew) return [\n                                3,\n                                4\n                            ];\n                            dailyDate = router.query.daily;\n                            if (!(dailyDate && /^\\d{4}-\\d{1,2}-\\d{1,2}$/.test(dailyDate))) return [\n                                3,\n                                1\n                            ];\n                            initNote({\n                                id: id,\n                                title: dailyDate,\n                                content: \"\\n\",\n                                pid: settings.daily_root_id,\n                                isDailyNote: true\n                            });\n                            return [\n                                3,\n                                3\n                            ];\n                        case 1:\n                            return [\n                                4,\n                                libs_web_cache_note__WEBPACK_IMPORTED_MODULE_11__[\"default\"].getItem(id)\n                            ];\n                        case 2:\n                            cachedNote = _state.sent();\n                            if (cachedNote) {\n                                initNote(cachedNote);\n                                return [\n                                    2\n                                ];\n                            }\n                            initNote({\n                                id: id,\n                                title: \"\",\n                                content: \"\\n\",\n                                pid: (typeof pid === \"string\" ? pid : undefined) || \"root\"\n                            });\n                            _state.label = 3;\n                        case 3:\n                            return [\n                                3,\n                                7\n                            ];\n                        case 4:\n                            _state.trys.push([\n                                4,\n                                6,\n                                ,\n                                7\n                            ]);\n                            // 🔧 修复：直接调用 NoteState 的 fetchNote 来更新 note 状态\n                            console.log(\"\\uD83D\\uDD04 Loading note via NoteState.fetchNote:\", id);\n                            return [\n                                4,\n                                fetchNote(id)\n                            ];\n                        case 5:\n                            _state.sent();\n                            return [\n                                3,\n                                7\n                            ];\n                        case 6:\n                            error = _state.sent();\n                            console.error(\"Failed to load note:\", error);\n                            toast(\"Failed to load note\", \"error\");\n                            return [\n                                3,\n                                7\n                            ];\n                        case 7:\n                            return [\n                                2\n                            ];\n                    }\n                });\n            });\n            return function initializeEditor() {\n                return _ref.apply(this, arguments);\n            };\n        }();\n        initializeEditor();\n    }, [\n        id,\n        isNew,\n        pid,\n        router.query.daily,\n        settings.daily_root_id,\n        initNote,\n        findOrCreateNote,\n        fetchNote,\n        loadNoteOnDemand,\n        toast, \n    ]);\n    if (!id || Array.isArray(id)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Loading...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n            lineNumber: 104,\n            columnNumber: 16\n        }, _this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_5__[\"default\"].Provider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_note_nav__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                lineNumber: 109,\n                columnNumber: 13\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_editor_delete_alert__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                lineNumber: 110,\n                columnNumber: 13\n            }, _this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"h-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(components_editor_lexical_main_editor__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    readOnly: false\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 17\n                }, _this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n                lineNumber: 111,\n                columnNumber: 13\n            }, _this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\container\\\\lexical-edit-container.tsx\",\n        lineNumber: 108,\n        columnNumber: 9\n    }, _this);\n};\n_s(LexicalEditContainer, \"uLhHtFDRcKLsyU7rXN+LxiiX+GY=\", false, function() {\n    return [\n        next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        libs_web_state_note__WEBPACK_IMPORTED_MODULE_6__[\"default\"].useContainer,\n        libs_web_state_tree__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useContainer,\n        libs_web_state_ui__WEBPACK_IMPORTED_MODULE_8__[\"default\"].useContainer,\n        libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_9__.useToast,\n        libs_web_hooks_use_auto_save_on_leave__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    ];\n});\n_c = LexicalEditContainer;\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditContainer);\nvar _c;\n$RefreshReg$(_c, \"LexicalEditContainer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/container/lexical-edit-container.tsx\n"));

/***/ })

});