{"version": 3, "names": ["isArrayTypeAnnotation", "isArrowFunctionExpression", "isAssignmentExpression", "isAwaitExpression", "isBinary", "isBinaryExpression", "isUpdateExpression", "isCallExpression", "isClass", "isClassExpression", "isConditional", "isConditionalExpression", "isExportDeclaration", "isExportDefaultDeclaration", "isExpressionStatement", "isFor", "isForInStatement", "isForOfStatement", "isForStatement", "isFunctionExpression", "isIfStatement", "isIndexedAccessType", "isIntersectionTypeAnnotation", "isLogicalExpression", "isMemberExpression", "isNewExpression", "isNullableTypeAnnotation", "isObjectPattern", "isOptionalCallExpression", "isOptionalMemberExpression", "isReturnStatement", "isSequenceExpression", "isSwitchStatement", "isTSArrayType", "isTSAsExpression", "isTSInstantiationExpression", "isTSIntersectionType", "isTSNonNullExpression", "isTSOptionalType", "isTSRestType", "isTSTypeAssertion", "isTSUnionType", "isTaggedTemplateExpression", "isThrowStatement", "isTypeAnnotation", "isUnaryLike", "isUnionTypeAnnotation", "isVariableDeclarator", "isWhileStatement", "isYieldExpression", "PRECEDENCE", "in", "instanceof", "isClassExtendsClause", "node", "parent", "superClass", "hasPostfixPart", "object", "callee", "tag", "NullableTypeAnnotation", "FunctionTypeAnnotation", "printStack", "length", "UpdateExpression", "ObjectExpression", "isFirstInContext", "DoExpression", "async", "Binary", "operator", "left", "parentOp", "parentPos", "nodeOp", "nodePos", "right", "UnionTypeAnnotation", "OptionalIndexedAccessType", "objectType", "TSAsExpression", "TSTypeAssertion", "TSUnionType", "TSInferType", "TSInstantiationExpression", "typeParameters", "BinaryExpression", "SequenceExpression", "test", "discriminant", "expression", "YieldExpression", "ClassExpression", "UnaryLike", "FunctionExpression", "ArrowFunctionExpression", "ConditionalExpression", "OptionalMemberExpression", "AssignmentExpression", "LogicalExpression", "Identifier", "extra", "parenthesized", "id", "name", "isFollowedByBracket", "computed", "optional", "checkParam", "expressionStatement", "arrowBody", "exportDefault", "forHead", "forInHead", "forOfHead", "i", "declaration", "body", "init", "expressions", "prefix"], "sources": ["../../src/node/parentheses.ts"], "sourcesContent": ["import {\n  isArrayTypeAnnotation,\n  isArrowFunctionExpression,\n  isAssignmentExpression,\n  isAwaitExpression,\n  isBinary,\n  isBinaryExpression,\n  isUpdateExpression,\n  isCallExpression,\n  isClass,\n  isClassExpression,\n  isConditional,\n  isConditionalExpression,\n  isExportDeclaration,\n  isExportDefaultDeclaration,\n  isExpressionStatement,\n  isFor,\n  isForInStatement,\n  isForOfStatement,\n  isForStatement,\n  isFunctionExpression,\n  isIfStatement,\n  isIndexedAccessType,\n  isIntersectionTypeAnnotation,\n  isLogicalExpression,\n  isMemberExpression,\n  isNewExpression,\n  isNullableTypeAnnotation,\n  isObjectPattern,\n  isOptionalCallExpression,\n  isOptionalMemberExpression,\n  isReturnStatement,\n  isSequenceExpression,\n  isSwitchStatement,\n  isTSArrayType,\n  isTSAsExpression,\n  isTSInstantiationExpression,\n  isTSIntersectionType,\n  isTSNonNullExpression,\n  isTSOptionalType,\n  isTSRestType,\n  isTSTypeAssertion,\n  isTSUnionType,\n  isTaggedTemplateExpression,\n  isThrowStatement,\n  isTypeAnnotation,\n  isUnaryLike,\n  isUnionTypeAnnotation,\n  isVariableDeclarator,\n  isWhileStatement,\n  isYieldExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\nconst PRECEDENCE = {\n  \"||\": 0,\n  \"??\": 0,\n  \"|>\": 0,\n  \"&&\": 1,\n  \"|\": 2,\n  \"^\": 3,\n  \"&\": 4,\n  \"==\": 5,\n  \"===\": 5,\n  \"!=\": 5,\n  \"!==\": 5,\n  \"<\": 6,\n  \">\": 6,\n  \"<=\": 6,\n  \">=\": 6,\n  in: 6,\n  instanceof: 6,\n  \">>\": 7,\n  \"<<\": 7,\n  \">>>\": 7,\n  \"+\": 8,\n  \"-\": 8,\n  \"*\": 9,\n  \"/\": 9,\n  \"%\": 9,\n  \"**\": 10,\n};\n\nconst enum CheckParam {\n  expressionStatement = 1 << 0,\n  arrowBody = 1 << 1,\n  exportDefault = 1 << 2,\n  forHead = 1 << 3,\n  forInHead = 1 << 4,\n  forOfHead = 1 << 5,\n}\n\nconst isClassExtendsClause = (\n  node: t.Node,\n  parent: t.Node,\n): parent is t.Class => isClass(parent, { superClass: node });\n\nconst hasPostfixPart = (node: t.Node, parent: t.Node) =>\n  ((isMemberExpression(parent) || isOptionalMemberExpression(parent)) &&\n    parent.object === node) ||\n  ((isCallExpression(parent) ||\n    isOptionalCallExpression(parent) ||\n    isNewExpression(parent)) &&\n    parent.callee === node) ||\n  (isTaggedTemplateExpression(parent) && parent.tag === node) ||\n  isTSNonNullExpression(parent);\n\nexport function NullableTypeAnnotation(\n  node: t.NullableTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return isArrayTypeAnnotation(parent);\n}\n\nexport function FunctionTypeAnnotation(\n  node: t.FunctionTypeAnnotation,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  if (printStack.length < 3) return;\n\n  return (\n    // (() => A) | (() => B)\n    isUnionTypeAnnotation(parent) ||\n    // (() => A) & (() => B)\n    isIntersectionTypeAnnotation(parent) ||\n    // (() => A)[]\n    isArrayTypeAnnotation(parent) ||\n    // <T>(A: T): (T => T[]) => B => [A, B]\n    (isTypeAnnotation(parent) &&\n      // Check grandparent\n      isArrowFunctionExpression(printStack[printStack.length - 3]))\n  );\n}\n\nexport function UpdateExpression(\n  node: t.UpdateExpression,\n  parent: t.Node,\n): boolean {\n  return hasPostfixPart(node, parent) || isClassExtendsClause(node, parent);\n}\n\nexport function ObjectExpression(\n  node: t.ObjectExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.arrowBody,\n  );\n}\n\nexport function DoExpression(\n  node: t.DoExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  // `async do` can start an expression statement\n  return (\n    !node.async && isFirstInContext(printStack, CheckParam.expressionStatement)\n  );\n}\n\nexport function Binary(node: t.BinaryExpression, parent: t.Node): boolean {\n  if (\n    node.operator === \"**\" &&\n    isBinaryExpression(parent, { operator: \"**\" })\n  ) {\n    return parent.left === node;\n  }\n\n  if (isClassExtendsClause(node, parent)) {\n    return true;\n  }\n\n  if (\n    hasPostfixPart(node, parent) ||\n    isUnaryLike(parent) ||\n    isAwaitExpression(parent)\n  ) {\n    return true;\n  }\n\n  if (isBinary(parent)) {\n    const parentOp = parent.operator;\n    const parentPos = PRECEDENCE[parentOp];\n\n    const nodeOp = node.operator;\n    const nodePos = PRECEDENCE[nodeOp];\n\n    if (\n      // Logical expressions with the same precedence don't need parens.\n      (parentPos === nodePos &&\n        parent.right === node &&\n        !isLogicalExpression(parent)) ||\n      parentPos > nodePos\n    ) {\n      return true;\n    }\n  }\n}\n\nexport function UnionTypeAnnotation(\n  node: t.UnionTypeAnnotation,\n  parent: t.Node,\n): boolean {\n  return (\n    isArrayTypeAnnotation(parent) ||\n    isNullableTypeAnnotation(parent) ||\n    isIntersectionTypeAnnotation(parent) ||\n    isUnionTypeAnnotation(parent)\n  );\n}\n\nexport { UnionTypeAnnotation as IntersectionTypeAnnotation };\n\nexport function OptionalIndexedAccessType(\n  node: t.OptionalIndexedAccessType,\n  parent: t.Node,\n): boolean {\n  return isIndexedAccessType(parent, { objectType: node });\n}\n\nexport function TSAsExpression() {\n  return true;\n}\n\nexport function TSTypeAssertion() {\n  return true;\n}\n\nexport function TSUnionType(node: t.TSUnionType, parent: t.Node): boolean {\n  return (\n    isTSArrayType(parent) ||\n    isTSOptionalType(parent) ||\n    isTSIntersectionType(parent) ||\n    isTSUnionType(parent) ||\n    isTSRestType(parent)\n  );\n}\n\nexport { TSUnionType as TSIntersectionType };\n\nexport function TSInferType(node: t.TSInferType, parent: t.Node): boolean {\n  return isTSArrayType(parent) || isTSOptionalType(parent);\n}\n\nexport function TSInstantiationExpression(\n  node: t.TSInstantiationExpression,\n  parent: t.Node,\n) {\n  return (\n    (isCallExpression(parent) ||\n      isOptionalCallExpression(parent) ||\n      isNewExpression(parent) ||\n      isTSInstantiationExpression(parent)) &&\n    !!parent.typeParameters\n  );\n}\n\nexport function BinaryExpression(\n  node: t.BinaryExpression,\n  parent: t.Node,\n): boolean {\n  // let i = (1 in []);\n  // for ((1 in []);;);\n  return (\n    node.operator === \"in\" && (isVariableDeclarator(parent) || isFor(parent))\n  );\n}\n\nexport function SequenceExpression(\n  node: t.SequenceExpression,\n  parent: t.Node,\n): boolean {\n  if (\n    // Although parentheses wouldn\"t hurt around sequence\n    // expressions in the head of for loops, traditional style\n    // dictates that e.g. i++, j++ should not be wrapped with\n    // parentheses.\n    isForStatement(parent) ||\n    isThrowStatement(parent) ||\n    isReturnStatement(parent) ||\n    (isIfStatement(parent) && parent.test === node) ||\n    (isWhileStatement(parent) && parent.test === node) ||\n    (isForInStatement(parent) && parent.right === node) ||\n    (isSwitchStatement(parent) && parent.discriminant === node) ||\n    (isExpressionStatement(parent) && parent.expression === node)\n  ) {\n    return false;\n  }\n\n  // Otherwise err on the side of overparenthesization, adding\n  // explicit exceptions above if this proves overzealous.\n  return true;\n}\n\nexport function YieldExpression(\n  node: t.YieldExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    isBinary(parent) ||\n    isUnaryLike(parent) ||\n    hasPostfixPart(node, parent) ||\n    (isAwaitExpression(parent) && isYieldExpression(node)) ||\n    (isConditionalExpression(parent) && node === parent.test) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport { YieldExpression as AwaitExpression };\n\nexport function ClassExpression(\n  node: t.ClassExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function UnaryLike(\n  node:\n    | t.UnaryLike\n    | t.ArrowFunctionExpression\n    | t.ConditionalExpression\n    | t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    hasPostfixPart(node, parent) ||\n    isBinaryExpression(parent, { operator: \"**\", left: node }) ||\n    isClassExtendsClause(node, parent)\n  );\n}\n\nexport function FunctionExpression(\n  node: t.FunctionExpression,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  return isFirstInContext(\n    printStack,\n    CheckParam.expressionStatement | CheckParam.exportDefault,\n  );\n}\n\nexport function ArrowFunctionExpression(\n  node: t.ArrowFunctionExpression,\n  parent: t.Node,\n): boolean {\n  return isExportDeclaration(parent) || ConditionalExpression(node, parent);\n}\n\nexport function ConditionalExpression(\n  node:\n    | t.ConditionalExpression\n    | t.ArrowFunctionExpression\n    | t.AssignmentExpression,\n  parent?: t.Node,\n): boolean {\n  if (\n    isUnaryLike(parent) ||\n    isBinary(parent) ||\n    isConditionalExpression(parent, { test: node }) ||\n    isAwaitExpression(parent) ||\n    isTSTypeAssertion(parent) ||\n    isTSAsExpression(parent)\n  ) {\n    return true;\n  }\n\n  return UnaryLike(node, parent);\n}\n\nexport function OptionalMemberExpression(\n  node: t.OptionalMemberExpression,\n  parent: t.Node,\n): boolean {\n  return (\n    isCallExpression(parent, { callee: node }) ||\n    isMemberExpression(parent, { object: node })\n  );\n}\n\nexport { OptionalMemberExpression as OptionalCallExpression };\n\nexport function AssignmentExpression(\n  node: t.AssignmentExpression,\n  parent: t.Node,\n): boolean {\n  if (isObjectPattern(node.left)) {\n    return true;\n  } else {\n    return ConditionalExpression(node, parent);\n  }\n}\n\nexport function LogicalExpression(\n  node: t.LogicalExpression,\n  parent: t.Node,\n): boolean {\n  switch (node.operator) {\n    case \"||\":\n      if (!isLogicalExpression(parent)) return false;\n      return parent.operator === \"??\" || parent.operator === \"&&\";\n    case \"&&\":\n      return isLogicalExpression(parent, { operator: \"??\" });\n    case \"??\":\n      return isLogicalExpression(parent) && parent.operator !== \"??\";\n  }\n}\n\nexport function Identifier(\n  node: t.Identifier,\n  parent: t.Node,\n  printStack: Array<t.Node>,\n): boolean {\n  // 13.15.2 AssignmentExpression RS: Evaluation\n  // (fn) = function () {};\n  if (\n    node.extra?.parenthesized &&\n    isAssignmentExpression(parent, { left: node }) &&\n    (isFunctionExpression(parent.right) || isClassExpression(parent.right)) &&\n    parent.right.id == null\n  ) {\n    return true;\n  }\n  // Non-strict code allows the identifier `let`, but it cannot occur as-is in\n  // certain contexts to avoid ambiguity with contextual keyword `let`.\n  if (node.name === \"let\") {\n    // Some contexts only forbid `let [`, so check if the next token would\n    // be the left bracket of a computed member expression.\n    const isFollowedByBracket =\n      isMemberExpression(parent, {\n        object: node,\n        computed: true,\n      }) ||\n      isOptionalMemberExpression(parent, {\n        object: node,\n        computed: true,\n        optional: false,\n      });\n    return isFirstInContext(\n      printStack,\n      isFollowedByBracket\n        ? CheckParam.expressionStatement |\n            CheckParam.forHead |\n            CheckParam.forInHead |\n            CheckParam.forOfHead\n        : CheckParam.forOfHead,\n    );\n  }\n\n  // ECMAScript specifically forbids a for-of loop from starting with the\n  // token sequence `for (async of`, because it would be ambiguous with\n  // `for (async of => {};;)`, so we need to add extra parentheses.\n  //\n  // If the parent is a for-await-of loop (i.e. parent.await === true), the\n  // parentheses aren't strictly needed, but we add them anyway because\n  // some tools (including earlier Babel versions) can't parse\n  // `for await (async of [])` without them.\n  return (\n    node.name === \"async\" && isForOfStatement(parent) && node === parent.left\n  );\n}\n\n// Walk up the print stack to determine if our node can come first\n// in a particular context.\nfunction isFirstInContext(\n  printStack: Array<t.Node>,\n  checkParam: CheckParam,\n): boolean {\n  const expressionStatement = checkParam & CheckParam.expressionStatement;\n  const arrowBody = checkParam & CheckParam.arrowBody;\n  const exportDefault = checkParam & CheckParam.exportDefault;\n  const forHead = checkParam & CheckParam.forHead;\n  const forInHead = checkParam & CheckParam.forInHead;\n  const forOfHead = checkParam & CheckParam.forOfHead;\n\n  let i = printStack.length - 1;\n  if (i <= 0) return;\n  let node = printStack[i];\n  i--;\n  let parent = printStack[i];\n  while (i >= 0) {\n    if (\n      (expressionStatement &&\n        isExpressionStatement(parent, { expression: node })) ||\n      (exportDefault &&\n        isExportDefaultDeclaration(parent, { declaration: node })) ||\n      (arrowBody && isArrowFunctionExpression(parent, { body: node })) ||\n      (forHead && isForStatement(parent, { init: node })) ||\n      (forInHead && isForInStatement(parent, { left: node })) ||\n      (forOfHead && isForOfStatement(parent, { left: node }))\n    ) {\n      return true;\n    }\n\n    if (\n      i > 0 &&\n      ((hasPostfixPart(node, parent) && !isNewExpression(parent)) ||\n        (isSequenceExpression(parent) && parent.expressions[0] === node) ||\n        (isUpdateExpression(parent) && !parent.prefix) ||\n        isConditional(parent, { test: node }) ||\n        isBinary(parent, { left: node }) ||\n        isAssignmentExpression(parent, { left: node }))\n    ) {\n      node = parent;\n      i--;\n      parent = printStack[i];\n    } else {\n      return false;\n    }\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;EACEA,qB;EACAC,yB;EACAC,sB;EACAC,iB;EACAC,Q;EACAC,kB;EACAC,kB;EACAC,gB;EACAC,O;EACAC,iB;EACAC,a;EACAC,uB;EACAC,mB;EACAC,0B;EACAC,qB;EACAC,K;EACAC,gB;EACAC,gB;EACAC,c;EACAC,oB;EACAC,a;EACAC,mB;EACAC,4B;EACAC,mB;EACAC,kB;EACAC,e;EACAC,wB;EACAC,e;EACAC,wB;EACAC,0B;EACAC,iB;EACAC,oB;EACAC,iB;EACAC,a;EACAC,gB;EACAC,2B;EACAC,oB;EACAC,qB;EACAC,gB;EACAC,Y;EACAC,iB;EACAC,a;EACAC,0B;EACAC,gB;EACAC,gB;EACAC,W;EACAC,qB;EACAC,oB;EACAC,gB;EACAC;;AAGF,MAAMC,UAAU,GAAG;EACjB,MAAM,CADW;EAEjB,MAAM,CAFW;EAGjB,MAAM,CAHW;EAIjB,MAAM,CAJW;EAKjB,KAAK,CALY;EAMjB,KAAK,CANY;EAOjB,KAAK,CAPY;EAQjB,MAAM,CARW;EASjB,OAAO,CATU;EAUjB,MAAM,CAVW;EAWjB,OAAO,CAXU;EAYjB,KAAK,CAZY;EAajB,KAAK,CAbY;EAcjB,MAAM,CAdW;EAejB,MAAM,CAfW;EAgBjBC,EAAE,EAAE,CAhBa;EAiBjBC,UAAU,EAAE,CAjBK;EAkBjB,MAAM,CAlBW;EAmBjB,MAAM,CAnBW;EAoBjB,OAAO,CApBU;EAqBjB,KAAK,CArBY;EAsBjB,KAAK,CAtBY;EAuBjB,KAAK,CAvBY;EAwBjB,KAAK,CAxBY;EAyBjB,KAAK,CAzBY;EA0BjB,MAAM;AA1BW,CAAnB;;AAsCA,MAAMC,oBAAoB,GAAG,CAC3BC,IAD2B,EAE3BC,MAF2B,KAGL/C,OAAO,CAAC+C,MAAD,EAAS;EAAEC,UAAU,EAAEF;AAAd,CAAT,CAH/B;;AAKA,MAAMG,cAAc,GAAG,CAACH,IAAD,EAAeC,MAAf,KACpB,CAAC/B,kBAAkB,CAAC+B,MAAD,CAAlB,IAA8B1B,0BAA0B,CAAC0B,MAAD,CAAzD,KACCA,MAAM,CAACG,MAAP,KAAkBJ,IADpB,IAEC,CAAC/C,gBAAgB,CAACgD,MAAD,CAAhB,IACA3B,wBAAwB,CAAC2B,MAAD,CADxB,IAEA9B,eAAe,CAAC8B,MAAD,CAFhB,KAGCA,MAAM,CAACI,MAAP,KAAkBL,IALpB,IAMCZ,0BAA0B,CAACa,MAAD,CAA1B,IAAsCA,MAAM,CAACK,GAAP,KAAeN,IANtD,IAOAjB,qBAAqB,CAACkB,MAAD,CARvB;;AAUO,SAASM,sBAAT,CACLP,IADK,EAELC,MAFK,EAGI;EACT,OAAOvD,qBAAqB,CAACuD,MAAD,CAA5B;AACD;;AAEM,SAASO,sBAAT,CACLR,IADK,EAELC,MAFK,EAGLQ,UAHK,EAII;EACT,IAAIA,UAAU,CAACC,MAAX,GAAoB,CAAxB,EAA2B;EAE3B,OAEElB,qBAAqB,CAACS,MAAD,CAArB,IAEAjC,4BAA4B,CAACiC,MAAD,CAF5B,IAIAvD,qBAAqB,CAACuD,MAAD,CAJrB,IAMCX,gBAAgB,CAACW,MAAD,CAAhB,IAECtD,yBAAyB,CAAC8D,UAAU,CAACA,UAAU,CAACC,MAAX,GAAoB,CAArB,CAAX,CAV7B;AAYD;;AAEM,SAASC,gBAAT,CACLX,IADK,EAELC,MAFK,EAGI;EACT,OAAOE,cAAc,CAACH,IAAD,EAAOC,MAAP,CAAd,IAAgCF,oBAAoB,CAACC,IAAD,EAAOC,MAAP,CAA3D;AACD;;AAEM,SAASW,gBAAT,CACLZ,IADK,EAELC,MAFK,EAGLQ,UAHK,EAII;EACT,OAAOI,gBAAgB,CACrBJ,UADqB,EAErB,KAFqB,CAAvB;AAID;;AAEM,SAASK,YAAT,CACLd,IADK,EAELC,MAFK,EAGLQ,UAHK,EAII;EAET,OACE,CAACT,IAAI,CAACe,KAAN,IAAeF,gBAAgB,CAACJ,UAAD,IADjC;AAGD;;AAEM,SAASO,MAAT,CAAgBhB,IAAhB,EAA0CC,MAA1C,EAAmE;EACxE,IACED,IAAI,CAACiB,QAAL,KAAkB,IAAlB,IACAlE,kBAAkB,CAACkD,MAAD,EAAS;IAAEgB,QAAQ,EAAE;EAAZ,CAAT,CAFpB,EAGE;IACA,OAAOhB,MAAM,CAACiB,IAAP,KAAgBlB,IAAvB;EACD;;EAED,IAAID,oBAAoB,CAACC,IAAD,EAAOC,MAAP,CAAxB,EAAwC;IACtC,OAAO,IAAP;EACD;;EAED,IACEE,cAAc,CAACH,IAAD,EAAOC,MAAP,CAAd,IACAV,WAAW,CAACU,MAAD,CADX,IAEApD,iBAAiB,CAACoD,MAAD,CAHnB,EAIE;IACA,OAAO,IAAP;EACD;;EAED,IAAInD,QAAQ,CAACmD,MAAD,CAAZ,EAAsB;IACpB,MAAMkB,QAAQ,GAAGlB,MAAM,CAACgB,QAAxB;IACA,MAAMG,SAAS,GAAGxB,UAAU,CAACuB,QAAD,CAA5B;IAEA,MAAME,MAAM,GAAGrB,IAAI,CAACiB,QAApB;IACA,MAAMK,OAAO,GAAG1B,UAAU,CAACyB,MAAD,CAA1B;;IAEA,IAEGD,SAAS,KAAKE,OAAd,IACCrB,MAAM,CAACsB,KAAP,KAAiBvB,IADlB,IAEC,CAAC/B,mBAAmB,CAACgC,MAAD,CAFtB,IAGAmB,SAAS,GAAGE,OALd,EAME;MACA,OAAO,IAAP;IACD;EACF;AACF;;AAEM,SAASE,mBAAT,CACLxB,IADK,EAELC,MAFK,EAGI;EACT,OACEvD,qBAAqB,CAACuD,MAAD,CAArB,IACA7B,wBAAwB,CAAC6B,MAAD,CADxB,IAEAjC,4BAA4B,CAACiC,MAAD,CAF5B,IAGAT,qBAAqB,CAACS,MAAD,CAJvB;AAMD;;AAIM,SAASwB,yBAAT,CACLzB,IADK,EAELC,MAFK,EAGI;EACT,OAAOlC,mBAAmB,CAACkC,MAAD,EAAS;IAAEyB,UAAU,EAAE1B;EAAd,CAAT,CAA1B;AACD;;AAEM,SAAS2B,cAAT,GAA0B;EAC/B,OAAO,IAAP;AACD;;AAEM,SAASC,eAAT,GAA2B;EAChC,OAAO,IAAP;AACD;;AAEM,SAASC,WAAT,CAAqB7B,IAArB,EAA0CC,MAA1C,EAAmE;EACxE,OACEtB,aAAa,CAACsB,MAAD,CAAb,IACAjB,gBAAgB,CAACiB,MAAD,CADhB,IAEAnB,oBAAoB,CAACmB,MAAD,CAFpB,IAGAd,aAAa,CAACc,MAAD,CAHb,IAIAhB,YAAY,CAACgB,MAAD,CALd;AAOD;;AAIM,SAAS6B,WAAT,CAAqB9B,IAArB,EAA0CC,MAA1C,EAAmE;EACxE,OAAOtB,aAAa,CAACsB,MAAD,CAAb,IAAyBjB,gBAAgB,CAACiB,MAAD,CAAhD;AACD;;AAEM,SAAS8B,yBAAT,CACL/B,IADK,EAELC,MAFK,EAGL;EACA,OACE,CAAChD,gBAAgB,CAACgD,MAAD,CAAhB,IACC3B,wBAAwB,CAAC2B,MAAD,CADzB,IAEC9B,eAAe,CAAC8B,MAAD,CAFhB,IAGCpB,2BAA2B,CAACoB,MAAD,CAH7B,KAIA,CAAC,CAACA,MAAM,CAAC+B,cALX;AAOD;;AAEM,SAASC,gBAAT,CACLjC,IADK,EAELC,MAFK,EAGI;EAGT,OACED,IAAI,CAACiB,QAAL,KAAkB,IAAlB,KAA2BxB,oBAAoB,CAACQ,MAAD,CAApB,IAAgCxC,KAAK,CAACwC,MAAD,CAAhE,CADF;AAGD;;AAEM,SAASiC,kBAAT,CACLlC,IADK,EAELC,MAFK,EAGI;EACT,IAKErC,cAAc,CAACqC,MAAD,CAAd,IACAZ,gBAAgB,CAACY,MAAD,CADhB,IAEAzB,iBAAiB,CAACyB,MAAD,CAFjB,IAGCnC,aAAa,CAACmC,MAAD,CAAb,IAAyBA,MAAM,CAACkC,IAAP,KAAgBnC,IAH1C,IAICN,gBAAgB,CAACO,MAAD,CAAhB,IAA4BA,MAAM,CAACkC,IAAP,KAAgBnC,IAJ7C,IAKCtC,gBAAgB,CAACuC,MAAD,CAAhB,IAA4BA,MAAM,CAACsB,KAAP,KAAiBvB,IAL9C,IAMCtB,iBAAiB,CAACuB,MAAD,CAAjB,IAA6BA,MAAM,CAACmC,YAAP,KAAwBpC,IANtD,IAOCxC,qBAAqB,CAACyC,MAAD,CAArB,IAAiCA,MAAM,CAACoC,UAAP,KAAsBrC,IAZ1D,EAaE;IACA,OAAO,KAAP;EACD;;EAID,OAAO,IAAP;AACD;;AAEM,SAASsC,eAAT,CACLtC,IADK,EAELC,MAFK,EAGI;EACT,OACEnD,QAAQ,CAACmD,MAAD,CAAR,IACAV,WAAW,CAACU,MAAD,CADX,IAEAE,cAAc,CAACH,IAAD,EAAOC,MAAP,CAFd,IAGCpD,iBAAiB,CAACoD,MAAD,CAAjB,IAA6BN,iBAAiB,CAACK,IAAD,CAH/C,IAIC3C,uBAAuB,CAAC4C,MAAD,CAAvB,IAAmCD,IAAI,KAAKC,MAAM,CAACkC,IAJpD,IAKApC,oBAAoB,CAACC,IAAD,EAAOC,MAAP,CANtB;AAQD;;AAIM,SAASsC,eAAT,CACLvC,IADK,EAELC,MAFK,EAGLQ,UAHK,EAII;EACT,OAAOI,gBAAgB,CACrBJ,UADqB,EAErB,KAFqB,CAAvB;AAID;;AAEM,SAAS+B,SAAT,CACLxC,IADK,EAMLC,MANK,EAOI;EACT,OACEE,cAAc,CAACH,IAAD,EAAOC,MAAP,CAAd,IACAlD,kBAAkB,CAACkD,MAAD,EAAS;IAAEgB,QAAQ,EAAE,IAAZ;IAAkBC,IAAI,EAAElB;EAAxB,CAAT,CADlB,IAEAD,oBAAoB,CAACC,IAAD,EAAOC,MAAP,CAHtB;AAKD;;AAEM,SAASwC,kBAAT,CACLzC,IADK,EAELC,MAFK,EAGLQ,UAHK,EAII;EACT,OAAOI,gBAAgB,CACrBJ,UADqB,EAErB,KAFqB,CAAvB;AAID;;AAEM,SAASiC,uBAAT,CACL1C,IADK,EAELC,MAFK,EAGI;EACT,OAAO3C,mBAAmB,CAAC2C,MAAD,CAAnB,IAA+B0C,qBAAqB,CAAC3C,IAAD,EAAOC,MAAP,CAA3D;AACD;;AAEM,SAAS0C,qBAAT,CACL3C,IADK,EAKLC,MALK,EAMI;EACT,IACEV,WAAW,CAACU,MAAD,CAAX,IACAnD,QAAQ,CAACmD,MAAD,CADR,IAEA5C,uBAAuB,CAAC4C,MAAD,EAAS;IAAEkC,IAAI,EAAEnC;EAAR,CAAT,CAFvB,IAGAnD,iBAAiB,CAACoD,MAAD,CAHjB,IAIAf,iBAAiB,CAACe,MAAD,CAJjB,IAKArB,gBAAgB,CAACqB,MAAD,CANlB,EAOE;IACA,OAAO,IAAP;EACD;;EAED,OAAOuC,SAAS,CAACxC,IAAD,EAAOC,MAAP,CAAhB;AACD;;AAEM,SAAS2C,wBAAT,CACL5C,IADK,EAELC,MAFK,EAGI;EACT,OACEhD,gBAAgB,CAACgD,MAAD,EAAS;IAAEI,MAAM,EAAEL;EAAV,CAAT,CAAhB,IACA9B,kBAAkB,CAAC+B,MAAD,EAAS;IAAEG,MAAM,EAAEJ;EAAV,CAAT,CAFpB;AAID;;AAIM,SAAS6C,oBAAT,CACL7C,IADK,EAELC,MAFK,EAGI;EACT,IAAI5B,eAAe,CAAC2B,IAAI,CAACkB,IAAN,CAAnB,EAAgC;IAC9B,OAAO,IAAP;EACD,CAFD,MAEO;IACL,OAAOyB,qBAAqB,CAAC3C,IAAD,EAAOC,MAAP,CAA5B;EACD;AACF;;AAEM,SAAS6C,iBAAT,CACL9C,IADK,EAELC,MAFK,EAGI;EACT,QAAQD,IAAI,CAACiB,QAAb;IACE,KAAK,IAAL;MACE,IAAI,CAAChD,mBAAmB,CAACgC,MAAD,CAAxB,EAAkC,OAAO,KAAP;MAClC,OAAOA,MAAM,CAACgB,QAAP,KAAoB,IAApB,IAA4BhB,MAAM,CAACgB,QAAP,KAAoB,IAAvD;;IACF,KAAK,IAAL;MACE,OAAOhD,mBAAmB,CAACgC,MAAD,EAAS;QAAEgB,QAAQ,EAAE;MAAZ,CAAT,CAA1B;;IACF,KAAK,IAAL;MACE,OAAOhD,mBAAmB,CAACgC,MAAD,CAAnB,IAA+BA,MAAM,CAACgB,QAAP,KAAoB,IAA1D;EAPJ;AASD;;AAEM,SAAS8B,UAAT,CACL/C,IADK,EAELC,MAFK,EAGLQ,UAHK,EAII;EAAA;;EAGT,IACE,eAAAT,IAAI,CAACgD,KAAL,yBAAYC,aAAZ,IACArG,sBAAsB,CAACqD,MAAD,EAAS;IAAEiB,IAAI,EAAElB;EAAR,CAAT,CADtB,KAECnC,oBAAoB,CAACoC,MAAM,CAACsB,KAAR,CAApB,IAAsCpE,iBAAiB,CAAC8C,MAAM,CAACsB,KAAR,CAFxD,KAGAtB,MAAM,CAACsB,KAAP,CAAa2B,EAAb,IAAmB,IAJrB,EAKE;IACA,OAAO,IAAP;EACD;;EAGD,IAAIlD,IAAI,CAACmD,IAAL,KAAc,KAAlB,EAAyB;IAGvB,MAAMC,mBAAmB,GACvBlF,kBAAkB,CAAC+B,MAAD,EAAS;MACzBG,MAAM,EAAEJ,IADiB;MAEzBqD,QAAQ,EAAE;IAFe,CAAT,CAAlB,IAIA9E,0BAA0B,CAAC0B,MAAD,EAAS;MACjCG,MAAM,EAAEJ,IADyB;MAEjCqD,QAAQ,EAAE,IAFuB;MAGjCC,QAAQ,EAAE;IAHuB,CAAT,CAL5B;IAUA,OAAOzC,gBAAgB,CACrBJ,UADqB,EAErB2C,mBAAmB,GACf,eADe,KAFE,CAAvB;EASD;;EAUD,OACEpD,IAAI,CAACmD,IAAL,KAAc,OAAd,IAAyBxF,gBAAgB,CAACsC,MAAD,CAAzC,IAAqDD,IAAI,KAAKC,MAAM,CAACiB,IADvE;AAGD;;AAID,SAASL,gBAAT,CACEJ,UADF,EAEE8C,UAFF,EAGW;EACT,MAAMC,mBAAmB,GAAGD,UAAU,IAAtC;EACA,MAAME,SAAS,GAAGF,UAAU,IAA5B;EACA,MAAMG,aAAa,GAAGH,UAAU,IAAhC;EACA,MAAMI,OAAO,GAAGJ,UAAU,IAA1B;EACA,MAAMK,SAAS,GAAGL,UAAU,KAA5B;EACA,MAAMM,SAAS,GAAGN,UAAU,KAA5B;EAEA,IAAIO,CAAC,GAAGrD,UAAU,CAACC,MAAX,GAAoB,CAA5B;EACA,IAAIoD,CAAC,IAAI,CAAT,EAAY;EACZ,IAAI9D,IAAI,GAAGS,UAAU,CAACqD,CAAD,CAArB;EACAA,CAAC;EACD,IAAI7D,MAAM,GAAGQ,UAAU,CAACqD,CAAD,CAAvB;;EACA,OAAOA,CAAC,IAAI,CAAZ,EAAe;IACb,IACGN,mBAAmB,IAClBhG,qBAAqB,CAACyC,MAAD,EAAS;MAAEoC,UAAU,EAAErC;IAAd,CAAT,CADvB,IAEC0D,aAAa,IACZnG,0BAA0B,CAAC0C,MAAD,EAAS;MAAE8D,WAAW,EAAE/D;IAAf,CAAT,CAH5B,IAICyD,SAAS,IAAI9G,yBAAyB,CAACsD,MAAD,EAAS;MAAE+D,IAAI,EAAEhE;IAAR,CAAT,CAJvC,IAKC2D,OAAO,IAAI/F,cAAc,CAACqC,MAAD,EAAS;MAAEgE,IAAI,EAAEjE;IAAR,CAAT,CAL1B,IAMC4D,SAAS,IAAIlG,gBAAgB,CAACuC,MAAD,EAAS;MAAEiB,IAAI,EAAElB;IAAR,CAAT,CAN9B,IAOC6D,SAAS,IAAIlG,gBAAgB,CAACsC,MAAD,EAAS;MAAEiB,IAAI,EAAElB;IAAR,CAAT,CARhC,EASE;MACA,OAAO,IAAP;IACD;;IAED,IACE8D,CAAC,GAAG,CAAJ,KACE3D,cAAc,CAACH,IAAD,EAAOC,MAAP,CAAd,IAAgC,CAAC9B,eAAe,CAAC8B,MAAD,CAAjD,IACExB,oBAAoB,CAACwB,MAAD,CAApB,IAAgCA,MAAM,CAACiE,WAAP,CAAmB,CAAnB,MAA0BlE,IAD5D,IAEEhD,kBAAkB,CAACiD,MAAD,CAAlB,IAA8B,CAACA,MAAM,CAACkE,MAFxC,IAGC/G,aAAa,CAAC6C,MAAD,EAAS;MAAEkC,IAAI,EAAEnC;IAAR,CAAT,CAHd,IAIClD,QAAQ,CAACmD,MAAD,EAAS;MAAEiB,IAAI,EAAElB;IAAR,CAAT,CAJT,IAKCpD,sBAAsB,CAACqD,MAAD,EAAS;MAAEiB,IAAI,EAAElB;IAAR,CAAT,CANxB,CADF,EAQE;MACAA,IAAI,GAAGC,MAAP;MACA6D,CAAC;MACD7D,MAAM,GAAGQ,UAAU,CAACqD,CAAD,CAAnB;IACD,CAZD,MAYO;MACL,OAAO,KAAP;IACD;EACF;;EAED,OAAO,KAAP;AACD"}