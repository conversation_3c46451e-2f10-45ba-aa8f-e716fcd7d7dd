name: Release

on:
  release:
    types: [published]
  workflow_dispatch:
    inputs:
      tag:
        description: 'Release tag'
        required: true
        default: 'latest'

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: motea

jobs:
  release-docker:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}
          tags: |
            type=ref,event=tag
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=semver,pattern={{major}}
            type=raw,value=latest,enable={{is_default_branch}}
            type=raw,value=${{ github.event.inputs.tag }},enable=${{ github.event_name == 'workflow_dispatch' }}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            BUILDTIME=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
            VERSION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.version'] }}
            REVISION=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.revision'] }}

      - name: Generate release notes
        id: release_notes
        run: |
          cat << EOF > release_notes.md
          ## Docker Images

          This release includes multi-architecture Docker images:

          ### Pull Commands
          \`\`\`bash
          # Latest release
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:latest

          # Specific version
          docker pull ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }}
          \`\`\`

          ### Supported Architectures
          - linux/amd64
          - linux/arm64

          ### Quick Start
          \`\`\`bash
          # Download configuration template
          curl -O https://raw.githubusercontent.com/${{ github.repository }}/main/motea.conf.example
          cp motea.conf.example motea.conf
          # Edit motea.conf to set your password

          # Run container
          docker run -d \\
            --name motea \\
            -p 3000:3000 \\
            -v motea_data:/data \\
            -v ./motea.conf:/app/motea.conf:ro \\
            --restart unless-stopped \\
            ${{ env.REGISTRY }}/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }}
          \`\`\`

          ### Docker Compose
          \`\`\`yaml
          version: '3.8'
          services:
            motea:
              image: ${{ env.REGISTRY }}/${{ github.repository_owner }}/${{ env.IMAGE_NAME }}:${{ github.ref_name }}
              ports:
                - "3000:3000"
              environment:
                - PASSWORD=your_secure_password
                - PRELOAD_NOTES_COUNT=10
              volumes:
                - motea_data:/data
                - ./motea.conf:/app/motea.conf:ro
          volumes:
            motea_data:
          \`\`\`

          For more deployment options, see [STANDALONE.md](https://github.com/${{ github.repository }}/blob/main/STANDALONE.md).
          EOF

      - name: Update release with Docker info
        if: github.event_name == 'release'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const releaseNotes = fs.readFileSync('release_notes.md', 'utf8');
            
            await github.rest.repos.updateRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              release_id: context.payload.release.id,
              body: context.payload.release.body + '\n\n' + releaseNotes
            });

  # Docker Hub integration removed as we're using GitHub Container Registry
