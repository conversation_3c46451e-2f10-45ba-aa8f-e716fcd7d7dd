{"version": 3, "names": ["corejs2DefaultWebIncludes", "v", "OptionValidator", "allPluginsList", "Object", "keys", "pluginsList", "modulePlugins", "moduleTransformations", "map", "m", "getValidIncludesAndExcludes", "type", "corejs", "Array", "from", "Set", "corejs2Polyfills", "corejs3Polyfills", "flatMap", "array", "fn", "prototype", "concat", "apply", "expandIncludesAndExcludes", "filterList", "length", "filterableItems", "invalidFilters", "selected<PERSON><PERSON><PERSON>", "filter", "re", "RegExp", "normalizePluginName", "e", "push", "items", "item", "test", "replace", "invariant", "join", "plugin", "checkDuplicateIncludeExcludes", "include", "exclude", "duplicates", "opt", "indexOf", "normalizeTargets", "targets", "isArray", "browsers", "validateModulesOption", "modulesOpt", "ModulesOption", "auto", "toString", "false", "validateUseBuiltInsOption", "builtInsOpt", "UseBuiltInsOption", "normalizeCoreJSOption", "useBuiltIns", "proposals", "rawVersion", "undefined", "console", "warn", "version", "Boolean", "semver", "coerce", "String", "major", "RangeError", "normalizeOptions", "opts", "validateTopLevelOptions", "TopLevelOptions", "bugfixes", "validateBooleanOption", "config<PERSON><PERSON>", "validateStringOption", "process", "cwd", "debug", "forceAllTransforms", "ignoreBrowserslistConfig", "loose", "modules", "shippedProposals", "spec", "browserslistEnv"], "sources": ["../src/normalize-options.ts"], "sourcesContent": ["import semver, { type SemVer } from \"semver\";\nimport corejs2Polyfills from \"@babel/compat-data/corejs2-built-ins\";\n// @ts-expect-error Fixme: TS can not infer types from ../data/core-js-compat.js\n// but we can't import core-js-compat/data.json because JSON imports do\n// not work on Node 14\nimport corejs3Polyfills from \"../data/core-js-compat\";\nimport { plugins as pluginsList } from \"./plugins-compat-data\";\nimport moduleTransformations from \"./module-transformations\";\nimport { TopLevelOptions, ModulesOption, UseBuiltInsOption } from \"./options\";\nimport { OptionValidator } from \"@babel/helper-validator-option\";\n\nconst corejs2DefaultWebIncludes = [\n  \"web.timers\",\n  \"web.immediate\",\n  \"web.dom.iterable\",\n];\n\nimport type {\n  BuiltInsOption,\n  CorejsOption,\n  ModuleOption,\n  Options,\n  PluginListOption,\n} from \"./types\";\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\n\nconst v = new OptionValidator(PACKAGE_JSON.name);\n\nconst allPluginsList = Object.keys(pluginsList);\n\n// NOTE: Since module plugins are handled separately compared to other plugins (via the \"modules\" option) it\n// should only be possible to exclude and not include module plugins, otherwise it's possible that preset-env\n// will add a module plugin twice.\nconst modulePlugins = [\n  \"transform-dynamic-import\",\n  ...Object.keys(moduleTransformations).map(m => moduleTransformations[m]),\n];\n\nconst getValidIncludesAndExcludes = (\n  type: \"include\" | \"exclude\",\n  corejs: number | false,\n) =>\n  Array.from(\n    new Set([\n      ...allPluginsList,\n      ...(type === \"exclude\" ? modulePlugins : []),\n      ...(corejs\n        ? corejs == 2\n          ? [...Object.keys(corejs2Polyfills), ...corejs2DefaultWebIncludes]\n          : Object.keys(corejs3Polyfills)\n        : []),\n    ]),\n  );\n\nfunction flatMap<T, U>(array: Array<T>, fn: (item: T) => Array<U>): Array<U> {\n  return Array.prototype.concat.apply([], array.map(fn));\n}\n\nconst expandIncludesAndExcludes = (\n  filterList: PluginListOption = [],\n  type: \"include\" | \"exclude\",\n  corejs: number | false,\n) => {\n  if (filterList.length === 0) return [];\n\n  const filterableItems = getValidIncludesAndExcludes(type, corejs);\n\n  const invalidFilters: PluginListOption = [];\n  const selectedPlugins = flatMap(filterList, filter => {\n    let re: RegExp;\n    if (typeof filter === \"string\") {\n      try {\n        re = new RegExp(`^${normalizePluginName(filter)}$`);\n      } catch (e) {\n        invalidFilters.push(filter);\n        return [];\n      }\n    } else {\n      re = filter;\n    }\n    const items = filterableItems.filter(\n      item =>\n        re.test(item) ||\n        // For backwards compatibility, we also support matching against the\n        // proposal- name.\n        // TODO(Babel 8): Remove this.\n        re.test(item.replace(/^transform-/, \"proposal-\")),\n    );\n    if (items.length === 0) invalidFilters.push(filter);\n    return items;\n  });\n\n  v.invariant(\n    invalidFilters.length === 0,\n    `The plugins/built-ins '${invalidFilters.join(\n      \", \",\n    )}' passed to the '${type}' option are not\n    valid. Please check data/[plugin-features|built-in-features].js in babel-preset-env`,\n  );\n\n  return selectedPlugins;\n};\n\nexport const normalizePluginName = (plugin: string) =>\n  plugin.replace(/^(@babel\\/|babel-)(plugin-)?/, \"\");\n\nexport const checkDuplicateIncludeExcludes = (\n  include: Array<string> = [],\n  exclude: Array<string> = [],\n) => {\n  const duplicates = include.filter(opt => exclude.indexOf(opt) >= 0);\n\n  v.invariant(\n    duplicates.length === 0,\n    `The plugins/built-ins '${duplicates.join(\n      \", \",\n    )}' were found in both the \"include\" and\n    \"exclude\" options.`,\n  );\n};\n\nconst normalizeTargets = (\n  targets: string | string[] | Options[\"targets\"],\n): Options[\"targets\"] => {\n  // TODO: Allow to use only query or strings as a targets from next breaking change.\n  if (typeof targets === \"string\" || Array.isArray(targets)) {\n    return { browsers: targets };\n  }\n  return { ...targets };\n};\n\nexport const validateModulesOption = (\n  modulesOpt: ModuleOption = ModulesOption.auto,\n) => {\n  v.invariant(\n    // @ts-expect-error we have provided fallback for undefined keys\n    ModulesOption[modulesOpt.toString()] || modulesOpt === ModulesOption.false,\n    `The 'modules' option must be one of \\n` +\n      ` - 'false' to indicate no module processing\\n` +\n      ` - a specific module type: 'commonjs', 'amd', 'umd', 'systemjs'` +\n      ` - 'auto' (default) which will automatically select 'false' if the current\\n` +\n      `   process is known to support ES module syntax, or \"commonjs\" otherwise\\n`,\n  );\n\n  return modulesOpt;\n};\n\nexport const validateUseBuiltInsOption = (\n  builtInsOpt: BuiltInsOption = false,\n) => {\n  v.invariant(\n    // @ts-expect-error we have provided fallback for undefined keys\n    UseBuiltInsOption[builtInsOpt.toString()] ||\n      builtInsOpt === UseBuiltInsOption.false,\n    `The 'useBuiltIns' option must be either\n    'false' (default) to indicate no polyfill,\n    '\"entry\"' to indicate replacing the entry polyfill, or\n    '\"usage\"' to import only used polyfills per file`,\n  );\n\n  return builtInsOpt;\n};\n\nexport type NormalizedCorejsOption = {\n  proposals: boolean;\n  version: SemVer | null | false;\n};\n\nexport function normalizeCoreJSOption(\n  corejs: CorejsOption | undefined | null,\n  useBuiltIns: BuiltInsOption,\n): NormalizedCorejsOption {\n  let proposals = false;\n  let rawVersion;\n\n  if (useBuiltIns && corejs === undefined) {\n    rawVersion = 2;\n    console.warn(\n      \"\\nWARNING (@babel/preset-env): We noticed you're using the `useBuiltIns` option without declaring a \" +\n        \"core-js version. Currently, we assume version 2.x when no version \" +\n        \"is passed. Since this default version will likely change in future \" +\n        \"versions of Babel, we recommend explicitly setting the core-js version \" +\n        \"you are using via the `corejs` option.\\n\" +\n        \"\\nYou should also be sure that the version you pass to the `corejs` \" +\n        \"option matches the version specified in your `package.json`'s \" +\n        \"`dependencies` section. If it doesn't, you need to run one of the \" +\n        \"following commands:\\n\\n\" +\n        \"  npm install --save core-js@2    npm install --save core-js@3\\n\" +\n        \"  yarn add core-js@2              yarn add core-js@3\\n\\n\" +\n        \"More info about useBuiltIns: https://babeljs.io/docs/en/babel-preset-env#usebuiltins\\n\" +\n        \"More info about core-js: https://babeljs.io/docs/en/babel-preset-env#corejs\",\n    );\n  } else if (typeof corejs === \"object\" && corejs !== null) {\n    rawVersion = corejs.version;\n    proposals = Boolean(corejs.proposals);\n  } else {\n    rawVersion = corejs;\n  }\n\n  const version = rawVersion ? semver.coerce(String(rawVersion)) : false;\n\n  if (!useBuiltIns && version) {\n    console.warn(\n      \"\\nWARNING (@babel/preset-env): The `corejs` option only has an effect when the `useBuiltIns` option is not `false`\\n\",\n    );\n  }\n\n  if (useBuiltIns && (!version || version.major < 2 || version.major > 3)) {\n    throw new RangeError(\n      \"Invalid Option: The version passed to `corejs` is invalid. Currently, \" +\n        \"only core-js@2 and core-js@3 are supported.\",\n    );\n  }\n\n  return { version, proposals };\n}\n\nexport default function normalizeOptions(opts: Options) {\n  v.validateTopLevelOptions(opts, TopLevelOptions);\n\n  const useBuiltIns = validateUseBuiltInsOption(opts.useBuiltIns);\n\n  const corejs = normalizeCoreJSOption(opts.corejs, useBuiltIns);\n\n  const include = expandIncludesAndExcludes(\n    opts.include,\n    TopLevelOptions.include,\n    !!corejs.version && corejs.version.major,\n  );\n\n  const exclude = expandIncludesAndExcludes(\n    opts.exclude,\n    TopLevelOptions.exclude,\n    !!corejs.version && corejs.version.major,\n  );\n\n  checkDuplicateIncludeExcludes(include, exclude);\n\n  return {\n    bugfixes: v.validateBooleanOption(\n      TopLevelOptions.bugfixes,\n      opts.bugfixes,\n      process.env.BABEL_8_BREAKING ? true : false,\n    ),\n    configPath: v.validateStringOption(\n      TopLevelOptions.configPath,\n      opts.configPath,\n      process.cwd(),\n    ),\n    corejs,\n    debug: v.validateBooleanOption(TopLevelOptions.debug, opts.debug, false),\n    include,\n    exclude,\n    forceAllTransforms: v.validateBooleanOption(\n      TopLevelOptions.forceAllTransforms,\n      opts.forceAllTransforms,\n      false,\n    ),\n    ignoreBrowserslistConfig: v.validateBooleanOption(\n      TopLevelOptions.ignoreBrowserslistConfig,\n      opts.ignoreBrowserslistConfig,\n      false,\n    ),\n    loose: v.validateBooleanOption<boolean>(TopLevelOptions.loose, opts.loose),\n    modules: validateModulesOption(opts.modules),\n    shippedProposals: v.validateBooleanOption(\n      TopLevelOptions.shippedProposals,\n      opts.shippedProposals,\n      false,\n    ),\n    spec: v.validateBooleanOption(TopLevelOptions.spec, opts.spec, false),\n    targets: normalizeTargets(opts.targets),\n    useBuiltIns: useBuiltIns,\n    browserslistEnv: v.validateStringOption<string>(\n      TopLevelOptions.browserslistEnv,\n      opts.browserslistEnv,\n    ),\n  };\n}\n"], "mappings": ";;;;;;;;;;AAAA;;AACA;;AAIA;;AACA;;AACA;;AACA;;AACA;;AAEA,MAAMA,yBAAyB,GAAG,CAChC,YADgC,EAEhC,eAFgC,EAGhC,kBAHgC,CAAlC;AAgBA,MAAMC,CAAC,GAAG,IAAIC,sCAAJ,qBAAV;AAEA,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAP,CAAYC,0BAAZ,CAAvB;AAKA,MAAMC,aAAa,GAAG,CACpB,0BADoB,EAEpB,GAAGH,MAAM,CAACC,IAAP,CAAYG,8BAAZ,EAAmCC,GAAnC,CAAuCC,CAAC,IAAIF,8BAAA,CAAsBE,CAAtB,CAA5C,CAFiB,CAAtB;;AAKA,MAAMC,2BAA2B,GAAG,CAClCC,IADkC,EAElCC,MAFkC,KAIlCC,KAAK,CAACC,IAAN,CACE,IAAIC,GAAJ,CAAQ,CACN,GAAGb,cADG,EAEN,IAAIS,IAAI,KAAK,SAAT,GAAqBL,aAArB,GAAqC,EAAzC,CAFM,EAGN,IAAIM,MAAM,GACNA,MAAM,IAAI,CAAV,GACE,CAAC,GAAGT,MAAM,CAACC,IAAP,CAAYY,gBAAZ,CAAJ,EAAmC,GAAGjB,yBAAtC,CADF,GAEEI,MAAM,CAACC,IAAP,CAAYa,aAAZ,CAHI,GAIN,EAJJ,CAHM,CAAR,CADF,CAJF;;AAgBA,SAASC,OAAT,CAAuBC,KAAvB,EAAwCC,EAAxC,EAA6E;EAC3E,OAAOP,KAAK,CAACQ,SAAN,CAAgBC,MAAhB,CAAuBC,KAAvB,CAA6B,EAA7B,EAAiCJ,KAAK,CAACX,GAAN,CAAUY,EAAV,CAAjC,CAAP;AACD;;AAED,MAAMI,yBAAyB,GAAG,CAChCC,UAA4B,GAAG,EADC,EAEhCd,IAFgC,EAGhCC,MAHgC,KAI7B;EACH,IAAIa,UAAU,CAACC,MAAX,KAAsB,CAA1B,EAA6B,OAAO,EAAP;EAE7B,MAAMC,eAAe,GAAGjB,2BAA2B,CAACC,IAAD,EAAOC,MAAP,CAAnD;EAEA,MAAMgB,cAAgC,GAAG,EAAzC;EACA,MAAMC,eAAe,GAAGX,OAAO,CAACO,UAAD,EAAaK,MAAM,IAAI;IACpD,IAAIC,EAAJ;;IACA,IAAI,OAAOD,MAAP,KAAkB,QAAtB,EAAgC;MAC9B,IAAI;QACFC,EAAE,GAAG,IAAIC,MAAJ,CAAY,IAAGC,mBAAmB,CAACH,MAAD,CAAS,GAA3C,CAAL;MACD,CAFD,CAEE,OAAOI,CAAP,EAAU;QACVN,cAAc,CAACO,IAAf,CAAoBL,MAApB;QACA,OAAO,EAAP;MACD;IACF,CAPD,MAOO;MACLC,EAAE,GAAGD,MAAL;IACD;;IACD,MAAMM,KAAK,GAAGT,eAAe,CAACG,MAAhB,CACZO,IAAI,IACFN,EAAE,CAACO,IAAH,CAAQD,IAAR,KAIAN,EAAE,CAACO,IAAH,CAAQD,IAAI,CAACE,OAAL,CAAa,aAAb,EAA4B,WAA5B,CAAR,CANU,CAAd;IAQA,IAAIH,KAAK,CAACV,MAAN,KAAiB,CAArB,EAAwBE,cAAc,CAACO,IAAf,CAAoBL,MAApB;IACxB,OAAOM,KAAP;EACD,CAtB8B,CAA/B;EAwBApC,CAAC,CAACwC,SAAF,CACEZ,cAAc,CAACF,MAAf,KAA0B,CAD5B,EAEG,0BAAyBE,cAAc,CAACa,IAAf,CACxB,IADwB,CAExB,oBAAmB9B,IAAK;AAC9B,wFALE;EAQA,OAAOkB,eAAP;AACD,CA3CD;;AA6CO,MAAMI,mBAAmB,GAAIS,MAAD,IACjCA,MAAM,CAACH,OAAP,CAAe,8BAAf,EAA+C,EAA/C,CADK;;;;AAGA,MAAMI,6BAA6B,GAAG,CAC3CC,OAAsB,GAAG,EADkB,EAE3CC,OAAsB,GAAG,EAFkB,KAGxC;EACH,MAAMC,UAAU,GAAGF,OAAO,CAACd,MAAR,CAAeiB,GAAG,IAAIF,OAAO,CAACG,OAAR,CAAgBD,GAAhB,KAAwB,CAA9C,CAAnB;EAEA/C,CAAC,CAACwC,SAAF,CACEM,UAAU,CAACpB,MAAX,KAAsB,CADxB,EAEG,0BAAyBoB,UAAU,CAACL,IAAX,CACxB,IADwB,CAExB;AACN,uBALE;AAOD,CAbM;;;;AAeP,MAAMQ,gBAAgB,GACpBC,OADuB,IAEA;EAEvB,IAAI,OAAOA,OAAP,KAAmB,QAAnB,IAA+BrC,KAAK,CAACsC,OAAN,CAAcD,OAAd,CAAnC,EAA2D;IACzD,OAAO;MAAEE,QAAQ,EAAEF;IAAZ,CAAP;EACD;;EACD,yBAAYA,OAAZ;AACD,CARD;;AAUO,MAAMG,qBAAqB,GAAG,CACnCC,UAAwB,GAAGC,sBAAA,CAAcC,IADN,KAEhC;EACHxD,CAAC,CAACwC,SAAF,CAEEe,sBAAA,CAAcD,UAAU,CAACG,QAAX,EAAd,KAAwCH,UAAU,KAAKC,sBAAA,CAAcG,KAFvE,EAGG,wCAAD,GACG,+CADH,GAEG,iEAFH,GAGG,8EAHH,GAIG,4EAPL;EAUA,OAAOJ,UAAP;AACD,CAdM;;;;AAgBA,MAAMK,yBAAyB,GAAG,CACvCC,WAA2B,GAAG,KADS,KAEpC;EACH5D,CAAC,CAACwC,SAAF,CAEEqB,0BAAA,CAAkBD,WAAW,CAACH,QAAZ,EAAlB,KACEG,WAAW,KAAKC,0BAAA,CAAkBH,KAHtC,EAIG;AACL;AACA;AACA,qDAPE;EAUA,OAAOE,WAAP;AACD,CAdM;;;;AAqBA,SAASE,qBAAT,CACLlD,MADK,EAELmD,WAFK,EAGmB;EACxB,IAAIC,SAAS,GAAG,KAAhB;EACA,IAAIC,UAAJ;;EAEA,IAAIF,WAAW,IAAInD,MAAM,KAAKsD,SAA9B,EAAyC;IACvCD,UAAU,GAAG,CAAb;IACAE,OAAO,CAACC,IAAR,CACE,yGACE,oEADF,GAEE,qEAFF,GAGE,yEAHF,GAIE,0CAJF,GAKE,sEALF,GAME,gEANF,GAOE,oEAPF,GAQE,yBARF,GASE,kEATF,GAUE,0DAVF,GAWE,wFAXF,GAYE,6EAbJ;EAeD,CAjBD,MAiBO,IAAI,OAAOxD,MAAP,KAAkB,QAAlB,IAA8BA,MAAM,KAAK,IAA7C,EAAmD;IACxDqD,UAAU,GAAGrD,MAAM,CAACyD,OAApB;IACAL,SAAS,GAAGM,OAAO,CAAC1D,MAAM,CAACoD,SAAR,CAAnB;EACD,CAHM,MAGA;IACLC,UAAU,GAAGrD,MAAb;EACD;;EAED,MAAMyD,OAAO,GAAGJ,UAAU,GAAGM,OAAM,CAACC,MAAP,CAAcC,MAAM,CAACR,UAAD,CAApB,CAAH,GAAuC,KAAjE;;EAEA,IAAI,CAACF,WAAD,IAAgBM,OAApB,EAA6B;IAC3BF,OAAO,CAACC,IAAR,CACE,sHADF;EAGD;;EAED,IAAIL,WAAW,KAAK,CAACM,OAAD,IAAYA,OAAO,CAACK,KAAR,GAAgB,CAA5B,IAAiCL,OAAO,CAACK,KAAR,GAAgB,CAAtD,CAAf,EAAyE;IACvE,MAAM,IAAIC,UAAJ,CACJ,2EACE,6CAFE,CAAN;EAID;;EAED,OAAO;IAAEN,OAAF;IAAWL;EAAX,CAAP;AACD;;AAEc,SAASY,gBAAT,CAA0BC,IAA1B,EAAyC;EACtD7E,CAAC,CAAC8E,uBAAF,CAA0BD,IAA1B,EAAgCE,wBAAhC;EAEA,MAAMhB,WAAW,GAAGJ,yBAAyB,CAACkB,IAAI,CAACd,WAAN,CAA7C;EAEA,MAAMnD,MAAM,GAAGkD,qBAAqB,CAACe,IAAI,CAACjE,MAAN,EAAcmD,WAAd,CAApC;EAEA,MAAMnB,OAAO,GAAGpB,yBAAyB,CACvCqD,IAAI,CAACjC,OADkC,EAEvCmC,wBAAA,CAAgBnC,OAFuB,EAGvC,CAAC,CAAChC,MAAM,CAACyD,OAAT,IAAoBzD,MAAM,CAACyD,OAAP,CAAeK,KAHI,CAAzC;EAMA,MAAM7B,OAAO,GAAGrB,yBAAyB,CACvCqD,IAAI,CAAChC,OADkC,EAEvCkC,wBAAA,CAAgBlC,OAFuB,EAGvC,CAAC,CAACjC,MAAM,CAACyD,OAAT,IAAoBzD,MAAM,CAACyD,OAAP,CAAeK,KAHI,CAAzC;EAMA/B,6BAA6B,CAACC,OAAD,EAAUC,OAAV,CAA7B;EAEA,OAAO;IACLmC,QAAQ,EAAEhF,CAAC,CAACiF,qBAAF,CACRF,wBAAA,CAAgBC,QADR,EAERH,IAAI,CAACG,QAFG,EAG8B,KAH9B,CADL;IAMLE,UAAU,EAAElF,CAAC,CAACmF,oBAAF,CACVJ,wBAAA,CAAgBG,UADN,EAEVL,IAAI,CAACK,UAFK,EAGVE,OAAO,CAACC,GAAR,EAHU,CANP;IAWLzE,MAXK;IAYL0E,KAAK,EAAEtF,CAAC,CAACiF,qBAAF,CAAwBF,wBAAA,CAAgBO,KAAxC,EAA+CT,IAAI,CAACS,KAApD,EAA2D,KAA3D,CAZF;IAaL1C,OAbK;IAcLC,OAdK;IAeL0C,kBAAkB,EAAEvF,CAAC,CAACiF,qBAAF,CAClBF,wBAAA,CAAgBQ,kBADE,EAElBV,IAAI,CAACU,kBAFa,EAGlB,KAHkB,CAff;IAoBLC,wBAAwB,EAAExF,CAAC,CAACiF,qBAAF,CACxBF,wBAAA,CAAgBS,wBADQ,EAExBX,IAAI,CAACW,wBAFmB,EAGxB,KAHwB,CApBrB;IAyBLC,KAAK,EAAEzF,CAAC,CAACiF,qBAAF,CAAiCF,wBAAA,CAAgBU,KAAjD,EAAwDZ,IAAI,CAACY,KAA7D,CAzBF;IA0BLC,OAAO,EAAErC,qBAAqB,CAACwB,IAAI,CAACa,OAAN,CA1BzB;IA2BLC,gBAAgB,EAAE3F,CAAC,CAACiF,qBAAF,CAChBF,wBAAA,CAAgBY,gBADA,EAEhBd,IAAI,CAACc,gBAFW,EAGhB,KAHgB,CA3Bb;IAgCLC,IAAI,EAAE5F,CAAC,CAACiF,qBAAF,CAAwBF,wBAAA,CAAgBa,IAAxC,EAA8Cf,IAAI,CAACe,IAAnD,EAAyD,KAAzD,CAhCD;IAiCL1C,OAAO,EAAED,gBAAgB,CAAC4B,IAAI,CAAC3B,OAAN,CAjCpB;IAkCLa,WAAW,EAAEA,WAlCR;IAmCL8B,eAAe,EAAE7F,CAAC,CAACmF,oBAAF,CACfJ,wBAAA,CAAgBc,eADD,EAEfhB,IAAI,CAACgB,eAFU;EAnCZ,CAAP;AAwCD"}