{"version": 3, "names": ["incrementId", "id", "idx", "length", "unshift", "current", "createPrivateUidGeneratorForClass", "classPath", "currentPrivateId", "privateNames", "Set", "traverse", "PrivateName", "path", "add", "node", "name", "reifiedId", "String", "fromCharCode", "has", "t", "privateName", "identifier", "createLazyPrivateUidGeneratorForClass", "generator", "replaceClassWithVar", "type", "varId", "scope", "generateUidIdentifierBasedOnNode", "classId", "rename", "insertBefore", "variableDeclaration", "variableDeclarator", "get", "replaceWith", "cloneNode", "className", "parent", "generateDeclaredUidIdentifier", "parentPath", "newClassExpr", "classExpression", "superClass", "body", "newPath", "sequenceExpression", "generateClassProperty", "key", "value", "isStatic", "classPrivateProperty", "undefined", "classProperty", "addProxyAccessorsFor", "element", "original<PERSON>ey", "<PERSON><PERSON><PERSON>", "isComputed", "static", "getterBody", "blockStatement", "returnStatement", "memberExpression", "thisExpression", "setterBody", "expressionStatement", "assignmentExpression", "getter", "setter", "classPrivateMethod", "classMethod", "insertAfter", "extractProxyAccessorsFor", "functionExpression", "FIELD", "ACCESSOR", "METHOD", "GETTER", "SETTER", "STATIC", "getElementKind", "kind", "isDecoratorInfo", "info", "filteredOrderedDecoratorInfo", "filtered", "filter", "el", "generateDecorationExprs", "arrayExpression", "map", "decs", "decorators", "decInfo", "numericLiteral", "privateMethods", "Array", "isArray", "push", "extractElementLocalAssignments", "decorationInfo", "localIds", "locals", "addCallAccessorsFor", "getId", "setId", "callExpression", "isNotTsParameter", "movePrivateAccessor", "methodLocalVar", "params", "block", "isClassDecoratableElementPath", "staticBlockToIIFE", "arrowFunctionExpression", "maybeSequenceExpression", "exprs", "unaryExpression", "transformClass", "state", "constant<PERSON>uper", "version", "classDecorators", "hasElementDecorators", "generateClassPrivateUid", "computed", "newId", "valueNode", "newField", "elementDecoratorInfo", "firstFieldPath", "constructorPath", "requiresProtoInit", "requiresStaticInit", "decoratedPrivateMethods", "protoInitLocal", "staticInitLocal", "classInitLocal", "classLocal", "assignments", "scopeParent", "memoiseExpression", "expression", "hint", "localEvaluatedId", "localId", "classDecorator", "generateUidIdentifier", "hasDecorators", "decoratorPath", "isPrivate", "isClassMethod", "newFieldInitId", "newValue", "initId", "valuePath", "v", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "file", "refToPreserve", "replace", "async", "isAsync", "remove", "nameExpr", "stringLiteral", "d", "elementDecorations", "classDecorations", "protoInitCall", "CallExpression", "exit", "is<PERSON><PERSON><PERSON>", "skip", "super", "spreadElement", "restElement", "size", "parentParentPath", "left", "buildCodeFrameError", "classInitInjected", "classInitCall", "originalClass", "statics", "staticBlocks", "for<PERSON>ach", "isStaticBlock", "isProperty", "isClassProperty", "isClassPrivateProperty", "isClassPrivateMethod", "allValues", "staticsClass", "template", "ast", "addHelper", "staticBlock", "toStatement", "constructorBody", "newExpr", "newExpression", "arguments", "arrayPattern", "Boolean", "expr", "crawl", "assertVersion", "assumption", "loose", "VISITED", "WeakSet", "inherits", "syntaxDecorators", "visitor", "declaration", "splitExportDeclaration", "Class"], "sources": ["../src/transformer-2022-03.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from \"@babel/traverse\";\nimport { types as t, template } from \"@babel/core\";\nimport syntaxDecorators from \"@babel/plugin-syntax-decorators\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport * as charCodes from \"charcodes\";\nimport type { PluginAPI, PluginObject, PluginPass } from \"@babel/core\";\nimport type { Options } from \"./index\";\n\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty;\n\ntype ClassElement =\n  | ClassDecoratableElement\n  | t.TSDeclareMethod\n  | t.TSIndexSignature\n  | t.StaticBlock;\n\nfunction incrementId(id: number[], idx = id.length - 1): void {\n  // If index is -1, id needs an additional character, unshift A\n  if (idx === -1) {\n    id.unshift(charCodes.uppercaseA);\n    return;\n  }\n\n  const current = id[idx];\n\n  if (current === charCodes.uppercaseZ) {\n    // if current is Z, skip to a\n    id[idx] = charCodes.lowercaseA;\n  } else if (current === charCodes.lowercaseZ) {\n    // if current is z, reset to A and carry the 1\n    id[idx] = charCodes.uppercaseA;\n    incrementId(id, idx - 1);\n  } else {\n    // else, increment by one\n    id[idx] = current + 1;\n  }\n}\n\n/**\n * Generates a new private name that is unique to the given class. This can be\n * used to create extra class fields and methods for the implementation, while\n * keeping the length of those names as small as possible. This is important for\n * minification purposes (though private names can generally be minified,\n * transpilations and polyfills cannot yet).\n */\nfunction createPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  const currentPrivateId: number[] = [];\n  const privateNames = new Set<string>();\n\n  classPath.traverse({\n    PrivateName(path) {\n      privateNames.add(path.node.id.name);\n    },\n  });\n\n  return (): t.PrivateName => {\n    let reifiedId;\n    do {\n      incrementId(currentPrivateId);\n      reifiedId = String.fromCharCode(...currentPrivateId);\n    } while (privateNames.has(reifiedId));\n\n    return t.privateName(t.identifier(reifiedId));\n  };\n}\n\n/**\n * Wraps the above generator function so that it's run lazily the first time\n * it's actually required. Several types of decoration do not require this, so it\n * saves iterating the class elements an additional time and allocating the space\n * for the Sets of element names.\n */\nfunction createLazyPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  let generator: () => t.PrivateName;\n\n  return (): t.PrivateName => {\n    if (!generator) {\n      generator = createPrivateUidGeneratorForClass(classPath);\n    }\n\n    return generator();\n  };\n}\n\n/**\n * Takes a class definition and replaces it with an equivalent class declaration\n * which is then assigned to a local variable. This allows us to reassign the\n * local variable with the decorated version of the class. The class definition\n * retains its original name so that `toString` is not affected, other\n * references to the class are renamed instead.\n */\nfunction replaceClassWithVar(\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): [t.Identifier, NodePath<t.ClassDeclaration | t.ClassExpression>] {\n  if (path.type === \"ClassDeclaration\") {\n    const varId = path.scope.generateUidIdentifierBasedOnNode(path.node.id);\n    const classId = t.identifier(path.node.id.name);\n\n    path.scope.rename(classId.name, varId.name);\n\n    path.insertBefore(\n      t.variableDeclaration(\"let\", [t.variableDeclarator(varId)]),\n    );\n    path.get(\"id\").replaceWith(classId);\n\n    return [t.cloneNode(varId), path];\n  } else {\n    let className: string;\n    let varId: t.Identifier;\n\n    if (path.node.id) {\n      className = path.node.id.name;\n      varId = path.scope.parent.generateDeclaredUidIdentifier(className);\n      path.scope.rename(className, varId.name);\n    } else if (\n      path.parentPath.node.type === \"VariableDeclarator\" &&\n      path.parentPath.node.id.type === \"Identifier\"\n    ) {\n      className = path.parentPath.node.id.name;\n      varId = path.scope.parent.generateDeclaredUidIdentifier(className);\n    } else {\n      varId =\n        path.scope.parent.generateDeclaredUidIdentifier(\"decorated_class\");\n    }\n\n    const newClassExpr = t.classExpression(\n      className && t.identifier(className),\n      path.node.superClass,\n      path.node.body,\n    );\n\n    const [newPath] = path.replaceWith(\n      t.sequenceExpression([newClassExpr, varId]),\n    );\n\n    return [\n      t.cloneNode(varId),\n      newPath.get(\"expressions.0\") as NodePath<t.ClassExpression>,\n    ];\n  }\n}\n\nfunction generateClassProperty(\n  key: t.PrivateName | t.Identifier,\n  value: t.Expression | undefined,\n  isStatic: boolean,\n): t.ClassPrivateProperty | t.ClassProperty {\n  if (key.type === \"PrivateName\") {\n    return t.classPrivateProperty(key, value, undefined, isStatic);\n  } else {\n    return t.classProperty(key, value, undefined, undefined, isStatic);\n  }\n}\n\nfunction addProxyAccessorsFor(\n  element: NodePath<ClassDecoratableElement>,\n  originalKey: t.PrivateName | t.Expression,\n  targetKey: t.PrivateName,\n  isComputed = false,\n): void {\n  const { static: isStatic } = element.node;\n\n  const getterBody = t.blockStatement([\n    t.returnStatement(\n      t.memberExpression(t.thisExpression(), t.cloneNode(targetKey)),\n    ),\n  ]);\n\n  const setterBody = t.blockStatement([\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(t.thisExpression(), t.cloneNode(targetKey)),\n        t.identifier(\"v\"),\n      ),\n    ),\n  ]);\n\n  let getter: t.ClassMethod | t.ClassPrivateMethod,\n    setter: t.ClassMethod | t.ClassPrivateMethod;\n\n  if (originalKey.type === \"PrivateName\") {\n    getter = t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(originalKey),\n      [],\n      getterBody,\n      isStatic,\n    );\n    setter = t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(originalKey),\n      [t.identifier(\"v\")],\n      setterBody,\n      isStatic,\n    );\n  } else {\n    getter = t.classMethod(\n      \"get\",\n      t.cloneNode(originalKey),\n      [],\n      getterBody,\n      isComputed,\n      isStatic,\n    );\n    setter = t.classMethod(\n      \"set\",\n      t.cloneNode(originalKey),\n      [t.identifier(\"v\")],\n      setterBody,\n      isComputed,\n      isStatic,\n    );\n  }\n\n  element.insertAfter(setter);\n  element.insertAfter(getter);\n}\n\nfunction extractProxyAccessorsFor(\n  targetKey: t.PrivateName,\n): t.FunctionExpression[] {\n  return [\n    t.functionExpression(\n      undefined,\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.memberExpression(t.thisExpression(), t.cloneNode(targetKey)),\n        ),\n      ]),\n    ),\n    t.functionExpression(\n      undefined,\n      [t.identifier(\"value\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.assignmentExpression(\n            \"=\",\n            t.memberExpression(t.thisExpression(), t.cloneNode(targetKey)),\n            t.identifier(\"value\"),\n          ),\n        ),\n      ]),\n    ),\n  ];\n}\n\nconst FIELD = 0;\nconst ACCESSOR = 1;\nconst METHOD = 2;\nconst GETTER = 3;\nconst SETTER = 4;\n\nconst STATIC = 5;\n\nfunction getElementKind(element: NodePath<ClassDecoratableElement>): number {\n  switch (element.node.type) {\n    case \"ClassProperty\":\n    case \"ClassPrivateProperty\":\n      return FIELD;\n    case \"ClassAccessorProperty\":\n      return ACCESSOR;\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n      if (element.node.kind === \"get\") {\n        return GETTER;\n      } else if (element.node.kind === \"set\") {\n        return SETTER;\n      } else {\n        return METHOD;\n      }\n  }\n}\n\n// Information about the decorators applied to an element\ninterface DecoratorInfo {\n  // The expressions of the decorators themselves\n  decorators: t.Expression[];\n\n  // The kind of the decorated value, matches the kind value passed to applyDecs\n  kind: number;\n\n  // whether or not the field is static\n  isStatic: boolean;\n\n  // The name of the decorator\n  name: t.StringLiteral | t.Expression;\n\n  privateMethods: t.FunctionExpression | t.FunctionExpression[] | undefined;\n\n  // The names of local variables that will be used/returned from the decoration\n  locals: t.Identifier | t.Identifier[] | undefined;\n}\n\n// Information about a computed property key. These must be evaluated\n// interspersed with decorator expressions, which is why they get added to the\n// array of DecoratorInfos later on.\ninterface ComputedPropInfo {\n  localComputedNameId: t.Identifier;\n  keyNode: t.Expression;\n}\n\nfunction isDecoratorInfo(\n  info: DecoratorInfo | ComputedPropInfo,\n): info is DecoratorInfo {\n  return \"decorators\" in info;\n}\n\nfunction filteredOrderedDecoratorInfo(\n  info: (DecoratorInfo | ComputedPropInfo)[],\n): DecoratorInfo[] {\n  const filtered = info.filter(isDecoratorInfo);\n\n  return [\n    ...filtered.filter(\n      el => el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...filtered.filter(\n      el => !el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...filtered.filter(el => el.isStatic && el.kind === FIELD),\n    ...filtered.filter(el => !el.isStatic && el.kind === FIELD),\n  ];\n}\n\nfunction generateDecorationExprs(\n  info: (DecoratorInfo | ComputedPropInfo)[],\n): t.ArrayExpression {\n  return t.arrayExpression(\n    filteredOrderedDecoratorInfo(info).map(el => {\n      const decs =\n        el.decorators.length > 1\n          ? t.arrayExpression(el.decorators)\n          : el.decorators[0];\n\n      const kind = el.isStatic ? el.kind + STATIC : el.kind;\n\n      const decInfo = [decs, t.numericLiteral(kind), el.name];\n\n      const { privateMethods } = el;\n\n      if (Array.isArray(privateMethods)) {\n        decInfo.push(...privateMethods);\n      } else if (privateMethods) {\n        decInfo.push(privateMethods);\n      }\n\n      return t.arrayExpression(decInfo);\n    }),\n  );\n}\n\nfunction extractElementLocalAssignments(\n  decorationInfo: (DecoratorInfo | ComputedPropInfo)[],\n) {\n  const localIds: t.Identifier[] = [];\n\n  for (const el of filteredOrderedDecoratorInfo(decorationInfo)) {\n    const { locals } = el;\n\n    if (Array.isArray(locals)) {\n      localIds.push(...locals);\n    } else if (locals !== undefined) {\n      localIds.push(locals);\n    }\n  }\n\n  return localIds;\n}\n\nfunction addCallAccessorsFor(\n  element: NodePath,\n  key: t.PrivateName,\n  getId: t.Identifier,\n  setId: t.Identifier,\n) {\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(key),\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.callExpression(t.cloneNode(getId), [t.thisExpression()]),\n        ),\n      ]),\n    ),\n  );\n\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(key),\n      [t.identifier(\"v\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.callExpression(t.cloneNode(setId), [\n            t.thisExpression(),\n            t.identifier(\"v\"),\n          ]),\n        ),\n      ]),\n    ),\n  );\n}\n\nfunction isNotTsParameter(\n  node: t.Identifier | t.Pattern | t.RestElement | t.TSParameterProperty,\n): node is t.Identifier | t.Pattern | t.RestElement {\n  return node.type !== \"TSParameterProperty\";\n}\n\nfunction movePrivateAccessor(\n  element: NodePath<t.ClassPrivateMethod>,\n  key: t.PrivateName,\n  methodLocalVar: t.Identifier,\n  isStatic: boolean,\n) {\n  let params: (t.Identifier | t.RestElement)[];\n  let block: t.Statement[];\n\n  if (element.node.kind === \"set\") {\n    params = [t.identifier(\"v\")];\n    block = [\n      t.expressionStatement(\n        t.callExpression(methodLocalVar, [\n          t.thisExpression(),\n          t.identifier(\"v\"),\n        ]),\n      ),\n    ];\n  } else {\n    params = [];\n    block = [\n      t.returnStatement(t.callExpression(methodLocalVar, [t.thisExpression()])),\n    ];\n  }\n\n  element.replaceWith(\n    t.classPrivateMethod(\n      element.node.kind,\n      t.cloneNode(key),\n      params,\n      t.blockStatement(block),\n      isStatic,\n    ),\n  );\n}\n\nfunction isClassDecoratableElementPath(\n  path: NodePath<ClassElement>,\n): path is NodePath<ClassDecoratableElement> {\n  const { type } = path;\n\n  return (\n    type !== \"TSDeclareMethod\" &&\n    type !== \"TSIndexSignature\" &&\n    type !== \"StaticBlock\"\n  );\n}\n\nfunction staticBlockToIIFE(block: t.StaticBlock) {\n  return t.callExpression(\n    t.arrowFunctionExpression([], t.blockStatement(block.body)),\n    [],\n  );\n}\n\nfunction maybeSequenceExpression(exprs: t.Expression[]) {\n  if (exprs.length === 0) return t.unaryExpression(\"void\", t.numericLiteral(0));\n  if (exprs.length === 1) return exprs[0];\n  return t.sequenceExpression(exprs);\n}\n\nfunction transformClass(\n  path: NodePath<t.ClassExpression | t.ClassDeclaration>,\n  state: PluginPass,\n  constantSuper: boolean,\n  version: \"2022-03\" | \"2021-12\",\n): NodePath {\n  const body = path.get(\"body.body\");\n\n  const classDecorators = path.node.decorators;\n  let hasElementDecorators = false;\n\n  const generateClassPrivateUid = createLazyPrivateUidGeneratorForClass(path);\n\n  // Iterate over the class to see if we need to decorate it, and also to\n  // transform simple auto accessors which are not decorated\n  for (const element of body) {\n    if (!isClassDecoratableElementPath(element)) {\n      continue;\n    }\n\n    if (element.node.decorators && element.node.decorators.length > 0) {\n      hasElementDecorators = true;\n    } else if (element.node.type === \"ClassAccessorProperty\") {\n      const { key, value, static: isStatic, computed } = element.node;\n\n      const newId = generateClassPrivateUid();\n\n      const valueNode = value ? t.cloneNode(value) : undefined;\n\n      const newField = generateClassProperty(newId, valueNode, isStatic);\n\n      const [newPath] = element.replaceWith(newField);\n      addProxyAccessorsFor(newPath, key, newId, computed);\n    }\n  }\n\n  // If nothing is decorated, return\n  if (!classDecorators && !hasElementDecorators) return;\n\n  const elementDecoratorInfo: (DecoratorInfo | ComputedPropInfo)[] = [];\n\n  let firstFieldPath:\n    | NodePath<t.ClassProperty | t.ClassPrivateProperty>\n    | undefined;\n  let constructorPath: NodePath<t.ClassMethod> | undefined;\n  let requiresProtoInit = false;\n  let requiresStaticInit = false;\n  const decoratedPrivateMethods = new Set<string>();\n\n  let protoInitLocal: t.Identifier,\n    staticInitLocal: t.Identifier,\n    classInitLocal: t.Identifier,\n    classLocal: t.Identifier;\n  const assignments: t.AssignmentExpression[] = [];\n  const scopeParent: Scope = path.scope.parent;\n\n  const memoiseExpression = (expression: t.Expression, hint: string) => {\n    const localEvaluatedId = scopeParent.generateDeclaredUidIdentifier(hint);\n    assignments.push(t.assignmentExpression(\"=\", localEvaluatedId, expression));\n    return t.cloneNode(localEvaluatedId);\n  };\n\n  if (classDecorators) {\n    classInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initClass\");\n\n    const [localId, classPath] = replaceClassWithVar(path);\n    path = classPath;\n    classLocal = localId;\n\n    path.node.decorators = null;\n\n    for (const classDecorator of classDecorators) {\n      if (!scopeParent.isStatic(classDecorator.expression)) {\n        classDecorator.expression = memoiseExpression(\n          classDecorator.expression,\n          \"dec\",\n        );\n      }\n    }\n  } else {\n    if (!path.node.id) {\n      path.node.id = path.scope.generateUidIdentifier(\"Class\");\n    }\n    classLocal = t.cloneNode(path.node.id);\n  }\n\n  if (hasElementDecorators) {\n    for (const element of body) {\n      if (!isClassDecoratableElementPath(element)) {\n        continue;\n      }\n\n      const { node } = element;\n      const decorators = element.get(\"decorators\");\n\n      const hasDecorators = Array.isArray(decorators) && decorators.length > 0;\n\n      if (hasDecorators) {\n        for (const decoratorPath of decorators) {\n          if (!scopeParent.isStatic(decoratorPath.node.expression)) {\n            decoratorPath.node.expression = memoiseExpression(\n              decoratorPath.node.expression,\n              \"dec\",\n            );\n          }\n        }\n      }\n\n      const isComputed =\n        \"computed\" in element.node && element.node.computed === true;\n      if (isComputed) {\n        if (!scopeParent.isStatic(node.key)) {\n          node.key = memoiseExpression(node.key as t.Expression, \"computedKey\");\n        }\n      }\n\n      const kind = getElementKind(element);\n      const { key } = node;\n\n      const isPrivate = key.type === \"PrivateName\";\n\n      const isStatic = !!element.node.static;\n\n      let name = \"computedKey\";\n\n      if (isPrivate) {\n        name = (key as t.PrivateName).id.name;\n      } else if (!isComputed && key.type === \"Identifier\") {\n        name = key.name;\n      }\n\n      if (element.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = element;\n      }\n\n      if (hasDecorators) {\n        let locals: t.Identifier | t.Identifier[];\n        let privateMethods: t.FunctionExpression | t.FunctionExpression[];\n\n        if (kind === ACCESSOR) {\n          const { value } = element.node as t.ClassAccessorProperty;\n\n          const params: t.Expression[] = [t.thisExpression()];\n\n          if (value) {\n            params.push(t.cloneNode(value));\n          }\n\n          const newId = generateClassPrivateUid();\n          const newFieldInitId =\n            element.scope.parent.generateDeclaredUidIdentifier(`init_${name}`);\n          const newValue = t.callExpression(\n            t.cloneNode(newFieldInitId),\n            params,\n          );\n\n          const newField = generateClassProperty(newId, newValue, isStatic);\n          const [newPath] = element.replaceWith(newField);\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(newId);\n\n            const getId = newPath.scope.parent.generateDeclaredUidIdentifier(\n              `get_${name}`,\n            );\n            const setId = newPath.scope.parent.generateDeclaredUidIdentifier(\n              `set_${name}`,\n            );\n\n            addCallAccessorsFor(newPath, key as t.PrivateName, getId, setId);\n\n            locals = [newFieldInitId, getId, setId];\n          } else {\n            addProxyAccessorsFor(newPath, key, newId, isComputed);\n            locals = newFieldInitId;\n          }\n        } else if (kind === FIELD) {\n          const initId = element.scope.parent.generateDeclaredUidIdentifier(\n            `init_${name}`,\n          );\n          const valuePath = (\n            element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n          ).get(\"value\");\n\n          valuePath.replaceWith(\n            t.callExpression(\n              t.cloneNode(initId),\n              [t.thisExpression(), valuePath.node].filter(v => v),\n            ),\n          );\n\n          locals = initId;\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(key as t.PrivateName);\n          }\n        } else if (isPrivate) {\n          locals = element.scope.parent.generateDeclaredUidIdentifier(\n            `call_${name}`,\n          ) as t.Identifier;\n\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element as NodePath<t.ClassPrivateMethod>,\n            objectRef: classLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classLocal,\n          });\n\n          replaceSupers.replace();\n\n          const {\n            params,\n            body,\n            async: isAsync,\n          } = element.node as t.ClassPrivateMethod;\n\n          privateMethods = t.functionExpression(\n            undefined,\n            params.filter(isNotTsParameter),\n            body,\n            isAsync,\n          );\n\n          if (kind === GETTER || kind === SETTER) {\n            movePrivateAccessor(\n              element as NodePath<t.ClassPrivateMethod>,\n              t.cloneNode(key as t.PrivateName),\n              t.cloneNode(locals),\n              isStatic,\n            );\n          } else {\n            const node = element.node as t.ClassPrivateMethod;\n\n            // Unshift\n            path.node.body.body.unshift(\n              t.classPrivateProperty(\n                key as t.PrivateName,\n                t.cloneNode(locals),\n                [],\n                node.static,\n              ),\n            );\n\n            decoratedPrivateMethods.add((key as t.PrivateName).id.name);\n\n            element.remove();\n          }\n        }\n\n        let nameExpr: t.Expression;\n\n        if (isComputed) {\n          nameExpr = t.cloneNode(key as t.Expression);\n        } else if (key.type === \"PrivateName\") {\n          nameExpr = t.stringLiteral(key.id.name);\n        } else if (key.type === \"Identifier\") {\n          nameExpr = t.stringLiteral(key.name);\n        } else {\n          nameExpr = t.cloneNode(key as t.Expression);\n        }\n\n        elementDecoratorInfo.push({\n          kind,\n          decorators: decorators.map(d => d.node.expression),\n          name: nameExpr,\n          isStatic,\n          privateMethods,\n          locals,\n        });\n\n        if (kind !== FIELD) {\n          if (isStatic) {\n            requiresStaticInit = true;\n          } else {\n            requiresProtoInit = true;\n          }\n        }\n\n        if (element.node) {\n          element.node.decorators = null;\n        }\n\n        if (!firstFieldPath && (kind === FIELD || kind === ACCESSOR)) {\n          firstFieldPath = element as NodePath<\n            t.ClassProperty | t.ClassPrivateProperty\n          >;\n        }\n      }\n    }\n  }\n\n  const elementDecorations = generateDecorationExprs(elementDecoratorInfo);\n  const classDecorations = t.arrayExpression(\n    (classDecorators || []).map(d => d.expression),\n  );\n\n  const locals: t.Identifier[] =\n    extractElementLocalAssignments(elementDecoratorInfo);\n\n  if (requiresProtoInit) {\n    protoInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initProto\");\n    locals.push(protoInitLocal);\n\n    const protoInitCall = t.callExpression(t.cloneNode(protoInitLocal), [\n      t.thisExpression(),\n    ]);\n\n    if (firstFieldPath) {\n      const value = firstFieldPath.get(\"value\");\n      const body: t.Expression[] = [protoInitCall];\n\n      if (value.node) {\n        body.push(value.node);\n      }\n\n      value.replaceWith(t.sequenceExpression(body));\n    } else if (constructorPath) {\n      if (path.node.superClass) {\n        path.traverse({\n          CallExpression: {\n            exit(path) {\n              if (!path.get(\"callee\").isSuper()) return;\n\n              path.replaceWith(\n                t.callExpression(t.cloneNode(protoInitLocal), [path.node]),\n              );\n\n              path.skip();\n            },\n          },\n        });\n      } else {\n        constructorPath.node.body.body.unshift(\n          t.expressionStatement(protoInitCall),\n        );\n      }\n    } else {\n      const body: t.Statement[] = [t.expressionStatement(protoInitCall)];\n\n      if (path.node.superClass) {\n        body.unshift(\n          t.expressionStatement(\n            t.callExpression(t.super(), [\n              t.spreadElement(t.identifier(\"args\")),\n            ]),\n          ),\n        );\n      }\n\n      path.node.body.body.unshift(\n        t.classMethod(\n          \"constructor\",\n          t.identifier(\"constructor\"),\n          [t.restElement(t.identifier(\"args\"))],\n          t.blockStatement(body),\n        ),\n      );\n    }\n  }\n\n  if (requiresStaticInit) {\n    staticInitLocal = scopeParent.generateDeclaredUidIdentifier(\"initStatic\");\n    locals.push(staticInitLocal);\n  }\n\n  if (decoratedPrivateMethods.size > 0) {\n    path.traverse({\n      PrivateName(path) {\n        if (!decoratedPrivateMethods.has(path.node.id.name)) return;\n\n        const parentPath = path.parentPath;\n        const parentParentPath = parentPath.parentPath;\n\n        if (\n          // this.bar().#x = 123;\n          (parentParentPath.node.type === \"AssignmentExpression\" &&\n            parentParentPath.node.left === parentPath.node) ||\n          // this.#x++;\n          parentParentPath.node.type === \"UpdateExpression\" ||\n          // ([...this.#x] = foo);\n          parentParentPath.node.type === \"RestElement\" ||\n          // ([this.#x] = foo);\n          parentParentPath.node.type === \"ArrayPattern\" ||\n          // ({ a: this.#x } = bar);\n          (parentParentPath.node.type === \"ObjectProperty\" &&\n            parentParentPath.node.value === parentPath.node &&\n            parentParentPath.parentPath.type === \"ObjectPattern\") ||\n          // for (this.#x of []);\n          (parentParentPath.node.type === \"ForOfStatement\" &&\n            parentParentPath.node.left === parentPath.node)\n        ) {\n          throw path.buildCodeFrameError(\n            `Decorated private methods are not updatable, but \"#${path.node.id.name}\" is updated via this expression.`,\n          );\n        }\n      },\n    });\n  }\n\n  let classInitInjected = false;\n  const classInitCall =\n    classInitLocal && t.callExpression(t.cloneNode(classInitLocal), []);\n\n  const originalClass = path.node;\n\n  if (classDecorators) {\n    locals.push(classLocal, classInitLocal);\n    const statics: (\n      | t.ClassProperty\n      | t.ClassPrivateProperty\n      | t.ClassPrivateMethod\n    )[] = [];\n    let staticBlocks: t.StaticBlock[] = [];\n    path.get(\"body.body\").forEach(element => {\n      // Static blocks cannot be compiled to \"instance blocks\", but we can inline\n      // them as IIFEs in the next property.\n      if (element.isStaticBlock()) {\n        staticBlocks.push(element.node);\n        element.remove();\n        return;\n      }\n\n      const isProperty =\n        element.isClassProperty() || element.isClassPrivateProperty();\n\n      if (\n        (isProperty || element.isClassPrivateMethod()) &&\n        element.node.static\n      ) {\n        if (isProperty && staticBlocks.length > 0) {\n          const allValues: t.Expression[] = staticBlocks.map(staticBlockToIIFE);\n          if (element.node.value) allValues.push(element.node.value);\n          element.node.value = maybeSequenceExpression(allValues);\n          staticBlocks = [];\n        }\n\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      }\n    });\n\n    if (statics.length > 0 || staticBlocks.length > 0) {\n      const staticsClass = template.expression.ast`\n        class extends ${state.addHelper(\"identity\")} {}\n      ` as t.ClassExpression;\n      staticsClass.body.body = [\n        t.staticBlock([t.toStatement(path.node, false)]),\n        ...statics,\n      ];\n\n      const constructorBody: t.Expression[] = [];\n\n      const newExpr = t.newExpression(staticsClass, []);\n\n      if (staticBlocks.length > 0) {\n        constructorBody.push(...staticBlocks.map(staticBlockToIIFE));\n      }\n      if (classInitCall) {\n        classInitInjected = true;\n        constructorBody.push(classInitCall);\n      }\n      if (constructorBody.length > 0) {\n        constructorBody.unshift(\n          t.callExpression(t.super(), [t.cloneNode(classLocal)]),\n        );\n\n        staticsClass.body.body.push(\n          t.classMethod(\n            \"constructor\",\n            t.identifier(\"constructor\"),\n            [],\n            t.blockStatement([\n              t.expressionStatement(t.sequenceExpression(constructorBody)),\n            ]),\n          ),\n        );\n      } else {\n        newExpr.arguments.push(t.cloneNode(classLocal));\n      }\n\n      path.replaceWith(newExpr);\n    }\n  }\n  if (!classInitInjected && classInitCall) {\n    path.node.body.body.push(\n      t.staticBlock([t.expressionStatement(classInitCall)]),\n    );\n  }\n\n  originalClass.body.body.unshift(\n    t.staticBlock(\n      [\n        t.expressionStatement(\n          t.assignmentExpression(\n            \"=\",\n            t.arrayPattern(locals),\n            t.callExpression(\n              state.addHelper(\n                version === \"2021-12\" ? \"applyDecs\" : \"applyDecs2203\",\n              ),\n              [t.thisExpression(), elementDecorations, classDecorations],\n            ),\n          ),\n        ),\n        requiresStaticInit &&\n          t.expressionStatement(\n            t.callExpression(t.cloneNode(staticInitLocal), [\n              t.thisExpression(),\n            ]),\n          ),\n      ].filter(Boolean),\n    ),\n  );\n\n  // When path is a ClassExpression, path.insertBefore will convert `path`\n  // into a SequenceExpression\n  path.insertBefore(assignments.map(expr => t.expressionStatement(expr)));\n\n  // Recrawl the scope to make sure new identifiers are properly synced\n  path.scope.crawl();\n\n  return path;\n}\n\nexport default function (\n  { assertVersion, assumption }: PluginAPI,\n  { loose }: Options,\n  version: \"2022-03\" | \"2021-12\",\n): PluginObject {\n  assertVersion(version === \"2021-12\" ? \"^7.16.0\" : \"^7.19.0\");\n\n  const VISITED = new WeakSet<NodePath>();\n  const constantSuper = assumption(\"constantSuper\") ?? loose;\n\n  return {\n    name: \"proposal-decorators\",\n    inherits: syntaxDecorators,\n\n    visitor: {\n      \"ExportNamedDeclaration|ExportDefaultDeclaration\"(\n        path: NodePath<t.ExportNamedDeclaration | t.ExportDefaultDeclaration>,\n      ) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          declaration.decorators?.length > 0\n        ) {\n          splitExportDeclaration(path);\n        }\n      },\n\n      Class(path, state) {\n        if (VISITED.has(path)) return;\n\n        const newPath = transformClass(path, state, constantSuper, version);\n        if (newPath) VISITED.add(newPath);\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;;AACA;;AACA;;AACA;;AACA;;AAkBA,SAASA,WAAT,CAAqBC,EAArB,EAAmCC,GAAG,GAAGD,EAAE,CAACE,MAAH,GAAY,CAArD,EAA8D;EAE5D,IAAID,GAAG,KAAK,CAAC,CAAb,EAAgB;IACdD,EAAE,CAACG,OAAH;IACA;EACD;;EAED,MAAMC,OAAO,GAAGJ,EAAE,CAACC,GAAD,CAAlB;;EAEA,IAAIG,OAAO,OAAX,EAAsC;IAEpCJ,EAAE,CAACC,GAAD,CAAF;EACD,CAHD,MAGO,IAAIG,OAAO,QAAX,EAAsC;IAE3CJ,EAAE,CAACC,GAAD,CAAF;IACAF,WAAW,CAACC,EAAD,EAAKC,GAAG,GAAG,CAAX,CAAX;EACD,CAJM,MAIA;IAELD,EAAE,CAACC,GAAD,CAAF,GAAUG,OAAO,GAAG,CAApB;EACD;AACF;;AASD,SAASC,iCAAT,CACEC,SADF,EAEuB;EACrB,MAAMC,gBAA0B,GAAG,EAAnC;EACA,MAAMC,YAAY,GAAG,IAAIC,GAAJ,EAArB;EAEAH,SAAS,CAACI,QAAV,CAAmB;IACjBC,WAAW,CAACC,IAAD,EAAO;MAChBJ,YAAY,CAACK,GAAb,CAAiBD,IAAI,CAACE,IAAL,CAAUd,EAAV,CAAae,IAA9B;IACD;;EAHgB,CAAnB;EAMA,OAAO,MAAqB;IAC1B,IAAIC,SAAJ;;IACA,GAAG;MACDjB,WAAW,CAACQ,gBAAD,CAAX;MACAS,SAAS,GAAGC,MAAM,CAACC,YAAP,CAAoB,GAAGX,gBAAvB,CAAZ;IACD,CAHD,QAGSC,YAAY,CAACW,GAAb,CAAiBH,SAAjB,CAHT;;IAKA,OAAOI,WAAA,CAAEC,WAAF,CAAcD,WAAA,CAAEE,UAAF,CAAaN,SAAb,CAAd,CAAP;EACD,CARD;AASD;;AAQD,SAASO,qCAAT,CACEjB,SADF,EAEuB;EACrB,IAAIkB,SAAJ;EAEA,OAAO,MAAqB;IAC1B,IAAI,CAACA,SAAL,EAAgB;MACdA,SAAS,GAAGnB,iCAAiC,CAACC,SAAD,CAA7C;IACD;;IAED,OAAOkB,SAAS,EAAhB;EACD,CAND;AAOD;;AASD,SAASC,mBAAT,CACEb,IADF,EAEoE;EAClE,IAAIA,IAAI,CAACc,IAAL,KAAc,kBAAlB,EAAsC;IACpC,MAAMC,KAAK,GAAGf,IAAI,CAACgB,KAAL,CAAWC,gCAAX,CAA4CjB,IAAI,CAACE,IAAL,CAAUd,EAAtD,CAAd;;IACA,MAAM8B,OAAO,GAAGV,WAAA,CAAEE,UAAF,CAAaV,IAAI,CAACE,IAAL,CAAUd,EAAV,CAAae,IAA1B,CAAhB;;IAEAH,IAAI,CAACgB,KAAL,CAAWG,MAAX,CAAkBD,OAAO,CAACf,IAA1B,EAAgCY,KAAK,CAACZ,IAAtC;IAEAH,IAAI,CAACoB,YAAL,CACEZ,WAAA,CAAEa,mBAAF,CAAsB,KAAtB,EAA6B,CAACb,WAAA,CAAEc,kBAAF,CAAqBP,KAArB,CAAD,CAA7B,CADF;IAGAf,IAAI,CAACuB,GAAL,CAAS,IAAT,EAAeC,WAAf,CAA2BN,OAA3B;IAEA,OAAO,CAACV,WAAA,CAAEiB,SAAF,CAAYV,KAAZ,CAAD,EAAqBf,IAArB,CAAP;EACD,CAZD,MAYO;IACL,IAAI0B,SAAJ;IACA,IAAIX,KAAJ;;IAEA,IAAIf,IAAI,CAACE,IAAL,CAAUd,EAAd,EAAkB;MAChBsC,SAAS,GAAG1B,IAAI,CAACE,IAAL,CAAUd,EAAV,CAAae,IAAzB;MACAY,KAAK,GAAGf,IAAI,CAACgB,KAAL,CAAWW,MAAX,CAAkBC,6BAAlB,CAAgDF,SAAhD,CAAR;MACA1B,IAAI,CAACgB,KAAL,CAAWG,MAAX,CAAkBO,SAAlB,EAA6BX,KAAK,CAACZ,IAAnC;IACD,CAJD,MAIO,IACLH,IAAI,CAAC6B,UAAL,CAAgB3B,IAAhB,CAAqBY,IAArB,KAA8B,oBAA9B,IACAd,IAAI,CAAC6B,UAAL,CAAgB3B,IAAhB,CAAqBd,EAArB,CAAwB0B,IAAxB,KAAiC,YAF5B,EAGL;MACAY,SAAS,GAAG1B,IAAI,CAAC6B,UAAL,CAAgB3B,IAAhB,CAAqBd,EAArB,CAAwBe,IAApC;MACAY,KAAK,GAAGf,IAAI,CAACgB,KAAL,CAAWW,MAAX,CAAkBC,6BAAlB,CAAgDF,SAAhD,CAAR;IACD,CANM,MAMA;MACLX,KAAK,GACHf,IAAI,CAACgB,KAAL,CAAWW,MAAX,CAAkBC,6BAAlB,CAAgD,iBAAhD,CADF;IAED;;IAED,MAAME,YAAY,GAAGtB,WAAA,CAAEuB,eAAF,CACnBL,SAAS,IAAIlB,WAAA,CAAEE,UAAF,CAAagB,SAAb,CADM,EAEnB1B,IAAI,CAACE,IAAL,CAAU8B,UAFS,EAGnBhC,IAAI,CAACE,IAAL,CAAU+B,IAHS,CAArB;;IAMA,MAAM,CAACC,OAAD,IAAYlC,IAAI,CAACwB,WAAL,CAChBhB,WAAA,CAAE2B,kBAAF,CAAqB,CAACL,YAAD,EAAef,KAAf,CAArB,CADgB,CAAlB;IAIA,OAAO,CACLP,WAAA,CAAEiB,SAAF,CAAYV,KAAZ,CADK,EAELmB,OAAO,CAACX,GAAR,CAAY,eAAZ,CAFK,CAAP;EAID;AACF;;AAED,SAASa,qBAAT,CACEC,GADF,EAEEC,KAFF,EAGEC,QAHF,EAI4C;EAC1C,IAAIF,GAAG,CAACvB,IAAJ,KAAa,aAAjB,EAAgC;IAC9B,OAAON,WAAA,CAAEgC,oBAAF,CAAuBH,GAAvB,EAA4BC,KAA5B,EAAmCG,SAAnC,EAA8CF,QAA9C,CAAP;EACD,CAFD,MAEO;IACL,OAAO/B,WAAA,CAAEkC,aAAF,CAAgBL,GAAhB,EAAqBC,KAArB,EAA4BG,SAA5B,EAAuCA,SAAvC,EAAkDF,QAAlD,CAAP;EACD;AACF;;AAED,SAASI,oBAAT,CACEC,OADF,EAEEC,WAFF,EAGEC,SAHF,EAIEC,UAAU,GAAG,KAJf,EAKQ;EACN,MAAM;IAAEC,MAAM,EAAET;EAAV,IAAuBK,OAAO,CAAC1C,IAArC;;EAEA,MAAM+C,UAAU,GAAGzC,WAAA,CAAE0C,cAAF,CAAiB,CAClC1C,WAAA,CAAE2C,eAAF,CACE3C,WAAA,CAAE4C,gBAAF,CAAmB5C,WAAA,CAAE6C,cAAF,EAAnB,EAAuC7C,WAAA,CAAEiB,SAAF,CAAYqB,SAAZ,CAAvC,CADF,CADkC,CAAjB,CAAnB;;EAMA,MAAMQ,UAAU,GAAG9C,WAAA,CAAE0C,cAAF,CAAiB,CAClC1C,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEgD,oBAAF,CACE,GADF,EAEEhD,WAAA,CAAE4C,gBAAF,CAAmB5C,WAAA,CAAE6C,cAAF,EAAnB,EAAuC7C,WAAA,CAAEiB,SAAF,CAAYqB,SAAZ,CAAvC,CAFF,EAGEtC,WAAA,CAAEE,UAAF,CAAa,GAAb,CAHF,CADF,CADkC,CAAjB,CAAnB;;EAUA,IAAI+C,MAAJ,EACEC,MADF;;EAGA,IAAIb,WAAW,CAAC/B,IAAZ,KAAqB,aAAzB,EAAwC;IACtC2C,MAAM,GAAGjD,WAAA,CAAEmD,kBAAF,CACP,KADO,EAEPnD,WAAA,CAAEiB,SAAF,CAAYoB,WAAZ,CAFO,EAGP,EAHO,EAIPI,UAJO,EAKPV,QALO,CAAT;IAOAmB,MAAM,GAAGlD,WAAA,CAAEmD,kBAAF,CACP,KADO,EAEPnD,WAAA,CAAEiB,SAAF,CAAYoB,WAAZ,CAFO,EAGP,CAACrC,WAAA,CAAEE,UAAF,CAAa,GAAb,CAAD,CAHO,EAIP4C,UAJO,EAKPf,QALO,CAAT;EAOD,CAfD,MAeO;IACLkB,MAAM,GAAGjD,WAAA,CAAEoD,WAAF,CACP,KADO,EAEPpD,WAAA,CAAEiB,SAAF,CAAYoB,WAAZ,CAFO,EAGP,EAHO,EAIPI,UAJO,EAKPF,UALO,EAMPR,QANO,CAAT;IAQAmB,MAAM,GAAGlD,WAAA,CAAEoD,WAAF,CACP,KADO,EAEPpD,WAAA,CAAEiB,SAAF,CAAYoB,WAAZ,CAFO,EAGP,CAACrC,WAAA,CAAEE,UAAF,CAAa,GAAb,CAAD,CAHO,EAIP4C,UAJO,EAKPP,UALO,EAMPR,QANO,CAAT;EAQD;;EAEDK,OAAO,CAACiB,WAAR,CAAoBH,MAApB;EACAd,OAAO,CAACiB,WAAR,CAAoBJ,MAApB;AACD;;AAED,SAASK,wBAAT,CACEhB,SADF,EAE0B;EACxB,OAAO,CACLtC,WAAA,CAAEuD,kBAAF,CACEtB,SADF,EAEE,EAFF,EAGEjC,WAAA,CAAE0C,cAAF,CAAiB,CACf1C,WAAA,CAAE2C,eAAF,CACE3C,WAAA,CAAE4C,gBAAF,CAAmB5C,WAAA,CAAE6C,cAAF,EAAnB,EAAuC7C,WAAA,CAAEiB,SAAF,CAAYqB,SAAZ,CAAvC,CADF,CADe,CAAjB,CAHF,CADK,EAULtC,WAAA,CAAEuD,kBAAF,CACEtB,SADF,EAEE,CAACjC,WAAA,CAAEE,UAAF,CAAa,OAAb,CAAD,CAFF,EAGEF,WAAA,CAAE0C,cAAF,CAAiB,CACf1C,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEgD,oBAAF,CACE,GADF,EAEEhD,WAAA,CAAE4C,gBAAF,CAAmB5C,WAAA,CAAE6C,cAAF,EAAnB,EAAuC7C,WAAA,CAAEiB,SAAF,CAAYqB,SAAZ,CAAvC,CAFF,EAGEtC,WAAA,CAAEE,UAAF,CAAa,OAAb,CAHF,CADF,CADe,CAAjB,CAHF,CAVK,CAAP;AAwBD;;AAED,MAAMsD,KAAK,GAAG,CAAd;AACA,MAAMC,QAAQ,GAAG,CAAjB;AACA,MAAMC,MAAM,GAAG,CAAf;AACA,MAAMC,MAAM,GAAG,CAAf;AACA,MAAMC,MAAM,GAAG,CAAf;AAEA,MAAMC,MAAM,GAAG,CAAf;;AAEA,SAASC,cAAT,CAAwB1B,OAAxB,EAA4E;EAC1E,QAAQA,OAAO,CAAC1C,IAAR,CAAaY,IAArB;IACE,KAAK,eAAL;IACA,KAAK,sBAAL;MACE,OAAOkD,KAAP;;IACF,KAAK,uBAAL;MACE,OAAOC,QAAP;;IACF,KAAK,aAAL;IACA,KAAK,oBAAL;MACE,IAAIrB,OAAO,CAAC1C,IAAR,CAAaqE,IAAb,KAAsB,KAA1B,EAAiC;QAC/B,OAAOJ,MAAP;MACD,CAFD,MAEO,IAAIvB,OAAO,CAAC1C,IAAR,CAAaqE,IAAb,KAAsB,KAA1B,EAAiC;QACtC,OAAOH,MAAP;MACD,CAFM,MAEA;QACL,OAAOF,MAAP;MACD;;EAdL;AAgBD;;AA8BD,SAASM,eAAT,CACEC,IADF,EAEyB;EACvB,OAAO,gBAAgBA,IAAvB;AACD;;AAED,SAASC,4BAAT,CACED,IADF,EAEmB;EACjB,MAAME,QAAQ,GAAGF,IAAI,CAACG,MAAL,CAAYJ,eAAZ,CAAjB;EAEA,OAAO,CACL,GAAGG,QAAQ,CAACC,MAAT,CACDC,EAAE,IAAIA,EAAE,CAACtC,QAAH,IAAesC,EAAE,CAACN,IAAH,IAAWN,QAA1B,IAAsCY,EAAE,CAACN,IAAH,IAAWH,MADtD,CADE,EAIL,GAAGO,QAAQ,CAACC,MAAT,CACDC,EAAE,IAAI,CAACA,EAAE,CAACtC,QAAJ,IAAgBsC,EAAE,CAACN,IAAH,IAAWN,QAA3B,IAAuCY,EAAE,CAACN,IAAH,IAAWH,MADvD,CAJE,EAOL,GAAGO,QAAQ,CAACC,MAAT,CAAgBC,EAAE,IAAIA,EAAE,CAACtC,QAAH,IAAesC,EAAE,CAACN,IAAH,KAAYP,KAAjD,CAPE,EAQL,GAAGW,QAAQ,CAACC,MAAT,CAAgBC,EAAE,IAAI,CAACA,EAAE,CAACtC,QAAJ,IAAgBsC,EAAE,CAACN,IAAH,KAAYP,KAAlD,CARE,CAAP;AAUD;;AAED,SAASc,uBAAT,CACEL,IADF,EAEqB;EACnB,OAAOjE,WAAA,CAAEuE,eAAF,CACLL,4BAA4B,CAACD,IAAD,CAA5B,CAAmCO,GAAnC,CAAuCH,EAAE,IAAI;IAC3C,MAAMI,IAAI,GACRJ,EAAE,CAACK,UAAH,CAAc5F,MAAd,GAAuB,CAAvB,GACIkB,WAAA,CAAEuE,eAAF,CAAkBF,EAAE,CAACK,UAArB,CADJ,GAEIL,EAAE,CAACK,UAAH,CAAc,CAAd,CAHN;IAKA,MAAMX,IAAI,GAAGM,EAAE,CAACtC,QAAH,GAAcsC,EAAE,CAACN,IAAH,GAAUF,MAAxB,GAAiCQ,EAAE,CAACN,IAAjD;IAEA,MAAMY,OAAO,GAAG,CAACF,IAAD,EAAOzE,WAAA,CAAE4E,cAAF,CAAiBb,IAAjB,CAAP,EAA+BM,EAAE,CAAC1E,IAAlC,CAAhB;IAEA,MAAM;MAAEkF;IAAF,IAAqBR,EAA3B;;IAEA,IAAIS,KAAK,CAACC,OAAN,CAAcF,cAAd,CAAJ,EAAmC;MACjCF,OAAO,CAACK,IAAR,CAAa,GAAGH,cAAhB;IACD,CAFD,MAEO,IAAIA,cAAJ,EAAoB;MACzBF,OAAO,CAACK,IAAR,CAAaH,cAAb;IACD;;IAED,OAAO7E,WAAA,CAAEuE,eAAF,CAAkBI,OAAlB,CAAP;EACD,CAnBD,CADK,CAAP;AAsBD;;AAED,SAASM,8BAAT,CACEC,cADF,EAEE;EACA,MAAMC,QAAwB,GAAG,EAAjC;;EAEA,KAAK,MAAMd,EAAX,IAAiBH,4BAA4B,CAACgB,cAAD,CAA7C,EAA+D;IAC7D,MAAM;MAAEE;IAAF,IAAaf,EAAnB;;IAEA,IAAIS,KAAK,CAACC,OAAN,CAAcK,MAAd,CAAJ,EAA2B;MACzBD,QAAQ,CAACH,IAAT,CAAc,GAAGI,MAAjB;IACD,CAFD,MAEO,IAAIA,MAAM,KAAKnD,SAAf,EAA0B;MAC/BkD,QAAQ,CAACH,IAAT,CAAcI,MAAd;IACD;EACF;;EAED,OAAOD,QAAP;AACD;;AAED,SAASE,mBAAT,CACEjD,OADF,EAEEP,GAFF,EAGEyD,KAHF,EAIEC,KAJF,EAKE;EACAnD,OAAO,CAACiB,WAAR,CACErD,WAAA,CAAEmD,kBAAF,CACE,KADF,EAEEnD,WAAA,CAAEiB,SAAF,CAAYY,GAAZ,CAFF,EAGE,EAHF,EAIE7B,WAAA,CAAE0C,cAAF,CAAiB,CACf1C,WAAA,CAAE2C,eAAF,CACE3C,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEiB,SAAF,CAAYqE,KAAZ,CAAjB,EAAqC,CAACtF,WAAA,CAAE6C,cAAF,EAAD,CAArC,CADF,CADe,CAAjB,CAJF,CADF;EAaAT,OAAO,CAACiB,WAAR,CACErD,WAAA,CAAEmD,kBAAF,CACE,KADF,EAEEnD,WAAA,CAAEiB,SAAF,CAAYY,GAAZ,CAFF,EAGE,CAAC7B,WAAA,CAAEE,UAAF,CAAa,GAAb,CAAD,CAHF,EAIEF,WAAA,CAAE0C,cAAF,CAAiB,CACf1C,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEiB,SAAF,CAAYsE,KAAZ,CAAjB,EAAqC,CACnCvF,WAAA,CAAE6C,cAAF,EADmC,EAEnC7C,WAAA,CAAEE,UAAF,CAAa,GAAb,CAFmC,CAArC,CADF,CADe,CAAjB,CAJF,CADF;AAeD;;AAED,SAASuF,gBAAT,CACE/F,IADF,EAEoD;EAClD,OAAOA,IAAI,CAACY,IAAL,KAAc,qBAArB;AACD;;AAED,SAASoF,mBAAT,CACEtD,OADF,EAEEP,GAFF,EAGE8D,cAHF,EAIE5D,QAJF,EAKE;EACA,IAAI6D,MAAJ;EACA,IAAIC,KAAJ;;EAEA,IAAIzD,OAAO,CAAC1C,IAAR,CAAaqE,IAAb,KAAsB,KAA1B,EAAiC;IAC/B6B,MAAM,GAAG,CAAC5F,WAAA,CAAEE,UAAF,CAAa,GAAb,CAAD,CAAT;IACA2F,KAAK,GAAG,CACN7F,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEwF,cAAF,CAAiBG,cAAjB,EAAiC,CAC/B3F,WAAA,CAAE6C,cAAF,EAD+B,EAE/B7C,WAAA,CAAEE,UAAF,CAAa,GAAb,CAF+B,CAAjC,CADF,CADM,CAAR;EAQD,CAVD,MAUO;IACL0F,MAAM,GAAG,EAAT;IACAC,KAAK,GAAG,CACN7F,WAAA,CAAE2C,eAAF,CAAkB3C,WAAA,CAAEwF,cAAF,CAAiBG,cAAjB,EAAiC,CAAC3F,WAAA,CAAE6C,cAAF,EAAD,CAAjC,CAAlB,CADM,CAAR;EAGD;;EAEDT,OAAO,CAACpB,WAAR,CACEhB,WAAA,CAAEmD,kBAAF,CACEf,OAAO,CAAC1C,IAAR,CAAaqE,IADf,EAEE/D,WAAA,CAAEiB,SAAF,CAAYY,GAAZ,CAFF,EAGE+D,MAHF,EAIE5F,WAAA,CAAE0C,cAAF,CAAiBmD,KAAjB,CAJF,EAKE9D,QALF,CADF;AASD;;AAED,SAAS+D,6BAAT,CACEtG,IADF,EAE6C;EAC3C,MAAM;IAAEc;EAAF,IAAWd,IAAjB;EAEA,OACEc,IAAI,KAAK,iBAAT,IACAA,IAAI,KAAK,kBADT,IAEAA,IAAI,KAAK,aAHX;AAKD;;AAED,SAASyF,iBAAT,CAA2BF,KAA3B,EAAiD;EAC/C,OAAO7F,WAAA,CAAEwF,cAAF,CACLxF,WAAA,CAAEgG,uBAAF,CAA0B,EAA1B,EAA8BhG,WAAA,CAAE0C,cAAF,CAAiBmD,KAAK,CAACpE,IAAvB,CAA9B,CADK,EAEL,EAFK,CAAP;AAID;;AAED,SAASwE,uBAAT,CAAiCC,KAAjC,EAAwD;EACtD,IAAIA,KAAK,CAACpH,MAAN,KAAiB,CAArB,EAAwB,OAAOkB,WAAA,CAAEmG,eAAF,CAAkB,MAAlB,EAA0BnG,WAAA,CAAE4E,cAAF,CAAiB,CAAjB,CAA1B,CAAP;EACxB,IAAIsB,KAAK,CAACpH,MAAN,KAAiB,CAArB,EAAwB,OAAOoH,KAAK,CAAC,CAAD,CAAZ;EACxB,OAAOlG,WAAA,CAAE2B,kBAAF,CAAqBuE,KAArB,CAAP;AACD;;AAED,SAASE,cAAT,CACE5G,IADF,EAEE6G,KAFF,EAGEC,aAHF,EAIEC,OAJF,EAKY;EACV,MAAM9E,IAAI,GAAGjC,IAAI,CAACuB,GAAL,CAAS,WAAT,CAAb;EAEA,MAAMyF,eAAe,GAAGhH,IAAI,CAACE,IAAL,CAAUgF,UAAlC;EACA,IAAI+B,oBAAoB,GAAG,KAA3B;EAEA,MAAMC,uBAAuB,GAAGvG,qCAAqC,CAACX,IAAD,CAArE;;EAIA,KAAK,MAAM4C,OAAX,IAAsBX,IAAtB,EAA4B;IAC1B,IAAI,CAACqE,6BAA6B,CAAC1D,OAAD,CAAlC,EAA6C;MAC3C;IACD;;IAED,IAAIA,OAAO,CAAC1C,IAAR,CAAagF,UAAb,IAA2BtC,OAAO,CAAC1C,IAAR,CAAagF,UAAb,CAAwB5F,MAAxB,GAAiC,CAAhE,EAAmE;MACjE2H,oBAAoB,GAAG,IAAvB;IACD,CAFD,MAEO,IAAIrE,OAAO,CAAC1C,IAAR,CAAaY,IAAb,KAAsB,uBAA1B,EAAmD;MACxD,MAAM;QAAEuB,GAAF;QAAOC,KAAP;QAAcU,MAAM,EAAET,QAAtB;QAAgC4E;MAAhC,IAA6CvE,OAAO,CAAC1C,IAA3D;MAEA,MAAMkH,KAAK,GAAGF,uBAAuB,EAArC;MAEA,MAAMG,SAAS,GAAG/E,KAAK,GAAG9B,WAAA,CAAEiB,SAAF,CAAYa,KAAZ,CAAH,GAAwBG,SAA/C;MAEA,MAAM6E,QAAQ,GAAGlF,qBAAqB,CAACgF,KAAD,EAAQC,SAAR,EAAmB9E,QAAnB,CAAtC;MAEA,MAAM,CAACL,OAAD,IAAYU,OAAO,CAACpB,WAAR,CAAoB8F,QAApB,CAAlB;MACA3E,oBAAoB,CAACT,OAAD,EAAUG,GAAV,EAAe+E,KAAf,EAAsBD,QAAtB,CAApB;IACD;EACF;;EAGD,IAAI,CAACH,eAAD,IAAoB,CAACC,oBAAzB,EAA+C;EAE/C,MAAMM,oBAA0D,GAAG,EAAnE;EAEA,IAAIC,cAAJ;EAGA,IAAIC,eAAJ;EACA,IAAIC,iBAAiB,GAAG,KAAxB;EACA,IAAIC,kBAAkB,GAAG,KAAzB;EACA,MAAMC,uBAAuB,GAAG,IAAI/H,GAAJ,EAAhC;EAEA,IAAIgI,cAAJ,EACEC,eADF,EAEEC,cAFF,EAGEC,UAHF;EAIA,MAAMC,WAAqC,GAAG,EAA9C;EACA,MAAMC,WAAkB,GAAGlI,IAAI,CAACgB,KAAL,CAAWW,MAAtC;;EAEA,MAAMwG,iBAAiB,GAAG,CAACC,UAAD,EAA2BC,IAA3B,KAA4C;IACpE,MAAMC,gBAAgB,GAAGJ,WAAW,CAACtG,6BAAZ,CAA0CyG,IAA1C,CAAzB;IACAJ,WAAW,CAACzC,IAAZ,CAAiBhF,WAAA,CAAEgD,oBAAF,CAAuB,GAAvB,EAA4B8E,gBAA5B,EAA8CF,UAA9C,CAAjB;IACA,OAAO5H,WAAA,CAAEiB,SAAF,CAAY6G,gBAAZ,CAAP;EACD,CAJD;;EAMA,IAAItB,eAAJ,EAAqB;IACnBe,cAAc,GAAGG,WAAW,CAACtG,6BAAZ,CAA0C,WAA1C,CAAjB;IAEA,MAAM,CAAC2G,OAAD,EAAU7I,SAAV,IAAuBmB,mBAAmB,CAACb,IAAD,CAAhD;IACAA,IAAI,GAAGN,SAAP;IACAsI,UAAU,GAAGO,OAAb;IAEAvI,IAAI,CAACE,IAAL,CAAUgF,UAAV,GAAuB,IAAvB;;IAEA,KAAK,MAAMsD,cAAX,IAA6BxB,eAA7B,EAA8C;MAC5C,IAAI,CAACkB,WAAW,CAAC3F,QAAZ,CAAqBiG,cAAc,CAACJ,UAApC,CAAL,EAAsD;QACpDI,cAAc,CAACJ,UAAf,GAA4BD,iBAAiB,CAC3CK,cAAc,CAACJ,UAD4B,EAE3C,KAF2C,CAA7C;MAID;IACF;EACF,CAjBD,MAiBO;IACL,IAAI,CAACpI,IAAI,CAACE,IAAL,CAAUd,EAAf,EAAmB;MACjBY,IAAI,CAACE,IAAL,CAAUd,EAAV,GAAeY,IAAI,CAACgB,KAAL,CAAWyH,qBAAX,CAAiC,OAAjC,CAAf;IACD;;IACDT,UAAU,GAAGxH,WAAA,CAAEiB,SAAF,CAAYzB,IAAI,CAACE,IAAL,CAAUd,EAAtB,CAAb;EACD;;EAED,IAAI6H,oBAAJ,EAA0B;IACxB,KAAK,MAAMrE,OAAX,IAAsBX,IAAtB,EAA4B;MAC1B,IAAI,CAACqE,6BAA6B,CAAC1D,OAAD,CAAlC,EAA6C;QAC3C;MACD;;MAED,MAAM;QAAE1C;MAAF,IAAW0C,OAAjB;MACA,MAAMsC,UAAU,GAAGtC,OAAO,CAACrB,GAAR,CAAY,YAAZ,CAAnB;MAEA,MAAMmH,aAAa,GAAGpD,KAAK,CAACC,OAAN,CAAcL,UAAd,KAA6BA,UAAU,CAAC5F,MAAX,GAAoB,CAAvE;;MAEA,IAAIoJ,aAAJ,EAAmB;QACjB,KAAK,MAAMC,aAAX,IAA4BzD,UAA5B,EAAwC;UACtC,IAAI,CAACgD,WAAW,CAAC3F,QAAZ,CAAqBoG,aAAa,CAACzI,IAAd,CAAmBkI,UAAxC,CAAL,EAA0D;YACxDO,aAAa,CAACzI,IAAd,CAAmBkI,UAAnB,GAAgCD,iBAAiB,CAC/CQ,aAAa,CAACzI,IAAd,CAAmBkI,UAD4B,EAE/C,KAF+C,CAAjD;UAID;QACF;MACF;;MAED,MAAMrF,UAAU,GACd,cAAcH,OAAO,CAAC1C,IAAtB,IAA8B0C,OAAO,CAAC1C,IAAR,CAAaiH,QAAb,KAA0B,IAD1D;;MAEA,IAAIpE,UAAJ,EAAgB;QACd,IAAI,CAACmF,WAAW,CAAC3F,QAAZ,CAAqBrC,IAAI,CAACmC,GAA1B,CAAL,EAAqC;UACnCnC,IAAI,CAACmC,GAAL,GAAW8F,iBAAiB,CAACjI,IAAI,CAACmC,GAAN,EAA2B,aAA3B,CAA5B;QACD;MACF;;MAED,MAAMkC,IAAI,GAAGD,cAAc,CAAC1B,OAAD,CAA3B;MACA,MAAM;QAAEP;MAAF,IAAUnC,IAAhB;MAEA,MAAM0I,SAAS,GAAGvG,GAAG,CAACvB,IAAJ,KAAa,aAA/B;MAEA,MAAMyB,QAAQ,GAAG,CAAC,CAACK,OAAO,CAAC1C,IAAR,CAAa8C,MAAhC;MAEA,IAAI7C,IAAI,GAAG,aAAX;;MAEA,IAAIyI,SAAJ,EAAe;QACbzI,IAAI,GAAIkC,GAAD,CAAuBjD,EAAvB,CAA0Be,IAAjC;MACD,CAFD,MAEO,IAAI,CAAC4C,UAAD,IAAeV,GAAG,CAACvB,IAAJ,KAAa,YAAhC,EAA8C;QACnDX,IAAI,GAAGkC,GAAG,CAAClC,IAAX;MACD;;MAED,IAAIyC,OAAO,CAACiG,aAAR,CAAsB;QAAEtE,IAAI,EAAE;MAAR,CAAtB,CAAJ,EAAoD;QAClDkD,eAAe,GAAG7E,OAAlB;MACD;;MAED,IAAI8F,aAAJ,EAAmB;QACjB,IAAI9C,MAAJ;QACA,IAAIP,cAAJ;;QAEA,IAAId,IAAI,KAAKN,QAAb,EAAuB;UACrB,MAAM;YAAE3B;UAAF,IAAYM,OAAO,CAAC1C,IAA1B;UAEA,MAAMkG,MAAsB,GAAG,CAAC5F,WAAA,CAAE6C,cAAF,EAAD,CAA/B;;UAEA,IAAIf,KAAJ,EAAW;YACT8D,MAAM,CAACZ,IAAP,CAAYhF,WAAA,CAAEiB,SAAF,CAAYa,KAAZ,CAAZ;UACD;;UAED,MAAM8E,KAAK,GAAGF,uBAAuB,EAArC;UACA,MAAM4B,cAAc,GAClBlG,OAAO,CAAC5B,KAAR,CAAcW,MAAd,CAAqBC,6BAArB,CAAoD,QAAOzB,IAAK,EAAhE,CADF;;UAEA,MAAM4I,QAAQ,GAAGvI,WAAA,CAAEwF,cAAF,CACfxF,WAAA,CAAEiB,SAAF,CAAYqH,cAAZ,CADe,EAEf1C,MAFe,CAAjB;;UAKA,MAAMkB,QAAQ,GAAGlF,qBAAqB,CAACgF,KAAD,EAAQ2B,QAAR,EAAkBxG,QAAlB,CAAtC;UACA,MAAM,CAACL,OAAD,IAAYU,OAAO,CAACpB,WAAR,CAAoB8F,QAApB,CAAlB;;UAEA,IAAIsB,SAAJ,EAAe;YACbvD,cAAc,GAAGvB,wBAAwB,CAACsD,KAAD,CAAzC;YAEA,MAAMtB,KAAK,GAAG5D,OAAO,CAAClB,KAAR,CAAcW,MAAd,CAAqBC,6BAArB,CACX,OAAMzB,IAAK,EADA,CAAd;YAGA,MAAM4F,KAAK,GAAG7D,OAAO,CAAClB,KAAR,CAAcW,MAAd,CAAqBC,6BAArB,CACX,OAAMzB,IAAK,EADA,CAAd;YAIA0F,mBAAmB,CAAC3D,OAAD,EAAUG,GAAV,EAAgCyD,KAAhC,EAAuCC,KAAvC,CAAnB;YAEAH,MAAM,GAAG,CAACkD,cAAD,EAAiBhD,KAAjB,EAAwBC,KAAxB,CAAT;UACD,CAbD,MAaO;YACLpD,oBAAoB,CAACT,OAAD,EAAUG,GAAV,EAAe+E,KAAf,EAAsBrE,UAAtB,CAApB;YACA6C,MAAM,GAAGkD,cAAT;UACD;QACF,CArCD,MAqCO,IAAIvE,IAAI,KAAKP,KAAb,EAAoB;UACzB,MAAMgF,MAAM,GAAGpG,OAAO,CAAC5B,KAAR,CAAcW,MAAd,CAAqBC,6BAArB,CACZ,QAAOzB,IAAK,EADA,CAAf;UAGA,MAAM8I,SAAS,GACbrG,OADgB,CAEhBrB,GAFgB,CAEZ,OAFY,CAAlB;UAIA0H,SAAS,CAACzH,WAAV,CACEhB,WAAA,CAAEwF,cAAF,CACExF,WAAA,CAAEiB,SAAF,CAAYuH,MAAZ,CADF,EAEE,CAACxI,WAAA,CAAE6C,cAAF,EAAD,EAAqB4F,SAAS,CAAC/I,IAA/B,EAAqC0E,MAArC,CAA4CsE,CAAC,IAAIA,CAAjD,CAFF,CADF;UAOAtD,MAAM,GAAGoD,MAAT;;UAEA,IAAIJ,SAAJ,EAAe;YACbvD,cAAc,GAAGvB,wBAAwB,CAACzB,GAAD,CAAzC;UACD;QACF,CApBM,MAoBA,IAAIuG,SAAJ,EAAe;UACpBhD,MAAM,GAAGhD,OAAO,CAAC5B,KAAR,CAAcW,MAAd,CAAqBC,6BAArB,CACN,QAAOzB,IAAK,EADN,CAAT;UAIA,MAAMgJ,aAAa,GAAG,IAAIC,4BAAJ,CAAkB;YACtCtC,aADsC;YAEtCuC,UAAU,EAAEzG,OAF0B;YAGtC0G,SAAS,EAAEtB,UAH2B;YAItCuB,QAAQ,EAAEvJ,IAAI,CAACE,IAAL,CAAU8B,UAJkB;YAKtCwH,IAAI,EAAE3C,KAAK,CAAC2C,IAL0B;YAMtCC,aAAa,EAAEzB;UANuB,CAAlB,CAAtB;UASAmB,aAAa,CAACO,OAAd;UAEA,MAAM;YACJtD,MADI;YAEJnE,IAFI;YAGJ0H,KAAK,EAAEC;UAHH,IAIFhH,OAAO,CAAC1C,IAJZ;UAMAmF,cAAc,GAAG7E,WAAA,CAAEuD,kBAAF,CACftB,SADe,EAEf2D,MAAM,CAACxB,MAAP,CAAcqB,gBAAd,CAFe,EAGfhE,IAHe,EAIf2H,OAJe,CAAjB;;UAOA,IAAIrF,IAAI,KAAKJ,MAAT,IAAmBI,IAAI,KAAKH,MAAhC,EAAwC;YACtC8B,mBAAmB,CACjBtD,OADiB,EAEjBpC,WAAA,CAAEiB,SAAF,CAAYY,GAAZ,CAFiB,EAGjB7B,WAAA,CAAEiB,SAAF,CAAYmE,MAAZ,CAHiB,EAIjBrD,QAJiB,CAAnB;UAMD,CAPD,MAOO;YACL,MAAMrC,IAAI,GAAG0C,OAAO,CAAC1C,IAArB;YAGAF,IAAI,CAACE,IAAL,CAAU+B,IAAV,CAAeA,IAAf,CAAoB1C,OAApB,CACEiB,WAAA,CAAEgC,oBAAF,CACEH,GADF,EAEE7B,WAAA,CAAEiB,SAAF,CAAYmE,MAAZ,CAFF,EAGE,EAHF,EAIE1F,IAAI,CAAC8C,MAJP,CADF;YASA4E,uBAAuB,CAAC3H,GAAxB,CAA6BoC,GAAD,CAAuBjD,EAAvB,CAA0Be,IAAtD;YAEAyC,OAAO,CAACiH,MAAR;UACD;QACF;;QAED,IAAIC,QAAJ;;QAEA,IAAI/G,UAAJ,EAAgB;UACd+G,QAAQ,GAAGtJ,WAAA,CAAEiB,SAAF,CAAYY,GAAZ,CAAX;QACD,CAFD,MAEO,IAAIA,GAAG,CAACvB,IAAJ,KAAa,aAAjB,EAAgC;UACrCgJ,QAAQ,GAAGtJ,WAAA,CAAEuJ,aAAF,CAAgB1H,GAAG,CAACjD,EAAJ,CAAOe,IAAvB,CAAX;QACD,CAFM,MAEA,IAAIkC,GAAG,CAACvB,IAAJ,KAAa,YAAjB,EAA+B;UACpCgJ,QAAQ,GAAGtJ,WAAA,CAAEuJ,aAAF,CAAgB1H,GAAG,CAAClC,IAApB,CAAX;QACD,CAFM,MAEA;UACL2J,QAAQ,GAAGtJ,WAAA,CAAEiB,SAAF,CAAYY,GAAZ,CAAX;QACD;;QAEDkF,oBAAoB,CAAC/B,IAArB,CAA0B;UACxBjB,IADwB;UAExBW,UAAU,EAAEA,UAAU,CAACF,GAAX,CAAegF,CAAC,IAAIA,CAAC,CAAC9J,IAAF,CAAOkI,UAA3B,CAFY;UAGxBjI,IAAI,EAAE2J,QAHkB;UAIxBvH,QAJwB;UAKxB8C,cALwB;UAMxBO;QANwB,CAA1B;;QASA,IAAIrB,IAAI,KAAKP,KAAb,EAAoB;UAClB,IAAIzB,QAAJ,EAAc;YACZoF,kBAAkB,GAAG,IAArB;UACD,CAFD,MAEO;YACLD,iBAAiB,GAAG,IAApB;UACD;QACF;;QAED,IAAI9E,OAAO,CAAC1C,IAAZ,EAAkB;UAChB0C,OAAO,CAAC1C,IAAR,CAAagF,UAAb,GAA0B,IAA1B;QACD;;QAED,IAAI,CAACsC,cAAD,KAAoBjD,IAAI,KAAKP,KAAT,IAAkBO,IAAI,KAAKN,QAA/C,CAAJ,EAA8D;UAC5DuD,cAAc,GAAG5E,OAAjB;QAGD;MACF;IACF;EACF;;EAED,MAAMqH,kBAAkB,GAAGnF,uBAAuB,CAACyC,oBAAD,CAAlD;;EACA,MAAM2C,gBAAgB,GAAG1J,WAAA,CAAEuE,eAAF,CACvB,CAACiC,eAAe,IAAI,EAApB,EAAwBhC,GAAxB,CAA4BgF,CAAC,IAAIA,CAAC,CAAC5B,UAAnC,CADuB,CAAzB;;EAIA,MAAMxC,MAAsB,GAC1BH,8BAA8B,CAAC8B,oBAAD,CADhC;;EAGA,IAAIG,iBAAJ,EAAuB;IACrBG,cAAc,GAAGK,WAAW,CAACtG,6BAAZ,CAA0C,WAA1C,CAAjB;IACAgE,MAAM,CAACJ,IAAP,CAAYqC,cAAZ;;IAEA,MAAMsC,aAAa,GAAG3J,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEiB,SAAF,CAAYoG,cAAZ,CAAjB,EAA8C,CAClErH,WAAA,CAAE6C,cAAF,EADkE,CAA9C,CAAtB;;IAIA,IAAImE,cAAJ,EAAoB;MAClB,MAAMlF,KAAK,GAAGkF,cAAc,CAACjG,GAAf,CAAmB,OAAnB,CAAd;MACA,MAAMU,IAAoB,GAAG,CAACkI,aAAD,CAA7B;;MAEA,IAAI7H,KAAK,CAACpC,IAAV,EAAgB;QACd+B,IAAI,CAACuD,IAAL,CAAUlD,KAAK,CAACpC,IAAhB;MACD;;MAEDoC,KAAK,CAACd,WAAN,CAAkBhB,WAAA,CAAE2B,kBAAF,CAAqBF,IAArB,CAAlB;IACD,CATD,MASO,IAAIwF,eAAJ,EAAqB;MAC1B,IAAIzH,IAAI,CAACE,IAAL,CAAU8B,UAAd,EAA0B;QACxBhC,IAAI,CAACF,QAAL,CAAc;UACZsK,cAAc,EAAE;YACdC,IAAI,CAACrK,IAAD,EAAO;cACT,IAAI,CAACA,IAAI,CAACuB,GAAL,CAAS,QAAT,EAAmB+I,OAAnB,EAAL,EAAmC;cAEnCtK,IAAI,CAACwB,WAAL,CACEhB,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEiB,SAAF,CAAYoG,cAAZ,CAAjB,EAA8C,CAAC7H,IAAI,CAACE,IAAN,CAA9C,CADF;cAIAF,IAAI,CAACuK,IAAL;YACD;;UATa;QADJ,CAAd;MAaD,CAdD,MAcO;QACL9C,eAAe,CAACvH,IAAhB,CAAqB+B,IAArB,CAA0BA,IAA1B,CAA+B1C,OAA/B,CACEiB,WAAA,CAAE+C,mBAAF,CAAsB4G,aAAtB,CADF;MAGD;IACF,CApBM,MAoBA;MACL,MAAMlI,IAAmB,GAAG,CAACzB,WAAA,CAAE+C,mBAAF,CAAsB4G,aAAtB,CAAD,CAA5B;;MAEA,IAAInK,IAAI,CAACE,IAAL,CAAU8B,UAAd,EAA0B;QACxBC,IAAI,CAAC1C,OAAL,CACEiB,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEgK,KAAF,EAAjB,EAA4B,CAC1BhK,WAAA,CAAEiK,aAAF,CAAgBjK,WAAA,CAAEE,UAAF,CAAa,MAAb,CAAhB,CAD0B,CAA5B,CADF,CADF;MAOD;;MAEDV,IAAI,CAACE,IAAL,CAAU+B,IAAV,CAAeA,IAAf,CAAoB1C,OAApB,CACEiB,WAAA,CAAEoD,WAAF,CACE,aADF,EAEEpD,WAAA,CAAEE,UAAF,CAAa,aAAb,CAFF,EAGE,CAACF,WAAA,CAAEkK,WAAF,CAAclK,WAAA,CAAEE,UAAF,CAAa,MAAb,CAAd,CAAD,CAHF,EAIEF,WAAA,CAAE0C,cAAF,CAAiBjB,IAAjB,CAJF,CADF;IAQD;EACF;;EAED,IAAI0F,kBAAJ,EAAwB;IACtBG,eAAe,GAAGI,WAAW,CAACtG,6BAAZ,CAA0C,YAA1C,CAAlB;IACAgE,MAAM,CAACJ,IAAP,CAAYsC,eAAZ;EACD;;EAED,IAAIF,uBAAuB,CAAC+C,IAAxB,GAA+B,CAAnC,EAAsC;IACpC3K,IAAI,CAACF,QAAL,CAAc;MACZC,WAAW,CAACC,IAAD,EAAO;QAChB,IAAI,CAAC4H,uBAAuB,CAACrH,GAAxB,CAA4BP,IAAI,CAACE,IAAL,CAAUd,EAAV,CAAae,IAAzC,CAAL,EAAqD;QAErD,MAAM0B,UAAU,GAAG7B,IAAI,CAAC6B,UAAxB;QACA,MAAM+I,gBAAgB,GAAG/I,UAAU,CAACA,UAApC;;QAEA,IAEG+I,gBAAgB,CAAC1K,IAAjB,CAAsBY,IAAtB,KAA+B,sBAA/B,IACC8J,gBAAgB,CAAC1K,IAAjB,CAAsB2K,IAAtB,KAA+BhJ,UAAU,CAAC3B,IAD5C,IAGA0K,gBAAgB,CAAC1K,IAAjB,CAAsBY,IAAtB,KAA+B,kBAH/B,IAKA8J,gBAAgB,CAAC1K,IAAjB,CAAsBY,IAAtB,KAA+B,aAL/B,IAOA8J,gBAAgB,CAAC1K,IAAjB,CAAsBY,IAAtB,KAA+B,cAP/B,IASC8J,gBAAgB,CAAC1K,IAAjB,CAAsBY,IAAtB,KAA+B,gBAA/B,IACC8J,gBAAgB,CAAC1K,IAAjB,CAAsBoC,KAAtB,KAAgCT,UAAU,CAAC3B,IAD5C,IAEC0K,gBAAgB,CAAC/I,UAAjB,CAA4Bf,IAA5B,KAAqC,eAXvC,IAaC8J,gBAAgB,CAAC1K,IAAjB,CAAsBY,IAAtB,KAA+B,gBAA/B,IACC8J,gBAAgB,CAAC1K,IAAjB,CAAsB2K,IAAtB,KAA+BhJ,UAAU,CAAC3B,IAhB9C,EAiBE;UACA,MAAMF,IAAI,CAAC8K,mBAAL,CACH,sDAAqD9K,IAAI,CAACE,IAAL,CAAUd,EAAV,CAAae,IAAK,mCADpE,CAAN;QAGD;MACF;;IA7BW,CAAd;EA+BD;;EAED,IAAI4K,iBAAiB,GAAG,KAAxB;;EACA,MAAMC,aAAa,GACjBjD,cAAc,IAAIvH,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEiB,SAAF,CAAYsG,cAAZ,CAAjB,EAA8C,EAA9C,CADpB;;EAGA,MAAMkD,aAAa,GAAGjL,IAAI,CAACE,IAA3B;;EAEA,IAAI8G,eAAJ,EAAqB;IACnBpB,MAAM,CAACJ,IAAP,CAAYwC,UAAZ,EAAwBD,cAAxB;IACA,MAAMmD,OAIH,GAAG,EAJN;IAKA,IAAIC,YAA6B,GAAG,EAApC;IACAnL,IAAI,CAACuB,GAAL,CAAS,WAAT,EAAsB6J,OAAtB,CAA8BxI,OAAO,IAAI;MAGvC,IAAIA,OAAO,CAACyI,aAAR,EAAJ,EAA6B;QAC3BF,YAAY,CAAC3F,IAAb,CAAkB5C,OAAO,CAAC1C,IAA1B;QACA0C,OAAO,CAACiH,MAAR;QACA;MACD;;MAED,MAAMyB,UAAU,GACd1I,OAAO,CAAC2I,eAAR,MAA6B3I,OAAO,CAAC4I,sBAAR,EAD/B;;MAGA,IACE,CAACF,UAAU,IAAI1I,OAAO,CAAC6I,oBAAR,EAAf,KACA7I,OAAO,CAAC1C,IAAR,CAAa8C,MAFf,EAGE;QACA,IAAIsI,UAAU,IAAIH,YAAY,CAAC7L,MAAb,GAAsB,CAAxC,EAA2C;UACzC,MAAMoM,SAAyB,GAAGP,YAAY,CAACnG,GAAb,CAAiBuB,iBAAjB,CAAlC;UACA,IAAI3D,OAAO,CAAC1C,IAAR,CAAaoC,KAAjB,EAAwBoJ,SAAS,CAAClG,IAAV,CAAe5C,OAAO,CAAC1C,IAAR,CAAaoC,KAA5B;UACxBM,OAAO,CAAC1C,IAAR,CAAaoC,KAAb,GAAqBmE,uBAAuB,CAACiF,SAAD,CAA5C;UACAP,YAAY,GAAG,EAAf;QACD;;QAEDvI,OAAO,CAAC1C,IAAR,CAAa8C,MAAb,GAAsB,KAAtB;QACAkI,OAAO,CAAC1F,IAAR,CAAa5C,OAAO,CAAC1C,IAArB;QACA0C,OAAO,CAACiH,MAAR;MACD;IACF,CA3BD;;IA6BA,IAAIqB,OAAO,CAAC5L,MAAR,GAAiB,CAAjB,IAAsB6L,YAAY,CAAC7L,MAAb,GAAsB,CAAhD,EAAmD;MACjD,MAAMqM,YAAY,GAAGC,cAAA,CAASxD,UAAT,CAAoByD,GAAI;AACnD,wBAAwBhF,KAAK,CAACiF,SAAN,CAAgB,UAAhB,CAA4B;AACpD,OAFM;MAGAH,YAAY,CAAC1J,IAAb,CAAkBA,IAAlB,GAAyB,CACvBzB,WAAA,CAAEuL,WAAF,CAAc,CAACvL,WAAA,CAAEwL,WAAF,CAAchM,IAAI,CAACE,IAAnB,EAAyB,KAAzB,CAAD,CAAd,CADuB,EAEvB,GAAGgL,OAFoB,CAAzB;MAKA,MAAMe,eAA+B,GAAG,EAAxC;;MAEA,MAAMC,OAAO,GAAG1L,WAAA,CAAE2L,aAAF,CAAgBR,YAAhB,EAA8B,EAA9B,CAAhB;;MAEA,IAAIR,YAAY,CAAC7L,MAAb,GAAsB,CAA1B,EAA6B;QAC3B2M,eAAe,CAACzG,IAAhB,CAAqB,GAAG2F,YAAY,CAACnG,GAAb,CAAiBuB,iBAAjB,CAAxB;MACD;;MACD,IAAIyE,aAAJ,EAAmB;QACjBD,iBAAiB,GAAG,IAApB;QACAkB,eAAe,CAACzG,IAAhB,CAAqBwF,aAArB;MACD;;MACD,IAAIiB,eAAe,CAAC3M,MAAhB,GAAyB,CAA7B,EAAgC;QAC9B2M,eAAe,CAAC1M,OAAhB,CACEiB,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEgK,KAAF,EAAjB,EAA4B,CAAChK,WAAA,CAAEiB,SAAF,CAAYuG,UAAZ,CAAD,CAA5B,CADF;QAIA2D,YAAY,CAAC1J,IAAb,CAAkBA,IAAlB,CAAuBuD,IAAvB,CACEhF,WAAA,CAAEoD,WAAF,CACE,aADF,EAEEpD,WAAA,CAAEE,UAAF,CAAa,aAAb,CAFF,EAGE,EAHF,EAIEF,WAAA,CAAE0C,cAAF,CAAiB,CACf1C,WAAA,CAAE+C,mBAAF,CAAsB/C,WAAA,CAAE2B,kBAAF,CAAqB8J,eAArB,CAAtB,CADe,CAAjB,CAJF,CADF;MAUD,CAfD,MAeO;QACLC,OAAO,CAACE,SAAR,CAAkB5G,IAAlB,CAAuBhF,WAAA,CAAEiB,SAAF,CAAYuG,UAAZ,CAAvB;MACD;;MAEDhI,IAAI,CAACwB,WAAL,CAAiB0K,OAAjB;IACD;EACF;;EACD,IAAI,CAACnB,iBAAD,IAAsBC,aAA1B,EAAyC;IACvChL,IAAI,CAACE,IAAL,CAAU+B,IAAV,CAAeA,IAAf,CAAoBuD,IAApB,CACEhF,WAAA,CAAEuL,WAAF,CAAc,CAACvL,WAAA,CAAE+C,mBAAF,CAAsByH,aAAtB,CAAD,CAAd,CADF;EAGD;;EAEDC,aAAa,CAAChJ,IAAd,CAAmBA,IAAnB,CAAwB1C,OAAxB,CACEiB,WAAA,CAAEuL,WAAF,CACE,CACEvL,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEgD,oBAAF,CACE,GADF,EAEEhD,WAAA,CAAE6L,YAAF,CAAezG,MAAf,CAFF,EAGEpF,WAAA,CAAEwF,cAAF,CACEa,KAAK,CAACiF,SAAN,CACE/E,OAAO,KAAK,SAAZ,GAAwB,WAAxB,GAAsC,eADxC,CADF,EAIE,CAACvG,WAAA,CAAE6C,cAAF,EAAD,EAAqB4G,kBAArB,EAAyCC,gBAAzC,CAJF,CAHF,CADF,CADF,EAaEvC,kBAAkB,IAChBnH,WAAA,CAAE+C,mBAAF,CACE/C,WAAA,CAAEwF,cAAF,CAAiBxF,WAAA,CAAEiB,SAAF,CAAYqG,eAAZ,CAAjB,EAA+C,CAC7CtH,WAAA,CAAE6C,cAAF,EAD6C,CAA/C,CADF,CAdJ,EAmBEuB,MAnBF,CAmBS0H,OAnBT,CADF,CADF;EA2BAtM,IAAI,CAACoB,YAAL,CAAkB6G,WAAW,CAACjD,GAAZ,CAAgBuH,IAAI,IAAI/L,WAAA,CAAE+C,mBAAF,CAAsBgJ,IAAtB,CAAxB,CAAlB;EAGAvM,IAAI,CAACgB,KAAL,CAAWwL,KAAX;EAEA,OAAOxM,IAAP;AACD;;AAEc,kBACb;EAAEyM,aAAF;EAAiBC;AAAjB,CADa,EAEb;EAAEC;AAAF,CAFa,EAGb5F,OAHa,EAIC;EAAA;;EACd0F,aAAa,CAAC1F,OAAO,KAAK,SAAZ,GAAwB,SAAxB,GAAoC,SAArC,CAAb;EAEA,MAAM6F,OAAO,GAAG,IAAIC,OAAJ,EAAhB;EACA,MAAM/F,aAAa,kBAAG4F,UAAU,CAAC,eAAD,CAAb,0BAAkCC,KAArD;EAEA,OAAO;IACLxM,IAAI,EAAE,qBADD;IAEL2M,QAAQ,EAAEC,+BAFL;IAILC,OAAO,EAAE;MACP,kDACEhN,IADF,EAEE;QAAA;;QACA,MAAM;UAAEiN;QAAF,IAAkBjN,IAAI,CAACE,IAA7B;;QACA,IACE,CAAA+M,WAAW,QAAX,YAAAA,WAAW,CAAEnM,IAAb,MAAsB,kBAAtB,IAGA,0BAAAmM,WAAW,CAAC/H,UAAZ,2CAAwB5F,MAAxB,IAAiC,CAJnC,EAKE;UACA,IAAA4N,qCAAA,EAAuBlN,IAAvB;QACD;MACF,CAbM;;MAePmN,KAAK,CAACnN,IAAD,EAAO6G,KAAP,EAAc;QACjB,IAAI+F,OAAO,CAACrM,GAAR,CAAYP,IAAZ,CAAJ,EAAuB;QAEvB,MAAMkC,OAAO,GAAG0E,cAAc,CAAC5G,IAAD,EAAO6G,KAAP,EAAcC,aAAd,EAA6BC,OAA7B,CAA9B;QACA,IAAI7E,OAAJ,EAAa0K,OAAO,CAAC3M,GAAR,CAAYiC,OAAZ;MACd;;IApBM;EAJJ,CAAP;AA2BD"}