{"version": 3, "names": ["REACT_ELEMENT_TYPE", "_createRawReactElement", "type", "props", "key", "children", "Symbol", "defaultProps", "<PERSON><PERSON><PERSON><PERSON>", "arguments", "length", "<PERSON><PERSON><PERSON><PERSON>", "Array", "i", "propName", "$$typeof", "undefined", "ref", "_owner"], "sources": ["../../src/helpers/jsx.js"], "sourcesContent": ["/* @minVersion 7.0.0-beta.0 */\n\nvar REACT_ELEMENT_TYPE;\n\nexport default function _createRawReactElement(type, props, key, children) {\n  if (!REACT_ELEMENT_TYPE) {\n    REACT_ELEMENT_TYPE =\n      (typeof Symbol === \"function\" &&\n        // \"for\" is a reserved keyword in ES3 so escaping it here for backward compatibility\n        Symbol[\"for\"] &&\n        Symbol[\"for\"](\"react.element\")) ||\n      0xeac7;\n  }\n\n  var defaultProps = type && type.defaultProps;\n  var childrenLength = arguments.length - 3;\n\n  if (!props && childrenLength !== 0) {\n    // If we're going to assign props.children, we create a new object now\n    // to avoid mutating defaultProps.\n    props = { children: void 0 };\n  }\n\n  if (childrenLength === 1) {\n    props.children = children;\n  } else if (childrenLength > 1) {\n    var childArray = new Array(childrenLength);\n    for (var i = 0; i < childrenLength; i++) {\n      childArray[i] = arguments[i + 3];\n    }\n    props.children = childArray;\n  }\n\n  if (props && defaultProps) {\n    for (var propName in defaultProps) {\n      if (props[propName] === void 0) {\n        props[propName] = defaultProps[propName];\n      }\n    }\n  } else if (!props) {\n    props = defaultProps || {};\n  }\n\n  return {\n    $$typeof: REACT_ELEMENT_TYPE,\n    type: type,\n    key: key === undefined ? null : \"\" + key,\n    ref: null,\n    props: props,\n    _owner: null,\n  };\n}\n"], "mappings": ";;;;;;AAEA,IAAIA,kBAAJ;;AAEe,SAASC,sBAAT,CAAgCC,IAAhC,EAAsCC,KAAtC,EAA6CC,GAA7C,EAAkDC,QAAlD,EAA4D;EACzE,IAAI,CAACL,kBAAL,EAAyB;IACvBA,kBAAkB,GACf,OAAOM,MAAP,KAAkB,UAAlB,IAECA,MAAM,CAAC,KAAD,CAFP,IAGCA,MAAM,CAAC,KAAD,CAAN,CAAc,eAAd,CAHF,IAIA,MALF;EAMD;;EAED,IAAIC,YAAY,GAAGL,IAAI,IAAIA,IAAI,CAACK,YAAhC;EACA,IAAIC,cAAc,GAAGC,SAAS,CAACC,MAAV,GAAmB,CAAxC;;EAEA,IAAI,CAACP,KAAD,IAAUK,cAAc,KAAK,CAAjC,EAAoC;IAGlCL,KAAK,GAAG;MAAEE,QAAQ,EAAE,KAAK;IAAjB,CAAR;EACD;;EAED,IAAIG,cAAc,KAAK,CAAvB,EAA0B;IACxBL,KAAK,CAACE,QAAN,GAAiBA,QAAjB;EACD,CAFD,MAEO,IAAIG,cAAc,GAAG,CAArB,EAAwB;IAC7B,IAAIG,UAAU,GAAG,IAAIC,KAAJ,CAAUJ,cAAV,CAAjB;;IACA,KAAK,IAAIK,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,cAApB,EAAoCK,CAAC,EAArC,EAAyC;MACvCF,UAAU,CAACE,CAAD,CAAV,GAAgBJ,SAAS,CAACI,CAAC,GAAG,CAAL,CAAzB;IACD;;IACDV,KAAK,CAACE,QAAN,GAAiBM,UAAjB;EACD;;EAED,IAAIR,KAAK,IAAII,YAAb,EAA2B;IACzB,KAAK,IAAIO,QAAT,IAAqBP,YAArB,EAAmC;MACjC,IAAIJ,KAAK,CAACW,QAAD,CAAL,KAAoB,KAAK,CAA7B,EAAgC;QAC9BX,KAAK,CAACW,QAAD,CAAL,GAAkBP,YAAY,CAACO,QAAD,CAA9B;MACD;IACF;EACF,CAND,MAMO,IAAI,CAACX,KAAL,EAAY;IACjBA,KAAK,GAAGI,YAAY,IAAI,EAAxB;EACD;;EAED,OAAO;IACLQ,QAAQ,EAAEf,kBADL;IAELE,IAAI,EAAEA,IAFD;IAGLE,GAAG,EAAEA,GAAG,KAAKY,SAAR,GAAoB,IAApB,GAA2B,KAAKZ,GAHhC;IAILa,GAAG,EAAE,IAJA;IAKLd,KAAK,EAAEA,KALF;IAMLe,MAAM,EAAE;EANH,CAAP;AAQD"}