"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./components/save-button.tsx":
/*!************************************!*\
  !*** ./components/save-button.tsx ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-ui/core */ \"./node_modules/@material-ui/core/esm/index.js\");\n/* harmony import */ var _heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @heroicons/react/outline */ \"./node_modules/@heroicons/react/outline/esm/index.js\");\n/* harmony import */ var libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/web/state/lexical-editor */ \"./libs/web/state/lexical-editor.ts\");\n/**\n * SaveButton Component\n *\n * Copyright (c) 2025 waycaan\n * Licensed under the MIT License\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n */ \n\nvar _this = undefined;\n\nvar _s = $RefreshSig$();\n\n\n\n\nvar useStyles = (0,_material_ui_core__WEBPACK_IMPORTED_MODULE_3__.makeStyles)({\n    saveButton: {\n        minWidth: \"80px\",\n        fontWeight: \"bold\",\n        textTransform: \"none\",\n        borderRadius: \"8px\",\n        boxShadow: \"none !important\",\n        \"&:hover\": {\n            opacity: 0.8,\n            boxShadow: \"none !important\"\n        },\n        \"&:focus\": {\n            boxShadow: \"none !important\"\n        },\n        \"&:active\": {\n            boxShadow: \"none !important\"\n        }\n    },\n    viewButton: {\n        backgroundColor: \"#6B7280 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#4B5563 !important\"\n        }\n    },\n    saveStateButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    },\n    syncingButton: {\n        backgroundColor: \"#3185eb !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#2563EB !important\"\n        }\n    },\n    syncedButton: {\n        backgroundColor: \"#FBBF24 !important\",\n        color: \"#000000 !important\",\n        \"&:hover\": {\n            backgroundColor: \"#F59E0B !important\"\n        }\n    },\n    failedButton: {\n        backgroundColor: \"#DC2626 !important\",\n        color: \"#FFFFFF !important\",\n        \"&:hover\": {\n            backgroundColor: \"#B91C1C !important\"\n        }\n    }\n});\nvar SaveButton = function(param) {\n    var className = param.className;\n    _s();\n    var classes = useStyles();\n    var ref = libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer(), syncToServer = ref.syncToServer, note = ref.note, getEditorState = ref.getEditorState, saveCurrentContent = ref.saveCurrentContent, clearAllSnapshots = ref.clearAllSnapshots;\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"view\"), syncStatus = ref1[0], setSyncStatus = ref1[1];\n    var syncedTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    var syncTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 🔧 重构：基于快照对比的状态检测机制\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (!(note === null || note === void 0 ? void 0 : note.id)) {\n            setSyncStatus(\"view\");\n            return;\n        }\n        var checkSnapshotChanges = function() {\n            try {\n                var editorState = getEditorState();\n                if (editorState.hasChanges) {\n                    // 有变化：设置为save状态\n                    if (syncStatus !== \"save\" && syncStatus !== \"syncing\") {\n                        setSyncStatus(\"save\");\n                    }\n                } else {\n                    // 无变化：设置为view状态\n                    if (syncStatus === \"save\") {\n                        setSyncStatus(\"view\");\n                    }\n                }\n            } catch (error) {\n                console.error(\"快照对比检查失败:\", error);\n            }\n        };\n        // 立即检查一次\n        checkSnapshotChanges();\n        // 定期检查快照变化\n        var interval = setInterval(checkSnapshotChanges, 500);\n        return function() {\n            clearInterval(interval);\n            if (syncedTimeoutRef.current) {\n                clearTimeout(syncedTimeoutRef.current);\n            }\n            if (syncTimeoutRef.current) {\n                clearTimeout(syncTimeoutRef.current);\n            }\n        };\n    }, [\n        note,\n        getEditorState,\n        syncStatus\n    ]);\n    // 手动保存流程\n    var handleSave = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(function() {\n        var saveSuccess, syncSuccess, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    setSyncStatus(\"syncing\");\n                    if (syncedTimeoutRef.current) {\n                        clearTimeout(syncedTimeoutRef.current);\n                    }\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                    }\n                    // 设置超时保护\n                    syncTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"fail\");\n                        setTimeout(function() {\n                            setSyncStatus(\"view\");\n                        }, 2000);\n                    }, 30000);\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        4,\n                        ,\n                        5\n                    ]);\n                    return [\n                        4,\n                        saveCurrentContent()\n                    ];\n                case 2:\n                    saveSuccess = _state.sent();\n                    if (!saveSuccess) {\n                        throw new Error(\"保存到IndexedDB失败\");\n                    }\n                    return [\n                        4,\n                        syncToServer()\n                    ];\n                case 3:\n                    syncSuccess = _state.sent();\n                    if (!syncSuccess) {\n                        throw new Error(\"同步到服务器失败\");\n                    }\n                    // 清除超时\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"synced\");\n                    // 3秒后自动变回view状态\n                    syncedTimeoutRef.current = setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 3000);\n                    return [\n                        3,\n                        5\n                    ];\n                case 4:\n                    error = _state.sent();\n                    console.error(\"手动保存失败:\", error);\n                    if (syncTimeoutRef.current) {\n                        clearTimeout(syncTimeoutRef.current);\n                        syncTimeoutRef.current = null;\n                    }\n                    setSyncStatus(\"fail\");\n                    setTimeout(function() {\n                        setSyncStatus(\"view\");\n                    }, 2000);\n                    return [\n                        3,\n                        5\n                    ];\n                case 5:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        syncToServer,\n        saveCurrentContent\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        if (true) {\n            window.saveButtonStatus = syncStatus;\n            window.saveButtonAutoSave = handleSave;\n        }\n        return function() {\n            if (true) {\n                delete window.saveButtonStatus;\n                delete window.saveButtonAutoSave;\n            }\n        };\n    }, [\n        syncStatus,\n        handleSave\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(function() {\n        var handleKeyDown = function(e) {\n            if ((e.ctrlKey || e.metaKey) && e.key === \"s\") {\n                var target = e.target;\n                var isInEditor = target.closest(\".ProseMirror\") || target.closest(\"[contenteditable]\") || target.closest(\"textarea\") || target.closest(\"input\");\n                if (isInEditor) {\n                    e.preventDefault();\n                    e.stopPropagation();\n                    handleSave();\n                }\n            }\n        };\n        document.addEventListener(\"keydown\", handleKeyDown, true);\n        return function() {\n            return document.removeEventListener(\"keydown\", handleKeyDown, true);\n        };\n    }, [\n        handleSave\n    ]);\n    var getButtonIcon = function() {\n        switch(syncStatus){\n            case \"view\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 237,\n                    columnNumber: 24\n                }, _this);\n            case \"save\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.DocumentIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 24\n                }, _this);\n            case \"syncing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.UploadIcon, {\n                    className: \"w-4 h-4 animate-pulse\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 241,\n                    columnNumber: 24\n                }, _this);\n            case \"synced\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.CheckIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 24\n                }, _this);\n            case \"fail\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.XIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 24\n                }, _this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_heroicons_react_outline__WEBPACK_IMPORTED_MODULE_6__.EyeIcon, {\n                    className: \"w-4 h-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 24\n                }, _this);\n        }\n    };\n    var getButtonText = function() {\n        switch(syncStatus){\n            case \"view\":\n                return \"View\";\n            case \"save\":\n                return \"Save\";\n            case \"syncing\":\n                return \"Syncing...\";\n            case \"synced\":\n                return \"Synced\";\n            case \"fail\":\n                return \"Failed\";\n            default:\n                return \"View\";\n        }\n    };\n    var getButtonClassName = function() {\n        var baseClass = \"\".concat(classes.saveButton);\n        switch(syncStatus){\n            case \"view\":\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n            case \"save\":\n                return \"\".concat(baseClass, \" \").concat(classes.saveStateButton);\n            case \"syncing\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncingButton);\n            case \"synced\":\n                return \"\".concat(baseClass, \" \").concat(classes.syncedButton);\n            case \"fail\":\n                return \"\".concat(baseClass, \" \").concat(classes.failedButton);\n            default:\n                return \"\".concat(baseClass, \" \").concat(classes.viewButton);\n        }\n    };\n    var isButtonDisabled = function() {\n        return syncStatus === \"syncing\" || syncStatus === \"view\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_ui_core__WEBPACK_IMPORTED_MODULE_3__.Button, {\n        variant: \"contained\",\n        startIcon: getButtonIcon(),\n        onClick: handleSave,\n        disabled: isButtonDisabled(),\n        className: \"\".concat(getButtonClassName(), \" \").concat(className || \"\"),\n        size: \"small\",\n        \"data-save-button\": \"true\",\n        children: getButtonText()\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\components\\\\save-button.tsx\",\n        lineNumber: 291,\n        columnNumber: 9\n    }, _this);\n};\n_s(SaveButton, \"KXoPEqmciSGrctGo2kTHz9TPoSU=\", false, function() {\n    return [\n        useStyles,\n        libs_web_state_lexical_editor__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer\n    ];\n});\n_c = SaveButton;\n/* harmony default export */ __webpack_exports__[\"default\"] = (SaveButton);\nvar _c;\n$RefreshReg$(_c, \"SaveButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/save-button.tsx\n"));

/***/ })

});