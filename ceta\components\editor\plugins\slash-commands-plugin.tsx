/**
 * Slash Commands Plugin for Lexical
 * Provides quick insertion of various content types using "/" trigger
 */

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext';
import {
    LexicalTypeaheadMenuPlugin,
    MenuOption,
    useBasicTypeaheadTriggerMatch,
} from '@lexical/react/LexicalTypeaheadMenuPlugin';
import { $createQuoteNode } from '@lexical/rich-text';
import { $getSelection, $isRangeSelection } from 'lexical';
import { $setBlocksType } from '@lexical/selection';
import {
    INSERT_ORDERED_LIST_COMMAND,
    INSERT_UNORDERED_LIST_COMMAND,
    INSERT_CHECK_LIST_COMMAND
} from '@lexical/list';
import { INSERT_HORIZONTAL_RULE_COMMAND } from '@lexical/react/LexicalHorizontalRuleNode';
import { useCallback, useMemo, useState } from 'react';
import { createPortal } from 'react-dom';
import { useTheme } from 'next-themes';

// Heroicons
import {
    ViewListIcon,
    CollectionIcon,
    ClipboardListIcon,
    AnnotationIcon,
    MinusIcon
} from '@heroicons/react/outline';

class SlashCommandOption extends MenuOption {
    title: string;
    icon?: JSX.Element;
    keywords: Array<string>;
    keyboardShortcut?: string;
    onSelect: (queryString: string) => void;

    constructor(
        title: string,
        options: {
            icon?: JSX.Element;
            keywords?: Array<string>;
            keyboardShortcut?: string;
            onSelect: (queryString: string) => void;
        },
    ) {
        super(title);
        this.title = title;
        this.keywords = options.keywords || [];
        this.icon = options.icon;
        this.keyboardShortcut = options.keyboardShortcut;
        this.onSelect = options.onSelect.bind(this);
    }
}

function SlashCommandMenuItem({
    index,
    isSelected,
    onClick,
    onMouseEnter,
    option,
}: {
    index: number;
    isSelected: boolean;
    onClick: () => void;
    onMouseEnter: () => void;
    option: SlashCommandOption;
}) {
    return (
        <li
            key={option.key}
            tabIndex={-1}
            className={`slash-command-item ${isSelected ? 'selected' : ''}`}
            ref={option.setRefElement}
            role="option"
            aria-selected={isSelected}
            id={'typeahead-item-' + index}
            onMouseEnter={onMouseEnter}
            onClick={onClick}
        >
            {option.icon && <div className="slash-command-icon">{option.icon}</div>}
            <div className="slash-command-text">
                <span className="slash-command-title">{option.title}</span>
                {option.keyboardShortcut && (
                    <span className="slash-command-shortcut">{option.keyboardShortcut}</span>
                )}
            </div>
        </li>
    );
}

export default function SlashCommandsPlugin(): JSX.Element {
    const [editor] = useLexicalComposerContext();
    const [queryString, setQueryString] = useState<string | null>(null);
    const { theme } = useTheme();

    const checkForTriggerMatch = useBasicTypeaheadTriggerMatch('/', {
        minLength: 0,
    });

    const options = useMemo(() => {
        const baseOptions = [
            new SlashCommandOption('Checklist', {
                icon: <ClipboardListIcon className="w-4 h-4" />,
                keywords: ['checklist', 'todo', 'task', 'checkbox'],
                onSelect: () => {
                    editor.dispatchCommand(INSERT_CHECK_LIST_COMMAND, undefined);
                },
            }),
            new SlashCommandOption('Bullet List', {
                icon: <ViewListIcon className="w-4 h-4" />,
                keywords: ['bullet', 'list', 'unordered', 'ul'],
                onSelect: () => {
                    editor.dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined);
                },
            }),
            new SlashCommandOption('Numbered List', {
                icon: <CollectionIcon className="w-4 h-4" />,
                keywords: ['numbered', 'list', 'ordered', 'ol'],
                onSelect: () => {
                    editor.dispatchCommand(INSERT_ORDERED_LIST_COMMAND, undefined);
                },
            }),
            new SlashCommandOption('Quote', {
                icon: <AnnotationIcon className="w-4 h-4" />,
                keywords: ['quote', 'blockquote', 'citation'],
                onSelect: () => {
                    editor.update(() => {
                        const selection = $getSelection();
                        if ($isRangeSelection(selection)) {
                            $setBlocksType(selection, () => $createQuoteNode());
                        }
                    });
                },
            }),
            new SlashCommandOption('Divider', {
                icon: <MinusIcon className="w-4 h-4" />,
                keywords: ['divider', 'separator', 'hr', 'horizontal', 'rule'],
                onSelect: () => {
                    editor.dispatchCommand(INSERT_HORIZONTAL_RULE_COMMAND, undefined);
                },
            }),
        ];

        if (!queryString) {
            return baseOptions;
        }

        return baseOptions.filter((option) => {
            return new RegExp(queryString, 'gi').exec(option.title) ||
                option.keywords != null
                    ? option.keywords.some((keyword) =>
                        new RegExp(queryString, 'gi').exec(keyword),
                    )
                    : false;
        });
    }, [editor, queryString]);

    const onSelectOption = useCallback(
        (
            selectedOption: SlashCommandOption,
            nodeToRemove: any,
            closeMenu: () => void,
            matchingString: string,
        ) => {
            editor.update(() => {
                if (nodeToRemove) {
                    nodeToRemove.remove();
                }
                selectedOption.onSelect(matchingString);
                closeMenu();
            });
        },
        [editor],
    );

    return (
        <>
            <LexicalTypeaheadMenuPlugin<SlashCommandOption>
                onQueryChange={setQueryString}
                onSelectOption={onSelectOption}
                triggerFn={checkForTriggerMatch}
                options={options}
                menuRenderFn={(
                    anchorElementRef,
                    { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex },
                ) =>
                    anchorElementRef.current && options.length
                        ? createPortal(
                            <div className={`slash-commands-menu ${theme === 'dark' ? 'dark' : ''}`}>
                                <ul className="slash-commands-list">
                                    {options.map((option, i: number) => (
                                        <SlashCommandMenuItem
                                            index={i}
                                            isSelected={selectedIndex === i}
                                            onClick={() => {
                                                setHighlightedIndex(i);
                                                selectOptionAndCleanUp(option);
                                            }}
                                            onMouseEnter={() => {
                                                setHighlightedIndex(i);
                                            }}
                                            key={option.key}
                                            option={option}
                                        />
                                    ))}
                                </ul>
                            </div>,
                            anchorElementRef.current,
                        )
                        : null
                }
            />
            <style jsx global>{`
                .slash-commands-menu {
                    background: #f4f4f5;
                    border: 1px solid #e5e7eb;
                    border-radius: 0.5rem;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
                    max-height: 300px;
                    min-width: 240px;
                    overflow-y: auto;
                    padding: 0.5rem 0;
                    z-index: 1000;
                    position: absolute;
                }

                .slash-commands-menu.dark {
                    background: #27272a;
                    border-color: #374151;
                }

                .slash-commands-list {
                    list-style: none;
                    margin: 0;
                    padding: 0;
                }

                .slash-command-item {
                    display: flex;
                    align-items: center;
                    padding: 0.75rem 1rem;
                    cursor: pointer;
                    transition: all 0.15s ease;
                    border-radius: 0.25rem;
                    margin: 0 0.25rem;
                }

                .slash-command-item:hover,
                .slash-command-item.selected {
                    background-color: #eab834;
                    color: black;
                }

                .slash-commands-menu.dark .slash-command-item:hover,
                .slash-commands-menu.dark .slash-command-item.selected {
                    background-color: #3185eb;
                    color: white;
                }

                .slash-command-item:hover .slash-command-icon,
                .slash-command-item.selected .slash-command-icon,
                .slash-command-item:hover .slash-command-title,
                .slash-command-item.selected .slash-command-title {
                    color: black;
                }

                .slash-commands-menu.dark .slash-command-item:hover .slash-command-icon,
                .slash-commands-menu.dark .slash-command-item.selected .slash-command-icon,
                .slash-commands-menu.dark .slash-command-item:hover .slash-command-title,
                .slash-commands-menu.dark .slash-command-item.selected .slash-command-title {
                    color: white;
                }

                .slash-command-icon {
                    margin-right: 0.75rem;
                    width: 2rem;
                    height: 2rem;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1rem;
                    color: #6b7280;
                    background-color: #e4e4e7;
                    border-radius: 0.375rem;
                    font-weight: 600;
                }

                .slash-commands-menu.dark .slash-command-icon {
                    background-color: #3f3f46;
                    color: #9ca3af;
                }

                .slash-command-text {
                    display: flex;
                    flex-direction: column;
                    flex: 1;
                }

                .slash-command-title {
                    font-size: 0.875rem;
                    font-weight: 600;
                    color: #111827;
                    line-height: 1.25;
                }

                .slash-commands-menu.dark .slash-command-title {
                    color: #f9fafb;
                }

                .slash-command-shortcut {
                    font-size: 0.75rem;
                    color: #6b7280;
                    margin-top: 0.125rem;
                }

                .slash-commands-menu.dark .slash-command-shortcut {
                    color: #9ca3af;
                }


            `}</style>
        </>
    );
}
