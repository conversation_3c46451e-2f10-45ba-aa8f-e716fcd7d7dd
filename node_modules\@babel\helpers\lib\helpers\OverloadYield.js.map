{"version": 3, "names": ["_OverloadYield", "value", "kind", "v", "k"], "sources": ["../../src/helpers/OverloadYield.js"], "sourcesContent": ["/* @minVersion 7.18.14 */\n\n/*\n * 'kind' is an enum:\n *   0 => This yield was an await expression\n *   1 => This yield comes from yield*\n */\nexport default function _OverloadYield(value, kind) {\n  this.v = value;\n  this.k = kind;\n}\n"], "mappings": ";;;;;;;AAOe,SAASA,cAAT,CAAwBC,KAAxB,EAA+BC,IAA/B,EAAqC;EAClD,KAAKC,CAAL,GAASF,KAAT;EACA,KAAKG,CAAL,GAASF,IAAT;AACD"}