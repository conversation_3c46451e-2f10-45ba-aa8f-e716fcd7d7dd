import NoteState from 'libs/web/state/note';
import { useRouter } from 'next/router';
import {
    use<PERSON><PERSON>back,
    MouseEvent as ReactMouseEvent,
    useState,
    useRef,
    useEffect,
} from 'react';
import { searchNote, searchRangeText } from 'libs/web/utils/search';
import { isNoteLink, NoteModel } from 'libs/shared/note';
import { useToast } from 'libs/web/hooks/use-toast';
import PortalState from 'libs/web/state/portal';
import { NoteCacheItem } from 'libs/web/cache';
import noteCache from 'libs/web/cache/note';
import { createContainer } from 'unstated-next';
import { LexicalEditorRef } from 'components/editor/lexical-editor';
import UIState from 'libs/web/state/ui';
import { has } from 'lodash';


const ROOT_ID = 'root';

const useLexicalEditor = (initNote?: NoteModel) => {
    // Use initNote if provided, otherwise try to get from NoteState
    let note = initNote;
    let createNoteWithTitle: any, updateNote: any, createNote: any;

    try {
        const noteState = NoteState.useContainer();
        createNoteWithTitle = noteState.createNoteWithTitle;
        updateNote = noteState.updateNote;
        createNote = noteState.createNote;
        const fetchNote = noteState.fetchNote; // 🔧 获取 fetchNote 函数

        // Only use noteState.note if no initNote is provided
        if (!note) {
            note = noteState.note;
        }
    } catch (error) {
        // If NoteState is not available, we'll work with just the initNote
        console.warn('NoteState not available in LexicalEditorState, using initNote only');
        createNoteWithTitle = async () => undefined;
        updateNote = async () => undefined;
        createNote = async () => undefined;
    }

    const router = useRouter();
    const toast = useToast();
    const editorEl = useRef<LexicalEditorRef>(null);

    // 🔧 新增：快照状态管理
    const [noteSnapshot, setNoteSnapshot] = useState<string | null>(null);
    const [currentEditorContent, setCurrentEditorContent] = useState<string>('');

    // Manual save function for IndexedDB
    const saveToIndexedDB = useCallback(
        async (data: Partial<NoteModel>) => {
            if (!note?.id) return;

            // 从 IndexedDB 获取最新数据作为基础，避免覆盖已保存的数据
            const existingNote = await noteCache.getItem(note.id);
            const baseNote = existingNote || note;

            const updatedNote = { ...baseNote, ...data };

            await noteCache.setItem(note.id, updatedNote);
        },
        [note]
    );

    const syncToServer = useCallback(
        async () => {
            if (!note?.id) return false;

            const isNew = has(router.query, 'new');

            try {
                const localNote = await noteCache.getItem(note.id);
                const noteToSave = localNote || note;

                if (isNew) {
                    const noteData = {
                        ...noteToSave,
                        pid: (router.query.pid as string) || ROOT_ID
                    };

                    const item = await createNote(noteData);

                    if (item) {
                        const noteUrl = `/${item.id}`;
                        if (router.asPath !== noteUrl) {
                            await router.replace(noteUrl, undefined, { shallow: true });
                        }
                        toast('Note saved to server', 'success');
                        return true;
                    }
                } else {
                    const updatedNote = await updateNote(noteToSave);

                    if (updatedNote) {
                        await noteCache.setItem(updatedNote.id, updatedNote);
                        // 🔧 关键修复：重新加载笔记以更新 NoteState
                        if (fetchNote) {
                            await fetchNote(updatedNote.id);
                        }
                        toast('Note updated on server', 'success');
                        return true;
                    }
                }
            } catch (error) {
                toast('Failed to save note to server', 'error');
                return false;
            }

            return false;
        },
        [note, router, createNote, updateNote, toast]
    );

    const onCreateLink = useCallback(
        async (title: string) => {
            if (!createNoteWithTitle) return '';

            const result = await createNoteWithTitle(title);
            if (result?.id) {
                return `/${result.id}`;
            }
            return '';
        },
        [createNoteWithTitle]
    );

    const onSearchLink = useCallback(
        async (term: string) => {
            return [];
        },
        []
    );

    const onClickLink = useCallback(
        (href: string, event: ReactMouseEvent) => {
            if (isNoteLink(href)) {
                event.preventDefault();
                router.push(href);
            } else {
                window.open(href, '_blank', 'noopener,noreferrer');
            }
        },
        [router]
    );

    const onUploadImage = useCallback(
        async (_file: File, _id?: string) => {
            // Image upload is disabled in PostgreSQL version
            toast('Image upload is not supported in this version', 'error');
            throw new Error('Image upload is not supported');
        },
        [toast]
    );

    const onHoverLink = useCallback((event: ReactMouseEvent) => {
        return true;
    }, []);

    const [backlinks, setBackLinks] = useState<NoteCacheItem[]>();

    const getBackLinks = useCallback(async () => {
        console.log(note?.id);
        const linkNotes: NoteCacheItem[] = [];
        if (!note?.id) return linkNotes;
        setBackLinks([]);
        await noteCache.iterate<NoteCacheItem, void>((value) => {
            if (value.linkIds?.includes(note?.id || '')) {
                linkNotes.push(value);
            }
        });
        setBackLinks(linkNotes);
    }, [note?.id]);

    // 🔧 简化：移除复杂的快照初始化函数，直接在 useEffect 中处理

    // 🔧 笔记切换时重置 currentEditorContent，确保 SaveButton 状态正确
    useEffect(() => {
        if (note?.id) {
            console.log('🔧 笔记切换，重置 currentEditorContent:', {
                noteId: note.id,
                noteContent: note.content?.substring(0, 50) || '(空内容)'
            });
            // 🔧 关键：重置 currentEditorContent 为当前笔记内容
            // 这样 SaveButton 对比时会显示 view 状态
            setCurrentEditorContent(note.content || '');
            setNoteSnapshot(note.content || '');
        } else {
            // 无笔记时清空状态
            setCurrentEditorContent('');
            setNoteSnapshot(null);
        }
    }, [note?.id, note?.content]);

    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作
    const onEditorChange = useCallback(
        (getValue: () => string): void => {
            const jsonContent = getValue();

            // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理
            setCurrentEditorContent(jsonContent);
        },
        []
    );

    // Function to handle title changes specifically
    const onTitleChange = useCallback(
        (title: string): void => {
            saveToIndexedDB({
                title,
                updated_at: new Date().toISOString()
            })?.catch((v) => console.error('Error whilst saving title to IndexedDB: %O', v));
        },
        [saveToIndexedDB]
    );

    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用
    const compareWithSnapshot = useCallback((): boolean => {
        // 如果是新建笔记（快照为null），任何JSON内容都算作变化
        if (noteSnapshot === null) {
            return currentEditorContent.trim() !== '';
        }

        // 已存在笔记：比较当前JSON内容与JSON快照
        const hasChanges = currentEditorContent !== noteSnapshot;
        return hasChanges;
    }, [noteSnapshot, currentEditorContent]);

    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用
    const getEditorState = useCallback(() => {
        return {
            hasChanges: compareWithSnapshot(),
            currentContent: currentEditorContent,
            snapshot: noteSnapshot,
            isNewNote: noteSnapshot === null
        };
    }, [compareWithSnapshot, currentEditorContent, noteSnapshot]);

    // 🔧 新增：清空所有快照的函数
    const clearAllSnapshots = useCallback(() => {
        console.log('🔧 清空所有快照');
        setNoteSnapshot(null);
        setCurrentEditorContent('');

        // 🔧 简化：清空后直接重置为当前笔记内容
        if (note?.id) {
            setCurrentEditorContent(note.content || '');
            setNoteSnapshot(note.content || '');
        }
    }, [note?.id, note?.content]);

    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用
    const saveCurrentContent = useCallback(async (): Promise<boolean> => {
        if (!note?.id) return false;

        try {
            // 获取标题
            let title: string;
            if (note?.isDailyNote) {
                title = note.title;
            } else {
                const titleInput = document.querySelector('h1 textarea') as HTMLTextAreaElement;
                if (titleInput && titleInput.value) {
                    title = titleInput.value.trim();
                } else {
                    // 对于JSON格式，使用现有标题或默认标题
                    title = note?.title || 'Untitled';
                }
            }

            // 保存JSON内容到IndexedDB
            await saveToIndexedDB({
                content: currentEditorContent, // 这里是JSON格式
                title,
                updated_at: new Date().toISOString()
            });

            // 🔧 按照用户要求：保存后清除 currentEditorContent
            // 因为保存后会重新加载缓存，编辑器直接使用 note.content
            setNoteSnapshot(null);
            setCurrentEditorContent('');

            return true;
        } catch (error) {
            console.error('🔧 保存JSON到IndexedDB失败:', error);
            return false;
        }
    }, [note, currentEditorContent, saveToIndexedDB]);

    return {
        onCreateLink,
        onSearchLink,
        onClickLink,
        onUploadImage,
        onHoverLink,
        getBackLinks,
        onEditorChange,
        onTitleChange,
        saveToIndexedDB,
        syncToServer,
        backlinks,
        editorEl,
        // 🔧 让编辑器直接使用 note.content
        note,
        // 🔧 暴露 currentEditorContent 给 SaveButton 用于对比
        currentEditorContent,
    };
};

const LexicalEditorState = createContainer(useLexicalEditor);

export default LexicalEditorState;
