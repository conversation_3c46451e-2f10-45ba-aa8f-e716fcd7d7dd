import NoteState from 'libs/web/state/note';
import { useRouter } from 'next/router';
import {
    use<PERSON><PERSON>back,
    MouseEvent as ReactMouseEvent,
    useState,
    useRef,
    useEffect,
} from 'react';
import { searchNote, searchRangeText } from 'libs/web/utils/search';
import { isNoteLink, NoteModel } from 'libs/shared/note';
import { useToast } from 'libs/web/hooks/use-toast';
import PortalState from 'libs/web/state/portal';
import { NoteCacheItem } from 'libs/web/cache';
import noteCache from 'libs/web/cache/note';
import { createContainer } from 'unstated-next';
import { LexicalEditorRef } from 'components/editor/lexical-editor';
import UIState from 'libs/web/state/ui';
import { has } from 'lodash';


const ROOT_ID = 'root';

const useLexicalEditor = (initNote?: NoteModel) => {
    // Use initNote if provided, otherwise try to get from NoteState
    let note = initNote;
    let createNoteWithTitle: any, updateNote: any, createNote: any;

    try {
        const noteState = NoteState.useContainer();
        createNoteWithTitle = noteState.createNoteWithTitle;
        updateNote = noteState.updateNote;
        createNote = noteState.createNote;

        // Only use noteState.note if no initNote is provided
        if (!note) {
            note = noteState.note;
        }
    } catch (error) {
        // If NoteState is not available, we'll work with just the initNote
        console.warn('NoteState not available in LexicalEditorState, using initNote only');
        createNoteWithTitle = async () => undefined;
        updateNote = async () => undefined;
        createNote = async () => undefined;
    }

    const router = useRouter();
    const toast = useToast();
    const editorEl = useRef<LexicalEditorRef>(null);

    // 🔧 新增：快照状态管理
    const [noteSnapshot, setNoteSnapshot] = useState<string | null>(null);
    const [currentEditorContent, setCurrentEditorContent] = useState<string>('');

    // Manual save function for IndexedDB
    const saveToIndexedDB = useCallback(
        async (data: Partial<NoteModel>) => {
            if (!note?.id) return;

            // 从 IndexedDB 获取最新数据作为基础，避免覆盖已保存的数据
            const existingNote = await noteCache.getItem(note.id);
            const baseNote = existingNote || note;

            const updatedNote = { ...baseNote, ...data };

            await noteCache.setItem(note.id, updatedNote);
        },
        [note]
    );

    const syncToServer = useCallback(
        async () => {
            if (!note?.id) return false;

            const isNew = has(router.query, 'new');

            try {
                const localNote = await noteCache.getItem(note.id);
                const noteToSave = localNote || note;

                if (isNew) {
                    const noteData = {
                        ...noteToSave,
                        pid: (router.query.pid as string) || ROOT_ID
                    };

                    const item = await createNote(noteData);

                    if (item) {
                        const noteUrl = `/${item.id}`;
                        if (router.asPath !== noteUrl) {
                            await router.replace(noteUrl, undefined, { shallow: true });
                        }
                        toast('Note saved to server', 'success');
                        return true;
                    }
                } else {
                    const updatedNote = await updateNote(noteToSave);

                    if (updatedNote) {
                        await noteCache.setItem(updatedNote.id, updatedNote);
                        toast('Note updated on server', 'success');
                        return true;
                    }
                }
            } catch (error) {
                toast('Failed to save note to server', 'error');
                return false;
            }

            return false;
        },
        [note, router, createNote, updateNote, toast]
    );

    const onCreateLink = useCallback(
        async (title: string) => {
            if (!createNoteWithTitle) return '';

            const result = await createNoteWithTitle(title);
            if (result?.id) {
                return `/${result.id}`;
            }
            return '';
        },
        [createNoteWithTitle]
    );

    const onSearchLink = useCallback(
        async (term: string) => {
            return [];
        },
        []
    );

    const onClickLink = useCallback(
        (href: string, event: ReactMouseEvent) => {
            if (isNoteLink(href)) {
                event.preventDefault();
                router.push(href);
            } else {
                window.open(href, '_blank', 'noopener,noreferrer');
            }
        },
        [router]
    );

    const onUploadImage = useCallback(
        async (_file: File, _id?: string) => {
            // Image upload is disabled in PostgreSQL version
            toast('Image upload is not supported in this version', 'error');
            throw new Error('Image upload is not supported');
        },
        [toast]
    );

    const onHoverLink = useCallback((event: ReactMouseEvent) => {
        return true;
    }, []);

    const [backlinks, setBackLinks] = useState<NoteCacheItem[]>();

    const getBackLinks = useCallback(async () => {
        console.log(note?.id);
        const linkNotes: NoteCacheItem[] = [];
        if (!note?.id) return linkNotes;
        setBackLinks([]);
        await noteCache.iterate<NoteCacheItem, void>((value) => {
            if (value.linkIds?.includes(note?.id || '')) {
                linkNotes.push(value);
            }
        });
        setBackLinks(linkNotes);
    }, [note?.id]);

    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照
    const initializeSnapshot = useCallback(async () => {
        console.log('🔧 开始初始化快照:', {
            noteId: note?.id,
            noteContent: note?.content?.substring(0, 100) + '...',
            noteContentLength: note?.content?.length || 0
        });

        if (!note?.id) {
            // 新建笔记：快照为空值
            console.log('🔧 新建笔记，设置空快照');
            setNoteSnapshot(null);
            setCurrentEditorContent('');
            return;
        }

        try {
            // 🔧 关键修复：每次切换笔记时，重新获取该笔记的缓存内容
            const cachedNote = await noteCache.getItem(note.id);
            let snapshotJsonContent = '';

            console.log('🔧 缓存检查结果:', {
                hasCachedNote: !!cachedNote,
                cachedContent: cachedNote?.content?.substring(0, 100) + '...',
                cachedContentLength: cachedNote?.content?.length || 0,
                noteContent: note.content?.substring(0, 100) + '...',
                noteContentLength: note.content?.length || 0
            });

            // 优先使用缓存中的内容
            if (cachedNote?.content) {
                snapshotJsonContent = cachedNote.content;
                console.log('🔧 使用缓存内容作为快照');
            }
            // 如果缓存为空，使用 note 对象的内容
            else if (note.content) {
                snapshotJsonContent = note.content;
                console.log('🔧 缓存为空，使用 note 对象内容作为快照');
            }
            // 如果都为空，设置空快照
            else {
                snapshotJsonContent = '';
                console.log('🔧 无内容，设置空快照');
            }

            console.log('🔧 快照初始化完成:', {
                noteId: note.id,
                hasContent: !!snapshotJsonContent,
                contentLength: snapshotJsonContent.length,
                isJson: snapshotJsonContent.startsWith('{'),
                source: cachedNote?.content ? 'cache' : (note.content ? 'note' : 'empty'),
                contentPreview: snapshotJsonContent.substring(0, 100) + '...'
            });

            // 🔧 关键：设置快照和当前编辑器内容
            setNoteSnapshot(snapshotJsonContent);
            setCurrentEditorContent(snapshotJsonContent);
        } catch (error) {
            console.error('JSON快照初始化失败:', error);
            // 失败时设置为空快照
            setNoteSnapshot(null);
            setCurrentEditorContent('');
        }
    }, [note?.id, note?.content]);

    // 当笔记ID变化或内容加载完成时初始化快照
    useEffect(() => {
        // 只有当 note 存在且有 ID 时才初始化
        if (note?.id) {
            initializeSnapshot();
        }
    }, [note?.id, note?.content, initializeSnapshot]);

    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作
    const onEditorChange = useCallback(
        (getValue: () => string): void => {
            const jsonContent = getValue();

            // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理
            setCurrentEditorContent(jsonContent);
        },
        []
    );

    // Function to handle title changes specifically
    const onTitleChange = useCallback(
        (title: string): void => {
            saveToIndexedDB({
                title,
                updated_at: new Date().toISOString()
            })?.catch((v) => console.error('Error whilst saving title to IndexedDB: %O', v));
        },
        [saveToIndexedDB]
    );

    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用
    const compareWithSnapshot = useCallback((): boolean => {
        // 如果是新建笔记（快照为null），任何JSON内容都算作变化
        if (noteSnapshot === null) {
            return currentEditorContent.trim() !== '';
        }

        // 已存在笔记：比较当前JSON内容与JSON快照
        const hasChanges = currentEditorContent !== noteSnapshot;
        return hasChanges;
    }, [noteSnapshot, currentEditorContent]);

    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用
    const getEditorState = useCallback(() => {
        return {
            hasChanges: compareWithSnapshot(),
            currentContent: currentEditorContent,
            snapshot: noteSnapshot,
            isNewNote: noteSnapshot === null
        };
    }, [compareWithSnapshot, currentEditorContent, noteSnapshot]);

    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用
    const saveCurrentContent = useCallback(async (): Promise<boolean> => {
        if (!note?.id) return false;

        try {
            // 获取标题
            let title: string;
            if (note?.isDailyNote) {
                title = note.title;
            } else {
                const titleInput = document.querySelector('h1 textarea') as HTMLTextAreaElement;
                if (titleInput && titleInput.value) {
                    title = titleInput.value.trim();
                } else {
                    // 对于JSON格式，使用现有标题或默认标题
                    title = note?.title || 'Untitled';
                }
            }

            // 保存JSON内容到IndexedDB
            await saveToIndexedDB({
                content: currentEditorContent, // 这里是JSON格式
                title,
                updated_at: new Date().toISOString()
            });

            // 🔧 关键修复：保存成功后更新快照为当前内容，使状态变为一致（view状态）
            setNoteSnapshot(currentEditorContent);

            return true;
        } catch (error) {
            console.error('🔧 保存JSON到IndexedDB失败:', error);
            return false;
        }
    }, [note, currentEditorContent, saveToIndexedDB]);

    return {
        onCreateLink,
        onSearchLink,
        onClickLink,
        onUploadImage,
        onHoverLink,
        getBackLinks,
        onEditorChange,
        onTitleChange,
        saveToIndexedDB,
        syncToServer,
        backlinks,
        editorEl,
        note,
        // 🔧 新增：快照对比相关功能
        getEditorState,
        saveCurrentContent,
        compareWithSnapshot,
        // 🔧 关键修复：返回当前编辑器内容作为编辑器的 value
        editorNote: {
            ...note,
            content: currentEditorContent
        },
    };
};

const LexicalEditorState = createContainer(useLexicalEditor);

export default LexicalEditorState;
