{"version": 3, "names": ["BABEL_POLYFILL_DEPRECATION", "NO_DIRECT_POLYFILL_IMPORT", "template", "regenerator", "deprecated", "usage", "name", "visitor", "ImportDeclaration", "path", "src", "getImportSource", "isPolyfillSource", "console", "warn", "replace", "remove", "replaceWithMultiple", "ast", "replaceWith", "Program", "get", "for<PERSON>ach", "bodyPath", "getRequireSource"], "sources": ["../../src/polyfills/babel-polyfill.ts"], "sourcesContent": ["import { getImportSource, getRequireSource, isPolyfillSource } from \"./utils\";\n\nimport type { NodePath } from \"@babel/traverse\";\nimport type * as t from \"@babel/types\";\n\nconst BABEL_POLYFILL_DEPRECATION = `\n  \\`@babel/polyfill\\` is deprecated. Please, use required parts of \\`core-js\\`\n  and \\`regenerator-runtime/runtime\\` separately`;\n\nconst NO_DIRECT_POLYFILL_IMPORT = `\n  When setting \\`useBuiltIns: 'usage'\\`, polyfills are automatically imported when needed.\n  Please remove the direct import of \\`SPECIFIER\\` or use \\`useBuiltIns: 'entry'\\` instead.`;\n\nexport default function (\n  { template }: any,\n  { regenerator, deprecated, usage }: any,\n) {\n  return {\n    name: \"preset-env/replace-babel-polyfill\",\n    visitor: {\n      ImportDeclaration(path: NodePath<t.ImportDeclaration>) {\n        const src = getImportSource(path);\n        if (usage && isPolyfillSource(src)) {\n          console.warn(NO_DIRECT_POLYFILL_IMPORT.replace(\"SPECIFIER\", src));\n          if (!deprecated) path.remove();\n        } else if (src === \"@babel/polyfill\") {\n          if (deprecated) {\n            console.warn(BABEL_POLYFILL_DEPRECATION);\n          } else if (regenerator) {\n            path.replaceWithMultiple(template.ast`\n              import \"core-js\";\n              import \"regenerator-runtime/runtime.js\";\n            `);\n          } else {\n            path.replaceWith(template.ast`\n              import \"core-js\";\n            `);\n          }\n        }\n      },\n      Program(path: NodePath<t.Program>) {\n        path.get(\"body\").forEach(bodyPath => {\n          const src = getRequireSource(bodyPath);\n          if (usage && isPolyfillSource(src)) {\n            console.warn(NO_DIRECT_POLYFILL_IMPORT.replace(\"SPECIFIER\", src));\n            if (!deprecated) bodyPath.remove();\n          } else if (src === \"@babel/polyfill\") {\n            if (deprecated) {\n              console.warn(BABEL_POLYFILL_DEPRECATION);\n            } else if (regenerator) {\n              bodyPath.replaceWithMultiple(template.ast`\n                require(\"core-js\");\n                require(\"regenerator-runtime/runtime.js\");\n              `);\n            } else {\n              bodyPath.replaceWith(template.ast`\n                require(\"core-js\");\n              `);\n            }\n          }\n        });\n      },\n    },\n  };\n}\n"], "mappings": ";;;;;;;AAAA;;AAKA,MAAMA,0BAA0B,GAAI;AACpC;AACA,iDAFA;AAIA,MAAMC,yBAAyB,GAAI;AACnC;AACA,4FAFA;;AAIe,kBACb;EAAEC;AAAF,CADa,EAEb;EAAEC,WAAF;EAAeC,UAAf;EAA2BC;AAA3B,CAFa,EAGb;EACA,OAAO;IACLC,IAAI,EAAE,mCADD;IAELC,OAAO,EAAE;MACPC,iBAAiB,CAACC,IAAD,EAAsC;QACrD,MAAMC,GAAG,GAAG,IAAAC,sBAAA,EAAgBF,IAAhB,CAAZ;;QACA,IAAIJ,KAAK,IAAI,IAAAO,uBAAA,EAAiBF,GAAjB,CAAb,EAAoC;UAClCG,OAAO,CAACC,IAAR,CAAab,yBAAyB,CAACc,OAA1B,CAAkC,WAAlC,EAA+CL,GAA/C,CAAb;UACA,IAAI,CAACN,UAAL,EAAiBK,IAAI,CAACO,MAAL;QAClB,CAHD,MAGO,IAAIN,GAAG,KAAK,iBAAZ,EAA+B;UACpC,IAAIN,UAAJ,EAAgB;YACdS,OAAO,CAACC,IAAR,CAAad,0BAAb;UACD,CAFD,MAEO,IAAIG,WAAJ,EAAiB;YACtBM,IAAI,CAACQ,mBAAL,CAAyBf,QAAQ,CAACgB,GAAI;AAClD;AACA;AACA,aAHY;UAID,CALM,MAKA;YACLT,IAAI,CAACU,WAAL,CAAiBjB,QAAQ,CAACgB,GAAI;AAC1C;AACA,aAFY;UAGD;QACF;MACF,CApBM;;MAqBPE,OAAO,CAACX,IAAD,EAA4B;QACjCA,IAAI,CAACY,GAAL,CAAS,MAAT,EAAiBC,OAAjB,CAAyBC,QAAQ,IAAI;UACnC,MAAMb,GAAG,GAAG,IAAAc,uBAAA,EAAiBD,QAAjB,CAAZ;;UACA,IAAIlB,KAAK,IAAI,IAAAO,uBAAA,EAAiBF,GAAjB,CAAb,EAAoC;YAClCG,OAAO,CAACC,IAAR,CAAab,yBAAyB,CAACc,OAA1B,CAAkC,WAAlC,EAA+CL,GAA/C,CAAb;YACA,IAAI,CAACN,UAAL,EAAiBmB,QAAQ,CAACP,MAAT;UAClB,CAHD,MAGO,IAAIN,GAAG,KAAK,iBAAZ,EAA+B;YACpC,IAAIN,UAAJ,EAAgB;cACdS,OAAO,CAACC,IAAR,CAAad,0BAAb;YACD,CAFD,MAEO,IAAIG,WAAJ,EAAiB;cACtBoB,QAAQ,CAACN,mBAAT,CAA6Bf,QAAQ,CAACgB,GAAI;AACxD;AACA;AACA,eAHc;YAID,CALM,MAKA;cACLK,QAAQ,CAACJ,WAAT,CAAqBjB,QAAQ,CAACgB,GAAI;AAChD;AACA,eAFc;YAGD;UACF;QACF,CAnBD;MAoBD;;IA1CM;EAFJ,CAAP;AA+CD"}