/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./libs/shared/const.ts":
/*!******************************!*\
  !*** ./libs/shared/const.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"CSRF_HEADER_KEY\": () => (/* binding */ CSRF_HEADER_KEY),\n/* harmony export */   \"DEMO_INJECTION\": () => (/* binding */ DEMO_INJECTION),\n/* harmony export */   \"IMPORT_FILE_LIMIT_SIZE\": () => (/* binding */ IMPORT_FILE_LIMIT_SIZE)\n/* harmony export */ });\nconst CSRF_HEADER_KEY = \"xsrf-token\";\nconst IMPORT_FILE_LIMIT_SIZE = 5 * 1024 * 1024;\nconst DEMO_INJECTION = `<div id=\"cusdis_thread\"\n  data-host=\"https://cusdis.com\"\n  data-app-id=\"61cfba44-ef71-4aa1-aa9b-58632fff9929\"\n  data-page-id=\"{id}\"\n  data-page-url=\"{url}\"\n  data-page-title=\"{title}\"\n></div>\n<script async defer src=\"https://cusdis.com/js/cusdis.es.js\"></script>`;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3NoYXJlZC9jb25zdC50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTyxNQUFNQSxlQUFlLEdBQUcsWUFBWSxDQUFDO0FBRXJDLE1BQU1DLHNCQUFzQixHQUFHLENBQUMsR0FBRyxJQUFJLEdBQUcsSUFBSSxDQUFDO0FBRS9DLE1BQU1DLGNBQWMsR0FBRyxDQUFDOzs7Ozs7O3NFQU91QyxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvc2hhcmVkL2NvbnN0LnRzP2ZlNTEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IENTUkZfSEVBREVSX0tFWSA9ICd4c3JmLXRva2VuJztcblxuZXhwb3J0IGNvbnN0IElNUE9SVF9GSUxFX0xJTUlUX1NJWkUgPSA1ICogMTAyNCAqIDEwMjQ7XG5cbmV4cG9ydCBjb25zdCBERU1PX0lOSkVDVElPTiA9IGA8ZGl2IGlkPVwiY3VzZGlzX3RocmVhZFwiXG4gIGRhdGEtaG9zdD1cImh0dHBzOi8vY3VzZGlzLmNvbVwiXG4gIGRhdGEtYXBwLWlkPVwiNjFjZmJhNDQtZWY3MS00YWExLWFhOWItNTg2MzJmZmY5OTI5XCJcbiAgZGF0YS1wYWdlLWlkPVwie2lkfVwiXG4gIGRhdGEtcGFnZS11cmw9XCJ7dXJsfVwiXG4gIGRhdGEtcGFnZS10aXRsZT1cInt0aXRsZX1cIlxuPjwvZGl2PlxuPHNjcmlwdCBhc3luYyBkZWZlciBzcmM9XCJodHRwczovL2N1c2Rpcy5jb20vanMvY3VzZGlzLmVzLmpzXCI+PC9zY3JpcHQ+YDtcbiJdLCJuYW1lcyI6WyJDU1JGX0hFQURFUl9LRVkiLCJJTVBPUlRfRklMRV9MSU1JVF9TSVpFIiwiREVNT19JTkpFQ1RJT04iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/shared/const.ts\n");

/***/ }),

/***/ "./libs/shared/meta.ts":
/*!*****************************!*\
  !*** ./libs/shared/meta.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"EDITOR_SIZE\": () => (/* binding */ EDITOR_SIZE),\n/* harmony export */   \"NOTE_DELETED\": () => (/* binding */ NOTE_DELETED),\n/* harmony export */   \"NOTE_PINNED\": () => (/* binding */ NOTE_PINNED),\n/* harmony export */   \"NOTE_SHARED\": () => (/* binding */ NOTE_SHARED),\n/* harmony export */   \"NUMBER_KEYS\": () => (/* binding */ NUMBER_KEYS),\n/* harmony export */   \"PAGE_META_KEY\": () => (/* binding */ PAGE_META_KEY)\n/* harmony export */ });\nvar NOTE_DELETED;\n(function(NOTE_DELETED) {\n    NOTE_DELETED[NOTE_DELETED[\"NORMAL\"] = 0] = \"NORMAL\";\n    NOTE_DELETED[NOTE_DELETED[\"DELETED\"] = 1] = \"DELETED\";\n})(NOTE_DELETED || (NOTE_DELETED = {}));\nvar NOTE_SHARED;\n(function(NOTE_SHARED) {\n    NOTE_SHARED[NOTE_SHARED[\"PRIVATE\"] = 0] = \"PRIVATE\";\n    NOTE_SHARED[NOTE_SHARED[\"PUBLIC\"] = 1] = \"PUBLIC\";\n})(NOTE_SHARED || (NOTE_SHARED = {}));\nvar NOTE_PINNED;\n(function(NOTE_PINNED) {\n    NOTE_PINNED[NOTE_PINNED[\"UNPINNED\"] = 0] = \"UNPINNED\";\n    NOTE_PINNED[NOTE_PINNED[\"PINNED\"] = 1] = \"PINNED\";\n})(NOTE_PINNED || (NOTE_PINNED = {}));\nvar EDITOR_SIZE;\n(function(EDITOR_SIZE) {\n    EDITOR_SIZE[EDITOR_SIZE[\"SMALL\"] = 0] = \"SMALL\";\n    EDITOR_SIZE[EDITOR_SIZE[\"LARGE\"] = 1] = \"LARGE\";\n    EDITOR_SIZE[EDITOR_SIZE[\"FULL\"] = 2] = \"FULL\";\n})(EDITOR_SIZE || (EDITOR_SIZE = {}));\nconst PAGE_META_KEY = [\n    \"title\",\n    \"pid\",\n    \"id\",\n    \"shared\",\n    \"pic\",\n    \"date\",\n    \"deleted\",\n    \"pinned\",\n    \"editorsize\", \n];\nconst NUMBER_KEYS = [\n    \"deleted\",\n    \"shared\",\n    \"pinned\",\n    \"editorsize\", \n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/meta.ts\n");

/***/ }),

/***/ "./libs/shared/settings.ts":
/*!*********************************!*\
  !*** ./libs/shared/settings.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"DEFAULT_SETTINGS\": () => (/* binding */ DEFAULT_SETTINGS),\n/* harmony export */   \"formatSettings\": () => (/* binding */ formatSettings)\n/* harmony export */ });\n/* harmony import */ var locales__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! locales */ \"./locales/index.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _meta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./meta */ \"./libs/shared/meta.ts\");\n/* harmony import */ var _tree__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tree */ \"./libs/shared/tree.ts\");\n\n\n\n\nconst DEFAULT_SETTINGS = Object.freeze({\n    split_sizes: [\n        30,\n        70\n    ],\n    daily_root_id: _tree__WEBPACK_IMPORTED_MODULE_3__.ROOT_ID,\n    sidebar_is_fold: false,\n    locale: locales__WEBPACK_IMPORTED_MODULE_0__.Locale.EN,\n    editorsize: _meta__WEBPACK_IMPORTED_MODULE_2__.EDITOR_SIZE.SMALL\n});\nfunction formatSettings(body = {}) {\n    const settings = {\n        ...DEFAULT_SETTINGS\n    };\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isString)(body.daily_root_id)) {\n        settings.daily_root_id = body.daily_root_id;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isArray)(body.split_sizes) && (0,lodash__WEBPACK_IMPORTED_MODULE_1__.isNumber)(body.split_sizes[0]) && (0,lodash__WEBPACK_IMPORTED_MODULE_1__.isNumber)(body.split_sizes[1])) {\n        // Sometimes when debugging mode is turned on in the browser,\n        // the size will become abnormal\n        const [size1, size2] = body.split_sizes;\n        if (size1 > 100 || size1 < 0 || size2 > 100 || size2 < 0) {\n            settings.split_sizes = DEFAULT_SETTINGS.split_sizes;\n        } else {\n            settings.split_sizes = [\n                size1,\n                size2\n            ];\n        }\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isBoolean)(body.sidebar_is_fold)) {\n        settings.sidebar_is_fold = body.sidebar_is_fold;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isString)(body.last_visit)) {\n        settings.last_visit = body.last_visit;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.values)(locales__WEBPACK_IMPORTED_MODULE_0__.Locale).includes(body.locale)) {\n        settings.locale = body.locale;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isString)(body.injection)) {\n        settings.injection = body.injection;\n    }\n    if ((0,lodash__WEBPACK_IMPORTED_MODULE_1__.isNumber)(body.editorsize)) {\n        settings.editorsize = body.editorsize;\n    }\n    return settings;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/settings.ts\n");

/***/ }),

/***/ "./libs/shared/tree.ts":
/*!*****************************!*\
  !*** ./libs/shared/tree.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"DEFAULT_TREE\": () => (/* binding */ DEFAULT_TREE),\n/* harmony export */   \"ROOT_ID\": () => (/* binding */ ROOT_ID),\n/* harmony export */   \"cleanItemModel\": () => (/* binding */ cleanItemModel),\n/* harmony export */   \"cleanTreeModel\": () => (/* binding */ cleanTreeModel),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   \"makeHierarchy\": () => (/* binding */ makeHierarchy)\n/* harmony export */ });\n/* harmony import */ var _atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @atlaskit/tree */ \"@atlaskit/tree\");\n/* harmony import */ var _atlaskit_tree__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ROOT_ID = \"root\";\nconst DEFAULT_TREE = {\n    rootId: ROOT_ID,\n    items: {\n        root: {\n            id: ROOT_ID,\n            children: []\n        }\n    }\n};\nfunction addItem(tree, id, pid = ROOT_ID) {\n    const newTree = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n    newTree.items[id] = newTree.items[id] || {\n        id,\n        children: []\n    };\n    const parentItem = newTree.items[pid];\n    if (parentItem) {\n        if (!parentItem.children.includes(id)) {\n            parentItem.children = [\n                ...parentItem.children,\n                id\n            ];\n        }\n    } else {\n        throw new Error(`Parent ID '${pid}' does not refer to a valid item`);\n    }\n    return newTree;\n}\nfunction mutateItem(tree, id, data) {\n    if (data.data) {\n        data.data = {\n            ...tree.items[id]?.data,\n            ...data.data\n        };\n    }\n    return (0,_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__.mutateTree)(tree, id, data);\n}\nfunction removeItem(tree, id) {\n    (0,lodash__WEBPACK_IMPORTED_MODULE_1__.forEach)(tree.items, (item)=>{\n        if (item.children.includes(id)) {\n            (0,lodash__WEBPACK_IMPORTED_MODULE_1__.pull)(item.children, id);\n            return false;\n        }\n    });\n    return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n}\nfunction moveItem(tree, source, destination) {\n    if (!destination) {\n        return tree;\n    }\n    return (0,_atlaskit_tree__WEBPACK_IMPORTED_MODULE_0__.moveItemOnTree)(tree, source, destination);\n}\n/**\n * 从原父节点上移除，添加到新的父节点上\n */ function restoreItem(tree, id, pid = ROOT_ID) {\n    tree = removeItem(tree, id);\n    tree = addItem(tree, id, pid);\n    return tree;\n}\nfunction deleteItem(tree, id) {\n    tree = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.cloneDeep)(tree);\n    delete tree.items[id];\n    return tree;\n}\nconst flattenTree = (tree, rootId = tree.rootId)=>{\n    if (!tree.items[rootId]) {\n        return [];\n    }\n    return (0,lodash__WEBPACK_IMPORTED_MODULE_1__.reduce)(tree.items[rootId].children, (accum, itemId)=>{\n        const item = tree.items[itemId];\n        const children = flattenTree({\n            rootId: item.id,\n            items: tree.items\n        });\n        return [\n            ...accum,\n            item,\n            ...children\n        ];\n    }, []);\n};\nfunction makeHierarchy(tree, rootId = tree.rootId) {\n    if (!tree.items[rootId]) {\n        return false;\n    }\n    const root = tree.items[rootId];\n    return {\n        ...root,\n        children: root.children.map((v)=>makeHierarchy(tree, v)).filter((v)=>!!v)\n    };\n}\nfunction cleanItemModel(model) {\n    if (!model.id) throw new Error(\"Missing id on tree model\");\n    const children = model.children ?? [];\n    return {\n        ...model,\n        id: model.id,\n        children,\n        hasChildren: children.length > 0,\n        data: model.data,\n        isExpanded: model.isExpanded ?? false\n    };\n}\nfunction cleanTreeModel(model) {\n    const items = {};\n    if (model.items) {\n        for(const itemId in model.items){\n            const item = model.items[itemId];\n            if (!item) {\n                continue;\n            }\n            const cleanedItem = cleanItemModel(item);\n            const children = [];\n            for (const child of cleanedItem.children){\n                if (child && model.items[child]) {\n                    children.push(child);\n                }\n            }\n            items[itemId] = {\n                ...cleanedItem,\n                children\n            };\n        }\n    }\n    return {\n        ...model,\n        rootId: model.rootId ?? ROOT_ID,\n        items: items\n    };\n}\nconst TreeActions = {\n    addItem,\n    mutateItem,\n    removeItem,\n    moveItem,\n    restoreItem,\n    deleteItem,\n    flattenTree,\n    makeHierarchy,\n    cleanTreeModel,\n    cleanItemModel\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TreeActions);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/shared/tree.ts\n");

/***/ }),

/***/ "./libs/web/api/fetcher.ts":
/*!*********************************!*\
  !*** ./libs/web/api/fetcher.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useFetcher)\n/* harmony export */ });\n/* harmony import */ var libs_shared_const__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/shared/const */ \"./libs/shared/const.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _state_csrf_token__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../state/csrf-token */ \"./libs/web/state/csrf-token.ts\");\n\n\n\nfunction useFetcher() {\n    const { 0: loading , 1: setLoading  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { 0: error , 1: setError  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const abortRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    const csrfToken = _state_csrf_token__WEBPACK_IMPORTED_MODULE_2__[\"default\"].useContainer();\n    const request = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async function request(params, payload) {\n        const controller = new AbortController();\n        setLoading(true);\n        setError(\"\");\n        abortRef.current = controller;\n        const init = {\n            signal: controller.signal,\n            method: params.method\n        };\n        init.headers = {\n            ...csrfToken && {\n                [libs_shared_const__WEBPACK_IMPORTED_MODULE_0__.CSRF_HEADER_KEY]: csrfToken\n            }\n        };\n        if (payload instanceof FormData) {\n            init.body = payload;\n        } else {\n            init.body = JSON.stringify(payload);\n            init.headers[\"Content-Type\"] = \"application/json\";\n        }\n        init.headers = {\n            ...init.headers,\n            ...params.headers || {}\n        };\n        try {\n            const response = await fetch(params.url, init);\n            if (!response.ok) {\n                throw await response.text();\n            }\n            if (response.status === 204) {\n                return;\n            }\n            return response.json();\n        } catch (e) {\n            if (!controller?.signal.aborted) {\n                setError(String(e));\n            }\n        } finally{\n            setLoading(false);\n        }\n    }, [\n        csrfToken\n    ]);\n    const abort = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        abortRef.current?.abort();\n    }, []);\n    return {\n        loading,\n        request,\n        abort,\n        error\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/api/fetcher.ts\n");

/***/ }),

/***/ "./libs/web/api/settings.ts":
/*!**********************************!*\
  !*** ./libs/web/api/settings.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSettingsAPI)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _fetcher__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./fetcher */ \"./libs/web/api/fetcher.ts\");\n\n\nfunction useSettingsAPI() {\n    const { request  } = (0,_fetcher__WEBPACK_IMPORTED_MODULE_1__[\"default\"])();\n    const mutate = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (body)=>{\n        return request({\n            method: \"POST\",\n            url: `/api/settings`\n        }, body);\n    }, [\n        request\n    ]);\n    return {\n        mutate\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9hcGkvc2V0dGluZ3MudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFvQztBQUNEO0FBR3BCLFNBQVNFLGNBQWMsR0FBRztJQUNyQyxNQUFNLEVBQUVDLE9BQU8sR0FBRSxHQUFHRixvREFBVSxFQUFFO0lBRWhDLE1BQU1HLE1BQU0sR0FBR0osa0RBQVcsQ0FDdEIsT0FBT0ssSUFBdUIsR0FBSztRQUMvQixPQUFPRixPQUFPLENBQ1Y7WUFDSUcsTUFBTSxFQUFFLE1BQU07WUFDZEMsR0FBRyxFQUFFLENBQUMsYUFBYSxDQUFDO1NBQ3ZCLEVBQ0RGLElBQUksQ0FDUCxDQUFDO0lBQ04sQ0FBQyxFQUNEO1FBQUNGLE9BQU87S0FBQyxDQUNaO0lBRUQsT0FBTztRQUNIQyxNQUFNO0tBQ1QsQ0FBQztBQUNOLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvd2ViL2FwaS9zZXR0aW5ncy50cz9mNzIzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHVzZUZldGNoZXIgZnJvbSAnLi9mZXRjaGVyJztcbmltcG9ydCB7IFNldHRpbmdzIH0gZnJvbSAnbGlicy9zaGFyZWQvc2V0dGluZ3MnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VTZXR0aW5nc0FQSSgpIHtcbiAgICBjb25zdCB7IHJlcXVlc3QgfSA9IHVzZUZldGNoZXIoKTtcblxuICAgIGNvbnN0IG11dGF0ZSA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAoYm9keTogUGFydGlhbDxTZXR0aW5ncz4pID0+IHtcbiAgICAgICAgICAgIHJldHVybiByZXF1ZXN0PFBhcnRpYWw8U2V0dGluZ3M+LCBTZXR0aW5ncz4oXG4gICAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgICAgICAgICAgICAgdXJsOiBgL2FwaS9zZXR0aW5nc2AsXG4gICAgICAgICAgICAgICAgfSxcbiAgICAgICAgICAgICAgICBib2R5XG4gICAgICAgICAgICApO1xuICAgICAgICB9LFxuICAgICAgICBbcmVxdWVzdF1cbiAgICApO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgICAgbXV0YXRlLFxuICAgIH07XG59XG4iXSwibmFtZXMiOlsidXNlQ2FsbGJhY2siLCJ1c2VGZXRjaGVyIiwidXNlU2V0dGluZ3NBUEkiLCJyZXF1ZXN0IiwibXV0YXRlIiwiYm9keSIsIm1ldGhvZCIsInVybCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./libs/web/api/settings.ts\n");

/***/ }),

/***/ "./libs/web/state/csrf-token.ts":
/*!**************************************!*\
  !*** ./libs/web/state/csrf-token.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unstated-next */ \"unstated-next\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(unstated_next__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useCsrfToken(token) {\n    return token;\n}\nconst CsrfTokenState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_0__.createContainer)(useCsrfToken);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CsrfTokenState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS9jc3JmLXRva2VuLnRzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFnRDtBQUVoRCxTQUFTQyxZQUFZLENBQUNDLEtBQWMsRUFBRTtJQUNsQyxPQUFPQSxLQUFLLENBQUM7QUFDakIsQ0FBQztBQUVELE1BQU1DLGNBQWMsR0FBR0gsOERBQWUsQ0FBQ0MsWUFBWSxDQUFDO0FBRXBELGlFQUFlRSxjQUFjLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9ub3RlYS8uL2xpYnMvd2ViL3N0YXRlL2NzcmYtdG9rZW4udHM/N2RmNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBjcmVhdGVDb250YWluZXIgfSBmcm9tICd1bnN0YXRlZC1uZXh0JztcblxuZnVuY3Rpb24gdXNlQ3NyZlRva2VuKHRva2VuPzogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRva2VuO1xufVxuXG5jb25zdCBDc3JmVG9rZW5TdGF0ZSA9IGNyZWF0ZUNvbnRhaW5lcih1c2VDc3JmVG9rZW4pO1xuXG5leHBvcnQgZGVmYXVsdCBDc3JmVG9rZW5TdGF0ZTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250YWluZXIiLCJ1c2VDc3JmVG9rZW4iLCJ0b2tlbiIsIkNzcmZUb2tlblN0YXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/web/state/csrf-token.ts\n");

/***/ }),

/***/ "./libs/web/state/portal.ts":
/*!**********************************!*\
  !*** ./libs/web/state/portal.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unstated-next */ \"unstated-next\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(unstated_next__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst useModalInstance = ()=>{\n    const { 0: visible , 1: setVisible  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const open = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setVisible(true);\n    }, []);\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return {\n        visible,\n        open,\n        close\n    };\n};\nconst useAnchorInstance = ()=>{\n    const { 0: anchor , 1: setAnchor  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const { 0: data , 1: setData  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const { 0: visible , 1: setVisible  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const open = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setVisible(true);\n    }, []);\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setVisible(false);\n    }, []);\n    return {\n        anchor,\n        open,\n        close,\n        data,\n        setData,\n        visible,\n        setAnchor\n    };\n};\nconst useModal = ()=>{\n    return {\n        search: useModalInstance(),\n        trash: useModalInstance(),\n        menu: useAnchorInstance(),\n        share: useAnchorInstance(),\n        preview: useAnchorInstance(),\n        linkToolbar: useAnchorInstance(),\n        editorWidthSelect: useAnchorInstance()\n    };\n};\nconst PortalState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_1__.createContainer)(useModal);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PortalState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/portal.ts\n");

/***/ }),

/***/ "./libs/web/state/ui.ts":
/*!******************************!*\
  !*** ./libs/web/state/ui.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* reexport safe */ _ui_index__WEBPACK_IMPORTED_MODULE_0__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _ui_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui/index */ \"./libs/web/state/ui/index.ts\");\n// Re-export UI state from the ui folder\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS91aS50cy5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBLHdDQUF3QztBQUNIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbm90ZWEvLi9saWJzL3dlYi9zdGF0ZS91aS50cz82N2RiIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFJlLWV4cG9ydCBVSSBzdGF0ZSBmcm9tIHRoZSB1aSBmb2xkZXJcbmV4cG9ydCB7IGRlZmF1bHQgfSBmcm9tICcuL3VpL2luZGV4JztcbiJdLCJuYW1lcyI6WyJkZWZhdWx0Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/web/state/ui.ts\n");

/***/ }),

/***/ "./libs/web/state/ui/index.ts":
/*!************************************!*\
  !*** ./libs/web/state/ui/index.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unstated-next */ \"unstated-next\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(unstated_next__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _settings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./settings */ \"./libs/web/state/ui/settings.ts\");\n/* harmony import */ var _sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sidebar */ \"./libs/web/state/ui/sidebar.ts\");\n/* harmony import */ var _split__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./split */ \"./libs/web/state/ui/split.ts\");\n/* harmony import */ var _title__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./title */ \"./libs/web/state/ui/title.ts\");\n\n\n\n\n\nconst DEFAULT_UA = {\n    isMobile: false,\n    isMobileOnly: false,\n    isTablet: false,\n    isBrowser: true,\n    isWechat: false,\n    isMac: false\n};\nfunction useUI({ ua =DEFAULT_UA , settings , disablePassword , IS_DEMO  } = {}) {\n    return {\n        ua,\n        sidebar: (0,_sidebar__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(ua?.isMobileOnly ? false : settings?.sidebar_is_fold, ua.isMobileOnly),\n        split: (0,_split__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(settings?.split_sizes),\n        title: (0,_title__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(),\n        settings: (0,_settings__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(settings),\n        disablePassword,\n        IS_DEMO\n    };\n}\nconst UIState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_0__.createContainer)(useUI);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (UIState);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS91aS9pbmRleC50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRWdEO0FBQ1g7QUFDRjtBQUNKO0FBQ0E7QUFFL0IsTUFBTUssVUFBVSxHQUFrQjtJQUM5QkMsUUFBUSxFQUFFLEtBQUs7SUFDZkMsWUFBWSxFQUFFLEtBQUs7SUFDbkJDLFFBQVEsRUFBRSxLQUFLO0lBQ2ZDLFNBQVMsRUFBRSxJQUFJO0lBQ2ZDLFFBQVEsRUFBRSxLQUFLO0lBQ2ZDLEtBQUssRUFBRSxLQUFLO0NBQ2Y7QUFTRCxTQUFTQyxLQUFLLENBQUMsRUFDWEMsRUFBRSxFQUFHUixVQUFVLEdBQ2ZTLFFBQVEsR0FDUkMsZUFBZSxHQUNmQyxPQUFPLEdBQ0gsR0FBRyxFQUFFLEVBQUU7SUFDWCxPQUFPO1FBQ0hILEVBQUU7UUFDRkksT0FBTyxFQUFFZixvREFBVSxDQUNmVyxFQUFFLEVBQUVOLFlBQVksR0FBRyxLQUFLLEdBQUdPLFFBQVEsRUFBRUksZUFBZSxFQUNwREwsRUFBRSxDQUFDTixZQUFZLENBQ2xCO1FBQ0RZLEtBQUssRUFBRWhCLGtEQUFRLENBQUNXLFFBQVEsRUFBRU0sV0FBVyxDQUFDO1FBQ3RDQyxLQUFLLEVBQUVqQixrREFBUSxFQUFFO1FBQ2pCVSxRQUFRLEVBQUViLHFEQUFXLENBQUNhLFFBQVEsQ0FBQztRQUMvQkMsZUFBZTtRQUNmQyxPQUFPO0tBQ1YsQ0FBQztBQUNOLENBQUM7QUFFRCxNQUFNTSxPQUFPLEdBQUd0Qiw4REFBZSxDQUFDWSxLQUFLLENBQUM7QUFFdEMsaUVBQWVVLE9BQU8sRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vdGVhLy4vbGlicy93ZWIvc3RhdGUvdWkvaW5kZXgudHM/MjdlYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXR0aW5ncyB9IGZyb20gJ2xpYnMvc2hhcmVkL3NldHRpbmdzJztcbmltcG9ydCB7IFVzZXJBZ2VudFR5cGUgfSBmcm9tICdsaWJzL3NoYXJlZC91YSc7XG5pbXBvcnQgeyBjcmVhdGVDb250YWluZXIgfSBmcm9tICd1bnN0YXRlZC1uZXh0JztcbmltcG9ydCB1c2VTZXR0aW5ncyBmcm9tICcuL3NldHRpbmdzJztcbmltcG9ydCB1c2VTaWRlYmFyIGZyb20gJy4vc2lkZWJhcic7XG5pbXBvcnQgdXNlU3BsaXQgZnJvbSAnLi9zcGxpdCc7XG5pbXBvcnQgdXNlVGl0bGUgZnJvbSAnLi90aXRsZSc7XG5cbmNvbnN0IERFRkFVTFRfVUE6IFVzZXJBZ2VudFR5cGUgPSB7XG4gICAgaXNNb2JpbGU6IGZhbHNlLFxuICAgIGlzTW9iaWxlT25seTogZmFsc2UsXG4gICAgaXNUYWJsZXQ6IGZhbHNlLFxuICAgIGlzQnJvd3NlcjogdHJ1ZSxcbiAgICBpc1dlY2hhdDogZmFsc2UsXG4gICAgaXNNYWM6IGZhbHNlLFxufTtcblxuaW50ZXJmYWNlIFByb3BzIHtcbiAgICB1YT86IFVzZXJBZ2VudFR5cGU7XG4gICAgc2V0dGluZ3M/OiBTZXR0aW5ncztcbiAgICBkaXNhYmxlUGFzc3dvcmQ/OiBib29sZWFuO1xuICAgIElTX0RFTU8/OiBib29sZWFuO1xufVxuXG5mdW5jdGlvbiB1c2VVSSh7XG4gICAgdWEgPSBERUZBVUxUX1VBLFxuICAgIHNldHRpbmdzLFxuICAgIGRpc2FibGVQYXNzd29yZCxcbiAgICBJU19ERU1PLFxufTogUHJvcHMgPSB7fSkge1xuICAgIHJldHVybiB7XG4gICAgICAgIHVhLFxuICAgICAgICBzaWRlYmFyOiB1c2VTaWRlYmFyKFxuICAgICAgICAgICAgdWE/LmlzTW9iaWxlT25seSA/IGZhbHNlIDogc2V0dGluZ3M/LnNpZGViYXJfaXNfZm9sZCxcbiAgICAgICAgICAgIHVhLmlzTW9iaWxlT25seVxuICAgICAgICApLFxuICAgICAgICBzcGxpdDogdXNlU3BsaXQoc2V0dGluZ3M/LnNwbGl0X3NpemVzKSxcbiAgICAgICAgdGl0bGU6IHVzZVRpdGxlKCksXG4gICAgICAgIHNldHRpbmdzOiB1c2VTZXR0aW5ncyhzZXR0aW5ncyksXG4gICAgICAgIGRpc2FibGVQYXNzd29yZCxcbiAgICAgICAgSVNfREVNTyxcbiAgICB9O1xufVxuXG5jb25zdCBVSVN0YXRlID0gY3JlYXRlQ29udGFpbmVyKHVzZVVJKTtcblxuZXhwb3J0IGRlZmF1bHQgVUlTdGF0ZTtcbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250YWluZXIiLCJ1c2VTZXR0aW5ncyIsInVzZVNpZGViYXIiLCJ1c2VTcGxpdCIsInVzZVRpdGxlIiwiREVGQVVMVF9VQSIsImlzTW9iaWxlIiwiaXNNb2JpbGVPbmx5IiwiaXNUYWJsZXQiLCJpc0Jyb3dzZXIiLCJpc1dlY2hhdCIsImlzTWFjIiwidXNlVUkiLCJ1YSIsInNldHRpbmdzIiwiZGlzYWJsZVBhc3N3b3JkIiwiSVNfREVNTyIsInNpZGViYXIiLCJzaWRlYmFyX2lzX2ZvbGQiLCJzcGxpdCIsInNwbGl0X3NpemVzIiwidGl0bGUiLCJVSVN0YXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./libs/web/state/ui/index.ts\n");

/***/ }),

/***/ "./libs/web/state/ui/settings.ts":
/*!***************************************!*\
  !*** ./libs/web/state/ui/settings.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSettings)\n/* harmony export */ });\n/* harmony import */ var libs_web_api_settings__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/api/settings */ \"./libs/web/api/settings.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction useSettings(initData = {}) {\n    const { 0: settings , 1: setSettings  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initData);\n    const { mutate  } = (0,libs_web_api_settings__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n    const updateSettings = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (body)=>{\n        await mutate(body);\n        setSettings((prev)=>{\n            return {\n                ...prev,\n                ...body\n            };\n        });\n    }, [\n        mutate\n    ]);\n    return {\n        settings,\n        updateSettings,\n        setSettings\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS91aS9zZXR0aW5ncy50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQ21EO0FBQ0w7QUFFL0IsU0FBU0csV0FBVyxDQUFDQyxRQUFRLEdBQUcsRUFBRSxFQUFjO0lBQzNELE1BQU0sS0FBQ0MsUUFBUSxNQUFFQyxXQUFXLE1BQUlMLCtDQUFRLENBQVdHLFFBQVEsQ0FBQztJQUM1RCxNQUFNLEVBQUVHLE1BQU0sR0FBRSxHQUFHUCxpRUFBYyxFQUFFO0lBRW5DLE1BQU1RLGNBQWMsR0FBR04sa0RBQVcsQ0FDOUIsT0FBT08sSUFBdUIsR0FBSztRQUMvQixNQUFNRixNQUFNLENBQUNFLElBQUksQ0FBQyxDQUFDO1FBRW5CSCxXQUFXLENBQUMsQ0FBQ0ksSUFBSSxHQUFLO1lBQ2xCLE9BQU87Z0JBQ0gsR0FBR0EsSUFBSTtnQkFDUCxHQUFHRCxJQUFJO2FBQ1YsQ0FBQztRQUNOLENBQUMsQ0FBQyxDQUFDO0lBQ1AsQ0FBQyxFQUNEO1FBQUNGLE1BQU07S0FBQyxDQUNYO0lBRUQsT0FBTztRQUFFRixRQUFRO1FBQUVHLGNBQWM7UUFBRUYsV0FBVztLQUFFLENBQUM7QUFDckQsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vdGVhLy4vbGlicy93ZWIvc3RhdGUvdWkvc2V0dGluZ3MudHM/YmNkYSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBTZXR0aW5ncyB9IGZyb20gJ2xpYnMvc2hhcmVkL3NldHRpbmdzJztcbmltcG9ydCB1c2VTZXR0aW5nc0FQSSBmcm9tICdsaWJzL3dlYi9hcGkvc2V0dGluZ3MnO1xuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiB1c2VTZXR0aW5ncyhpbml0RGF0YSA9IHt9IGFzIFNldHRpbmdzKSB7XG4gICAgY29uc3QgW3NldHRpbmdzLCBzZXRTZXR0aW5nc10gPSB1c2VTdGF0ZTxTZXR0aW5ncz4oaW5pdERhdGEpO1xuICAgIGNvbnN0IHsgbXV0YXRlIH0gPSB1c2VTZXR0aW5nc0FQSSgpO1xuXG4gICAgY29uc3QgdXBkYXRlU2V0dGluZ3MgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKGJvZHk6IFBhcnRpYWw8U2V0dGluZ3M+KSA9PiB7XG4gICAgICAgICAgICBhd2FpdCBtdXRhdGUoYm9keSk7XG5cbiAgICAgICAgICAgIHNldFNldHRpbmdzKChwcmV2KSA9PiB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAgICAgLi4ucHJldixcbiAgICAgICAgICAgICAgICAgICAgLi4uYm9keSxcbiAgICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIFttdXRhdGVdXG4gICAgKTtcblxuICAgIHJldHVybiB7IHNldHRpbmdzLCB1cGRhdGVTZXR0aW5ncywgc2V0U2V0dGluZ3MgfTtcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTZXR0aW5nc0FQSSIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VTZXR0aW5ncyIsImluaXREYXRhIiwic2V0dGluZ3MiLCJzZXRTZXR0aW5ncyIsIm11dGF0ZSIsInVwZGF0ZVNldHRpbmdzIiwiYm9keSIsInByZXYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/state/ui/settings.ts\n");

/***/ }),

/***/ "./libs/web/state/ui/sidebar.ts":
/*!**************************************!*\
  !*** ./libs/web/state/ui/sidebar.ts ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSidebar)\n/* harmony export */ });\n/* harmony import */ var libs_web_api_settings__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/api/settings */ \"./libs/web/api/settings.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction useSidebar(initState = false, isMobileOnly = false) {\n    const { 0: isFold , 1: setIsFold  } = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(initState);\n    const { mutate  } = (0,libs_web_api_settings__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n    const toggle = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(async (state)=>{\n        setIsFold((prev)=>{\n            const isFold = (0,lodash__WEBPACK_IMPORTED_MODULE_1__.isBoolean)(state) ? state : !prev;\n            if (!isMobileOnly) {\n                mutate({\n                    sidebar_is_fold: isFold\n                });\n            }\n            return isFold;\n        });\n    }, [\n        isMobileOnly,\n        mutate\n    ]);\n    const open = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        toggle(true)?.catch((v)=>console.error(\"Error whilst opening sidebar: %O\", v));\n    }, [\n        toggle\n    ]);\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(()=>{\n        toggle(false)?.catch((v)=>console.error(\"Error whilst closing sidebar: %O\", v));\n    }, [\n        toggle\n    ]);\n    return {\n        isFold,\n        toggle,\n        open,\n        close\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/ui/sidebar.ts\n");

/***/ }),

/***/ "./libs/web/state/ui/split.ts":
/*!************************************!*\
  !*** ./libs/web/state/ui/split.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useSplit)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var libs_shared_settings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! libs/shared/settings */ \"./libs/shared/settings.ts\");\n/* harmony import */ var libs_web_api_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! libs/web/api/settings */ \"./libs/web/api/settings.ts\");\n\n\n\nfunction useSplit(initData = libs_shared_settings__WEBPACK_IMPORTED_MODULE_1__.DEFAULT_SETTINGS.split_sizes) {\n    const { 0: sizes , 1: setSizes  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initData);\n    const sizesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(sizes);\n    const { mutate  } = (0,libs_web_api_settings__WEBPACK_IMPORTED_MODULE_2__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        sizesRef.current = sizes;\n    }, [\n        sizes\n    ]);\n    const saveSizes = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (sizes)=>{\n        setSizes(sizes);\n        await mutate({\n            split_sizes: sizes\n        });\n    }, [\n        mutate\n    ]);\n    const resize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(async (scale)=>{\n        const size = sizesRef.current?.[0] * scale;\n        await saveSizes([\n            size,\n            100 - size\n        ]);\n    }, [\n        saveSizes\n    ]);\n    return {\n        sizes,\n        saveSizes,\n        resize\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/ui/split.ts\n");

/***/ }),

/***/ "./libs/web/state/ui/title.ts":
/*!************************************!*\
  !*** ./libs/web/state/ui/title.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useTitle)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction useTitle() {\n    const { 0: value , 1: setTitle  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"Notea\");\n    const updateTitle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((text)=>{\n        setTitle(text ? `${text} - Notea` : \"Notea\");\n    }, []);\n    return {\n        value,\n        updateTitle\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS91aS90aXRsZS50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBOEM7QUFFL0IsU0FBU0UsUUFBUSxHQUFHO0lBQy9CLE1BQU0sS0FBQ0MsS0FBSyxNQUFFQyxRQUFRLE1BQUlKLCtDQUFRLENBQUMsT0FBTyxDQUFDO0lBRTNDLE1BQU1LLFdBQVcsR0FBR0osa0RBQVcsQ0FBQyxDQUFDSyxJQUFhLEdBQUs7UUFDL0NGLFFBQVEsQ0FBQ0UsSUFBSSxHQUFHLENBQUMsRUFBRUEsSUFBSSxDQUFDLFFBQVEsQ0FBQyxHQUFHLE9BQU8sQ0FBQyxDQUFDO0lBQ2pELENBQUMsRUFBRSxFQUFFLENBQUM7SUFFTixPQUFPO1FBQUVILEtBQUs7UUFBRUUsV0FBVztLQUFFLENBQUM7QUFDbEMsQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL25vdGVhLy4vbGlicy93ZWIvc3RhdGUvdWkvdGl0bGUudHM/OWYyZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1c2VTdGF0ZSwgdXNlQ2FsbGJhY2sgfSBmcm9tICdyZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHVzZVRpdGxlKCkge1xuICAgIGNvbnN0IFt2YWx1ZSwgc2V0VGl0bGVdID0gdXNlU3RhdGUoJ05vdGVhJyk7XG5cbiAgICBjb25zdCB1cGRhdGVUaXRsZSA9IHVzZUNhbGxiYWNrKCh0ZXh0Pzogc3RyaW5nKSA9PiB7XG4gICAgICAgIHNldFRpdGxlKHRleHQgPyBgJHt0ZXh0fSAtIE5vdGVhYCA6ICdOb3RlYScpO1xuICAgIH0sIFtdKTtcblxuICAgIHJldHVybiB7IHZhbHVlLCB1cGRhdGVUaXRsZSB9O1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VUaXRsZSIsInZhbHVlIiwic2V0VGl0bGUiLCJ1cGRhdGVUaXRsZSIsInRleHQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/state/ui/title.ts\n");

/***/ }),

/***/ "./libs/web/utils/i18n-provider.tsx":
/*!******************************************!*\
  !*** ./libs/web/utils/i18n-provider.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"I18nContext\": () => (/* binding */ I18nContext),\n/* harmony export */   \"default\": () => (/* binding */ I18nProvider),\n/* harmony export */   \"defaultLanguage\": () => (/* binding */ defaultLanguage),\n/* harmony export */   \"languages\": () => (/* binding */ languages)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var rosetta__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rosetta */ \"rosetta\");\n/* harmony import */ var rosetta__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(rosetta__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_settings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/settings */ \"./libs/shared/settings.ts\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash */ \"lodash\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var locales__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! locales */ \"./locales/index.ts\");\n/* harmony import */ var pupa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! pupa */ \"pupa\");\n/* harmony import */ var pupa__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(pupa__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\nconst i18n = rosetta__WEBPACK_IMPORTED_MODULE_2___default()();\nconst defaultLanguage = libs_shared_settings__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_SETTINGS.locale;\nconst languages = (0,lodash__WEBPACK_IMPORTED_MODULE_4__.values)(locales__WEBPACK_IMPORTED_MODULE_5__.Locale);\nconst I18nContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\n// default language\ni18n.locale(defaultLanguage);\nfunction I18nProvider({ children , locale , lngDict  }) {\n    const activeLocaleRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(locale || defaultLanguage);\n    const { 1: setTick  } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const firstRender = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const i18nWrapper = {\n        activeLocale: activeLocaleRef.current,\n        t: (key, ...args)=>{\n            if (activeLocaleRef.current === defaultLanguage) {\n                return pupa__WEBPACK_IMPORTED_MODULE_6___default()(Array.isArray(key) ? key.join(\"\") : key, args[0] ?? {});\n            }\n            return i18n.t(Array.isArray(key) ? key : [\n                key\n            ], ...args);\n        },\n        locale: (l, dict)=>{\n            i18n.locale(l);\n            activeLocaleRef.current = l;\n            if (dict) {\n                i18n.set(l, dict);\n            }\n            // force rerender to update view\n            setTick((tick)=>tick + 1);\n        }\n    };\n    // for initial SSR render\n    if (locale && firstRender.current === true) {\n        firstRender.current = false;\n        i18nWrapper.locale(locale, lngDict);\n    }\n    // when locale is updated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (locale) {\n            i18nWrapper.locale(locale, lngDict);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        lngDict,\n        locale\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(I18nContext.Provider, {\n        value: i18nWrapper,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\libs\\\\web\\\\utils\\\\i18n-provider.tsx\",\n        lineNumber: 72,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/utils/i18n-provider.tsx\n");

/***/ }),

/***/ "./locales/index.ts":
/*!**************************!*\
  !*** ./locales/index.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"Locale\": () => (/* binding */ Locale),\n/* harmony export */   \"configLocale\": () => (/* binding */ configLocale),\n/* harmony export */   \"muiLocale\": () => (/* binding */ muiLocale)\n/* harmony export */ });\n/* harmony import */ var _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @material-ui/core/locale */ \"@material-ui/core/locale\");\n/* harmony import */ var _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__);\n\nvar Locale;\n(function(Locale) {\n    Locale[\"ZH_CN\"] = \"zh-CN\";\n    Locale[\"EN\"] = \"en\";\n    Locale[\"de_DE\"] = \"de-DE\";\n    Locale[\"ru_RU\"] = \"ru-RU\";\n    Locale[\"ar\"] = \"ar\";\n    Locale[\"it_IT\"] = \"it-IT\";\n    Locale[\"nl_NL\"] = \"nl-NL\";\n    Locale[\"fr_FR\"] = \"fr-FR\";\n    Locale[\"sv_SE\"] = \"sv-SE\";\n})(Locale || (Locale = {}));\nconst muiLocale = {\n    [Locale.ZH_CN]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.zhCN,\n    [Locale.EN]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.enUS,\n    [Locale.de_DE]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.deDE,\n    [Locale.ru_RU]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.ruRU,\n    // FIXME: upgrade material-ui and import arEG\n    [Locale.ar]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.enUS,\n    [Locale.it_IT]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.itIT,\n    [Locale.nl_NL]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.nlNL,\n    [Locale.fr_FR]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.frFR,\n    [Locale.sv_SE]: _material_ui_core_locale__WEBPACK_IMPORTED_MODULE_0__.svSE\n};\nconst configLocale = {\n    [Locale.EN]: \"English\",\n    [Locale.ZH_CN]: \"简体中文\",\n    [Locale.de_DE]: \"Deutsch\",\n    [Locale.ru_RU]: \"Русский\",\n    [Locale.ar]: \"العربية\",\n    [Locale.it_IT]: \"Italiano\",\n    [Locale.nl_NL]: \"Nederlands\",\n    [Locale.fr_FR]: \"fran\\xe7ais\",\n    [Locale.sv_SE]: \"Svenska\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./locales/index.ts\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var tailwindcss_tailwind_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwindcss/tailwind.css */ \"./node_modules/tailwindcss/tailwind.css\");\n/* harmony import */ var tailwindcss_tailwind_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(tailwindcss_tailwind_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _fontsource_noto_sans_latin_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @fontsource/noto-sans/latin.css */ \"./node_modules/@fontsource/noto-sans/latin.css\");\n/* harmony import */ var _fontsource_noto_sans_latin_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_fontsource_noto_sans_latin_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_web_state_ui__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/web/state/ui */ \"./libs/web/state/ui.ts\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-themes */ \"next-themes\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_themes__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var libs_web_state_portal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! libs/web/state/portal */ \"./libs/web/state/portal.ts\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-div-100vh */ \"react-div-100vh\");\n/* harmony import */ var react_div_100vh__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(react_div_100vh__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @material-ui/core */ \"@material-ui/core\");\n/* harmony import */ var _material_ui_core__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(_material_ui_core__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var libs_web_utils_i18n_provider__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! libs/web/utils/i18n-provider */ \"./libs/web/utils/i18n-provider.tsx\");\n/* harmony import */ var libs_web_state_csrf_token__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! libs/web/state/csrf-token */ \"./libs/web/state/csrf-token.ts\");\n/* harmony import */ var locales__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! locales */ \"./locales/index.ts\");\n/* harmony import */ var notistack__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! notistack */ \"notistack\");\n/* harmony import */ var notistack__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(notistack__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var _material_ui_core_styles__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @material-ui/core/styles */ \"@material-ui/core/styles\");\n/* harmony import */ var _material_ui_core_styles__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(_material_ui_core_styles__WEBPACK_IMPORTED_MODULE_14__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst handleRejection = (event)=>{\n    if (/^ResizeObserver/.test(event.message)) {\n        event.stopImmediatePropagation();\n    }\n    if (event.reason === \"canceled\") {\n        event.preventDefault();\n    }\n};\nif (false) {}\nfunction DocumentHead() {\n    const { title  } = libs_web_state_ui__WEBPACK_IMPORTED_MODULE_3__[\"default\"].useContainer();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: title.value\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                lineNumber: 41,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                charSet: \"utf-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                lineNumber: 42,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"viewport\",\n                content: \"minimum-scale=1, initial-scale=1, width=device-width, shrink-to-fit=no, user-scalable=no, viewport-fit=cover\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                lineNumber: 43,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n        lineNumber: 40,\n        columnNumber: 9\n    }, this);\n}\nconst AppInner = ({ Component , pageProps  })=>{\n    const { resolvedTheme  } = (0,next_themes__WEBPACK_IMPORTED_MODULE_5__.useTheme)();\n    const settings = pageProps?.settings;\n    const muiTheme = (0,react__WEBPACK_IMPORTED_MODULE_9__.useMemo)(()=>(0,_material_ui_core_styles__WEBPACK_IMPORTED_MODULE_14__.createTheme)({\n            palette: {\n                type: resolvedTheme === \"dark\" ? \"dark\" : \"light\",\n                primary: {\n                    main: \"#3B82F6\"\n                },\n                secondary: {\n                    main: \"#6B7280\"\n                }\n            }\n        }, locales__WEBPACK_IMPORTED_MODULE_12__.muiLocale[settings?.locale]), [\n        resolvedTheme,\n        settings\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_9__.useEffect)(()=>{\n        const jssStyles = document.querySelector(\"#jss-server-side\");\n        if (jssStyles) {\n            jssStyles.parentElement?.removeChild(jssStyles);\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_ui_core__WEBPACK_IMPORTED_MODULE_8__.StylesProvider, {\n        injectFirst: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_ui_core__WEBPACK_IMPORTED_MODULE_8__.MuiThemeProvider, {\n            theme: muiTheme,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(libs_web_state_csrf_token__WEBPACK_IMPORTED_MODULE_11__[\"default\"].Provider, {\n                initialState: pageProps.csrfToken,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(libs_web_utils_i18n_provider__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    locale: settings?.locale,\n                    lngDict: pageProps.lngDict,\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(libs_web_state_ui__WEBPACK_IMPORTED_MODULE_3__[\"default\"].Provider, {\n                        initialState: {\n                            ua: pageProps?.ua,\n                            settings,\n                            disablePassword: pageProps?.disablePassword,\n                            IS_DEMO: pageProps.IS_DEMO\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(libs_web_state_portal__WEBPACK_IMPORTED_MODULE_6__[\"default\"].Provider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react_div_100vh__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DocumentHead, {}, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                                        lineNumber: 105,\n                                        columnNumber: 37\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(notistack__WEBPACK_IMPORTED_MODULE_13__.SnackbarProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                                            ...pageProps\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                                            lineNumber: 107,\n                                            columnNumber: 41\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 37\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 33\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                        lineNumber: 95,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n                lineNumber: 90,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n            lineNumber: 89,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n        lineNumber: 88,\n        columnNumber: 9\n    }, undefined);\n};\nfunction MyApp(props) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_5__.ThemeProvider, {\n        attribute: \"class\",\n        storageKey: \"nightwind-mode\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AppInner, {\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n            lineNumber: 121,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹\\\\pages\\\\_app.tsx\",\n        lineNumber: 120,\n        columnNumber: 12\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./node_modules/@fontsource/noto-sans/latin.css":
/*!******************************************************!*\
  !*** ./node_modules/@fontsource/noto-sans/latin.css ***!
  \******************************************************/
/***/ (() => {



/***/ }),

/***/ "./node_modules/tailwindcss/tailwind.css":
/*!***********************************************!*\
  !*** ./node_modules/tailwindcss/tailwind.css ***!
  \***********************************************/
/***/ (() => {



/***/ }),

/***/ "@atlaskit/tree":
/*!*********************************!*\
  !*** external "@atlaskit/tree" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@atlaskit/tree");

/***/ }),

/***/ "@material-ui/core":
/*!************************************!*\
  !*** external "@material-ui/core" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@material-ui/core");

/***/ }),

/***/ "@material-ui/core/locale":
/*!*******************************************!*\
  !*** external "@material-ui/core/locale" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@material-ui/core/locale");

/***/ }),

/***/ "@material-ui/core/styles":
/*!*******************************************!*\
  !*** external "@material-ui/core/styles" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("@material-ui/core/styles");

/***/ }),

/***/ "lodash":
/*!*************************!*\
  !*** external "lodash" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash");

/***/ }),

/***/ "next-themes":
/*!******************************!*\
  !*** external "next-themes" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("next-themes");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "notistack":
/*!****************************!*\
  !*** external "notistack" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("notistack");

/***/ }),

/***/ "pupa":
/*!***********************!*\
  !*** external "pupa" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("pupa");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-div-100vh":
/*!**********************************!*\
  !*** external "react-div-100vh" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-div-100vh");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "rosetta":
/*!**************************!*\
  !*** external "rosetta" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("rosetta");

/***/ }),

/***/ "unstated-next":
/*!********************************!*\
  !*** external "unstated-next" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("unstated-next");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./pages/_app.tsx"));
module.exports = __webpack_exports__;

})();