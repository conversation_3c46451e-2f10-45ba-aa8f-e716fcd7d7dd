{"version": 3, "file": "gen-mapping.mjs", "sources": ["../src/sourcemap-segment.ts", "../src/gen-mapping.ts"], "sourcesContent": ["type GeneratedColumn = number;\ntype SourcesIndex = number;\ntype SourceLine = number;\ntype SourceColumn = number;\ntype NamesIndex = number;\n\nexport type SourceMapSegment =\n  | [GeneratedColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn]\n  | [GeneratedColumn, SourcesIndex, SourceLine, SourceColumn, NamesIndex];\n\nexport const COLUMN = 0;\nexport const SOURCES_INDEX = 1;\nexport const SOURCE_LINE = 2;\nexport const SOURCE_COLUMN = 3;\nexport const NAMES_INDEX = 4;\n", "import { SetArray, put } from '@jridgewell/set-array';\nimport { encode } from '@jridgewell/sourcemap-codec';\nimport { TraceMap, decodedMappings } from '@jridgewell/trace-mapping';\n\nimport {\n  COLUMN,\n  SOURCES_INDEX,\n  SOURCE_LINE,\n  SOURCE_COLUMN,\n  NAMES_INDEX,\n} from './sourcemap-segment';\n\nimport type { SourceMapInput } from '@jridgewell/trace-mapping';\nimport type { SourceMapSegment } from './sourcemap-segment';\nimport type { DecodedSourceMap, EncodedSourceMap, Pos, Mapping } from './types';\n\nexport type { DecodedSourceMap, EncodedSourceMap, Mapping };\n\nexport type Options = {\n  file?: string | null;\n  sourceRoot?: string | null;\n};\n\nconst NO_NAME = -1;\n\n/**\n * A low-level API to associate a generated position with an original source position. Line and\n * column here are 0-based, unlike `addMapping`.\n */\nexport let addSegment: {\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source?: null,\n    sourceLine?: null,\n    sourceColumn?: null,\n    name?: null,\n    content?: null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name?: null,\n    content?: string | null,\n  ): void;\n  (\n    map: GenMapping,\n    genLine: number,\n    genColumn: number,\n    source: string,\n    sourceLine: number,\n    sourceColumn: number,\n    name: string,\n    content?: string | null,\n  ): void;\n};\n\n/**\n * A high-level API to associate a generated position with an original source position. Line is\n * 1-based, but column is 0-based, due to legacy behavior in `source-map` library.\n */\nexport let addMapping: {\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source?: null;\n      original?: null;\n      name?: null;\n      content?: null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name?: null;\n      content?: string | null;\n    },\n  ): void;\n  (\n    map: GenMapping,\n    mapping: {\n      generated: Pos;\n      source: string;\n      original: Pos;\n      name: string;\n      content?: string | null;\n    },\n  ): void;\n};\n\n/**\n * Same as `addSegment`, but will only add the segment if it generates useful information in the\n * resulting map. This only works correctly if segments are added **in order**, meaning you should\n * not add a segment with a lower generated line/column than one that came before.\n */\nexport let maybeAddSegment: typeof addSegment;\n\n/**\n * Same as `addMapping`, but will only add the mapping if it generates useful information in the\n * resulting map. This only works correctly if mappings are added **in order**, meaning you should\n * not add a mapping with a lower generated line/column than one that came before.\n */\nexport let maybeAddMapping: typeof addMapping;\n\n/**\n * Adds/removes the content of the source file to the source map.\n */\nexport let setSourceContent: (map: GenMapping, source: string, content: string | null) => void;\n\n/**\n * Returns a sourcemap object (with decoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let toDecodedMap: (map: GenMapping) => DecodedSourceMap;\n\n/**\n * Returns a sourcemap object (with encoded mappings) suitable for passing to a library that expects\n * a sourcemap, or to JSON.stringify.\n */\nexport let toEncodedMap: (map: GenMapping) => EncodedSourceMap;\n\n/**\n * Constructs a new GenMapping, using the already present mappings of the input.\n */\nexport let fromMap: (input: SourceMapInput) => GenMapping;\n\n/**\n * Returns an array of high-level mapping objects for every recorded segment, which could then be\n * passed to the `source-map` library.\n */\nexport let allMappings: (map: GenMapping) => Mapping[];\n\n// This split declaration is only so that terser can elminiate the static initialization block.\nlet addSegmentInternal: <S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  genLine: number,\n  genColumn: number,\n  source: S,\n  sourceLine: S extends string ? number : null | undefined,\n  sourceColumn: S extends string ? number : null | undefined,\n  name: S extends string ? string | null | undefined : null | undefined,\n  content: S extends string ? string | null | undefined : null | undefined,\n) => void;\n\n/**\n * Provides the state to generate a sourcemap.\n */\nexport class GenMapping {\n  private _names = new SetArray();\n  private _sources = new SetArray();\n  private _sourcesContent: (string | null)[] = [];\n  private _mappings: SourceMapSegment[][] = [];\n  declare file: string | null | undefined;\n  declare sourceRoot: string | null | undefined;\n\n  constructor({ file, sourceRoot }: Options = {}) {\n    this.file = file;\n    this.sourceRoot = sourceRoot;\n  }\n\n  static {\n    addSegment = (map, genLine, genColumn, source, sourceLine, sourceColumn, name, content) => {\n      return addSegmentInternal(\n        false,\n        map,\n        genLine,\n        genColumn,\n        source,\n        sourceLine,\n        sourceColumn,\n        name,\n        content,\n      );\n    };\n\n    maybeAddSegment = (\n      map,\n      genLine,\n      genColumn,\n      source,\n      sourceLine,\n      sourceColumn,\n      name,\n      content,\n    ) => {\n      return addSegmentInternal(\n        true,\n        map,\n        genLine,\n        genColumn,\n        source,\n        sourceLine,\n        sourceColumn,\n        name,\n        content,\n      );\n    };\n\n    addMapping = (map, mapping) => {\n      return addMappingInternal(false, map, mapping as Parameters<typeof addMappingInternal>[2]);\n    };\n\n    maybeAddMapping = (map, mapping) => {\n      return addMappingInternal(true, map, mapping as Parameters<typeof addMappingInternal>[2]);\n    };\n\n    setSourceContent = (map, source, content) => {\n      const { _sources: sources, _sourcesContent: sourcesContent } = map;\n      sourcesContent[put(sources, source)] = content;\n    };\n\n    toDecodedMap = (map) => {\n      const {\n        file,\n        sourceRoot,\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n      removeEmptyFinalLines(mappings);\n\n      return {\n        version: 3,\n        file: file || undefined,\n        names: names.array,\n        sourceRoot: sourceRoot || undefined,\n        sources: sources.array,\n        sourcesContent,\n        mappings,\n      };\n    };\n\n    toEncodedMap = (map) => {\n      const decoded = toDecodedMap(map);\n      return {\n        ...decoded,\n        mappings: encode(decoded.mappings as SourceMapSegment[][]),\n      };\n    };\n\n    allMappings = (map) => {\n      const out: Mapping[] = [];\n      const { _mappings: mappings, _sources: sources, _names: names } = map;\n\n      for (let i = 0; i < mappings.length; i++) {\n        const line = mappings[i];\n        for (let j = 0; j < line.length; j++) {\n          const seg = line[j];\n\n          const generated = { line: i + 1, column: seg[COLUMN] };\n          let source: string | undefined = undefined;\n          let original: Pos | undefined = undefined;\n          let name: string | undefined = undefined;\n\n          if (seg.length !== 1) {\n            source = sources.array[seg[SOURCES_INDEX]];\n            original = { line: seg[SOURCE_LINE] + 1, column: seg[SOURCE_COLUMN] };\n\n            if (seg.length === 5) name = names.array[seg[NAMES_INDEX]];\n          }\n\n          out.push({ generated, source, original, name } as Mapping);\n        }\n      }\n\n      return out;\n    };\n\n    fromMap = (input) => {\n      const map = new TraceMap(input);\n      const gen = new GenMapping({ file: map.file, sourceRoot: map.sourceRoot });\n\n      putAll(gen._names, map.names);\n      putAll(gen._sources, map.sources as string[]);\n      gen._sourcesContent = map.sourcesContent || map.sources.map(() => null);\n      gen._mappings = decodedMappings(map) as GenMapping['_mappings'];\n\n      return gen;\n    };\n\n    // Internal helpers\n    addSegmentInternal = (\n      skipable,\n      map,\n      genLine,\n      genColumn,\n      source,\n      sourceLine,\n      sourceColumn,\n      name,\n      content,\n    ) => {\n      const {\n        _mappings: mappings,\n        _sources: sources,\n        _sourcesContent: sourcesContent,\n        _names: names,\n      } = map;\n      const line = getLine(mappings, genLine);\n      const index = getColumnIndex(line, genColumn);\n\n      if (!source) {\n        if (skipable && skipSourceless(line, index)) return;\n        return insert(line, index, [genColumn]);\n      }\n\n      // Sigh, TypeScript can't figure out sourceLine and sourceColumn aren't nullish if source\n      // isn't nullish.\n      assert<number>(sourceLine);\n      assert<number>(sourceColumn);\n\n      const sourcesIndex = put(sources, source);\n      const namesIndex = name ? put(names, name) : NO_NAME;\n      if (sourcesIndex === sourcesContent.length) sourcesContent[sourcesIndex] = content ?? null;\n\n      if (skipable && skipSource(line, index, sourcesIndex, sourceLine, sourceColumn, namesIndex)) {\n        return;\n      }\n\n      return insert(\n        line,\n        index,\n        name\n          ? [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex]\n          : [genColumn, sourcesIndex, sourceLine, sourceColumn],\n      );\n    };\n  }\n}\n\nfunction assert<T>(_val: unknown): asserts _val is T {\n  // noop.\n}\n\nfunction getLine(mappings: SourceMapSegment[][], index: number): SourceMapSegment[] {\n  for (let i = mappings.length; i <= index; i++) {\n    mappings[i] = [];\n  }\n  return mappings[index];\n}\n\nfunction getColumnIndex(line: SourceMapSegment[], genColumn: number): number {\n  let index = line.length;\n  for (let i = index - 1; i >= 0; index = i--) {\n    const current = line[i];\n    if (genColumn >= current[COLUMN]) break;\n  }\n  return index;\n}\n\nfunction insert<T>(array: T[], index: number, value: T) {\n  for (let i = array.length; i > index; i--) {\n    array[i] = array[i - 1];\n  }\n  array[index] = value;\n}\n\nfunction removeEmptyFinalLines(mappings: SourceMapSegment[][]) {\n  const { length } = mappings;\n  let len = length;\n  for (let i = len - 1; i >= 0; len = i, i--) {\n    if (mappings[i].length > 0) break;\n  }\n  if (len < length) mappings.length = len;\n}\n\nfunction putAll(strarr: SetArray, array: string[]) {\n  for (let i = 0; i < array.length; i++) put(strarr, array[i]);\n}\n\nfunction skipSourceless(line: SourceMapSegment[], index: number): boolean {\n  // The start of a line is already sourceless, so adding a sourceless segment to the beginning\n  // doesn't generate any useful information.\n  if (index === 0) return true;\n\n  const prev = line[index - 1];\n  // If the previous segment is also sourceless, then adding another sourceless segment doesn't\n  // genrate any new information. Else, this segment will end the source/named segment and point to\n  // a sourceless position, which is useful.\n  return prev.length === 1;\n}\n\nfunction skipSource(\n  line: SourceMapSegment[],\n  index: number,\n  sourcesIndex: number,\n  sourceLine: number,\n  sourceColumn: number,\n  namesIndex: number,\n): boolean {\n  // A source/named segment at the start of a line gives position at that genColumn\n  if (index === 0) return false;\n\n  const prev = line[index - 1];\n\n  // If the previous segment is sourceless, then we're transitioning to a source.\n  if (prev.length === 1) return false;\n\n  // If the previous segment maps to the exact same source position, then this segment doesn't\n  // provide any new position information.\n  return (\n    sourcesIndex === prev[SOURCES_INDEX] &&\n    sourceLine === prev[SOURCE_LINE] &&\n    sourceColumn === prev[SOURCE_COLUMN] &&\n    namesIndex === (prev.length === 5 ? prev[NAMES_INDEX] : NO_NAME)\n  );\n}\n\nfunction addMappingInternal<S extends string | null | undefined>(\n  skipable: boolean,\n  map: GenMapping,\n  mapping: {\n    generated: Pos;\n    source: S;\n    original: S extends string ? Pos : null | undefined;\n    name: S extends string ? string | null | undefined : null | undefined;\n    content: S extends string ? string | null | undefined : null | undefined;\n  },\n) {\n  const { generated, source, original, name, content } = mapping;\n  if (!source) {\n    return addSegmentInternal(\n      skipable,\n      map,\n      generated.line - 1,\n      generated.column,\n      null,\n      null,\n      null,\n      null,\n      null,\n    );\n  }\n  const s: string = source;\n  assert<Pos>(original);\n  return addSegmentInternal(\n    skipable,\n    map,\n    generated.line - 1,\n    generated.column,\n    s,\n    original.line - 1,\n    original.column,\n    name,\n    content,\n  );\n}\n"], "names": [], "mappings": ";;;;AAWO,MAAM,MAAM,GAAG,CAAC,CAAC;AACjB,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,WAAW,GAAG,CAAC,CAAC;AACtB,MAAM,aAAa,GAAG,CAAC,CAAC;AACxB,MAAM,WAAW,GAAG,CAAC;;ACQ5B,MAAM,OAAO,GAAG,CAAC,CAAC,CAAC;AAEnB;;;AAGG;AACQ,IAAA,WA+BT;AAEF;;;AAGG;AACQ,IAAA,WA+BT;AAEF;;;;AAIG;AACQ,IAAA,gBAAmC;AAE9C;;;;AAIG;AACQ,IAAA,gBAAmC;AAE9C;;AAEG;AACQ,IAAA,iBAAoF;AAE/F;;;AAGG;AACQ,IAAA,aAAoD;AAE/D;;;AAGG;AACQ,IAAA,aAAoD;AAE/D;;AAEG;AACQ,IAAA,QAA+C;AAE1D;;;AAGG;AACQ,IAAA,YAA4C;AAEvD;AACA,IAAI,kBAUK,CAAC;AAEV;;AAEG;MACU,UAAU,CAAA;AAQrB,IAAA,WAAA,CAAY,EAAE,IAAI,EAAE,UAAU,KAAc,EAAE,EAAA;AAPtC,QAAA,IAAA,CAAA,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;AACxB,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,QAAQ,EAAE,CAAC;QAC1B,IAAe,CAAA,eAAA,GAAsB,EAAE,CAAC;QACxC,IAAS,CAAA,SAAA,GAAyB,EAAE,CAAC;AAK3C,QAAA,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACjB,QAAA,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;KAC9B;AA2KF,CAAA;AAzKC,CAAA,MAAA;AACE,IAAA,UAAU,GAAG,CAAC,GAAG,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,OAAO,KAAI;QACxF,OAAO,kBAAkB,CACvB,KAAK,EACL,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,eAAe,GAAG,CAChB,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,KACL;QACF,OAAO,kBAAkB,CACvB,IAAI,EACJ,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,CACR,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,UAAU,GAAG,CAAC,GAAG,EAAE,OAAO,KAAI;QAC5B,OAAO,kBAAkB,CAAC,KAAK,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;AAC7F,KAAC,CAAC;AAEF,IAAA,eAAe,GAAG,CAAC,GAAG,EAAE,OAAO,KAAI;QACjC,OAAO,kBAAkB,CAAC,IAAI,EAAE,GAAG,EAAE,OAAmD,CAAC,CAAC;AAC5F,KAAC,CAAC;IAEF,gBAAgB,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,KAAI;QAC1C,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,eAAe,EAAE,cAAc,EAAE,GAAG,GAAG,CAAC;QACnE,cAAc,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC;AACjD,KAAC,CAAC;AAEF,IAAA,YAAY,GAAG,CAAC,GAAG,KAAI;QACrB,MAAM,EACJ,IAAI,EACJ,UAAU,EACV,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;QACR,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAEhC,OAAO;AACL,YAAA,OAAO,EAAE,CAAC;YACV,IAAI,EAAE,IAAI,IAAI,SAAS;YACvB,KAAK,EAAE,KAAK,CAAC,KAAK;YAClB,UAAU,EAAE,UAAU,IAAI,SAAS;YACnC,OAAO,EAAE,OAAO,CAAC,KAAK;YACtB,cAAc;YACd,QAAQ;SACT,CAAC;AACJ,KAAC,CAAC;AAEF,IAAA,YAAY,GAAG,CAAC,GAAG,KAAI;AACrB,QAAA,MAAM,OAAO,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAClC,OACK,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,EAAA,OAAO,CACV,EAAA,EAAA,QAAQ,EAAE,MAAM,CAAC,OAAO,CAAC,QAAgC,CAAC,EAC1D,CAAA,CAAA;AACJ,KAAC,CAAC;AAEF,IAAA,WAAW,GAAG,CAAC,GAAG,KAAI;QACpB,MAAM,GAAG,GAAc,EAAE,CAAC;AAC1B,QAAA,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC;AAEtE,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,YAAA,MAAM,IAAI,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACzB,YAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACpC,gBAAA,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAEpB,gBAAA,MAAM,SAAS,GAAG,EAAE,IAAI,EAAE,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACvD,IAAI,MAAM,GAAuB,SAAS,CAAC;gBAC3C,IAAI,QAAQ,GAAoB,SAAS,CAAC;gBAC1C,IAAI,IAAI,GAAuB,SAAS,CAAC;AAEzC,gBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBACpB,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;AAC3C,oBAAA,QAAQ,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;AAEtE,oBAAA,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC;wBAAE,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5D,iBAAA;AAED,gBAAA,GAAG,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAa,CAAC,CAAC;AAC5D,aAAA;AACF,SAAA;AAED,QAAA,OAAO,GAAG,CAAC;AACb,KAAC,CAAC;AAEF,IAAA,OAAO,GAAG,CAAC,KAAK,KAAI;AAClB,QAAA,MAAM,GAAG,GAAG,IAAI,QAAQ,CAAC,KAAK,CAAC,CAAC;AAChC,QAAA,MAAM,GAAG,GAAG,IAAI,UAAU,CAAC,EAAE,IAAI,EAAE,GAAG,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,CAAC,UAAU,EAAE,CAAC,CAAC;QAE3E,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,CAAC,OAAmB,CAAC,CAAC;AAC9C,QAAA,GAAG,CAAC,eAAe,GAAG,GAAG,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;AACxE,QAAA,GAAG,CAAC,SAAS,GAAG,eAAe,CAAC,GAAG,CAA4B,CAAC;AAEhE,QAAA,OAAO,GAAG,CAAC;AACb,KAAC,CAAC;;IAGF,kBAAkB,GAAG,CACnB,QAAQ,EACR,GAAG,EACH,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,YAAY,EACZ,IAAI,EACJ,OAAO,KACL;AACF,QAAA,MAAM,EACJ,SAAS,EAAE,QAAQ,EACnB,QAAQ,EAAE,OAAO,EACjB,eAAe,EAAE,cAAc,EAC/B,MAAM,EAAE,KAAK,GACd,GAAG,GAAG,CAAC;QACR,MAAM,IAAI,GAAG,OAAO,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACxC,MAAM,KAAK,GAAG,cAAc,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAE9C,IAAI,CAAC,MAAM,EAAE;AACX,YAAA,IAAI,QAAQ,IAAI,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC;gBAAE,OAAO;YACpD,OAAO,MAAM,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,CAAC;AACzC,SAAA;QAOD,MAAM,YAAY,GAAG,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;AAC1C,QAAA,MAAM,UAAU,GAAG,IAAI,GAAG,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC;AACrD,QAAA,IAAI,YAAY,KAAK,cAAc,CAAC,MAAM;YAAE,cAAc,CAAC,YAAY,CAAC,GAAG,OAAO,KAAP,IAAA,IAAA,OAAO,KAAP,KAAA,CAAA,GAAA,OAAO,GAAI,IAAI,CAAC;AAE3F,QAAA,IAAI,QAAQ,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC,EAAE;YAC3F,OAAO;AACR,SAAA;AAED,QAAA,OAAO,MAAM,CACX,IAAI,EACJ,KAAK,EACL,IAAI;cACA,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,CAAC;cAC/D,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,CAAC,CACxD,CAAC;AACJ,KAAC,CAAC;AACJ,CAAC,GAAA,CAAA;AAOH,SAAS,OAAO,CAAC,QAA8B,EAAE,KAAa,EAAA;AAC5D,IAAA,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE;AAC7C,QAAA,QAAQ,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;AAClB,KAAA;AACD,IAAA,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;AACzB,CAAC;AAED,SAAS,cAAc,CAAC,IAAwB,EAAE,SAAiB,EAAA;AACjE,IAAA,IAAI,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;AACxB,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,EAAE;AAC3C,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACxB,QAAA,IAAI,SAAS,IAAI,OAAO,CAAC,MAAM,CAAC;YAAE,MAAM;AACzC,KAAA;AACD,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,MAAM,CAAI,KAAU,EAAE,KAAa,EAAE,KAAQ,EAAA;AACpD,IAAA,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,EAAE,EAAE;QACzC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzB,KAAA;AACD,IAAA,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;AACvB,CAAC;AAED,SAAS,qBAAqB,CAAC,QAA8B,EAAA;AAC3D,IAAA,MAAM,EAAE,MAAM,EAAE,GAAG,QAAQ,CAAC;IAC5B,IAAI,GAAG,GAAG,MAAM,CAAC;AACjB,IAAA,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAA,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC;YAAE,MAAM;AACnC,KAAA;IACD,IAAI,GAAG,GAAG,MAAM;AAAE,QAAA,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC;AAC1C,CAAC;AAED,SAAS,MAAM,CAAC,MAAgB,EAAE,KAAe,EAAA;AAC/C,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE;QAAE,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,cAAc,CAAC,IAAwB,EAAE,KAAa,EAAA;;;IAG7D,IAAI,KAAK,KAAK,CAAC;AAAE,QAAA,OAAO,IAAI,CAAC;IAE7B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;;;AAI7B,IAAA,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;AAC3B,CAAC;AAED,SAAS,UAAU,CACjB,IAAwB,EACxB,KAAa,EACb,YAAoB,EACpB,UAAkB,EAClB,YAAoB,EACpB,UAAkB,EAAA;;IAGlB,IAAI,KAAK,KAAK,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;IAE9B,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;;AAG7B,IAAA,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;AAAE,QAAA,OAAO,KAAK,CAAC;;;AAIpC,IAAA,QACE,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;AACpC,QAAA,UAAU,KAAK,IAAI,CAAC,WAAW,CAAC;AAChC,QAAA,YAAY,KAAK,IAAI,CAAC,aAAa,CAAC;QACpC,UAAU,MAAM,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,OAAO,CAAC,EAChE;AACJ,CAAC;AAED,SAAS,kBAAkB,CACzB,QAAiB,EACjB,GAAe,EACf,OAMC,EAAA;AAED,IAAA,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;IAC/D,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CAAC;AACH,KAAA;IACD,MAAM,CAAC,GAAW,MAAM,CAAC;AAEzB,IAAA,OAAO,kBAAkB,CACvB,QAAQ,EACR,GAAG,EACH,SAAS,CAAC,IAAI,GAAG,CAAC,EAClB,SAAS,CAAC,MAAM,EAChB,CAAC,EACD,QAAQ,CAAC,IAAI,GAAG,CAAC,EACjB,QAAQ,CAAC,MAAM,EACf,IAAI,EACJ,OAAO,CACR,CAAC;AACJ;;;;"}