{"name": "@atlaskit/tree", "version": "8.6.3", "description": "A React Component for displaying expandable and sortable tree hierarchies", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "repository": "https://bitbucket.org/atlassian/atlassian-frontend", "author": "Atlassian Pty Ltd", "license": "Apache-2.0", "main": "dist/cjs/index.js", "module": "dist/esm/index.js", "module:es2019": "dist/es2019/index.js", "types": "dist/types/index.d.ts", "typesVersions": {">=4.0 <4.5": {"*": ["dist/types-ts4.0/*"]}}, "sideEffects": false, "atlaskit:src": "src/index.ts", "atlassian": {"team": "Confluence Discovery & Organization", "deprecatedAutoEntryPoints": true, "releaseModel": "continuous", "website": {"name": "Tree"}}, "dependencies": {"@babel/runtime": "^7.0.0", "css-box-model": "^1.2.0", "react-beautiful-dnd-next": "11.0.5"}, "peerDependencies": {"react": "^16.8.0"}, "devDependencies": {"@atlaskit/button": "^16.3.0", "@atlaskit/docs": "*", "@atlaskit/icon": "^21.10.0", "@atlaskit/navigation": "^37.0.0", "@atlaskit/spinner": "^15.0.0", "@atlaskit/ssr": "*", "@atlaskit/theme": "^12.2.0", "@atlaskit/visual-regression": "*", "@atlassian/atlassian-frontend-prettier-config-1.0.1": "npm:@atlassian/atlassian-frontend-prettier-config@1.0.1", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "exenv": "^1.2.2", "react-dom": "^16.8.0", "styled-components": "^3.2.6", "typescript": "4.5.5", "wait-for-expect": "^1.2.0"}, "keywords": ["atlaskit", "react", "ui"], "prettier": "@atlassian/atlassian-frontend-prettier-config-1.0.1", "techstack": {"@repo/internal": {"theming": "tokens"}}}