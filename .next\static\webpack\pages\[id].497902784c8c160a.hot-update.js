"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_define_property_mjs__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @swc/helpers/src/_define_property.mjs */ \"./node_modules/@swc/helpers/src/_define_property.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_type_of.mjs */ \"./node_modules/@swc/helpers/src/_type_of.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 简化：移除复杂的快照机制\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref[0], setBackLinks = ref[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var ref, ref1, ref2, ref3, snapshotJsonContent;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                noteId: note === null || note === void 0 ? void 0 : note.id,\n                noteContent: (note === null || note === void 0 ? void 0 : (ref = note.content) === null || ref === void 0 ? void 0 : ref.substring(0, 100)) + \"...\",\n                noteContentLength: (note === null || note === void 0 ? void 0 : (ref1 = note.content) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n            });\n            if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                // 新建笔记：快照为空值\n                console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n                return [\n                    2\n                ];\n            }\n            try {\n                ;\n                snapshotJsonContent = \"\";\n                console.log(\"\\uD83D\\uDD27 快照内容来源分析:\", {\n                    noteContent: ((ref2 = note.content) === null || ref2 === void 0 ? void 0 : ref2.substring(0, 100)) + \"...\",\n                    noteContentLength: ((ref3 = note.content) === null || ref3 === void 0 ? void 0 : ref3.length) || 0,\n                    noteContentType: (0,_swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(note.content)\n                });\n                // 直接使用 note 对象的内容作为快照\n                if (note.content !== undefined && note.content !== null) {\n                    snapshotJsonContent = note.content;\n                    console.log(\"\\uD83D\\uDD27 使用 note 对象内容作为快照\");\n                } else {\n                    snapshotJsonContent = \"\";\n                    console.log(\"\\uD83D\\uDD27 note 内容为空，设置空快照\");\n                }\n                console.log(\"\\uD83D\\uDD27 快照初始化完成:\", {\n                    noteId: note.id,\n                    hasContent: !!snapshotJsonContent,\n                    contentLength: snapshotJsonContent.length,\n                    isJson: snapshotJsonContent.startsWith(\"{\"),\n                    source: (cachedNote === null || cachedNote === void 0 ? void 0 : cachedNote.content) ? \"cache\" : note.content ? \"note\" : \"empty\",\n                    contentPreview: snapshotJsonContent.substring(0, 100) + \"...\"\n                });\n                // 🔧 关键：设置快照和当前编辑器内容\n                setNoteSnapshot(snapshotJsonContent);\n                setCurrentEditorContent(snapshotJsonContent);\n            } catch (error) {\n                console.error(\"JSON快照初始化失败:\", error);\n                // 失败时设置为空快照\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n            }\n            return [\n                2\n            ];\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 当笔记ID变化或内容加载完成时初始化快照\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        // 🔧 修复：只有当 note 存在、有 ID 且内容已加载时才初始化\n        // 避免在内容未完全加载时就初始化快照\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            var ref;\n            console.log(\"\\uD83D\\uDD27 触发快照初始化条件:\", {\n                noteId: note.id,\n                hasContent: note.content !== undefined,\n                contentLength: ((ref = note.content) === null || ref === void 0 ? void 0 : ref.length) || 0\n            });\n            initializeSnapshot();\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        console.log(\"\\uD83D\\uDD27 清空所有快照\");\n        setNoteSnapshot(null);\n        setCurrentEditorContent(\"\");\n        // 🔧 修复：清空后重新初始化快照，确保编辑器显示正确内容\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            setTimeout(function() {\n                initializeSnapshot();\n            }, 0);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 关键修复：保存成功后更新快照为当前内容，而不是清空\n                    // 这样可以保持编辑器状态一致，避免内容丢失\n                    setNoteSnapshot(currentEditorContent);\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB,\n        clearAllSnapshots\n    ]);\n    return(// 🔧 简化：直接返回 note 对象，像 finals 版本一样\n    (0,_swc_helpers_src_define_property_mjs__WEBPACK_IMPORTED_MODULE_12__[\"default\"])({\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        note: note\n    }, \"note\", note));\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_13__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});