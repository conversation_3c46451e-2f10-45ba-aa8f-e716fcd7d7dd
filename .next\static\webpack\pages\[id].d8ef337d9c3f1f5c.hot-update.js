"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        13,\n                        ,\n                        14\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        12\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        12\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    if (!updateNote) return [\n                        3,\n                        11\n                    ];\n                    return [\n                        4,\n                        updateNote(updatedNote)\n                    ];\n                case 10:\n                    _state.sent();\n                    _state.label = 11;\n                case 11:\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 12:\n                    return [\n                        3,\n                        14\n                    ];\n                case 13:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 14:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 简化：移除复杂的快照初始化函数，直接在 useEffect 中处理\n    // 🔧 笔记切换时重置 currentEditorContent，确保 SaveButton 状态正确\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        if (note === null || note === void 0 ? void 0 : note.id) {\n            var ref;\n            console.log(\"\\uD83D\\uDD27 笔记切换，重置 currentEditorContent:\", {\n                noteId: note.id,\n                noteContent: ((ref = note.content) === null || ref === void 0 ? void 0 : ref.substring(0, 50)) || \"(空内容)\"\n            });\n            // 🔧 关键：重置 currentEditorContent 为当前笔记内容\n            // 这样 SaveButton 对比时会显示 view 状态\n            setCurrentEditorContent(note.content || \"\");\n            setNoteSnapshot(note.content || \"\");\n        } else {\n            // 无笔记时清空状态\n            setCurrentEditorContent(\"\");\n            setNoteSnapshot(null);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        console.log(\"\\uD83D\\uDD27 清空所有快照\");\n        setNoteSnapshot(null);\n        setCurrentEditorContent(\"\");\n        // 🔧 简化：清空后直接重置为当前笔记内容\n        if (note === null || note === void 0 ? void 0 : note.id) {\n            setCurrentEditorContent(note.content || \"\");\n            setNoteSnapshot(note.content || \"\");\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 按照用户要求：保存后清除 currentEditorContent\n                    // 因为保存后会重新加载缓存，编辑器直接使用 note.content\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        // 🔧 让编辑器直接使用 note.content\n        note: note,\n        // 🔧 暴露 currentEditorContent 给 SaveButton 用于对比\n        currentEditorContent: currentEditorContent\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_11__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});