{"name": "@babel/helper-wrap-function", "version": "7.19.0", "description": "Helper to wrap functions inside a function call.", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-helper-wrap-function"}, "homepage": "https://babel.dev/docs/en/next/babel-helper-wrap-function", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "dependencies": {"@babel/helper-function-name": "^7.19.0", "@babel/template": "^7.18.10", "@babel/traverse": "^7.19.0", "@babel/types": "^7.19.0"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}