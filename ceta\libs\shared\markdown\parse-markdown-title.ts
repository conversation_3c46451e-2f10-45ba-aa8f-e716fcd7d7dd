import { convertHtmlToMarkdown } from '../html-to-markdown';

/**
 * 检查内容是否为Lexical JSON格式
 */
function isLexicalJSON(content: string): boolean {
    try {
        const parsed = JSON.parse(content);
        return parsed && typeof parsed === 'object' && parsed.root && parsed.root.type === 'root';
    } catch {
        return false;
    }
}

/**
 * 从Lexical JSON中提取标题
 */
function extractTitleFromLexicalJSON(jsonContent: string): string | undefined {
    try {
        const editorState = JSON.parse(jsonContent);

        // 递归查找第一个有文本内容的节点
        function findFirstText(node: any): string {
            if (!node) return '';

            if (node.type === 'text' && node.text) {
                return node.text.trim();
            }

            if (node.children && Array.isArray(node.children)) {
                for (const child of node.children) {
                    const text = findFirstText(child);
                    if (text) return text;
                }
            }

            return '';
        }

        const firstText = findFirstText(editorState.root);

        // 清理标题文本，移除markdown格式符号
        if (firstText) {
            let title = firstText
                .replace(/^#+\s*/, '') // 移除标题标记
                .replace(/\*\*(.*?)\*\*/g, '$1') // 粗体
                .replace(/\*(.*?)\*/g, '$1') // 斜体
                .replace(/`(.*?)`/g, '$1') // 行内代码
                .replace(/!?\[(.*?)\]\(.*?\)/g, '$1') // 链接和图片
                .trim();

            return title.length > 0 ? title : undefined;
        }

        return undefined;
    } catch (error) {
        console.error('Error extracting title from Lexical JSON:', error);
        return undefined;
    }
}

/**
 * Parse the first line as title from HTML, Markdown, or Lexical JSON content
 */
export const parseMarkdownTitle = (content: string) => {
    // 🔧 新逻辑：首先检查是否为Lexical JSON格式
    if (isLexicalJSON(content)) {
        console.log('Detected Lexical JSON format, extracting title...');
        const title = extractTitleFromLexicalJSON(content);
        return {
            content: content, // 保留原始JSON内容
            title: title,
        };
    }

    // 原有逻辑：处理HTML/Markdown格式
    const markdown = convertHtmlToMarkdown(content);

    // Split by newline and get first non-empty line
    const lines = markdown.split('\n').filter(line => line.trim().length > 0);
    const firstLine = lines[0];

    if (!firstLine) {
        return { content, title: undefined };
    }

    // Remove heading markers if present
    let title = firstLine.replace(/^#+\s*/, '').trim();

    // Remove other common markdown formatting
    // Order matters here: remove links first, then other inline formatting
    title = title
        .replace(/!?\[(.*?)\]\(.*?\)/g, '$1') // Remove links and images, keeping the text: [text](url) or ![alt](url) -> text/alt
        .replace(/\*\*(.*?)\*\*/g, '$1') // Bold: **text** -> text
        .replace(/\*(.*?)\*/g, '$1')     // Italic: *text* -> text
        .replace(/__(.*?)__/g, '$1')    // Bold (underscore): __text__ -> text
        .replace(/_(.*?)_/g, '$1')      // Italic (underscore): _text_ -> text
        .replace(/~~(.*?)~~/g, '$1')    // Strikethrough: ~~text~~ -> text
        .replace(/`(.*?)`/g, '$1')      // Inline code: `code` -> code
        .trim();

    return {
        content: content, // 保留原始内容
        title: title.length > 0 ? title : undefined,
    };
};
