/**
 * SaveButton Component
 *
 * Copyright (c) 2025 waycaan
 * Licensed under the MIT License
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 */

import { FC, useState, useEffect, useCallback, useRef } from 'react';
import { Button, makeStyles } from '@material-ui/core';
import {
    EyeIcon,
    DocumentIcon,
    UploadIcon,
    CheckIcon,
    XIcon
} from '@heroicons/react/outline';
import LexicalEditorState from 'libs/web/state/lexical-editor';
import noteCache from 'libs/web/cache/note';

interface SaveButtonProps {
    className?: string;
}

type SyncStatus = 'view' | 'save' | 'syncing' | 'synced' | 'fail';

const useStyles = makeStyles({
    saveButton: {
        minWidth: '80px',
        fontWeight: 'bold',
        textTransform: 'none',
        borderRadius: '8px',
        boxShadow: 'none !important',
        '&:hover': {
            opacity: 0.8,
            boxShadow: 'none !important',
        },
        '&:focus': {
            boxShadow: 'none !important',
        },
        '&:active': {
            boxShadow: 'none !important',
        },
    },
    viewButton: {
        backgroundColor: '#6B7280 !important',
        color: '#FFFFFF !important',
        '&:hover': {
            backgroundColor: '#4B5563 !important',
        },
    },
    saveStateButton: {
        backgroundColor: '#DC2626 !important',
        color: '#FFFFFF !important',
        '&:hover': {
            backgroundColor: '#B91C1C !important',
        },
    },
    syncingButton: {
        backgroundColor: '#3185eb !important',
        color: '#FFFFFF !important',
        '&:hover': {
            backgroundColor: '#2563EB !important',
        },
    },
    syncedButton: {
        backgroundColor: '#FBBF24 !important',
        color: '#000000 !important',
        '&:hover': {
            backgroundColor: '#F59E0B !important',
        },
    },
    failedButton: {
        backgroundColor: '#DC2626 !important',
        color: '#FFFFFF !important',
        '&:hover': {
            backgroundColor: '#B91C1C !important',
        },
    },
});

const SaveButton: FC<SaveButtonProps> = ({ className }) => {
    const classes = useStyles();
    const { syncToServer, note, getEditorState, saveCurrentContent, clearAllSnapshots } = LexicalEditorState.useContainer();
    const [syncStatus, setSyncStatus] = useState<SyncStatus>('view');
    const syncedTimeoutRef = useRef<NodeJS.Timeout | null>(null);
    const syncTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    // 🔧 重构：基于快照对比的状态检测机制
    useEffect(() => {
        if (!note?.id) {
            setSyncStatus('view');
            return;
        }

        const checkSnapshotChanges = () => {
            try {
                const editorState = getEditorState();

                if (editorState.hasChanges) {
                    // 有变化：设置为save状态
                    if (syncStatus !== 'save' && syncStatus !== 'syncing') {
                        setSyncStatus('save');
                    }
                } else {
                    // 无变化：设置为view状态
                    if (syncStatus === 'save') {
                        setSyncStatus('view');
                    }
                }
            } catch (error) {
                console.error('快照对比检查失败:', error);
            }
        };

        // 立即检查一次
        checkSnapshotChanges();

        // 定期检查快照变化
        const interval = setInterval(checkSnapshotChanges, 500);

        return () => {
            clearInterval(interval);
            if (syncedTimeoutRef.current) {
                clearTimeout(syncedTimeoutRef.current);
            }
            if (syncTimeoutRef.current) {
                clearTimeout(syncTimeoutRef.current);
            }
        };
    }, [note, getEditorState, syncStatus]);

    // 手动保存流程
    const handleSave = useCallback(async () => {
        setSyncStatus('syncing');

        if (syncedTimeoutRef.current) {
            clearTimeout(syncedTimeoutRef.current);
        }
        if (syncTimeoutRef.current) {
            clearTimeout(syncTimeoutRef.current);
        }

        // 设置超时保护
        syncTimeoutRef.current = setTimeout(() => {
            setSyncStatus('fail');
            setTimeout(() => {
                setSyncStatus('view');
            }, 2000);
        }, 30000);

        try {
            // 第一步：保存当前内容到IndexedDB
            const saveSuccess = await saveCurrentContent();
            if (!saveSuccess) {
                throw new Error('保存到IndexedDB失败');
            }

            // 第二步：同步到服务器
            const syncSuccess = await syncToServer();
            if (!syncSuccess) {
                throw new Error('同步到服务器失败');
            }

            // 清除超时
            if (syncTimeoutRef.current) {
                clearTimeout(syncTimeoutRef.current);
                syncTimeoutRef.current = null;
            }

            setSyncStatus('synced');

            // 3秒后自动变回view状态
            syncedTimeoutRef.current = setTimeout(() => {
                setSyncStatus('view');
            }, 3000);
        } catch (error) {
            console.error('手动保存失败:', error);
            if (syncTimeoutRef.current) {
                clearTimeout(syncTimeoutRef.current);
                syncTimeoutRef.current = null;
            }
            setSyncStatus('fail');
            setTimeout(() => {
                setSyncStatus('view');
            }, 2000);
        }
    }, [syncToServer, saveCurrentContent]);

    useEffect(() => {
        if (typeof window !== 'undefined') {
            (window as any).saveButtonStatus = syncStatus;
            (window as any).saveButtonAutoSave = handleSave;
            // 🔧 新增：暴露清空快照功能给 useAutoSaveOnLeave
            (window as any).clearSnapshots = clearAllSnapshots;
        }

        return () => {
            if (typeof window !== 'undefined') {
                delete (window as any).saveButtonStatus;
                delete (window as any).saveButtonAutoSave;
                delete (window as any).clearSnapshots;
            }
        };
    }, [syncStatus, handleSave, clearAllSnapshots]);

    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                const target = e.target as HTMLElement;
                const isInEditor = target.closest('.ProseMirror') ||
                                 target.closest('[contenteditable]') ||
                                 target.closest('textarea') ||
                                 target.closest('input');

                if (isInEditor) {
                    e.preventDefault();
                    e.stopPropagation();
                    handleSave();
                }
            }
        };

        document.addEventListener('keydown', handleKeyDown, true);
        return () => document.removeEventListener('keydown', handleKeyDown, true);
    }, [handleSave]);

    const getButtonIcon = () => {
        switch (syncStatus) {
            case 'view':
                return <EyeIcon className="w-4 h-4" />;
            case 'save':
                return <DocumentIcon className="w-4 h-4" />;
            case 'syncing':
                return <UploadIcon className="w-4 h-4 animate-pulse" />;
            case 'synced':
                return <CheckIcon className="w-4 h-4" />;
            case 'fail':
                return <XIcon className="w-4 h-4" />;
            default:
                return <EyeIcon className="w-4 h-4" />;
        }
    };

    const getButtonText = () => {
        switch (syncStatus) {
            case 'view':
                return 'View';
            case 'save':
                return 'Save';
            case 'syncing':
                return 'Syncing...';
            case 'synced':
                return 'Synced';
            case 'fail':
                return 'Failed';
            default:
                return 'View';
        }
    };

    const getButtonClassName = () => {
        const baseClass = `${classes.saveButton}`;
        switch (syncStatus) {
            case 'view':
                return `${baseClass} ${classes.viewButton}`;
            case 'save':
                return `${baseClass} ${classes.saveStateButton}`;
            case 'syncing':
                return `${baseClass} ${classes.syncingButton}`;
            case 'synced':
                return `${baseClass} ${classes.syncedButton}`;
            case 'fail':
                return `${baseClass} ${classes.failedButton}`;
            default:
                return `${baseClass} ${classes.viewButton}`;
        }
    };

    const isButtonDisabled = () => {
        return syncStatus === 'syncing' || syncStatus === 'view';
    };

    return (
        <Button
            variant="contained"
            startIcon={getButtonIcon()}
            onClick={handleSave}
            disabled={isButtonDisabled()}
            className={`${getButtonClassName()} ${className || ''}`}
            size="small"
            data-save-button="true"
        >
            {getButtonText()}
        </Button>
    );
};

export default SaveButton;
