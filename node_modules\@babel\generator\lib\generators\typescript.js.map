{"version": 3, "names": ["TSTypeAnnotation", "node", "token", "space", "optional", "print", "typeAnnotation", "TSTypeParameterInstantiation", "parent", "printList", "params", "type", "length", "TSTypeParameter", "in", "word", "out", "name", "constraint", "default", "TSParameterProperty", "accessibility", "readonly", "_param", "parameter", "TSDeclareFunction", "declare", "_functionHead", "TSDeclareMethod", "_classMethodHead", "TSQualifiedName", "left", "right", "TSCallSignatureDeclaration", "tsPrintSignatureDeclarationBase", "TSConstructSignatureDeclaration", "TSPropertySignature", "initializer", "tsPrintPropertyOrMethodName", "computed", "key", "TSMethodSignature", "kind", "TSIndexSignature", "static", "isStatic", "_parameters", "parameters", "TSAnyKeyword", "TSBigIntKeyword", "TSUnknownKeyword", "TSNumberKeyword", "TSObjectKeyword", "TSBooleanKeyword", "TSStringKeyword", "TSSymbolKeyword", "TSVoidKeyword", "TSUndefinedKeyword", "TSNullKeyword", "TSNeverKeyword", "TSIntrinsicKeyword", "TSThisType", "TSFunctionType", "tsPrintFunctionOrConstructorType", "TSConstructorType", "abstract", "typeParameters", "returnType", "TSTypeReference", "typeName", "TSTypePredicate", "asserts", "parameterName", "TSTypeQuery", "exprName", "TSTypeLiteral", "tsPrintTypeLiteralOrInterfaceBody", "members", "tsPrintBraced", "printer", "indent", "newline", "member", "dedent", "sourceWithOffset", "loc", "rightBrace", "TSArrayType", "elementType", "TSTupleType", "elementTypes", "TSOptionalType", "TSRestType", "TSNamedTupleMember", "label", "TSUnionType", "tsPrintUnionOrIntersectionType", "TSIntersectionType", "sep", "printJoin", "types", "separator", "TSConditionalType", "checkType", "extendsType", "trueType", "falseType", "TSInferType", "typeParameter", "TSParenthesizedType", "TSTypeOperator", "operator", "TSIndexedAccessType", "objectType", "indexType", "TSMappedType", "nameType", "tokenIfPlusMinus", "self", "tok", "TSLiteralType", "literal", "TSExpressionWithTypeArguments", "expression", "TSInterfaceDeclaration", "id", "extends", "extendz", "body", "TSInterfaceBody", "TSTypeAliasDeclaration", "TSAsExpression", "TSTypeAssertion", "TSInstantiationExpression", "TSEnumDeclaration", "const", "isConst", "TSEnumMember", "TSModuleDeclaration", "global", "TSModuleBlock", "TSImportType", "argument", "qualifier", "TSImportEqualsDeclaration", "isExport", "moduleReference", "TSExternalModuleReference", "TSNonNullExpression", "TSExportAssignment", "TSNamespaceExportDeclaration", "tsPrintClassMemberModifiers", "isField", "override"], "sources": ["../../src/generators/typescript.ts"], "sourcesContent": ["import type Printer from \"../printer\";\nimport type * as t from \"@babel/types\";\n\nexport function TSTypeAnnotation(this: Printer, node: t.TSTypeAnnotation) {\n  this.token(\":\");\n  this.space();\n  // @ts-expect-error todo(flow->ts) can this be removed? `.optional` looks to be not existing property\n  if (node.optional) this.token(\"?\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSTypeParameterInstantiation(\n  this: Printer,\n  node: t.TSTypeParameterInstantiation,\n  parent: t.Node,\n): void {\n  this.token(\"<\");\n  this.printList(node.params, node, {});\n  if (parent.type === \"ArrowFunctionExpression\" && node.params.length === 1) {\n    this.token(\",\");\n  }\n  this.token(\">\");\n}\n\nexport { TSTypeParameterInstantiation as TSTypeParameterDeclaration };\n\nexport function TSTypeParameter(this: Printer, node: t.TSTypeParameter) {\n  if (node.in) {\n    this.word(\"in\");\n    this.space();\n  }\n\n  if (node.out) {\n    this.word(\"out\");\n    this.space();\n  }\n\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (node.name as unknown as string)\n      : (node.name as unknown as t.Identifier).name,\n  );\n\n  if (node.constraint) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.print(node.constraint, node);\n  }\n\n  if (node.default) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(node.default, node);\n  }\n}\n\nexport function TSParameterProperty(\n  this: Printer,\n  node: t.TSParameterProperty,\n) {\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n\n  if (node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this._param(node.parameter);\n}\n\nexport function TSDeclareFunction(this: Printer, node: t.TSDeclareFunction) {\n  if (node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this._functionHead(node);\n  this.token(\";\");\n}\n\nexport function TSDeclareMethod(this: Printer, node: t.TSDeclareMethod) {\n  this._classMethodHead(node);\n  this.token(\";\");\n}\n\nexport function TSQualifiedName(this: Printer, node: t.TSQualifiedName) {\n  this.print(node.left, node);\n  this.token(\".\");\n  this.print(node.right, node);\n}\n\nexport function TSCallSignatureDeclaration(\n  this: Printer,\n  node: t.TSCallSignatureDeclaration,\n) {\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSConstructSignatureDeclaration(\n  this: Printer,\n  node: t.TSConstructSignatureDeclaration,\n) {\n  this.word(\"new\");\n  this.space();\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSPropertySignature(\n  this: Printer,\n  node: t.TSPropertySignature,\n) {\n  const { readonly, initializer } = node;\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.print(node.typeAnnotation, node);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer, node);\n  }\n  this.token(\";\");\n}\n\nexport function tsPrintPropertyOrMethodName(\n  this: Printer,\n  node: t.TSPropertySignature | t.TSMethodSignature,\n) {\n  if (node.computed) {\n    this.token(\"[\");\n  }\n  this.print(node.key, node);\n  if (node.computed) {\n    this.token(\"]\");\n  }\n  if (node.optional) {\n    this.token(\"?\");\n  }\n}\n\nexport function TSMethodSignature(this: Printer, node: t.TSMethodSignature) {\n  const { kind } = node;\n  if (kind === \"set\" || kind === \"get\") {\n    this.word(kind);\n    this.space();\n  }\n  this.tsPrintPropertyOrMethodName(node);\n  this.tsPrintSignatureDeclarationBase(node);\n  this.token(\";\");\n}\n\nexport function TSIndexSignature(this: Printer, node: t.TSIndexSignature) {\n  const { readonly, static: isStatic } = node;\n  if (isStatic) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n  this.token(\"[\");\n  this._parameters(node.parameters, node);\n  this.token(\"]\");\n  this.print(node.typeAnnotation, node);\n  this.token(\";\");\n}\n\nexport function TSAnyKeyword(this: Printer) {\n  this.word(\"any\");\n}\nexport function TSBigIntKeyword(this: Printer) {\n  this.word(\"bigint\");\n}\nexport function TSUnknownKeyword(this: Printer) {\n  this.word(\"unknown\");\n}\nexport function TSNumberKeyword(this: Printer) {\n  this.word(\"number\");\n}\nexport function TSObjectKeyword(this: Printer) {\n  this.word(\"object\");\n}\nexport function TSBooleanKeyword(this: Printer) {\n  this.word(\"boolean\");\n}\nexport function TSStringKeyword(this: Printer) {\n  this.word(\"string\");\n}\nexport function TSSymbolKeyword(this: Printer) {\n  this.word(\"symbol\");\n}\nexport function TSVoidKeyword(this: Printer) {\n  this.word(\"void\");\n}\nexport function TSUndefinedKeyword(this: Printer) {\n  this.word(\"undefined\");\n}\nexport function TSNullKeyword(this: Printer) {\n  this.word(\"null\");\n}\nexport function TSNeverKeyword(this: Printer) {\n  this.word(\"never\");\n}\nexport function TSIntrinsicKeyword(this: Printer) {\n  this.word(\"intrinsic\");\n}\n\nexport function TSThisType(this: Printer) {\n  this.word(\"this\");\n}\n\nexport function TSFunctionType(this: Printer, node: t.TSFunctionType) {\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function TSConstructorType(this: Printer, node: t.TSConstructorType) {\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  this.word(\"new\");\n  this.space();\n  this.tsPrintFunctionOrConstructorType(node);\n}\n\nexport function tsPrintFunctionOrConstructorType(\n  this: Printer,\n  node: t.TSFunctionType | t.TSConstructorType,\n) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.params\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.parameters;\n  this.print(typeParameters, node);\n  this.token(\"(\");\n  this._parameters(parameters, node);\n  this.token(\")\");\n  this.space();\n  this.token(\"=>\");\n  this.space();\n  const returnType = process.env.BABEL_8_BREAKING\n    ? // @ts-ignore(Babel 7 vs Babel 8) Babel 8 AST shape\n      node.returnType\n    : // @ts-ignore(Babel 7 vs Babel 8) Babel 7 AST shape\n      node.typeAnnotation;\n  this.print(returnType.typeAnnotation, node);\n}\n\nexport function TSTypeReference(this: Printer, node: t.TSTypeReference) {\n  this.print(node.typeName, node, true);\n  this.print(node.typeParameters, node, true);\n}\n\nexport function TSTypePredicate(this: Printer, node: t.TSTypePredicate) {\n  if (node.asserts) {\n    this.word(\"asserts\");\n    this.space();\n  }\n  this.print(node.parameterName);\n  if (node.typeAnnotation) {\n    this.space();\n    this.word(\"is\");\n    this.space();\n    this.print(node.typeAnnotation.typeAnnotation);\n  }\n}\n\nexport function TSTypeQuery(this: Printer, node: t.TSTypeQuery) {\n  this.word(\"typeof\");\n  this.space();\n  this.print(node.exprName);\n\n  if (node.typeParameters) {\n    this.print(node.typeParameters, node);\n  }\n}\n\nexport function TSTypeLiteral(this: Printer, node: t.TSTypeLiteral) {\n  this.tsPrintTypeLiteralOrInterfaceBody(node.members, node);\n}\n\nexport function tsPrintTypeLiteralOrInterfaceBody(\n  this: Printer,\n  members: t.TSTypeElement[],\n  node: t.TSType | t.TSInterfaceBody,\n) {\n  tsPrintBraced(this, members, node);\n}\n\nfunction tsPrintBraced(printer: Printer, members: t.Node[], node: t.Node) {\n  printer.token(\"{\");\n  if (members.length) {\n    printer.indent();\n    printer.newline();\n    for (const member of members) {\n      printer.print(member, node);\n      //this.token(sep);\n      printer.newline();\n    }\n    printer.dedent();\n  }\n\n  printer.sourceWithOffset(\"end\", node.loc, 0, -1);\n\n  printer.rightBrace();\n}\n\nexport function TSArrayType(this: Printer, node: t.TSArrayType) {\n  this.print(node.elementType, node, true);\n\n  this.token(\"[]\");\n}\n\nexport function TSTupleType(this: Printer, node: t.TSTupleType) {\n  this.token(\"[\");\n  this.printList(node.elementTypes, node);\n  this.token(\"]\");\n}\n\nexport function TSOptionalType(this: Printer, node: t.TSOptionalType) {\n  this.print(node.typeAnnotation, node);\n  this.token(\"?\");\n}\n\nexport function TSRestType(this: Printer, node: t.TSRestType) {\n  this.token(\"...\");\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSNamedTupleMember(this: Printer, node: t.TSNamedTupleMember) {\n  this.print(node.label, node);\n  if (node.optional) this.token(\"?\");\n  this.token(\":\");\n  this.space();\n  this.print(node.elementType, node);\n}\n\nexport function TSUnionType(this: Printer, node: t.TSUnionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"|\");\n}\n\nexport function TSIntersectionType(this: Printer, node: t.TSIntersectionType) {\n  tsPrintUnionOrIntersectionType(this, node, \"&\");\n}\n\nfunction tsPrintUnionOrIntersectionType(\n  printer: Printer,\n  node: t.TSUnionType | t.TSIntersectionType,\n  sep: \"|\" | \"&\",\n) {\n  printer.printJoin(node.types, node, {\n    separator() {\n      this.space();\n      this.token(sep);\n      this.space();\n    },\n  });\n}\n\nexport function TSConditionalType(this: Printer, node: t.TSConditionalType) {\n  this.print(node.checkType);\n  this.space();\n  this.word(\"extends\");\n  this.space();\n  this.print(node.extendsType);\n  this.space();\n  this.token(\"?\");\n  this.space();\n  this.print(node.trueType);\n  this.space();\n  this.token(\":\");\n  this.space();\n  this.print(node.falseType);\n}\n\nexport function TSInferType(this: Printer, node: t.TSInferType) {\n  this.token(\"infer\");\n  this.space();\n  this.print(node.typeParameter);\n}\n\nexport function TSParenthesizedType(\n  this: Printer,\n  node: t.TSParenthesizedType,\n) {\n  this.token(\"(\");\n  this.print(node.typeAnnotation, node);\n  this.token(\")\");\n}\n\nexport function TSTypeOperator(this: Printer, node: t.TSTypeOperator) {\n  this.word(node.operator);\n  this.space();\n  this.print(node.typeAnnotation, node);\n}\n\nexport function TSIndexedAccessType(\n  this: Printer,\n  node: t.TSIndexedAccessType,\n) {\n  this.print(node.objectType, node, true);\n  this.token(\"[\");\n  this.print(node.indexType, node);\n  this.token(\"]\");\n}\n\nexport function TSMappedType(this: Printer, node: t.TSMappedType) {\n  const { nameType, optional, readonly, typeParameter } = node;\n  this.token(\"{\");\n  this.space();\n  if (readonly) {\n    tokenIfPlusMinus(this, readonly);\n    this.word(\"readonly\");\n    this.space();\n  }\n\n  this.token(\"[\");\n  this.word(\n    !process.env.BABEL_8_BREAKING\n      ? (typeParameter.name as unknown as string)\n      : (typeParameter.name as unknown as t.Identifier).name,\n  );\n  this.space();\n  this.word(\"in\");\n  this.space();\n  this.print(typeParameter.constraint, typeParameter);\n\n  if (nameType) {\n    this.space();\n    this.word(\"as\");\n    this.space();\n    this.print(nameType, node);\n  }\n\n  this.token(\"]\");\n\n  if (optional) {\n    tokenIfPlusMinus(this, optional);\n    this.token(\"?\");\n  }\n  this.token(\":\");\n  this.space();\n  this.print(node.typeAnnotation, node);\n  this.space();\n  this.token(\"}\");\n}\n\nfunction tokenIfPlusMinus(self: Printer, tok: true | \"+\" | \"-\") {\n  if (tok !== true) {\n    self.token(tok);\n  }\n}\n\nexport function TSLiteralType(this: Printer, node: t.TSLiteralType) {\n  this.print(node.literal, node);\n}\n\nexport function TSExpressionWithTypeArguments(\n  this: Printer,\n  node: t.TSExpressionWithTypeArguments,\n) {\n  this.print(node.expression, node);\n  this.print(node.typeParameters, node);\n}\n\nexport function TSInterfaceDeclaration(\n  this: Printer,\n  node: t.TSInterfaceDeclaration,\n) {\n  const { declare, id, typeParameters, extends: extendz, body } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"interface\");\n  this.space();\n  this.print(id, node);\n  this.print(typeParameters, node);\n  if (extendz?.length) {\n    this.space();\n    this.word(\"extends\");\n    this.space();\n    this.printList(extendz, node);\n  }\n  this.space();\n  this.print(body, node);\n}\n\nexport function TSInterfaceBody(this: Printer, node: t.TSInterfaceBody) {\n  this.tsPrintTypeLiteralOrInterfaceBody(node.body, node);\n}\n\nexport function TSTypeAliasDeclaration(\n  this: Printer,\n  node: t.TSTypeAliasDeclaration,\n) {\n  const { declare, id, typeParameters, typeAnnotation } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  this.word(\"type\");\n  this.space();\n  this.print(id, node);\n  this.print(typeParameters, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(typeAnnotation, node);\n  this.token(\";\");\n}\n\nexport function TSAsExpression(this: Printer, node: t.TSAsExpression) {\n  const { expression, typeAnnotation } = node;\n  this.print(expression, node);\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.print(typeAnnotation, node);\n}\n\nexport function TSTypeAssertion(this: Printer, node: t.TSTypeAssertion) {\n  const { typeAnnotation, expression } = node;\n  this.token(\"<\");\n  this.print(typeAnnotation, node);\n  this.token(\">\");\n  this.space();\n  this.print(expression, node);\n}\n\nexport function TSInstantiationExpression(\n  this: Printer,\n  node: t.TSInstantiationExpression,\n) {\n  this.print(node.expression, node);\n  this.print(node.typeParameters, node);\n}\n\nexport function TSEnumDeclaration(this: Printer, node: t.TSEnumDeclaration) {\n  const { declare, const: isConst, id, members } = node;\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (isConst) {\n    this.word(\"const\");\n    this.space();\n  }\n  this.word(\"enum\");\n  this.space();\n  this.print(id, node);\n  this.space();\n  tsPrintBraced(this, members, node);\n}\n\nexport function TSEnumMember(this: Printer, node: t.TSEnumMember) {\n  const { id, initializer } = node;\n  this.print(id, node);\n  if (initializer) {\n    this.space();\n    this.token(\"=\");\n    this.space();\n    this.print(initializer, node);\n  }\n  this.token(\",\");\n}\n\nexport function TSModuleDeclaration(\n  this: Printer,\n  node: t.TSModuleDeclaration,\n) {\n  const { declare, id } = node;\n\n  if (declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n\n  if (!node.global) {\n    this.word(id.type === \"Identifier\" ? \"namespace\" : \"module\");\n    this.space();\n  }\n  this.print(id, node);\n\n  if (!node.body) {\n    this.token(\";\");\n    return;\n  }\n\n  let body = node.body;\n  while (body.type === \"TSModuleDeclaration\") {\n    this.token(\".\");\n    this.print(body.id, body);\n    body = body.body;\n  }\n\n  this.space();\n  this.print(body, node);\n}\n\nexport function TSModuleBlock(this: Printer, node: t.TSModuleBlock) {\n  tsPrintBraced(this, node.body, node);\n}\n\nexport function TSImportType(this: Printer, node: t.TSImportType) {\n  const { argument, qualifier, typeParameters } = node;\n  this.word(\"import\");\n  this.token(\"(\");\n  this.print(argument, node);\n  this.token(\")\");\n  if (qualifier) {\n    this.token(\".\");\n    this.print(qualifier, node);\n  }\n  if (typeParameters) {\n    this.print(typeParameters, node);\n  }\n}\n\nexport function TSImportEqualsDeclaration(\n  this: Printer,\n  node: t.TSImportEqualsDeclaration,\n) {\n  const { isExport, id, moduleReference } = node;\n  if (isExport) {\n    this.word(\"export\");\n    this.space();\n  }\n  this.word(\"import\");\n  this.space();\n  this.print(id, node);\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(moduleReference, node);\n  this.token(\";\");\n}\n\nexport function TSExternalModuleReference(\n  this: Printer,\n  node: t.TSExternalModuleReference,\n) {\n  this.token(\"require(\");\n  this.print(node.expression, node);\n  this.token(\")\");\n}\n\nexport function TSNonNullExpression(\n  this: Printer,\n  node: t.TSNonNullExpression,\n) {\n  this.print(node.expression, node);\n  this.token(\"!\");\n}\n\nexport function TSExportAssignment(this: Printer, node: t.TSExportAssignment) {\n  this.word(\"export\");\n  this.space();\n  this.token(\"=\");\n  this.space();\n  this.print(node.expression, node);\n  this.token(\";\");\n}\n\nexport function TSNamespaceExportDeclaration(\n  this: Printer,\n  node: t.TSNamespaceExportDeclaration,\n) {\n  this.word(\"export\");\n  this.space();\n  this.word(\"as\");\n  this.space();\n  this.word(\"namespace\");\n  this.space();\n  this.print(node.id, node);\n}\n\nexport function tsPrintSignatureDeclarationBase(this: Printer, node: any) {\n  const { typeParameters } = node;\n  const parameters = process.env.BABEL_8_BREAKING\n    ? node.params\n    : node.parameters;\n  this.print(typeParameters, node);\n  this.token(\"(\");\n  this._parameters(parameters, node);\n  this.token(\")\");\n  const returnType = process.env.BABEL_8_BREAKING\n    ? node.returnType\n    : node.typeAnnotation;\n  this.print(returnType, node);\n}\n\nexport function tsPrintClassMemberModifiers(\n  this: Printer,\n  node:\n    | t.ClassProperty\n    | t.ClassAccessorProperty\n    | t.ClassMethod\n    | t.ClassPrivateMethod\n    | t.TSDeclareMethod,\n) {\n  const isField =\n    node.type === \"ClassAccessorProperty\" || node.type === \"ClassProperty\";\n  if (isField && node.declare) {\n    this.word(\"declare\");\n    this.space();\n  }\n  if (node.accessibility) {\n    this.word(node.accessibility);\n    this.space();\n  }\n  if (node.static) {\n    this.word(\"static\");\n    this.space();\n  }\n  if (node.override) {\n    this.word(\"override\");\n    this.space();\n  }\n  if (node.abstract) {\n    this.word(\"abstract\");\n    this.space();\n  }\n  if (isField && node.readonly) {\n    this.word(\"readonly\");\n    this.space();\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,SAASA,gBAAT,CAAyCC,IAAzC,EAAmE;EACxE,KAAKC,SAAL;EACA,KAAKC,KAAL;EAEA,IAAIF,IAAI,CAACG,QAAT,EAAmB,KAAKF,SAAL;EACnB,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;AACD;;AAEM,SAASM,4BAAT,CAELN,IAFK,EAGLO,MAHK,EAIC;EACN,KAAKN,SAAL;EACA,KAAKO,SAAL,CAAeR,IAAI,CAACS,MAApB,EAA4BT,IAA5B,EAAkC,EAAlC;;EACA,IAAIO,MAAM,CAACG,IAAP,KAAgB,yBAAhB,IAA6CV,IAAI,CAACS,MAAL,CAAYE,MAAZ,KAAuB,CAAxE,EAA2E;IACzE,KAAKV,SAAL;EACD;;EACD,KAAKA,SAAL;AACD;;AAIM,SAASW,eAAT,CAAwCZ,IAAxC,EAAiE;EACtE,IAAIA,IAAI,CAACa,EAAT,EAAa;IACX,KAAKC,IAAL,CAAU,IAAV;IACA,KAAKZ,KAAL;EACD;;EAED,IAAIF,IAAI,CAACe,GAAT,EAAc;IACZ,KAAKD,IAAL,CAAU,KAAV;IACA,KAAKZ,KAAL;EACD;;EAED,KAAKY,IAAL,CAEOd,IAAI,CAACgB,IAFZ;;EAMA,IAAIhB,IAAI,CAACiB,UAAT,EAAqB;IACnB,KAAKf,KAAL;IACA,KAAKY,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACiB,UAAhB,EAA4BjB,IAA5B;EACD;;EAED,IAAIA,IAAI,CAACkB,OAAT,EAAkB;IAChB,KAAKhB,KAAL;IACA,KAAKD,SAAL;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACkB,OAAhB,EAAyBlB,IAAzB;EACD;AACF;;AAEM,SAASmB,mBAAT,CAELnB,IAFK,EAGL;EACA,IAAIA,IAAI,CAACoB,aAAT,EAAwB;IACtB,KAAKN,IAAL,CAAUd,IAAI,CAACoB,aAAf;IACA,KAAKlB,KAAL;EACD;;EAED,IAAIF,IAAI,CAACqB,QAAT,EAAmB;IACjB,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EAED,KAAKoB,MAAL,CAAYtB,IAAI,CAACuB,SAAjB;AACD;;AAEM,SAASC,iBAAT,CAA0CxB,IAA1C,EAAqE;EAC1E,IAAIA,IAAI,CAACyB,OAAT,EAAkB;IAChB,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKwB,aAAL,CAAmB1B,IAAnB;;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS0B,eAAT,CAAwC3B,IAAxC,EAAiE;EACtE,KAAK4B,gBAAL,CAAsB5B,IAAtB;;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS4B,eAAT,CAAwC7B,IAAxC,EAAiE;EACtE,KAAKI,KAAL,CAAWJ,IAAI,CAAC8B,IAAhB,EAAsB9B,IAAtB;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAAC+B,KAAhB,EAAuB/B,IAAvB;AACD;;AAEM,SAASgC,0BAAT,CAELhC,IAFK,EAGL;EACA,KAAKiC,+BAAL,CAAqCjC,IAArC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASiC,+BAAT,CAELlC,IAFK,EAGL;EACA,KAAKc,IAAL,CAAU,KAAV;EACA,KAAKZ,KAAL;EACA,KAAK+B,+BAAL,CAAqCjC,IAArC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASkC,mBAAT,CAELnC,IAFK,EAGL;EACA,MAAM;IAAEqB,QAAF;IAAYe;EAAZ,IAA4BpC,IAAlC;;EACA,IAAIqB,QAAJ,EAAc;IACZ,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKmC,2BAAL,CAAiCrC,IAAjC;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;;EACA,IAAIoC,WAAJ,EAAiB;IACf,KAAKlC,KAAL;IACA,KAAKD,SAAL;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWgC,WAAX,EAAwBpC,IAAxB;EACD;;EACD,KAAKC,SAAL;AACD;;AAEM,SAASoC,2BAAT,CAELrC,IAFK,EAGL;EACA,IAAIA,IAAI,CAACsC,QAAT,EAAmB;IACjB,KAAKrC,SAAL;EACD;;EACD,KAAKG,KAAL,CAAWJ,IAAI,CAACuC,GAAhB,EAAqBvC,IAArB;;EACA,IAAIA,IAAI,CAACsC,QAAT,EAAmB;IACjB,KAAKrC,SAAL;EACD;;EACD,IAAID,IAAI,CAACG,QAAT,EAAmB;IACjB,KAAKF,SAAL;EACD;AACF;;AAEM,SAASuC,iBAAT,CAA0CxC,IAA1C,EAAqE;EAC1E,MAAM;IAAEyC;EAAF,IAAWzC,IAAjB;;EACA,IAAIyC,IAAI,KAAK,KAAT,IAAkBA,IAAI,KAAK,KAA/B,EAAsC;IACpC,KAAK3B,IAAL,CAAU2B,IAAV;IACA,KAAKvC,KAAL;EACD;;EACD,KAAKmC,2BAAL,CAAiCrC,IAAjC;EACA,KAAKiC,+BAAL,CAAqCjC,IAArC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASyC,gBAAT,CAAyC1C,IAAzC,EAAmE;EACxE,MAAM;IAAEqB,QAAF;IAAYsB,MAAM,EAAEC;EAApB,IAAiC5C,IAAvC;;EACA,IAAI4C,QAAJ,EAAc;IACZ,KAAK9B,IAAL,CAAU,QAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAImB,QAAJ,EAAc;IACZ,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKD,SAAL;;EACA,KAAK4C,WAAL,CAAiB7C,IAAI,CAAC8C,UAAtB,EAAkC9C,IAAlC;;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS8C,YAAT,GAAqC;EAC1C,KAAKjC,IAAL,CAAU,KAAV;AACD;;AACM,SAASkC,eAAT,GAAwC;EAC7C,KAAKlC,IAAL,CAAU,QAAV;AACD;;AACM,SAASmC,gBAAT,GAAyC;EAC9C,KAAKnC,IAAL,CAAU,SAAV;AACD;;AACM,SAASoC,eAAT,GAAwC;EAC7C,KAAKpC,IAAL,CAAU,QAAV;AACD;;AACM,SAASqC,eAAT,GAAwC;EAC7C,KAAKrC,IAAL,CAAU,QAAV;AACD;;AACM,SAASsC,gBAAT,GAAyC;EAC9C,KAAKtC,IAAL,CAAU,SAAV;AACD;;AACM,SAASuC,eAAT,GAAwC;EAC7C,KAAKvC,IAAL,CAAU,QAAV;AACD;;AACM,SAASwC,eAAT,GAAwC;EAC7C,KAAKxC,IAAL,CAAU,QAAV;AACD;;AACM,SAASyC,aAAT,GAAsC;EAC3C,KAAKzC,IAAL,CAAU,MAAV;AACD;;AACM,SAAS0C,kBAAT,GAA2C;EAChD,KAAK1C,IAAL,CAAU,WAAV;AACD;;AACM,SAAS2C,aAAT,GAAsC;EAC3C,KAAK3C,IAAL,CAAU,MAAV;AACD;;AACM,SAAS4C,cAAT,GAAuC;EAC5C,KAAK5C,IAAL,CAAU,OAAV;AACD;;AACM,SAAS6C,kBAAT,GAA2C;EAChD,KAAK7C,IAAL,CAAU,WAAV;AACD;;AAEM,SAAS8C,UAAT,GAAmC;EACxC,KAAK9C,IAAL,CAAU,MAAV;AACD;;AAEM,SAAS+C,cAAT,CAAuC7D,IAAvC,EAA+D;EACpE,KAAK8D,gCAAL,CAAsC9D,IAAtC;AACD;;AAEM,SAAS+D,iBAAT,CAA0C/D,IAA1C,EAAqE;EAC1E,IAAIA,IAAI,CAACgE,QAAT,EAAmB;IACjB,KAAKlD,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,KAAV;EACA,KAAKZ,KAAL;EACA,KAAK4D,gCAAL,CAAsC9D,IAAtC;AACD;;AAEM,SAAS8D,gCAAT,CAEL9D,IAFK,EAGL;EACA,MAAM;IAAEiE;EAAF,IAAqBjE,IAA3B;EACA,MAAM8C,UAAU,GAIZ9C,IAAI,CAAC8C,UAJT;EAKA,KAAK1C,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACA,KAAKC,SAAL;;EACA,KAAK4C,WAAL,CAAiBC,UAAjB,EAA6B9C,IAA7B;;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL;EACA,KAAKD,KAAL,CAAW,IAAX;EACA,KAAKC,KAAL;EACA,MAAMgE,UAAU,GAIZlE,IAAI,CAACK,cAJT;EAKA,KAAKD,KAAL,CAAW8D,UAAU,CAAC7D,cAAtB,EAAsCL,IAAtC;AACD;;AAEM,SAASmE,eAAT,CAAwCnE,IAAxC,EAAiE;EACtE,KAAKI,KAAL,CAAWJ,IAAI,CAACoE,QAAhB,EAA0BpE,IAA1B,EAAgC,IAAhC;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC,EAAsC,IAAtC;AACD;;AAEM,SAASqE,eAAT,CAAwCrE,IAAxC,EAAiE;EACtE,IAAIA,IAAI,CAACsE,OAAT,EAAkB;IAChB,KAAKxD,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKE,KAAL,CAAWJ,IAAI,CAACuE,aAAhB;;EACA,IAAIvE,IAAI,CAACK,cAAT,EAAyB;IACvB,KAAKH,KAAL;IACA,KAAKY,IAAL,CAAU,IAAV;IACA,KAAKZ,KAAL;IACA,KAAKE,KAAL,CAAWJ,IAAI,CAACK,cAAL,CAAoBA,cAA/B;EACD;AACF;;AAEM,SAASmE,WAAT,CAAoCxE,IAApC,EAAyD;EAC9D,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACyE,QAAhB;;EAEA,IAAIzE,IAAI,CAACiE,cAAT,EAAyB;IACvB,KAAK7D,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC;EACD;AACF;;AAEM,SAAS0E,aAAT,CAAsC1E,IAAtC,EAA6D;EAClE,KAAK2E,iCAAL,CAAuC3E,IAAI,CAAC4E,OAA5C,EAAqD5E,IAArD;AACD;;AAEM,SAAS2E,iCAAT,CAELC,OAFK,EAGL5E,IAHK,EAIL;EACA6E,aAAa,CAAC,IAAD,EAAOD,OAAP,EAAgB5E,IAAhB,CAAb;AACD;;AAED,SAAS6E,aAAT,CAAuBC,OAAvB,EAAyCF,OAAzC,EAA4D5E,IAA5D,EAA0E;EACxE8E,OAAO,CAAC7E,KAAR,CAAc,GAAd;;EACA,IAAI2E,OAAO,CAACjE,MAAZ,EAAoB;IAClBmE,OAAO,CAACC,MAAR;IACAD,OAAO,CAACE,OAAR;;IACA,KAAK,MAAMC,MAAX,IAAqBL,OAArB,EAA8B;MAC5BE,OAAO,CAAC1E,KAAR,CAAc6E,MAAd,EAAsBjF,IAAtB;MAEA8E,OAAO,CAACE,OAAR;IACD;;IACDF,OAAO,CAACI,MAAR;EACD;;EAEDJ,OAAO,CAACK,gBAAR,CAAyB,KAAzB,EAAgCnF,IAAI,CAACoF,GAArC,EAA0C,CAA1C,EAA6C,CAAC,CAA9C;EAEAN,OAAO,CAACO,UAAR;AACD;;AAEM,SAASC,WAAT,CAAoCtF,IAApC,EAAyD;EAC9D,KAAKI,KAAL,CAAWJ,IAAI,CAACuF,WAAhB,EAA6BvF,IAA7B,EAAmC,IAAnC;EAEA,KAAKC,KAAL,CAAW,IAAX;AACD;;AAEM,SAASuF,WAAT,CAAoCxF,IAApC,EAAyD;EAC9D,KAAKC,SAAL;EACA,KAAKO,SAAL,CAAeR,IAAI,CAACyF,YAApB,EAAkCzF,IAAlC;EACA,KAAKC,SAAL;AACD;;AAEM,SAASyF,cAAT,CAAuC1F,IAAvC,EAA+D;EACpE,KAAKI,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS0F,UAAT,CAAmC3F,IAAnC,EAAuD;EAC5D,KAAKC,KAAL,CAAW,KAAX;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;AACD;;AAEM,SAAS4F,kBAAT,CAA2C5F,IAA3C,EAAuE;EAC5E,KAAKI,KAAL,CAAWJ,IAAI,CAAC6F,KAAhB,EAAuB7F,IAAvB;EACA,IAAIA,IAAI,CAACG,QAAT,EAAmB,KAAKF,SAAL;EACnB,KAAKA,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACuF,WAAhB,EAA6BvF,IAA7B;AACD;;AAEM,SAAS8F,WAAT,CAAoC9F,IAApC,EAAyD;EAC9D+F,8BAA8B,CAAC,IAAD,EAAO/F,IAAP,EAAa,GAAb,CAA9B;AACD;;AAEM,SAASgG,kBAAT,CAA2ChG,IAA3C,EAAuE;EAC5E+F,8BAA8B,CAAC,IAAD,EAAO/F,IAAP,EAAa,GAAb,CAA9B;AACD;;AAED,SAAS+F,8BAAT,CACEjB,OADF,EAEE9E,IAFF,EAGEiG,GAHF,EAIE;EACAnB,OAAO,CAACoB,SAAR,CAAkBlG,IAAI,CAACmG,KAAvB,EAA8BnG,IAA9B,EAAoC;IAClCoG,SAAS,GAAG;MACV,KAAKlG,KAAL;MACA,KAAKD,KAAL,CAAWgG,GAAX;MACA,KAAK/F,KAAL;IACD;;EALiC,CAApC;AAOD;;AAEM,SAASmG,iBAAT,CAA0CrG,IAA1C,EAAqE;EAC1E,KAAKI,KAAL,CAAWJ,IAAI,CAACsG,SAAhB;EACA,KAAKpG,KAAL;EACA,KAAKY,IAAL,CAAU,SAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACuG,WAAhB;EACA,KAAKrG,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACwG,QAAhB;EACA,KAAKtG,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACyG,SAAhB;AACD;;AAEM,SAASC,WAAT,CAAoC1G,IAApC,EAAyD;EAC9D,KAAKC,KAAL,CAAW,OAAX;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAAC2G,aAAhB;AACD;;AAEM,SAASC,mBAAT,CAEL5G,IAFK,EAGL;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKC,SAAL;AACD;;AAEM,SAAS4G,cAAT,CAAuC7G,IAAvC,EAA+D;EACpE,KAAKc,IAAL,CAAUd,IAAI,CAAC8G,QAAf;EACA,KAAK5G,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;AACD;;AAEM,SAAS+G,mBAAT,CAEL/G,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACgH,UAAhB,EAA4BhH,IAA5B,EAAkC,IAAlC;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAACiH,SAAhB,EAA2BjH,IAA3B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASiH,YAAT,CAAqClH,IAArC,EAA2D;EAChE,MAAM;IAAEmH,QAAF;IAAYhH,QAAZ;IAAsBkB,QAAtB;IAAgCsF;EAAhC,IAAkD3G,IAAxD;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL;;EACA,IAAImB,QAAJ,EAAc;IACZ+F,gBAAgB,CAAC,IAAD,EAAO/F,QAAP,CAAhB;IACA,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EAED,KAAKD,SAAL;EACA,KAAKa,IAAL,CAEO6F,aAAa,CAAC3F,IAFrB;EAKA,KAAKd,KAAL;EACA,KAAKY,IAAL,CAAU,IAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWuG,aAAa,CAAC1F,UAAzB,EAAqC0F,aAArC;;EAEA,IAAIQ,QAAJ,EAAc;IACZ,KAAKjH,KAAL;IACA,KAAKY,IAAL,CAAU,IAAV;IACA,KAAKZ,KAAL;IACA,KAAKE,KAAL,CAAW+G,QAAX,EAAqBnH,IAArB;EACD;;EAED,KAAKC,SAAL;;EAEA,IAAIE,QAAJ,EAAc;IACZiH,gBAAgB,CAAC,IAAD,EAAOjH,QAAP,CAAhB;IACA,KAAKF,SAAL;EACD;;EACD,KAAKA,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAACK,cAAhB,EAAgCL,IAAhC;EACA,KAAKE,KAAL;EACA,KAAKD,SAAL;AACD;;AAED,SAASmH,gBAAT,CAA0BC,IAA1B,EAAyCC,GAAzC,EAAgE;EAC9D,IAAIA,GAAG,KAAK,IAAZ,EAAkB;IAChBD,IAAI,CAACpH,KAAL,CAAWqH,GAAX;EACD;AACF;;AAEM,SAASC,aAAT,CAAsCvH,IAAtC,EAA6D;EAClE,KAAKI,KAAL,CAAWJ,IAAI,CAACwH,OAAhB,EAAyBxH,IAAzB;AACD;;AAEM,SAASyH,6BAAT,CAELzH,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAAC0H,UAAhB,EAA4B1H,IAA5B;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC;AACD;;AAEM,SAAS2H,sBAAT,CAEL3H,IAFK,EAGL;EACA,MAAM;IAAEyB,OAAF;IAAWmG,EAAX;IAAe3D,cAAf;IAA+B4D,OAAO,EAAEC,OAAxC;IAAiDC;EAAjD,IAA0D/H,IAAhE;;EACA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,WAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWwH,EAAX,EAAe5H,IAAf;EACA,KAAKI,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;;EACA,IAAI8H,OAAJ,YAAIA,OAAO,CAAEnH,MAAb,EAAqB;IACnB,KAAKT,KAAL;IACA,KAAKY,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;IACA,KAAKM,SAAL,CAAesH,OAAf,EAAwB9H,IAAxB;EACD;;EACD,KAAKE,KAAL;EACA,KAAKE,KAAL,CAAW2H,IAAX,EAAiB/H,IAAjB;AACD;;AAEM,SAASgI,eAAT,CAAwChI,IAAxC,EAAiE;EACtE,KAAK2E,iCAAL,CAAuC3E,IAAI,CAAC+H,IAA5C,EAAkD/H,IAAlD;AACD;;AAEM,SAASiI,sBAAT,CAELjI,IAFK,EAGL;EACA,MAAM;IAAEyB,OAAF;IAAWmG,EAAX;IAAe3D,cAAf;IAA+B5D;EAA/B,IAAkDL,IAAxD;;EACA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,MAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWwH,EAAX,EAAe5H,IAAf;EACA,KAAKI,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACA,KAAKE,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWC,cAAX,EAA2BL,IAA3B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASiI,cAAT,CAAuClI,IAAvC,EAA+D;EACpE,MAAM;IAAE0H,UAAF;IAAcrH;EAAd,IAAiCL,IAAvC;EACA,KAAKI,KAAL,CAAWsH,UAAX,EAAuB1H,IAAvB;EACA,KAAKE,KAAL;EACA,KAAKY,IAAL,CAAU,IAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWC,cAAX,EAA2BL,IAA3B;AACD;;AAEM,SAASmI,eAAT,CAAwCnI,IAAxC,EAAiE;EACtE,MAAM;IAAEK,cAAF;IAAkBqH;EAAlB,IAAiC1H,IAAvC;EACA,KAAKC,SAAL;EACA,KAAKG,KAAL,CAAWC,cAAX,EAA2BL,IAA3B;EACA,KAAKC,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWsH,UAAX,EAAuB1H,IAAvB;AACD;;AAEM,SAASoI,yBAAT,CAELpI,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAAC0H,UAAhB,EAA4B1H,IAA5B;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAACiE,cAAhB,EAAgCjE,IAAhC;AACD;;AAEM,SAASqI,iBAAT,CAA0CrI,IAA1C,EAAqE;EAC1E,MAAM;IAAEyB,OAAF;IAAW6G,KAAK,EAAEC,OAAlB;IAA2BX,EAA3B;IAA+BhD;EAA/B,IAA2C5E,IAAjD;;EACA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIqI,OAAJ,EAAa;IACX,KAAKzH,IAAL,CAAU,OAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,MAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWwH,EAAX,EAAe5H,IAAf;EACA,KAAKE,KAAL;EACA2E,aAAa,CAAC,IAAD,EAAOD,OAAP,EAAgB5E,IAAhB,CAAb;AACD;;AAEM,SAASwI,YAAT,CAAqCxI,IAArC,EAA2D;EAChE,MAAM;IAAE4H,EAAF;IAAMxF;EAAN,IAAsBpC,IAA5B;EACA,KAAKI,KAAL,CAAWwH,EAAX,EAAe5H,IAAf;;EACA,IAAIoC,WAAJ,EAAiB;IACf,KAAKlC,KAAL;IACA,KAAKD,SAAL;IACA,KAAKC,KAAL;IACA,KAAKE,KAAL,CAAWgC,WAAX,EAAwBpC,IAAxB;EACD;;EACD,KAAKC,SAAL;AACD;;AAEM,SAASwI,mBAAT,CAELzI,IAFK,EAGL;EACA,MAAM;IAAEyB,OAAF;IAAWmG;EAAX,IAAkB5H,IAAxB;;EAEA,IAAIyB,OAAJ,EAAa;IACX,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EAED,IAAI,CAACF,IAAI,CAAC0I,MAAV,EAAkB;IAChB,KAAK5H,IAAL,CAAU8G,EAAE,CAAClH,IAAH,KAAY,YAAZ,GAA2B,WAA3B,GAAyC,QAAnD;IACA,KAAKR,KAAL;EACD;;EACD,KAAKE,KAAL,CAAWwH,EAAX,EAAe5H,IAAf;;EAEA,IAAI,CAACA,IAAI,CAAC+H,IAAV,EAAgB;IACd,KAAK9H,SAAL;IACA;EACD;;EAED,IAAI8H,IAAI,GAAG/H,IAAI,CAAC+H,IAAhB;;EACA,OAAOA,IAAI,CAACrH,IAAL,KAAc,qBAArB,EAA4C;IAC1C,KAAKT,SAAL;IACA,KAAKG,KAAL,CAAW2H,IAAI,CAACH,EAAhB,EAAoBG,IAApB;IACAA,IAAI,GAAGA,IAAI,CAACA,IAAZ;EACD;;EAED,KAAK7H,KAAL;EACA,KAAKE,KAAL,CAAW2H,IAAX,EAAiB/H,IAAjB;AACD;;AAEM,SAAS2I,aAAT,CAAsC3I,IAAtC,EAA6D;EAClE6E,aAAa,CAAC,IAAD,EAAO7E,IAAI,CAAC+H,IAAZ,EAAkB/H,IAAlB,CAAb;AACD;;AAEM,SAAS4I,YAAT,CAAqC5I,IAArC,EAA2D;EAChE,MAAM;IAAE6I,QAAF;IAAYC,SAAZ;IAAuB7E;EAAvB,IAA0CjE,IAAhD;EACA,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKb,SAAL;EACA,KAAKG,KAAL,CAAWyI,QAAX,EAAqB7I,IAArB;EACA,KAAKC,SAAL;;EACA,IAAI6I,SAAJ,EAAe;IACb,KAAK7I,SAAL;IACA,KAAKG,KAAL,CAAW0I,SAAX,EAAsB9I,IAAtB;EACD;;EACD,IAAIiE,cAAJ,EAAoB;IAClB,KAAK7D,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACD;AACF;;AAEM,SAAS+I,yBAAT,CAEL/I,IAFK,EAGL;EACA,MAAM;IAAEgJ,QAAF;IAAYpB,EAAZ;IAAgBqB;EAAhB,IAAoCjJ,IAA1C;;EACA,IAAIgJ,QAAJ,EAAc;IACZ,KAAKlI,IAAL,CAAU,QAAV;IACA,KAAKZ,KAAL;EACD;;EACD,KAAKY,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWwH,EAAX,EAAe5H,IAAf;EACA,KAAKE,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAW6I,eAAX,EAA4BjJ,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASiJ,yBAAT,CAELlJ,IAFK,EAGL;EACA,KAAKC,KAAL,CAAW,UAAX;EACA,KAAKG,KAAL,CAAWJ,IAAI,CAAC0H,UAAhB,EAA4B1H,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASkJ,mBAAT,CAELnJ,IAFK,EAGL;EACA,KAAKI,KAAL,CAAWJ,IAAI,CAAC0H,UAAhB,EAA4B1H,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASmJ,kBAAT,CAA2CpJ,IAA3C,EAAuE;EAC5E,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKD,SAAL;EACA,KAAKC,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAAC0H,UAAhB,EAA4B1H,IAA5B;EACA,KAAKC,SAAL;AACD;;AAEM,SAASoJ,4BAAT,CAELrJ,IAFK,EAGL;EACA,KAAKc,IAAL,CAAU,QAAV;EACA,KAAKZ,KAAL;EACA,KAAKY,IAAL,CAAU,IAAV;EACA,KAAKZ,KAAL;EACA,KAAKY,IAAL,CAAU,WAAV;EACA,KAAKZ,KAAL;EACA,KAAKE,KAAL,CAAWJ,IAAI,CAAC4H,EAAhB,EAAoB5H,IAApB;AACD;;AAEM,SAASiC,+BAAT,CAAwDjC,IAAxD,EAAmE;EACxE,MAAM;IAAEiE;EAAF,IAAqBjE,IAA3B;EACA,MAAM8C,UAAU,GAEZ9C,IAAI,CAAC8C,UAFT;EAGA,KAAK1C,KAAL,CAAW6D,cAAX,EAA2BjE,IAA3B;EACA,KAAKC,SAAL;;EACA,KAAK4C,WAAL,CAAiBC,UAAjB,EAA6B9C,IAA7B;;EACA,KAAKC,SAAL;EACA,MAAMiE,UAAU,GAEZlE,IAAI,CAACK,cAFT;EAGA,KAAKD,KAAL,CAAW8D,UAAX,EAAuBlE,IAAvB;AACD;;AAEM,SAASsJ,2BAAT,CAELtJ,IAFK,EAQL;EACA,MAAMuJ,OAAO,GACXvJ,IAAI,CAACU,IAAL,KAAc,uBAAd,IAAyCV,IAAI,CAACU,IAAL,KAAc,eADzD;;EAEA,IAAI6I,OAAO,IAAIvJ,IAAI,CAACyB,OAApB,EAA6B;IAC3B,KAAKX,IAAL,CAAU,SAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIF,IAAI,CAACoB,aAAT,EAAwB;IACtB,KAAKN,IAAL,CAAUd,IAAI,CAACoB,aAAf;IACA,KAAKlB,KAAL;EACD;;EACD,IAAIF,IAAI,CAAC2C,MAAT,EAAiB;IACf,KAAK7B,IAAL,CAAU,QAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIF,IAAI,CAACwJ,QAAT,EAAmB;IACjB,KAAK1I,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIF,IAAI,CAACgE,QAAT,EAAmB;IACjB,KAAKlD,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;;EACD,IAAIqJ,OAAO,IAAIvJ,IAAI,CAACqB,QAApB,EAA8B;IAC5B,KAAKP,IAAL,CAAU,UAAV;IACA,KAAKZ,KAAL;EACD;AACF"}