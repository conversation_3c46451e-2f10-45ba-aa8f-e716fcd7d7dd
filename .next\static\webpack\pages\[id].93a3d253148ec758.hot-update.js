"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/[id]",{

/***/ "./libs/web/state/lexical-editor.ts":
/*!******************************************!*\
  !*** ./libs/web/state/lexical-editor.ts ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @swc/helpers/src/_async_to_generator.mjs */ \"./node_modules/@swc/helpers/src/_async_to_generator.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @swc/helpers/src/_object_spread.mjs */ \"./node_modules/@swc/helpers/src/_object_spread.mjs\");\n/* harmony import */ var _swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @swc/helpers/src/_object_spread_props.mjs */ \"./node_modules/@swc/helpers/src/_object_spread_props.mjs\");\n/* harmony import */ var _swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @swc/helpers/src/_type_of.mjs */ \"./node_modules/@swc/helpers/src/_type_of.mjs\");\n/* harmony import */ var _swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @swc/helpers/src/_ts_generator.mjs */ \"./node_modules/@swc/helpers/src/_ts_generator.mjs\");\n/* harmony import */ var libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! libs/web/state/note */ \"./libs/web/state/note.ts\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var libs_shared_note__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! libs/shared/note */ \"./libs/shared/note.ts\");\n/* harmony import */ var libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! libs/web/hooks/use-toast */ \"./libs/web/hooks/use-toast.ts\");\n/* harmony import */ var libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! libs/web/cache/note */ \"./libs/web/cache/note.ts\");\n/* harmony import */ var unstated_next__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! unstated-next */ \"./node_modules/unstated-next/dist/unstated-next.mjs\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash */ \"./node_modules/lodash/lodash.js\");\n/* harmony import */ var lodash__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(lodash__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar ROOT_ID = \"root\";\nvar useLexicalEditor = function(initNote) {\n    // Use initNote if provided, otherwise try to get from NoteState\n    var note = initNote;\n    var createNoteWithTitle, updateNote, createNote;\n    try {\n        var noteState = libs_web_state_note__WEBPACK_IMPORTED_MODULE_0__[\"default\"].useContainer();\n        createNoteWithTitle = noteState.createNoteWithTitle;\n        updateNote = noteState.updateNote;\n        createNote = noteState.createNote;\n        // Only use noteState.note if no initNote is provided\n        if (!note) {\n            note = noteState.note;\n        }\n    } catch (error) {\n        // If NoteState is not available, we'll work with just the initNote\n        console.warn(\"NoteState not available in LexicalEditorState, using initNote only\");\n        createNoteWithTitle = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        updateNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n        createNote = /*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    undefined\n                ];\n            });\n        });\n    }\n    var router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    var toast = (0,libs_web_hooks_use_toast__WEBPACK_IMPORTED_MODULE_4__.useToast)();\n    var editorEl = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    // 🔧 新增：快照状态管理\n    var ref = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null), noteSnapshot = ref[0], setNoteSnapshot = ref[1];\n    var ref1 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\"), currentEditorContent = ref1[0], setCurrentEditorContent = ref1[1];\n    // Manual save function for IndexedDB\n    var saveToIndexedDB = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(data) {\n            var existingNote, baseNote, updatedNote;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                            2\n                        ];\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                        ];\n                    case 1:\n                        existingNote = _state.sent();\n                        baseNote = existingNote || note;\n                        updatedNote = (0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, baseNote, data);\n                        return [\n                            4,\n                            libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(note.id, updatedNote)\n                        ];\n                    case 2:\n                        _state.sent();\n                        return [\n                            2\n                        ];\n                }\n            });\n        });\n        return function(data) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        note\n    ]);\n    var syncToServer = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var isNew, localNote, noteToSave, noteData, item, noteUrl, updatedNote, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    isNew = (0,lodash__WEBPACK_IMPORTED_MODULE_6__.has)(router.query, \"new\");\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        11,\n                        ,\n                        12\n                    ]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].getItem(note.id)\n                    ];\n                case 2:\n                    localNote = _state.sent();\n                    noteToSave = localNote || note;\n                    if (!isNew) return [\n                        3,\n                        7\n                    ];\n                    noteData = (0,_swc_helpers_src_object_spread_props_mjs__WEBPACK_IMPORTED_MODULE_10__[\"default\"])((0,_swc_helpers_src_object_spread_mjs__WEBPACK_IMPORTED_MODULE_9__[\"default\"])({}, noteToSave), {\n                        pid: router.query.pid || ROOT_ID\n                    });\n                    return [\n                        4,\n                        createNote(noteData)\n                    ];\n                case 3:\n                    item = _state.sent();\n                    if (!item) return [\n                        3,\n                        6\n                    ];\n                    noteUrl = \"/\".concat(item.id);\n                    if (!(router.asPath !== noteUrl)) return [\n                        3,\n                        5\n                    ];\n                    return [\n                        4,\n                        router.replace(noteUrl, undefined, {\n                            shallow: true\n                        })\n                    ];\n                case 4:\n                    _state.sent();\n                    _state.label = 5;\n                case 5:\n                    toast(\"Note saved to server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 6:\n                    return [\n                        3,\n                        10\n                    ];\n                case 7:\n                    return [\n                        4,\n                        updateNote(noteToSave)\n                    ];\n                case 8:\n                    updatedNote = _state.sent();\n                    if (!updatedNote) return [\n                        3,\n                        10\n                    ];\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].setItem(updatedNote.id, updatedNote)\n                    ];\n                case 9:\n                    _state.sent();\n                    toast(\"Note updated on server\", \"success\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 10:\n                    return [\n                        3,\n                        12\n                    ];\n                case 11:\n                    error = _state.sent();\n                    toast(\"Failed to save note to server\", \"error\");\n                    return [\n                        2,\n                        false\n                    ];\n                case 12:\n                    return [\n                        2,\n                        false\n                    ];\n            }\n        });\n    }), [\n        note,\n        router,\n        createNote,\n        updateNote,\n        toast\n    ]);\n    var onCreateLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(title) {\n            var result;\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                switch(_state.label){\n                    case 0:\n                        if (!createNoteWithTitle) return [\n                            2,\n                            \"\"\n                        ];\n                        return [\n                            4,\n                            createNoteWithTitle(title)\n                        ];\n                    case 1:\n                        result = _state.sent();\n                        if (result === null || result === void 0 ? void 0 : result.id) {\n                            return [\n                                2,\n                                \"/\".concat(result.id)\n                            ];\n                        }\n                        return [\n                            2,\n                            \"\"\n                        ];\n                }\n            });\n        });\n        return function(title) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        createNoteWithTitle\n    ]);\n    var onSearchLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(term) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                return [\n                    2,\n                    []\n                ];\n            });\n        });\n        return function(term) {\n            return _ref.apply(this, arguments);\n        };\n    }(), []);\n    var onClickLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(href, event) {\n        if ((0,libs_shared_note__WEBPACK_IMPORTED_MODULE_3__.isNoteLink)(href)) {\n            event.preventDefault();\n            router.push(href);\n        } else {\n            window.open(href, \"_blank\", \"noopener,noreferrer\");\n        }\n    }, [\n        router\n    ]);\n    var onUploadImage = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        var _ref = (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function(_file, _id) {\n            return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n                // Image upload is disabled in PostgreSQL version\n                toast(\"Image upload is not supported in this version\", \"error\");\n                throw new Error(\"Image upload is not supported\");\n            });\n        });\n        return function(_file, _id) {\n            return _ref.apply(this, arguments);\n        };\n    }(), [\n        toast\n    ]);\n    var onHoverLink = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(event) {\n        return true;\n    }, []);\n    var ref2 = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(), backlinks = ref2[0], setBackLinks = ref2[1];\n    var getBackLinks = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var linkNotes;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    console.log(note === null || note === void 0 ? void 0 : note.id);\n                    linkNotes = [];\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        linkNotes\n                    ];\n                    setBackLinks([]);\n                    return [\n                        4,\n                        libs_web_cache_note__WEBPACK_IMPORTED_MODULE_5__[\"default\"].iterate(function(value) {\n                            var ref;\n                            if ((ref = value.linkIds) === null || ref === void 0 ? void 0 : ref.includes((note === null || note === void 0 ? void 0 : note.id) || \"\")) {\n                                linkNotes.push(value);\n                            }\n                        })\n                    ];\n                case 1:\n                    _state.sent();\n                    setBackLinks(linkNotes);\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id\n    ]);\n    // 🔧 快照初始化逻辑 - 打开笔记时设置JSON快照\n    var initializeSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var ref, ref1, ref2, ref3, snapshotJsonContent;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            console.log(\"\\uD83D\\uDD27 开始初始化快照:\", {\n                noteId: note === null || note === void 0 ? void 0 : note.id,\n                noteContent: (note === null || note === void 0 ? void 0 : (ref = note.content) === null || ref === void 0 ? void 0 : ref.substring(0, 100)) + \"...\",\n                noteContentLength: (note === null || note === void 0 ? void 0 : (ref1 = note.content) === null || ref1 === void 0 ? void 0 : ref1.length) || 0\n            });\n            if (!(note === null || note === void 0 ? void 0 : note.id)) {\n                // 新建笔记：快照为空值\n                console.log(\"\\uD83D\\uDD27 新建笔记，设置空快照\");\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n                return [\n                    2\n                ];\n            }\n            try {\n                ;\n                snapshotJsonContent = \"\";\n                console.log(\"\\uD83D\\uDD27 快照内容来源分析:\", {\n                    noteContent: ((ref2 = note.content) === null || ref2 === void 0 ? void 0 : ref2.substring(0, 100)) + \"...\",\n                    noteContentLength: ((ref3 = note.content) === null || ref3 === void 0 ? void 0 : ref3.length) || 0,\n                    noteContentType: (0,_swc_helpers_src_type_of_mjs__WEBPACK_IMPORTED_MODULE_11__[\"default\"])(note.content)\n                });\n                // 直接使用 note 对象的内容作为快照\n                if (note.content !== undefined && note.content !== null) {\n                    snapshotJsonContent = note.content;\n                    console.log(\"\\uD83D\\uDD27 使用 note 对象内容作为快照\");\n                } else {\n                    snapshotJsonContent = \"\";\n                    console.log(\"\\uD83D\\uDD27 note 内容为空，设置空快照\");\n                }\n                console.log(\"\\uD83D\\uDD27 快照初始化完成:\", {\n                    noteId: note.id,\n                    hasContent: !!snapshotJsonContent,\n                    contentLength: snapshotJsonContent.length,\n                    isJson: snapshotJsonContent.startsWith(\"{\"),\n                    source: note.content ? \"note\" : \"empty\",\n                    contentPreview: snapshotJsonContent.substring(0, 100) + \"...\"\n                });\n                // 🔧 关键：设置快照和当前编辑器内容\n                setNoteSnapshot(snapshotJsonContent);\n                setCurrentEditorContent(snapshotJsonContent);\n            } catch (error) {\n                console.error(\"JSON快照初始化失败:\", error);\n                // 失败时设置为空快照\n                setNoteSnapshot(null);\n                setCurrentEditorContent(\"\");\n            }\n            return [\n                2\n            ];\n        });\n    }), [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 当笔记ID变化时立即重置 currentEditorContent\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(function() {\n        if (note === null || note === void 0 ? void 0 : note.id) {\n            var ref;\n            console.log(\"\\uD83D\\uDD27 笔记切换，重置编辑器状态:\", {\n                noteId: note.id,\n                noteTitle: note.title,\n                hasContent: !!note.content,\n                contentLength: ((ref = note.content) === null || ref === void 0 ? void 0 : ref.length) || 0\n            });\n            // 🔧 关键：立即重置 currentEditorContent 为新笔记的内容\n            setCurrentEditorContent(note.content || \"\");\n            setNoteSnapshot(note.content || \"\");\n        } else {\n            // 🔧 没有笔记时清空状态\n            console.log(\"\\uD83D\\uDD27 无笔记，清空编辑器状态\");\n            setCurrentEditorContent(\"\");\n            setNoteSnapshot(null);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content\n    ]);\n    // 简化的 onChange 处理 - 只更新当前编辑器内容，不做其他操作\n    var onEditorChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(getValue) {\n        var jsonContent = getValue();\n        // 只更新当前编辑器内容状态，其他逻辑交给 SaveButton 处理\n        setCurrentEditorContent(jsonContent);\n    }, []);\n    // Function to handle title changes specifically\n    var onTitleChange = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function(title) {\n        var ref;\n        (ref = saveToIndexedDB({\n            title: title,\n            updated_at: new Date().toISOString()\n        })) === null || ref === void 0 ? void 0 : ref.catch(function(v) {\n            return console.error(\"Error whilst saving title to IndexedDB: %O\", v);\n        });\n    }, [\n        saveToIndexedDB\n    ]);\n    // 🔧 修复：JSON快照对比功能 - 供SaveButton使用\n    var compareWithSnapshot = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        // 如果是新建笔记（快照为null），任何JSON内容都算作变化\n        if (noteSnapshot === null) {\n            return currentEditorContent.trim() !== \"\";\n        }\n        // 已存在笔记：比较当前JSON内容与JSON快照\n        var hasChanges = currentEditorContent !== noteSnapshot;\n        return hasChanges;\n    }, [\n        noteSnapshot,\n        currentEditorContent\n    ]);\n    // 🔧 新增：获取当前编辑器状态 - 供SaveButton使用\n    var getEditorState = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        return {\n            hasChanges: compareWithSnapshot(),\n            currentContent: currentEditorContent,\n            snapshot: noteSnapshot,\n            isNewNote: noteSnapshot === null\n        };\n    }, [\n        compareWithSnapshot,\n        currentEditorContent,\n        noteSnapshot\n    ]);\n    // 🔧 新增：清空所有快照的函数\n    var clearAllSnapshots = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(function() {\n        console.log(\"\\uD83D\\uDD27 清空所有快照\");\n        setNoteSnapshot(null);\n        setCurrentEditorContent(\"\");\n        // 🔧 修复：清空后重新初始化快照，确保编辑器显示正确内容\n        if ((note === null || note === void 0 ? void 0 : note.id) && (note === null || note === void 0 ? void 0 : note.content) !== undefined) {\n            setTimeout(function() {\n                initializeSnapshot();\n            }, 0);\n        }\n    }, [\n        note === null || note === void 0 ? void 0 : note.id,\n        note === null || note === void 0 ? void 0 : note.content,\n        initializeSnapshot\n    ]);\n    // 🔧 修复：保存当前JSON内容到IndexedDB - 供SaveButton调用\n    var saveCurrentContent = (0,react__WEBPACK_IMPORTED_MODULE_2__.useCallback)(/*#__PURE__*/ (0,_swc_helpers_src_async_to_generator_mjs__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(function() {\n        var title, titleInput, error;\n        return (0,_swc_helpers_src_ts_generator_mjs__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(this, function(_state) {\n            switch(_state.label){\n                case 0:\n                    if (!(note === null || note === void 0 ? void 0 : note.id)) return [\n                        2,\n                        false\n                    ];\n                    _state.label = 1;\n                case 1:\n                    _state.trys.push([\n                        1,\n                        3,\n                        ,\n                        4\n                    ]);\n                    if (note === null || note === void 0 ? void 0 : note.isDailyNote) {\n                        title = note.title;\n                    } else {\n                        titleInput = document.querySelector(\"h1 textarea\");\n                        if (titleInput && titleInput.value) {\n                            title = titleInput.value.trim();\n                        } else {\n                            // 对于JSON格式，使用现有标题或默认标题\n                            title = (note === null || note === void 0 ? void 0 : note.title) || \"Untitled\";\n                        }\n                    }\n                    // 保存JSON内容到IndexedDB\n                    return [\n                        4,\n                        saveToIndexedDB({\n                            content: currentEditorContent,\n                            title: title,\n                            updated_at: new Date().toISOString()\n                        })\n                    ];\n                case 2:\n                    _state.sent();\n                    // 🔧 按照用户要求：保存后清除 currentEditorContent\n                    // 因为保存后会重新加载缓存，编辑器直接使用 note.content\n                    setNoteSnapshot(null);\n                    setCurrentEditorContent(\"\");\n                    return [\n                        2,\n                        true\n                    ];\n                case 3:\n                    error = _state.sent();\n                    console.error(\"\\uD83D\\uDD27 保存JSON到IndexedDB失败:\", error);\n                    return [\n                        2,\n                        false\n                    ];\n                case 4:\n                    return [\n                        2\n                    ];\n            }\n        });\n    }), [\n        note,\n        currentEditorContent,\n        saveToIndexedDB,\n        clearAllSnapshots\n    ]);\n    return {\n        onCreateLink: onCreateLink,\n        onSearchLink: onSearchLink,\n        onClickLink: onClickLink,\n        onUploadImage: onUploadImage,\n        onHoverLink: onHoverLink,\n        getBackLinks: getBackLinks,\n        onEditorChange: onEditorChange,\n        onTitleChange: onTitleChange,\n        saveToIndexedDB: saveToIndexedDB,\n        syncToServer: syncToServer,\n        backlinks: backlinks,\n        editorEl: editorEl,\n        // 🔧 让编辑器直接使用 note.content\n        note: note,\n        // 🔧 暴露 currentEditorContent 给 SaveButton 用于对比\n        currentEditorContent: currentEditorContent\n    };\n};\nvar LexicalEditorState = (0,unstated_next__WEBPACK_IMPORTED_MODULE_12__.createContainer)(useLexicalEditor);\n/* harmony default export */ __webpack_exports__[\"default\"] = (LexicalEditorState);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevExports = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevExports) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports on update so we can compare the boundary\n                // signatures.\n                module.hot.dispose(function (data) {\n                    data.prevExports = currentExports;\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevExports !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevExports, currentExports)) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevExports !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9saWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvci50cy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7OztBQUFBOzs7OztBQUE0QztBQUNKO0FBT3pCO0FBRTBDO0FBQ0w7QUFHUjtBQUNJO0FBR25CO0FBRzdCLElBQU1XLE9BQU8sR0FBRyxNQUFNO0FBRXRCLElBQU1DLGdCQUFnQixHQUFHLFNBQUNDLFFBQW9CLEVBQUs7SUFDL0MsZ0VBQWdFO0lBQ2hFLElBQUlDLElBQUksR0FBR0QsUUFBUTtJQUNuQixJQUFJRSxtQkFBbUIsRUFBT0MsVUFBVSxFQUFPQyxVQUFVO0lBRXpELElBQUk7UUFDQSxJQUFNQyxTQUFTLEdBQUdsQix3RUFBc0IsRUFBRTtRQUMxQ2UsbUJBQW1CLEdBQUdHLFNBQVMsQ0FBQ0gsbUJBQW1CLENBQUM7UUFDcERDLFVBQVUsR0FBR0UsU0FBUyxDQUFDRixVQUFVLENBQUM7UUFDbENDLFVBQVUsR0FBR0MsU0FBUyxDQUFDRCxVQUFVLENBQUM7UUFFbEMscURBQXFEO1FBQ3JELElBQUksQ0FBQ0gsSUFBSSxFQUFFO1lBQ1BBLElBQUksR0FBR0ksU0FBUyxDQUFDSixJQUFJLENBQUM7UUFDMUIsQ0FBQztJQUNMLEVBQUUsT0FBT00sS0FBSyxFQUFFO1FBQ1osbUVBQW1FO1FBQ25FQyxPQUFPLENBQUNDLElBQUksQ0FBQyxvRUFBb0UsQ0FBQyxDQUFDO1FBQ25GUCxtQkFBbUIsaUJBQUc7O2dCQUFZUTs7b0JBQUFBLFNBQVM7a0JBQUE7O1NBQUEsRUFBQztRQUM1Q1AsVUFBVSxpQkFBRzs7Z0JBQVlPOztvQkFBQUEsU0FBUztrQkFBQTs7U0FBQSxFQUFDO1FBQ25DTixVQUFVLGlCQUFHOztnQkFBWU07O29CQUFBQSxTQUFTO2tCQUFBOztTQUFBLEVBQUM7SUFDdkMsQ0FBQztJQUVELElBQU1DLE1BQU0sR0FBR3ZCLHNEQUFTLEVBQUU7SUFDMUIsSUFBTXdCLEtBQUssR0FBR2xCLGtFQUFRLEVBQUU7SUFDeEIsSUFBTW1CLFFBQVEsR0FBR3RCLDZDQUFNLENBQW1CLElBQUksQ0FBQztJQUUvQyxjQUFjO0lBQ2QsSUFBd0NELEdBQTZCLEdBQTdCQSwrQ0FBUSxDQUFnQixJQUFJLENBQUMsRUFBOUR3QixZQUFZLEdBQXFCeEIsR0FBNkIsR0FBbEQsRUFBRXlCLGVBQWUsR0FBSXpCLEdBQTZCLEdBQWpDO0lBQ3BDLElBQXdEQSxJQUFvQixHQUFwQkEsK0NBQVEsQ0FBUyxFQUFFLENBQUMsRUFBckUwQixvQkFBb0IsR0FBNkIxQixJQUFvQixHQUFqRCxFQUFFMkIsdUJBQXVCLEdBQUkzQixJQUFvQixHQUF4QjtJQUVwRCxxQ0FBcUM7SUFDckMsSUFBTTRCLGVBQWUsR0FBRzdCLGtEQUFXO21CQUMvQiw2RkFBTzhCLElBQXdCLEVBQUs7Z0JBSTFCQyxZQUFZLEVBQ1pDLFFBQVEsRUFFUkMsV0FBVzs7Ozt3QkFOakIsSUFBSSxDQUFDckIsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7OzBCQUFPO3dCQUdEOzs0QkFBTTVCLG1FQUFpQixDQUFDTSxJQUFJLENBQUNzQixFQUFFLENBQUM7MEJBQUE7O3dCQUEvQ0gsWUFBWSxHQUFHLGFBQWdDO3dCQUMvQ0MsUUFBUSxHQUFHRCxZQUFZLElBQUluQixJQUFJLENBQUM7d0JBRWhDcUIsV0FBVyxHQUFHLG1GQUFLRCxRQUFRLEVBQUtGLElBQUksQ0FBRSxDQUFDO3dCQUU3Qzs7NEJBQU14QixtRUFBaUIsQ0FBQ00sSUFBSSxDQUFDc0IsRUFBRSxFQUFFRCxXQUFXLENBQUM7MEJBQUE7O3dCQUE3QyxhQUE2QyxDQUFDOzs7Ozs7UUFDbEQsQ0FBQzt3QkFWTUgsSUFBd0I7OztTQVcvQjtRQUFDbEIsSUFBSTtLQUFDLENBQ1Q7SUFFRCxJQUFNeUIsWUFBWSxHQUFHckMsa0RBQVcsZUFDNUIsK0ZBQVk7WUFHRnNDLEtBQUssRUFHREMsU0FBUyxFQUNUQyxVQUFVLEVBR05DLFFBQVEsRUFLUkMsSUFBSSxFQUdBQyxPQUFPLEVBUVhWLFdBQVcsRUFRaEJmLEtBQUs7Ozs7b0JBakNkLElBQUksQ0FBQ04sQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7O3dCQUFPLEtBQUs7c0JBQUM7b0JBRXRCSSxLQUFLLEdBQUc5QiwyQ0FBRyxDQUFDYyxNQUFNLENBQUNzQixLQUFLLEVBQUUsS0FBSyxDQUFDLENBQUM7Ozs7Ozs7OztvQkFHakI7O3dCQUFNdEMsbUVBQWlCLENBQUNNLElBQUksQ0FBQ3NCLEVBQUUsQ0FBQztzQkFBQTs7b0JBQTVDSyxTQUFTLEdBQUcsYUFBZ0M7b0JBQzVDQyxVQUFVLEdBQUdELFNBQVMsSUFBSTNCLElBQUksQ0FBQzt5QkFFakMwQixLQUFLLEVBQUxBOzs7c0JBQUs7b0JBQ0NHLFFBQVEsR0FBRyx5S0FDVkQsVUFBVTt3QkFDYkssR0FBRyxFQUFFLE1BQU8sQ0FBQ0QsS0FBSyxDQUFDQyxHQUFHLElBQWVwQyxPQUFPO3NCQUMvQyxDQUFDO29CQUVXOzt3QkFBTU0sVUFBVSxDQUFDMEIsUUFBUSxDQUFDO3NCQUFBOztvQkFBakNDLElBQUksR0FBRyxhQUEwQjt5QkFFbkNBLElBQUksRUFBSkE7OztzQkFBSTtvQkFDRUMsT0FBTyxHQUFHLEdBQUUsQ0FBVSxPQUFSRCxJQUFJLENBQUNSLEVBQUUsQ0FBRSxDQUFDO3lCQUMxQlosQ0FBQUEsTUFBTSxDQUFDd0IsTUFBTSxLQUFLSCxPQUFPLEdBQXpCckI7OztzQkFBeUI7b0JBQ3pCOzt3QkFBTUEsTUFBTSxDQUFDeUIsT0FBTyxDQUFDSixPQUFPLEVBQUV0QixTQUFTLEVBQUU7NEJBQUUyQixPQUFPLEVBQUUsSUFBSTt5QkFBRSxDQUFDO3NCQUFBOztvQkFBM0QsYUFBMkQsQ0FBQzs7O29CQUVoRXpCLEtBQUssQ0FBQyxzQkFBc0IsRUFBRSxTQUFTLENBQUMsQ0FBQztvQkFDekM7O3dCQUFPLElBQUk7c0JBQUM7Ozs7Ozs7b0JBR0k7O3dCQUFNVCxVQUFVLENBQUMwQixVQUFVLENBQUM7c0JBQUE7O29CQUExQ1AsV0FBVyxHQUFHLGFBQTRCO3lCQUU1Q0EsV0FBVyxFQUFYQTs7O3NCQUFXO29CQUNYOzt3QkFBTTNCLG1FQUFpQixDQUFDMkIsV0FBVyxDQUFDQyxFQUFFLEVBQUVELFdBQVcsQ0FBQztzQkFBQTs7b0JBQXBELGFBQW9ELENBQUM7b0JBQ3JEVixLQUFLLENBQUMsd0JBQXdCLEVBQUUsU0FBUyxDQUFDLENBQUM7b0JBQzNDOzt3QkFBTyxJQUFJO3NCQUFDOzs7Ozs7O29CQUdmTCxLQUFLO29CQUNWSyxLQUFLLENBQUMsK0JBQStCLEVBQUUsT0FBTyxDQUFDLENBQUM7b0JBQ2hEOzt3QkFBTyxLQUFLO3NCQUFDOztvQkFHakI7O3dCQUFPLEtBQUs7c0JBQUM7OztJQUNqQixDQUFDLEdBQ0Q7UUFBQ1gsSUFBSTtRQUFFVSxNQUFNO1FBQUVQLFVBQVU7UUFBRUQsVUFBVTtRQUFFUyxLQUFLO0tBQUMsQ0FDaEQ7SUFFRCxJQUFNMEIsWUFBWSxHQUFHakQsa0RBQVc7bUJBQzVCLDZGQUFPa0QsS0FBYSxFQUFLO2dCQUdmQyxNQUFNOzs7O3dCQUZaLElBQUksQ0FBQ3RDLG1CQUFtQixFQUFFOzs0QkFBTyxFQUFFOzBCQUFDO3dCQUVyQjs7NEJBQU1BLG1CQUFtQixDQUFDcUMsS0FBSyxDQUFDOzBCQUFBOzt3QkFBekNDLE1BQU0sR0FBRyxhQUFnQzt3QkFDL0MsSUFBSUEsTUFBTSxhQUFOQSxNQUFNLFdBQUksR0FBVkEsS0FBQUEsQ0FBVSxHQUFWQSxNQUFNLENBQUVqQixFQUFFLEVBQUU7NEJBQ1o7O2dDQUFRLEdBQUMsQ0FBWSxPQUFWaUIsTUFBTSxDQUFDakIsRUFBRSxDQUFFOzhCQUFDO3dCQUMzQixDQUFDO3dCQUNEOzs0QkFBTyxFQUFFOzBCQUFDOzs7UUFDZCxDQUFDO3dCQVJNZ0IsS0FBYTs7O1NBU3BCO1FBQUNyQyxtQkFBbUI7S0FBQyxDQUN4QjtJQUVELElBQU11QyxZQUFZLEdBQUdwRCxrREFBVzttQkFDNUIsNkZBQU9xRCxJQUFZLEVBQUs7O2dCQUNwQjs7O2tCQUFVOztRQUNkLENBQUM7d0JBRk1BLElBQVk7OztTQUduQixFQUFFLENBQ0w7SUFFRCxJQUFNQyxXQUFXLEdBQUd0RCxrREFBVyxDQUMzQixTQUFDdUQsSUFBWSxFQUFFQyxLQUFzQixFQUFLO1FBQ3RDLElBQUlwRCw0REFBVSxDQUFDbUQsSUFBSSxDQUFDLEVBQUU7WUFDbEJDLEtBQUssQ0FBQ0MsY0FBYyxFQUFFLENBQUM7WUFDdkJuQyxNQUFNLENBQUNvQyxJQUFJLENBQUNILElBQUksQ0FBQyxDQUFDO1FBQ3RCLE9BQU87WUFDSEksTUFBTSxDQUFDQyxJQUFJLENBQUNMLElBQUksRUFBRSxRQUFRLEVBQUUscUJBQXFCLENBQUMsQ0FBQztRQUN2RCxDQUFDO0lBQ0wsQ0FBQyxFQUNEO1FBQUNqQyxNQUFNO0tBQUMsQ0FDWDtJQUVELElBQU11QyxhQUFhLEdBQUc3RCxrREFBVzttQkFDN0IsNkZBQU84RCxLQUFXLEVBQUVDLEdBQVksRUFBSzs7Z0JBQ2pDLGlEQUFpRDtnQkFDakR4QyxLQUFLLENBQUMsK0NBQStDLEVBQUUsT0FBTyxDQUFDLENBQUM7Z0JBQ2hFLE1BQU0sSUFBSXlDLEtBQUssQ0FBQywrQkFBK0IsQ0FBQyxDQUFDOztRQUNyRCxDQUFDO3dCQUpNRixLQUFXLEVBQUVDLEdBQVk7OztTQUtoQztRQUFDeEMsS0FBSztLQUFDLENBQ1Y7SUFFRCxJQUFNMEMsV0FBVyxHQUFHakUsa0RBQVcsQ0FBQyxTQUFDd0QsS0FBc0IsRUFBSztRQUN4RCxPQUFPLElBQUksQ0FBQztJQUNoQixDQUFDLEVBQUUsRUFBRSxDQUFDO0lBRU4sSUFBa0N2RCxJQUEyQixHQUEzQkEsK0NBQVEsRUFBbUIsRUFBdERpRSxTQUFTLEdBQWtCakUsSUFBMkIsR0FBN0MsRUFBRWtFLFlBQVksR0FBSWxFLElBQTJCLEdBQS9CO0lBRTlCLElBQU1tRSxZQUFZLEdBQUdwRSxrREFBVyxlQUFDLCtGQUFZO1lBRW5DcUUsU0FBUzs7OztvQkFEZmxELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQzFELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxDQUFDLENBQUM7b0JBQ2hCbUMsU0FBUyxLQUFzQixDQUFDO29CQUN0QyxJQUFJLENBQUN6RCxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsR0FBRTs7d0JBQU9tQyxTQUFTO3NCQUFDO29CQUNoQ0YsWUFBWSxJQUFJLENBQUM7b0JBQ2pCOzt3QkFBTTdELG1FQUFpQixDQUFzQixTQUFDa0UsS0FBSyxFQUFLO2dDQUNoREEsR0FBYTs0QkFBakIsSUFBSUEsQ0FBQUEsR0FBYSxHQUFiQSxLQUFLLENBQUNDLE9BQU8sY0FBYkQsR0FBYSxXQUFVLEdBQXZCQSxLQUFBQSxDQUF1QixHQUF2QkEsR0FBYSxDQUFFRSxRQUFRLENBQUM5RCxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsS0FBSSxFQUFFLENBQUMsRUFBRTtnQ0FDekNtQyxTQUFTLENBQUNYLElBQUksQ0FBQ2MsS0FBSyxDQUFDLENBQUM7NEJBQzFCLENBQUM7d0JBQ0wsQ0FBQyxDQUFDO3NCQUFBOztvQkFKRixhQUlFLENBQUM7b0JBQ0hMLFlBQVksQ0FBQ0UsU0FBUyxDQUFDLENBQUM7Ozs7OztJQUM1QixDQUFDLEdBQUU7UUFBQ3pELElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtLQUFDLENBQUM7SUFFZCw0QkFBNEI7SUFDNUIsSUFBTXlDLGtCQUFrQixHQUFHM0Usa0RBQVcsZUFBQywrRkFBWTtZQUc5QlksR0FBYSxFQUNQQSxJQUFhLEVBaUJmQSxJQUFZLEVBQ05BLElBQVksRUFKL0JnRSxtQkFBbUI7O1lBakIzQnpELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx1QkFBWSxFQUFFO2dCQUN0Qk8sTUFBTSxFQUFFakUsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFO2dCQUNoQjRDLFdBQVcsRUFBRWxFLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsQ0FBQUEsR0FBYSxHQUFiQSxJQUFJLENBQUVtRSxPQUFPLGNBQWJuRSxHQUFhLGNBQWJBLEtBQUFBLENBQWEsR0FBYkEsR0FBYSxDQUFFb0UsU0FBUyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsSUFBRyxLQUFLO2dCQUNyREMsaUJBQWlCLEVBQUVyRSxDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBUyxHQUFiQSxLQUFBQSxDQUFhLEdBQWJBLENBQUFBLElBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTyxjQUFibkUsSUFBYSxjQUFiQSxLQUFBQSxDQUFhLEdBQWJBLElBQWEsQ0FBRXNFLE1BQU0sQ0FBUixJQUFZLENBQUM7YUFDaEQsQ0FBQyxDQUFDO1lBRUgsSUFBSSxDQUFDdEUsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEdBQUU7Z0JBQ1gsYUFBYTtnQkFDYmYsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLHlCQUFjLENBQUMsQ0FBQztnQkFDNUI1QyxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUM7Z0JBQ3RCRSx1QkFBdUIsQ0FBQyxFQUFFLENBQUMsQ0FBQztnQkFDNUI7O2tCQUFPO1lBQ1gsQ0FBQztZQUVELElBQUk7O2dCQUdJZ0QsbUJBQW1CLEdBQUcsRUFBRSxDQUFDO2dCQUU3QnpELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx3QkFBYSxFQUFFO29CQUN2QlEsV0FBVyxFQUFFbEUsQ0FBQUEsQ0FBQUEsSUFBWSxHQUFaQSxJQUFJLENBQUNtRSxPQUFPLGNBQVpuRSxJQUFZLFdBQVcsR0FBdkJBLEtBQUFBLENBQXVCLEdBQXZCQSxJQUFZLENBQUVvRSxTQUFTLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxJQUFHLEtBQUs7b0JBQ3BEQyxpQkFBaUIsRUFBRXJFLENBQUFBLENBQUFBLElBQVksR0FBWkEsSUFBSSxDQUFDbUUsT0FBTyxjQUFabkUsSUFBWSxXQUFRLEdBQXBCQSxLQUFBQSxDQUFvQixHQUFwQkEsSUFBWSxDQUFFc0UsTUFBTSxLQUFJLENBQUM7b0JBQzVDQyxlQUFlLEVBQUUseUVBQW1CLENBQVp2RSxJQUFJLENBQUNtRSxPQUFPO2lCQUN2QyxDQUFDLENBQUM7Z0JBRUgsc0JBQXNCO2dCQUN0QixJQUFJbkUsSUFBSSxDQUFDbUUsT0FBTyxLQUFLMUQsU0FBUyxJQUFJVCxJQUFJLENBQUNtRSxPQUFPLEtBQUssSUFBSSxFQUFFO29CQUNyREgsbUJBQW1CLEdBQUdoRSxJQUFJLENBQUNtRSxPQUFPLENBQUM7b0JBQ25DNUQsT0FBTyxDQUFDbUQsR0FBRyxDQUFDLCtCQUFvQixDQUFDLENBQUM7Z0JBQ3RDLE9BQU87b0JBQ0hNLG1CQUFtQixHQUFHLEVBQUUsQ0FBQztvQkFDekJ6RCxPQUFPLENBQUNtRCxHQUFHLENBQUMsOEJBQW1CLENBQUMsQ0FBQztnQkFDckMsQ0FBQztnQkFFRG5ELE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyx1QkFBWSxFQUFFO29CQUN0Qk8sTUFBTSxFQUFFakUsSUFBSSxDQUFDc0IsRUFBRTtvQkFDZmtELFVBQVUsRUFBRSxDQUFDLENBQUNSLG1CQUFtQjtvQkFDakNTLGFBQWEsRUFBRVQsbUJBQW1CLENBQUNNLE1BQU07b0JBQ3pDSSxNQUFNLEVBQUVWLG1CQUFtQixDQUFDVyxVQUFVLENBQUMsR0FBRyxDQUFDO29CQUMzQ0MsTUFBTSxFQUFFNUUsSUFBSSxDQUFDbUUsT0FBTyxHQUFHLE1BQU0sR0FBRyxPQUFPO29CQUN2Q1UsY0FBYyxFQUFFYixtQkFBbUIsQ0FBQ0ksU0FBUyxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsR0FBRyxLQUFLO2lCQUNoRSxDQUFDLENBQUM7Z0JBRUgsb0JBQW9CO2dCQUNwQnRELGVBQWUsQ0FBQ2tELG1CQUFtQixDQUFDLENBQUM7Z0JBQ3JDaEQsdUJBQXVCLENBQUNnRCxtQkFBbUIsQ0FBQyxDQUFDO1lBQ2pELEVBQUUsT0FBTzFELEtBQUssRUFBRTtnQkFDWkMsT0FBTyxDQUFDRCxLQUFLLENBQUMsY0FBYyxFQUFFQSxLQUFLLENBQUMsQ0FBQztnQkFDckMsWUFBWTtnQkFDWlEsZUFBZSxDQUFDLElBQUksQ0FBQyxDQUFDO2dCQUN0QkUsdUJBQXVCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDaEMsQ0FBQzs7Ozs7SUFDTCxDQUFDLEdBQUU7UUFBQ2hCLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtRQUFFdEIsSUFBSSxhQUFKQSxJQUFJLFdBQVMsR0FBYkEsS0FBQUEsQ0FBYSxHQUFiQSxJQUFJLENBQUVtRSxPQUFPO0tBQUMsQ0FBQztJQUU3QixvQ0FBb0M7SUFDcEM1RSxnREFBUyxDQUFDLFdBQU07UUFDWixJQUFJUyxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUUsRUFBRTtnQkFLU3RCLEdBQVk7WUFKL0JPLE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyw0QkFBaUIsRUFBRTtnQkFDM0JPLE1BQU0sRUFBRWpFLElBQUksQ0FBQ3NCLEVBQUU7Z0JBQ2Z3RCxTQUFTLEVBQUU5RSxJQUFJLENBQUNzQyxLQUFLO2dCQUNyQmtDLFVBQVUsRUFBRSxDQUFDLENBQUN4RSxJQUFJLENBQUNtRSxPQUFPO2dCQUMxQk0sYUFBYSxFQUFFekUsQ0FBQUEsQ0FBQUEsR0FBWSxHQUFaQSxJQUFJLENBQUNtRSxPQUFPLGNBQVpuRSxHQUFZLFdBQVEsR0FBcEJBLEtBQUFBLENBQW9CLEdBQXBCQSxHQUFZLENBQUVzRSxNQUFNLEtBQUksQ0FBQzthQUMzQyxDQUFDLENBQUM7WUFDSCx5Q0FBeUM7WUFDekN0RCx1QkFBdUIsQ0FBQ2hCLElBQUksQ0FBQ21FLE9BQU8sSUFBSSxFQUFFLENBQUMsQ0FBQztZQUM1Q3JELGVBQWUsQ0FBQ2QsSUFBSSxDQUFDbUUsT0FBTyxJQUFJLEVBQUUsQ0FBQyxDQUFDO1FBQ3hDLE9BQU87WUFDSCxjQUFjO1lBQ2Q1RCxPQUFPLENBQUNtRCxHQUFHLENBQUMsMEJBQWUsQ0FBQyxDQUFDO1lBQzdCMUMsdUJBQXVCLENBQUMsRUFBRSxDQUFDLENBQUM7WUFDNUJGLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQztRQUMxQixDQUFDO0lBQ0wsQ0FBQyxFQUFFO1FBQUNkLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRTtRQUFFdEIsSUFBSSxhQUFKQSxJQUFJLFdBQVMsR0FBYkEsS0FBQUEsQ0FBYSxHQUFiQSxJQUFJLENBQUVtRSxPQUFPO0tBQUMsQ0FBQyxDQUFDO0lBRTlCLHNDQUFzQztJQUN0QyxJQUFNWSxjQUFjLEdBQUczRixrREFBVyxDQUM5QixTQUFDNEYsUUFBc0IsRUFBVztRQUM5QixJQUFNQyxXQUFXLEdBQUdELFFBQVEsRUFBRTtRQUU5QixvQ0FBb0M7UUFDcENoRSx1QkFBdUIsQ0FBQ2lFLFdBQVcsQ0FBQyxDQUFDO0lBQ3pDLENBQUMsRUFDRCxFQUFFLENBQ0w7SUFFRCxnREFBZ0Q7SUFDaEQsSUFBTUMsYUFBYSxHQUFHOUYsa0RBQVcsQ0FDN0IsU0FBQ2tELEtBQWEsRUFBVztZQUNyQnJCLEdBR0U7UUFIRkEsQ0FBQUEsR0FHRSxHQUhGQSxlQUFlLENBQUM7WUFDWnFCLEtBQUssRUFBTEEsS0FBSztZQUNMNkMsVUFBVSxFQUFFLElBQUlDLElBQUksRUFBRSxDQUFDQyxXQUFXLEVBQUU7U0FDdkMsQ0FBQyxjQUhGcEUsR0FHRSxXQUFPLEdBSFRBLEtBQUFBLENBR1MsR0FIVEEsR0FHRSxDQUFFcUUsS0FBSyxDQUFDLFNBQUNDLENBQUM7bUJBQUtoRixPQUFPLENBQUNELEtBQUssQ0FBQyw0Q0FBNEMsRUFBRWlGLENBQUMsQ0FBQztTQUFBLENBQUMsQ0FBQztJQUNyRixDQUFDLEVBQ0Q7UUFBQ3RFLGVBQWU7S0FBQyxDQUNwQjtJQUVELGtDQUFrQztJQUNsQyxJQUFNdUUsbUJBQW1CLEdBQUdwRyxrREFBVyxDQUFDLFdBQWU7UUFDbkQsaUNBQWlDO1FBQ2pDLElBQUl5QixZQUFZLEtBQUssSUFBSSxFQUFFO1lBQ3ZCLE9BQU9FLG9CQUFvQixDQUFDMEUsSUFBSSxFQUFFLEtBQUssRUFBRSxDQUFDO1FBQzlDLENBQUM7UUFFRCwwQkFBMEI7UUFDMUIsSUFBTUMsVUFBVSxHQUFHM0Usb0JBQW9CLEtBQUtGLFlBQVk7UUFDeEQsT0FBTzZFLFVBQVUsQ0FBQztJQUN0QixDQUFDLEVBQUU7UUFBQzdFLFlBQVk7UUFBRUUsb0JBQW9CO0tBQUMsQ0FBQztJQUV4QyxpQ0FBaUM7SUFDakMsSUFBTTRFLGNBQWMsR0FBR3ZHLGtEQUFXLENBQUMsV0FBTTtRQUNyQyxPQUFPO1lBQ0hzRyxVQUFVLEVBQUVGLG1CQUFtQixFQUFFO1lBQ2pDSSxjQUFjLEVBQUU3RSxvQkFBb0I7WUFDcEM4RSxRQUFRLEVBQUVoRixZQUFZO1lBQ3RCaUYsU0FBUyxFQUFFakYsWUFBWSxLQUFLLElBQUk7U0FDbkMsQ0FBQztJQUNOLENBQUMsRUFBRTtRQUFDMkUsbUJBQW1CO1FBQUV6RSxvQkFBb0I7UUFBRUYsWUFBWTtLQUFDLENBQUM7SUFFN0QsaUJBQWlCO0lBQ2pCLElBQU1rRixpQkFBaUIsR0FBRzNHLGtEQUFXLENBQUMsV0FBTTtRQUN4Q21CLE9BQU8sQ0FBQ21ELEdBQUcsQ0FBQyxxQkFBVSxDQUFDLENBQUM7UUFDeEI1QyxlQUFlLENBQUMsSUFBSSxDQUFDLENBQUM7UUFDdEJFLHVCQUF1QixDQUFDLEVBQUUsQ0FBQyxDQUFDO1FBRTVCLDhCQUE4QjtRQUM5QixJQUFJaEIsQ0FBQUEsSUFBSSxhQUFKQSxJQUFJLFdBQUksR0FBUkEsS0FBQUEsQ0FBUSxHQUFSQSxJQUFJLENBQUVzQixFQUFFLEtBQUl0QixDQUFBQSxJQUFJLGFBQUpBLElBQUksV0FBUyxHQUFiQSxLQUFBQSxDQUFhLEdBQWJBLElBQUksQ0FBRW1FLE9BQU8sTUFBSzFELFNBQVMsRUFBRTtZQUN6Q3VGLFVBQVUsQ0FBQyxXQUFNO2dCQUNiakMsa0JBQWtCLEVBQUUsQ0FBQztZQUN6QixDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUM7UUFDVixDQUFDO0lBQ0wsQ0FBQyxFQUFFO1FBQUMvRCxJQUFJLGFBQUpBLElBQUksV0FBSSxHQUFSQSxLQUFBQSxDQUFRLEdBQVJBLElBQUksQ0FBRXNCLEVBQUU7UUFBRXRCLElBQUksYUFBSkEsSUFBSSxXQUFTLEdBQWJBLEtBQUFBLENBQWEsR0FBYkEsSUFBSSxDQUFFbUUsT0FBTztRQUFFSixrQkFBa0I7S0FBQyxDQUFDO0lBRWpELDRDQUE0QztJQUM1QyxJQUFNa0Msa0JBQWtCLEdBQUc3RyxrREFBVyxlQUFDLCtGQUE4QjtZQUt6RGtELEtBQUssRUFJQzRELFVBQVUsRUFzQmY1RixLQUFLOzs7O29CQTlCZCxJQUFJLENBQUNOLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFJLEdBQVJBLEtBQUFBLENBQVEsR0FBUkEsSUFBSSxDQUFFc0IsRUFBRSxHQUFFOzt3QkFBTyxLQUFLO3NCQUFDOzs7Ozs7Ozs7b0JBS3hCLElBQUl0QixJQUFJLGFBQUpBLElBQUksV0FBYSxHQUFqQkEsS0FBQUEsQ0FBaUIsR0FBakJBLElBQUksQ0FBRW1HLFdBQVcsRUFBRTt3QkFDbkI3RCxLQUFLLEdBQUd0QyxJQUFJLENBQUNzQyxLQUFLLENBQUM7b0JBQ3ZCLE9BQU87d0JBQ0c0RCxVQUFVLEdBQUdFLFFBQVEsQ0FBQ0MsYUFBYSxDQUFDLGFBQWEsQ0FBQyxDQUF3Qjt3QkFDaEYsSUFBSUgsVUFBVSxJQUFJQSxVQUFVLENBQUN0QyxLQUFLLEVBQUU7NEJBQ2hDdEIsS0FBSyxHQUFHNEQsVUFBVSxDQUFDdEMsS0FBSyxDQUFDNkIsSUFBSSxFQUFFLENBQUM7d0JBQ3BDLE9BQU87NEJBQ0gsdUJBQXVCOzRCQUN2Qm5ELEtBQUssR0FBR3RDLENBQUFBLElBQUksYUFBSkEsSUFBSSxXQUFPLEdBQVhBLEtBQUFBLENBQVcsR0FBWEEsSUFBSSxDQUFFc0MsS0FBSyxLQUFJLFVBQVUsQ0FBQzt3QkFDdEMsQ0FBQztvQkFDTCxDQUFDO29CQUVELHFCQUFxQjtvQkFDckI7O3dCQUFNckIsZUFBZSxDQUFDOzRCQUNsQmtELE9BQU8sRUFBRXBELG9CQUFvQjs0QkFDN0J1QixLQUFLLEVBQUxBLEtBQUs7NEJBQ0w2QyxVQUFVLEVBQUUsSUFBSUMsSUFBSSxFQUFFLENBQUNDLFdBQVcsRUFBRTt5QkFDdkMsQ0FBQztzQkFBQTs7b0JBSkYsYUFJRSxDQUFDO29CQUVILHNDQUFzQztvQkFDdEMsb0NBQW9DO29CQUNwQ3ZFLGVBQWUsQ0FBQyxJQUFJLENBQUMsQ0FBQztvQkFDdEJFLHVCQUF1QixDQUFDLEVBQUUsQ0FBQyxDQUFDO29CQUU1Qjs7d0JBQU8sSUFBSTtzQkFBQzs7b0JBQ1BWLEtBQUs7b0JBQ1ZDLE9BQU8sQ0FBQ0QsS0FBSyxDQUFDLGtDQUF1QixFQUFFQSxLQUFLLENBQUMsQ0FBQztvQkFDOUM7O3dCQUFPLEtBQUs7c0JBQUM7Ozs7Ozs7SUFFckIsQ0FBQyxHQUFFO1FBQUNOLElBQUk7UUFBRWUsb0JBQW9CO1FBQUVFLGVBQWU7UUFBRThFLGlCQUFpQjtLQUFDLENBQUM7SUFFcEUsT0FBTztRQUNIMUQsWUFBWSxFQUFaQSxZQUFZO1FBQ1pHLFlBQVksRUFBWkEsWUFBWTtRQUNaRSxXQUFXLEVBQVhBLFdBQVc7UUFDWE8sYUFBYSxFQUFiQSxhQUFhO1FBQ2JJLFdBQVcsRUFBWEEsV0FBVztRQUNYRyxZQUFZLEVBQVpBLFlBQVk7UUFDWnVCLGNBQWMsRUFBZEEsY0FBYztRQUNkRyxhQUFhLEVBQWJBLGFBQWE7UUFDYmpFLGVBQWUsRUFBZkEsZUFBZTtRQUNmUSxZQUFZLEVBQVpBLFlBQVk7UUFDWjZCLFNBQVMsRUFBVEEsU0FBUztRQUNUMUMsUUFBUSxFQUFSQSxRQUFRO1FBQ1IsMEJBQTBCO1FBQzFCWixJQUFJLEVBQUpBLElBQUk7UUFDSiw4Q0FBOEM7UUFDOUNlLG9CQUFvQixFQUFwQkEsb0JBQW9CO0tBQ3ZCLENBQUM7QUFDTixDQUFDO0FBRUQsSUFBTXVGLGtCQUFrQixHQUFHM0csK0RBQWUsQ0FBQ0csZ0JBQWdCLENBQUM7QUFFNUQsK0RBQWV3RyxrQkFBa0IsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9saWJzL3dlYi9zdGF0ZS9sZXhpY2FsLWVkaXRvci50cz80M2Y4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBOb3RlU3RhdGUgZnJvbSAnbGlicy93ZWIvc3RhdGUvbm90ZSc7XG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcic7XG5pbXBvcnQge1xuICAgIHVzZUNhbGxiYWNrLFxuICAgIE1vdXNlRXZlbnQgYXMgUmVhY3RNb3VzZUV2ZW50LFxuICAgIHVzZVN0YXRlLFxuICAgIHVzZVJlZixcbiAgICB1c2VFZmZlY3QsXG59IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHNlYXJjaE5vdGUsIHNlYXJjaFJhbmdlVGV4dCB9IGZyb20gJ2xpYnMvd2ViL3V0aWxzL3NlYXJjaCc7XG5pbXBvcnQgeyBpc05vdGVMaW5rLCBOb3RlTW9kZWwgfSBmcm9tICdsaWJzL3NoYXJlZC9ub3RlJztcbmltcG9ydCB7IHVzZVRvYXN0IH0gZnJvbSAnbGlicy93ZWIvaG9va3MvdXNlLXRvYXN0JztcbmltcG9ydCBQb3J0YWxTdGF0ZSBmcm9tICdsaWJzL3dlYi9zdGF0ZS9wb3J0YWwnO1xuaW1wb3J0IHsgTm90ZUNhY2hlSXRlbSB9IGZyb20gJ2xpYnMvd2ViL2NhY2hlJztcbmltcG9ydCBub3RlQ2FjaGUgZnJvbSAnbGlicy93ZWIvY2FjaGUvbm90ZSc7XG5pbXBvcnQgeyBjcmVhdGVDb250YWluZXIgfSBmcm9tICd1bnN0YXRlZC1uZXh0JztcbmltcG9ydCB7IExleGljYWxFZGl0b3JSZWYgfSBmcm9tICdjb21wb25lbnRzL2VkaXRvci9sZXhpY2FsLWVkaXRvcic7XG5pbXBvcnQgVUlTdGF0ZSBmcm9tICdsaWJzL3dlYi9zdGF0ZS91aSc7XG5pbXBvcnQgeyBoYXMgfSBmcm9tICdsb2Rhc2gnO1xuXG5cbmNvbnN0IFJPT1RfSUQgPSAncm9vdCc7XG5cbmNvbnN0IHVzZUxleGljYWxFZGl0b3IgPSAoaW5pdE5vdGU/OiBOb3RlTW9kZWwpID0+IHtcbiAgICAvLyBVc2UgaW5pdE5vdGUgaWYgcHJvdmlkZWQsIG90aGVyd2lzZSB0cnkgdG8gZ2V0IGZyb20gTm90ZVN0YXRlXG4gICAgbGV0IG5vdGUgPSBpbml0Tm90ZTtcbiAgICBsZXQgY3JlYXRlTm90ZVdpdGhUaXRsZTogYW55LCB1cGRhdGVOb3RlOiBhbnksIGNyZWF0ZU5vdGU6IGFueTtcblxuICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IG5vdGVTdGF0ZSA9IE5vdGVTdGF0ZS51c2VDb250YWluZXIoKTtcbiAgICAgICAgY3JlYXRlTm90ZVdpdGhUaXRsZSA9IG5vdGVTdGF0ZS5jcmVhdGVOb3RlV2l0aFRpdGxlO1xuICAgICAgICB1cGRhdGVOb3RlID0gbm90ZVN0YXRlLnVwZGF0ZU5vdGU7XG4gICAgICAgIGNyZWF0ZU5vdGUgPSBub3RlU3RhdGUuY3JlYXRlTm90ZTtcblxuICAgICAgICAvLyBPbmx5IHVzZSBub3RlU3RhdGUubm90ZSBpZiBubyBpbml0Tm90ZSBpcyBwcm92aWRlZFxuICAgICAgICBpZiAoIW5vdGUpIHtcbiAgICAgICAgICAgIG5vdGUgPSBub3RlU3RhdGUubm90ZTtcbiAgICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIC8vIElmIE5vdGVTdGF0ZSBpcyBub3QgYXZhaWxhYmxlLCB3ZSdsbCB3b3JrIHdpdGgganVzdCB0aGUgaW5pdE5vdGVcbiAgICAgICAgY29uc29sZS53YXJuKCdOb3RlU3RhdGUgbm90IGF2YWlsYWJsZSBpbiBMZXhpY2FsRWRpdG9yU3RhdGUsIHVzaW5nIGluaXROb3RlIG9ubHknKTtcbiAgICAgICAgY3JlYXRlTm90ZVdpdGhUaXRsZSA9IGFzeW5jICgpID0+IHVuZGVmaW5lZDtcbiAgICAgICAgdXBkYXRlTm90ZSA9IGFzeW5jICgpID0+IHVuZGVmaW5lZDtcbiAgICAgICAgY3JlYXRlTm90ZSA9IGFzeW5jICgpID0+IHVuZGVmaW5lZDtcbiAgICB9XG5cbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcbiAgICBjb25zdCB0b2FzdCA9IHVzZVRvYXN0KCk7XG4gICAgY29uc3QgZWRpdG9yRWwgPSB1c2VSZWY8TGV4aWNhbEVkaXRvclJlZj4obnVsbCk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8muW/q+eFp+eKtuaAgeeuoeeQhlxuICAgIGNvbnN0IFtub3RlU25hcHNob3QsIHNldE5vdGVTbmFwc2hvdF0gPSB1c2VTdGF0ZTxzdHJpbmcgfCBudWxsPihudWxsKTtcbiAgICBjb25zdCBbY3VycmVudEVkaXRvckNvbnRlbnQsIHNldEN1cnJlbnRFZGl0b3JDb250ZW50XSA9IHVzZVN0YXRlPHN0cmluZz4oJycpO1xuXG4gICAgLy8gTWFudWFsIHNhdmUgZnVuY3Rpb24gZm9yIEluZGV4ZWREQlxuICAgIGNvbnN0IHNhdmVUb0luZGV4ZWREQiA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAoZGF0YTogUGFydGlhbDxOb3RlTW9kZWw+KSA9PiB7XG4gICAgICAgICAgICBpZiAoIW5vdGU/LmlkKSByZXR1cm47XG5cbiAgICAgICAgICAgIC8vIOS7jiBJbmRleGVkREIg6I635Y+W5pyA5paw5pWw5o2u5L2c5Li65Z+656GA77yM6YG/5YWN6KaG55uW5bey5L+d5a2Y55qE5pWw5o2uXG4gICAgICAgICAgICBjb25zdCBleGlzdGluZ05vdGUgPSBhd2FpdCBub3RlQ2FjaGUuZ2V0SXRlbShub3RlLmlkKTtcbiAgICAgICAgICAgIGNvbnN0IGJhc2VOb3RlID0gZXhpc3RpbmdOb3RlIHx8IG5vdGU7XG5cbiAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWROb3RlID0geyAuLi5iYXNlTm90ZSwgLi4uZGF0YSB9O1xuXG4gICAgICAgICAgICBhd2FpdCBub3RlQ2FjaGUuc2V0SXRlbShub3RlLmlkLCB1cGRhdGVkTm90ZSk7XG4gICAgICAgIH0sXG4gICAgICAgIFtub3RlXVxuICAgICk7XG5cbiAgICBjb25zdCBzeW5jVG9TZXJ2ZXIgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKCkgPT4ge1xuICAgICAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuIGZhbHNlO1xuXG4gICAgICAgICAgICBjb25zdCBpc05ldyA9IGhhcyhyb3V0ZXIucXVlcnksICduZXcnKTtcblxuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBjb25zdCBsb2NhbE5vdGUgPSBhd2FpdCBub3RlQ2FjaGUuZ2V0SXRlbShub3RlLmlkKTtcbiAgICAgICAgICAgICAgICBjb25zdCBub3RlVG9TYXZlID0gbG9jYWxOb3RlIHx8IG5vdGU7XG5cbiAgICAgICAgICAgICAgICBpZiAoaXNOZXcpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3Qgbm90ZURhdGEgPSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAuLi5ub3RlVG9TYXZlLFxuICAgICAgICAgICAgICAgICAgICAgICAgcGlkOiAocm91dGVyLnF1ZXJ5LnBpZCBhcyBzdHJpbmcpIHx8IFJPT1RfSURcbiAgICAgICAgICAgICAgICAgICAgfTtcblxuICAgICAgICAgICAgICAgICAgICBjb25zdCBpdGVtID0gYXdhaXQgY3JlYXRlTm90ZShub3RlRGF0YSk7XG5cbiAgICAgICAgICAgICAgICAgICAgaWYgKGl0ZW0pIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IG5vdGVVcmwgPSBgLyR7aXRlbS5pZH1gO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKHJvdXRlci5hc1BhdGggIT09IG5vdGVVcmwpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBhd2FpdCByb3V0ZXIucmVwbGFjZShub3RlVXJsLCB1bmRlZmluZWQsIHsgc2hhbGxvdzogdHJ1ZSB9KTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIHRvYXN0KCdOb3RlIHNhdmVkIHRvIHNlcnZlcicsICdzdWNjZXNzJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IHVwZGF0ZWROb3RlID0gYXdhaXQgdXBkYXRlTm90ZShub3RlVG9TYXZlKTtcblxuICAgICAgICAgICAgICAgICAgICBpZiAodXBkYXRlZE5vdGUpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGF3YWl0IG5vdGVDYWNoZS5zZXRJdGVtKHVwZGF0ZWROb3RlLmlkLCB1cGRhdGVkTm90ZSk7XG4gICAgICAgICAgICAgICAgICAgICAgICB0b2FzdCgnTm90ZSB1cGRhdGVkIG9uIHNlcnZlcicsICdzdWNjZXNzJyk7XG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAgICAgdG9hc3QoJ0ZhaWxlZCB0byBzYXZlIG5vdGUgdG8gc2VydmVyJywgJ2Vycm9yJyk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4gZmFsc2U7XG4gICAgICAgIH0sXG4gICAgICAgIFtub3RlLCByb3V0ZXIsIGNyZWF0ZU5vdGUsIHVwZGF0ZU5vdGUsIHRvYXN0XVxuICAgICk7XG5cbiAgICBjb25zdCBvbkNyZWF0ZUxpbmsgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKHRpdGxlOiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIGlmICghY3JlYXRlTm90ZVdpdGhUaXRsZSkgcmV0dXJuICcnO1xuXG4gICAgICAgICAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBjcmVhdGVOb3RlV2l0aFRpdGxlKHRpdGxlKTtcbiAgICAgICAgICAgIGlmIChyZXN1bHQ/LmlkKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGAvJHtyZXN1bHQuaWR9YDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHJldHVybiAnJztcbiAgICAgICAgfSxcbiAgICAgICAgW2NyZWF0ZU5vdGVXaXRoVGl0bGVdXG4gICAgKTtcblxuICAgIGNvbnN0IG9uU2VhcmNoTGluayA9IHVzZUNhbGxiYWNrKFxuICAgICAgICBhc3luYyAodGVybTogc3RyaW5nKSA9PiB7XG4gICAgICAgICAgICByZXR1cm4gW107XG4gICAgICAgIH0sXG4gICAgICAgIFtdXG4gICAgKTtcblxuICAgIGNvbnN0IG9uQ2xpY2tMaW5rID0gdXNlQ2FsbGJhY2soXG4gICAgICAgIChocmVmOiBzdHJpbmcsIGV2ZW50OiBSZWFjdE1vdXNlRXZlbnQpID0+IHtcbiAgICAgICAgICAgIGlmIChpc05vdGVMaW5rKGhyZWYpKSB7XG4gICAgICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgICAgICByb3V0ZXIucHVzaChocmVmKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgd2luZG93Lm9wZW4oaHJlZiwgJ19ibGFuaycsICdub29wZW5lcixub3JlZmVycmVyJyk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIFtyb3V0ZXJdXG4gICAgKTtcblxuICAgIGNvbnN0IG9uVXBsb2FkSW1hZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgYXN5bmMgKF9maWxlOiBGaWxlLCBfaWQ/OiBzdHJpbmcpID0+IHtcbiAgICAgICAgICAgIC8vIEltYWdlIHVwbG9hZCBpcyBkaXNhYmxlZCBpbiBQb3N0Z3JlU1FMIHZlcnNpb25cbiAgICAgICAgICAgIHRvYXN0KCdJbWFnZSB1cGxvYWQgaXMgbm90IHN1cHBvcnRlZCBpbiB0aGlzIHZlcnNpb24nLCAnZXJyb3InKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcignSW1hZ2UgdXBsb2FkIGlzIG5vdCBzdXBwb3J0ZWQnKTtcbiAgICAgICAgfSxcbiAgICAgICAgW3RvYXN0XVxuICAgICk7XG5cbiAgICBjb25zdCBvbkhvdmVyTGluayA9IHVzZUNhbGxiYWNrKChldmVudDogUmVhY3RNb3VzZUV2ZW50KSA9PiB7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgIH0sIFtdKTtcblxuICAgIGNvbnN0IFtiYWNrbGlua3MsIHNldEJhY2tMaW5rc10gPSB1c2VTdGF0ZTxOb3RlQ2FjaGVJdGVtW10+KCk7XG5cbiAgICBjb25zdCBnZXRCYWNrTGlua3MgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKG5vdGU/LmlkKTtcbiAgICAgICAgY29uc3QgbGlua05vdGVzOiBOb3RlQ2FjaGVJdGVtW10gPSBbXTtcbiAgICAgICAgaWYgKCFub3RlPy5pZCkgcmV0dXJuIGxpbmtOb3RlcztcbiAgICAgICAgc2V0QmFja0xpbmtzKFtdKTtcbiAgICAgICAgYXdhaXQgbm90ZUNhY2hlLml0ZXJhdGU8Tm90ZUNhY2hlSXRlbSwgdm9pZD4oKHZhbHVlKSA9PiB7XG4gICAgICAgICAgICBpZiAodmFsdWUubGlua0lkcz8uaW5jbHVkZXMobm90ZT8uaWQgfHwgJycpKSB7XG4gICAgICAgICAgICAgICAgbGlua05vdGVzLnB1c2godmFsdWUpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9KTtcbiAgICAgICAgc2V0QmFja0xpbmtzKGxpbmtOb3Rlcyk7XG4gICAgfSwgW25vdGU/LmlkXSk7XG5cbiAgICAvLyDwn5SnIOW/q+eFp+WIneWni+WMlumAu+i+kSAtIOaJk+W8gOeslOiusOaXtuiuvue9rkpTT07lv6vnhadcbiAgICBjb25zdCBpbml0aWFsaXplU25hcHNob3QgPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIOW8gOWni+WIneWni+WMluW/q+eFpzonLCB7XG4gICAgICAgICAgICBub3RlSWQ6IG5vdGU/LmlkLFxuICAgICAgICAgICAgbm90ZUNvbnRlbnQ6IG5vdGU/LmNvbnRlbnQ/LnN1YnN0cmluZygwLCAxMDApICsgJy4uLicsXG4gICAgICAgICAgICBub3RlQ29udGVudExlbmd0aDogbm90ZT8uY29udGVudD8ubGVuZ3RoIHx8IDBcbiAgICAgICAgfSk7XG5cbiAgICAgICAgaWYgKCFub3RlPy5pZCkge1xuICAgICAgICAgICAgLy8g5paw5bu656yU6K6w77ya5b+r54Wn5Li656m65YC8XG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDmlrDlu7rnrJTorrDvvIzorr7nva7nqbrlv6vnhacnKTtcbiAgICAgICAgICAgIHNldE5vdGVTbmFwc2hvdChudWxsKTtcbiAgICAgICAgICAgIHNldEN1cnJlbnRFZGl0b3JDb250ZW50KCcnKTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyDwn5SnIOS/ruWkje+8muS8mOWFiOS9v+eUqOW9k+WJjSBub3RlIOWvueixoeeahOWGheWuue+8jOWboOS4uuWug+aYr+acgOaWsOeahFxuICAgICAgICAgICAgLy8gbm90ZSDlr7nosaHmnaXoh6ogTm90ZVN0YXRl77yM5bey57uP5aSE55CG5LqG57yT5a2Y5ZKM5pyN5Yqh5Zmo5pWw5o2u55qE5ZCM5q2lXG4gICAgICAgICAgICBsZXQgc25hcHNob3RKc29uQ29udGVudCA9ICcnO1xuXG4gICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDlv6vnhaflhoXlrrnmnaXmupDliIbmnpA6Jywge1xuICAgICAgICAgICAgICAgIG5vdGVDb250ZW50OiBub3RlLmNvbnRlbnQ/LnN1YnN0cmluZygwLCAxMDApICsgJy4uLicsXG4gICAgICAgICAgICAgICAgbm90ZUNvbnRlbnRMZW5ndGg6IG5vdGUuY29udGVudD8ubGVuZ3RoIHx8IDAsXG4gICAgICAgICAgICAgICAgbm90ZUNvbnRlbnRUeXBlOiB0eXBlb2Ygbm90ZS5jb250ZW50XG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8g55u05o6l5L2/55SoIG5vdGUg5a+56LGh55qE5YaF5a655L2c5Li65b+r54WnXG4gICAgICAgICAgICBpZiAobm90ZS5jb250ZW50ICE9PSB1bmRlZmluZWQgJiYgbm90ZS5jb250ZW50ICE9PSBudWxsKSB7XG4gICAgICAgICAgICAgICAgc25hcHNob3RKc29uQ29udGVudCA9IG5vdGUuY29udGVudDtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDkvb/nlKggbm90ZSDlr7nosaHlhoXlrrnkvZzkuLrlv6vnhacnKTtcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgc25hcHNob3RKc29uQ29udGVudCA9ICcnO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIG5vdGUg5YaF5a655Li656m677yM6K6+572u56m65b+r54WnJyk7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIOW/q+eFp+WIneWni+WMluWujOaIkDonLCB7XG4gICAgICAgICAgICAgICAgbm90ZUlkOiBub3RlLmlkLFxuICAgICAgICAgICAgICAgIGhhc0NvbnRlbnQ6ICEhc25hcHNob3RKc29uQ29udGVudCxcbiAgICAgICAgICAgICAgICBjb250ZW50TGVuZ3RoOiBzbmFwc2hvdEpzb25Db250ZW50Lmxlbmd0aCxcbiAgICAgICAgICAgICAgICBpc0pzb246IHNuYXBzaG90SnNvbkNvbnRlbnQuc3RhcnRzV2l0aCgneycpLFxuICAgICAgICAgICAgICAgIHNvdXJjZTogbm90ZS5jb250ZW50ID8gJ25vdGUnIDogJ2VtcHR5JyxcbiAgICAgICAgICAgICAgICBjb250ZW50UHJldmlldzogc25hcHNob3RKc29uQ29udGVudC5zdWJzdHJpbmcoMCwgMTAwKSArICcuLi4nXG4gICAgICAgICAgICB9KTtcblxuICAgICAgICAgICAgLy8g8J+UpyDlhbPplK7vvJrorr7nva7lv6vnhaflkozlvZPliY3nvJbovpHlmajlhoXlrrlcbiAgICAgICAgICAgIHNldE5vdGVTbmFwc2hvdChzbmFwc2hvdEpzb25Db250ZW50KTtcbiAgICAgICAgICAgIHNldEN1cnJlbnRFZGl0b3JDb250ZW50KHNuYXBzaG90SnNvbkNvbnRlbnQpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcignSlNPTuW/q+eFp+WIneWni+WMluWksei0pTonLCBlcnJvcik7XG4gICAgICAgICAgICAvLyDlpLHotKXml7borr7nva7kuLrnqbrlv6vnhadcbiAgICAgICAgICAgIHNldE5vdGVTbmFwc2hvdChudWxsKTtcbiAgICAgICAgICAgIHNldEN1cnJlbnRFZGl0b3JDb250ZW50KCcnKTtcbiAgICAgICAgfVxuICAgIH0sIFtub3RlPy5pZCwgbm90ZT8uY29udGVudF0pO1xuXG4gICAgLy8g5b2T56yU6K6wSUTlj5jljJbml7bnq4vljbPph43nva4gY3VycmVudEVkaXRvckNvbnRlbnRcbiAgICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgICBpZiAobm90ZT8uaWQpIHtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIOeslOiusOWIh+aNou+8jOmHjee9rue8lui+keWZqOeKtuaAgTonLCB7XG4gICAgICAgICAgICAgICAgbm90ZUlkOiBub3RlLmlkLFxuICAgICAgICAgICAgICAgIG5vdGVUaXRsZTogbm90ZS50aXRsZSxcbiAgICAgICAgICAgICAgICBoYXNDb250ZW50OiAhIW5vdGUuY29udGVudCxcbiAgICAgICAgICAgICAgICBjb250ZW50TGVuZ3RoOiBub3RlLmNvbnRlbnQ/Lmxlbmd0aCB8fCAwXG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIC8vIPCflKcg5YWz6ZSu77ya56uL5Y2z6YeN572uIGN1cnJlbnRFZGl0b3JDb250ZW50IOS4uuaWsOeslOiusOeahOWGheWuuVxuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQobm90ZS5jb250ZW50IHx8ICcnKTtcbiAgICAgICAgICAgIHNldE5vdGVTbmFwc2hvdChub3RlLmNvbnRlbnQgfHwgJycpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgLy8g8J+UpyDmsqHmnInnrJTorrDml7bmuIXnqbrnirbmgIFcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKCfwn5SnIOaXoOeslOiusO+8jOa4heepuue8lui+keWZqOeKtuaAgScpO1xuICAgICAgICAgICAgc2V0Q3VycmVudEVkaXRvckNvbnRlbnQoJycpO1xuICAgICAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICB9XG4gICAgfSwgW25vdGU/LmlkLCBub3RlPy5jb250ZW50XSk7XG5cbiAgICAvLyDnroDljJbnmoQgb25DaGFuZ2Ug5aSE55CGIC0g5Y+q5pu05paw5b2T5YmN57yW6L6R5Zmo5YaF5a6577yM5LiN5YGa5YW25LuW5pON5L2cXG4gICAgY29uc3Qgb25FZGl0b3JDaGFuZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgKGdldFZhbHVlOiAoKSA9PiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGpzb25Db250ZW50ID0gZ2V0VmFsdWUoKTtcblxuICAgICAgICAgICAgLy8g5Y+q5pu05paw5b2T5YmN57yW6L6R5Zmo5YaF5a6554q25oCB77yM5YW25LuW6YC76L6R5Lqk57uZIFNhdmVCdXR0b24g5aSE55CGXG4gICAgICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudChqc29uQ29udGVudCk7XG4gICAgICAgIH0sXG4gICAgICAgIFtdXG4gICAgKTtcblxuICAgIC8vIEZ1bmN0aW9uIHRvIGhhbmRsZSB0aXRsZSBjaGFuZ2VzIHNwZWNpZmljYWxseVxuICAgIGNvbnN0IG9uVGl0bGVDaGFuZ2UgPSB1c2VDYWxsYmFjayhcbiAgICAgICAgKHRpdGxlOiBzdHJpbmcpOiB2b2lkID0+IHtcbiAgICAgICAgICAgIHNhdmVUb0luZGV4ZWREQih7XG4gICAgICAgICAgICAgICAgdGl0bGUsXG4gICAgICAgICAgICAgICAgdXBkYXRlZF9hdDogbmV3IERhdGUoKS50b0lTT1N0cmluZygpXG4gICAgICAgICAgICB9KT8uY2F0Y2goKHYpID0+IGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHdoaWxzdCBzYXZpbmcgdGl0bGUgdG8gSW5kZXhlZERCOiAlTycsIHYpKTtcbiAgICAgICAgfSxcbiAgICAgICAgW3NhdmVUb0luZGV4ZWREQl1cbiAgICApO1xuXG4gICAgLy8g8J+UpyDkv67lpI3vvJpKU09O5b+r54Wn5a+55q+U5Yqf6IO9IC0g5L6bU2F2ZUJ1dHRvbuS9v+eUqFxuICAgIGNvbnN0IGNvbXBhcmVXaXRoU25hcHNob3QgPSB1c2VDYWxsYmFjaygoKTogYm9vbGVhbiA9PiB7XG4gICAgICAgIC8vIOWmguaenOaYr+aWsOW7uueslOiusO+8iOW/q+eFp+S4um51bGzvvInvvIzku7vkvZVKU09O5YaF5a656YO9566X5L2c5Y+Y5YyWXG4gICAgICAgIGlmIChub3RlU25hcHNob3QgPT09IG51bGwpIHtcbiAgICAgICAgICAgIHJldHVybiBjdXJyZW50RWRpdG9yQ29udGVudC50cmltKCkgIT09ICcnO1xuICAgICAgICB9XG5cbiAgICAgICAgLy8g5bey5a2Y5Zyo56yU6K6w77ya5q+U6L6D5b2T5YmNSlNPTuWGheWuueS4jkpTT07lv6vnhadcbiAgICAgICAgY29uc3QgaGFzQ2hhbmdlcyA9IGN1cnJlbnRFZGl0b3JDb250ZW50ICE9PSBub3RlU25hcHNob3Q7XG4gICAgICAgIHJldHVybiBoYXNDaGFuZ2VzO1xuICAgIH0sIFtub3RlU25hcHNob3QsIGN1cnJlbnRFZGl0b3JDb250ZW50XSk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8muiOt+WPluW9k+WJjee8lui+keWZqOeKtuaAgSAtIOS+m1NhdmVCdXR0b27kvb/nlKhcbiAgICBjb25zdCBnZXRFZGl0b3JTdGF0ZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGhhc0NoYW5nZXM6IGNvbXBhcmVXaXRoU25hcHNob3QoKSxcbiAgICAgICAgICAgIGN1cnJlbnRDb250ZW50OiBjdXJyZW50RWRpdG9yQ29udGVudCxcbiAgICAgICAgICAgIHNuYXBzaG90OiBub3RlU25hcHNob3QsXG4gICAgICAgICAgICBpc05ld05vdGU6IG5vdGVTbmFwc2hvdCA9PT0gbnVsbFxuICAgICAgICB9O1xuICAgIH0sIFtjb21wYXJlV2l0aFNuYXBzaG90LCBjdXJyZW50RWRpdG9yQ29udGVudCwgbm90ZVNuYXBzaG90XSk7XG5cbiAgICAvLyDwn5SnIOaWsOWinu+8mua4heepuuaJgOacieW/q+eFp+eahOWHveaVsFxuICAgIGNvbnN0IGNsZWFyQWxsU25hcHNob3RzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZygn8J+UpyDmuIXnqbrmiYDmnInlv6vnhacnKTtcbiAgICAgICAgc2V0Tm90ZVNuYXBzaG90KG51bGwpO1xuICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudCgnJyk7XG5cbiAgICAgICAgLy8g8J+UpyDkv67lpI3vvJrmuIXnqbrlkI7ph43mlrDliJ3lp4vljJblv6vnhafvvIznoa7kv53nvJbovpHlmajmmL7npLrmraPnoa7lhoXlrrlcbiAgICAgICAgaWYgKG5vdGU/LmlkICYmIG5vdGU/LmNvbnRlbnQgIT09IHVuZGVmaW5lZCkge1xuICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgICAgICAgICAgaW5pdGlhbGl6ZVNuYXBzaG90KCk7XG4gICAgICAgICAgICB9LCAwKTtcbiAgICAgICAgfVxuICAgIH0sIFtub3RlPy5pZCwgbm90ZT8uY29udGVudCwgaW5pdGlhbGl6ZVNuYXBzaG90XSk7XG5cbiAgICAvLyDwn5SnIOS/ruWkje+8muS/neWtmOW9k+WJjUpTT07lhoXlrrnliLBJbmRleGVkREIgLSDkvptTYXZlQnV0dG9u6LCD55SoXG4gICAgY29uc3Qgc2F2ZUN1cnJlbnRDb250ZW50ID0gdXNlQ2FsbGJhY2soYXN5bmMgKCk6IFByb21pc2U8Ym9vbGVhbj4gPT4ge1xuICAgICAgICBpZiAoIW5vdGU/LmlkKSByZXR1cm4gZmFsc2U7XG5cbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgIC8vIOiOt+WPluagh+mimFxuICAgICAgICAgICAgbGV0IHRpdGxlOiBzdHJpbmc7XG4gICAgICAgICAgICBpZiAobm90ZT8uaXNEYWlseU5vdGUpIHtcbiAgICAgICAgICAgICAgICB0aXRsZSA9IG5vdGUudGl0bGU7XG4gICAgICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgICAgICAgIGNvbnN0IHRpdGxlSW5wdXQgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCdoMSB0ZXh0YXJlYScpIGFzIEhUTUxUZXh0QXJlYUVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgaWYgKHRpdGxlSW5wdXQgJiYgdGl0bGVJbnB1dC52YWx1ZSkge1xuICAgICAgICAgICAgICAgICAgICB0aXRsZSA9IHRpdGxlSW5wdXQudmFsdWUudHJpbSgpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIOWvueS6jkpTT07moLzlvI/vvIzkvb/nlKjnjrDmnInmoIfpopjmiJbpu5jorqTmoIfpophcbiAgICAgICAgICAgICAgICAgICAgdGl0bGUgPSBub3RlPy50aXRsZSB8fCAnVW50aXRsZWQnO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cblxuICAgICAgICAgICAgLy8g5L+d5a2YSlNPTuWGheWuueWIsEluZGV4ZWREQlxuICAgICAgICAgICAgYXdhaXQgc2F2ZVRvSW5kZXhlZERCKHtcbiAgICAgICAgICAgICAgICBjb250ZW50OiBjdXJyZW50RWRpdG9yQ29udGVudCwgLy8g6L+Z6YeM5pivSlNPTuagvOW8j1xuICAgICAgICAgICAgICAgIHRpdGxlLFxuICAgICAgICAgICAgICAgIHVwZGF0ZWRfYXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgICAgICAgICAgfSk7XG5cbiAgICAgICAgICAgIC8vIPCflKcg5oyJ54Wn55So5oi36KaB5rGC77ya5L+d5a2Y5ZCO5riF6ZmkIGN1cnJlbnRFZGl0b3JDb250ZW50XG4gICAgICAgICAgICAvLyDlm6DkuLrkv53lrZjlkI7kvJrph43mlrDliqDovb3nvJPlrZjvvIznvJbovpHlmajnm7TmjqXkvb/nlKggbm90ZS5jb250ZW50XG4gICAgICAgICAgICBzZXROb3RlU25hcHNob3QobnVsbCk7XG4gICAgICAgICAgICBzZXRDdXJyZW50RWRpdG9yQ29udGVudCgnJyk7XG5cbiAgICAgICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgICAgY29uc29sZS5lcnJvcign8J+UpyDkv53lrZhKU09O5YiwSW5kZXhlZERC5aSx6LSlOicsIGVycm9yKTtcbiAgICAgICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgICAgfVxuICAgIH0sIFtub3RlLCBjdXJyZW50RWRpdG9yQ29udGVudCwgc2F2ZVRvSW5kZXhlZERCLCBjbGVhckFsbFNuYXBzaG90c10pO1xuXG4gICAgcmV0dXJuIHtcbiAgICAgICAgb25DcmVhdGVMaW5rLFxuICAgICAgICBvblNlYXJjaExpbmssXG4gICAgICAgIG9uQ2xpY2tMaW5rLFxuICAgICAgICBvblVwbG9hZEltYWdlLFxuICAgICAgICBvbkhvdmVyTGluayxcbiAgICAgICAgZ2V0QmFja0xpbmtzLFxuICAgICAgICBvbkVkaXRvckNoYW5nZSxcbiAgICAgICAgb25UaXRsZUNoYW5nZSxcbiAgICAgICAgc2F2ZVRvSW5kZXhlZERCLFxuICAgICAgICBzeW5jVG9TZXJ2ZXIsXG4gICAgICAgIGJhY2tsaW5rcyxcbiAgICAgICAgZWRpdG9yRWwsXG4gICAgICAgIC8vIPCflKcg6K6p57yW6L6R5Zmo55u05o6l5L2/55SoIG5vdGUuY29udGVudFxuICAgICAgICBub3RlLFxuICAgICAgICAvLyDwn5SnIOaatOmcsiBjdXJyZW50RWRpdG9yQ29udGVudCDnu5kgU2F2ZUJ1dHRvbiDnlKjkuo7lr7nmr5RcbiAgICAgICAgY3VycmVudEVkaXRvckNvbnRlbnQsXG4gICAgfTtcbn07XG5cbmNvbnN0IExleGljYWxFZGl0b3JTdGF0ZSA9IGNyZWF0ZUNvbnRhaW5lcih1c2VMZXhpY2FsRWRpdG9yKTtcblxuZXhwb3J0IGRlZmF1bHQgTGV4aWNhbEVkaXRvclN0YXRlO1xuIl0sIm5hbWVzIjpbIk5vdGVTdGF0ZSIsInVzZVJvdXRlciIsInVzZUNhbGxiYWNrIiwidXNlU3RhdGUiLCJ1c2VSZWYiLCJ1c2VFZmZlY3QiLCJpc05vdGVMaW5rIiwidXNlVG9hc3QiLCJub3RlQ2FjaGUiLCJjcmVhdGVDb250YWluZXIiLCJoYXMiLCJST09UX0lEIiwidXNlTGV4aWNhbEVkaXRvciIsImluaXROb3RlIiwibm90ZSIsImNyZWF0ZU5vdGVXaXRoVGl0bGUiLCJ1cGRhdGVOb3RlIiwiY3JlYXRlTm90ZSIsIm5vdGVTdGF0ZSIsInVzZUNvbnRhaW5lciIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJ1bmRlZmluZWQiLCJyb3V0ZXIiLCJ0b2FzdCIsImVkaXRvckVsIiwibm90ZVNuYXBzaG90Iiwic2V0Tm90ZVNuYXBzaG90IiwiY3VycmVudEVkaXRvckNvbnRlbnQiLCJzZXRDdXJyZW50RWRpdG9yQ29udGVudCIsInNhdmVUb0luZGV4ZWREQiIsImRhdGEiLCJleGlzdGluZ05vdGUiLCJiYXNlTm90ZSIsInVwZGF0ZWROb3RlIiwiaWQiLCJnZXRJdGVtIiwic2V0SXRlbSIsInN5bmNUb1NlcnZlciIsImlzTmV3IiwibG9jYWxOb3RlIiwibm90ZVRvU2F2ZSIsIm5vdGVEYXRhIiwiaXRlbSIsIm5vdGVVcmwiLCJxdWVyeSIsInBpZCIsImFzUGF0aCIsInJlcGxhY2UiLCJzaGFsbG93Iiwib25DcmVhdGVMaW5rIiwidGl0bGUiLCJyZXN1bHQiLCJvblNlYXJjaExpbmsiLCJ0ZXJtIiwib25DbGlja0xpbmsiLCJocmVmIiwiZXZlbnQiLCJwcmV2ZW50RGVmYXVsdCIsInB1c2giLCJ3aW5kb3ciLCJvcGVuIiwib25VcGxvYWRJbWFnZSIsIl9maWxlIiwiX2lkIiwiRXJyb3IiLCJvbkhvdmVyTGluayIsImJhY2tsaW5rcyIsInNldEJhY2tMaW5rcyIsImdldEJhY2tMaW5rcyIsImxpbmtOb3RlcyIsImxvZyIsIml0ZXJhdGUiLCJ2YWx1ZSIsImxpbmtJZHMiLCJpbmNsdWRlcyIsImluaXRpYWxpemVTbmFwc2hvdCIsInNuYXBzaG90SnNvbkNvbnRlbnQiLCJub3RlSWQiLCJub3RlQ29udGVudCIsImNvbnRlbnQiLCJzdWJzdHJpbmciLCJub3RlQ29udGVudExlbmd0aCIsImxlbmd0aCIsIm5vdGVDb250ZW50VHlwZSIsImhhc0NvbnRlbnQiLCJjb250ZW50TGVuZ3RoIiwiaXNKc29uIiwic3RhcnRzV2l0aCIsInNvdXJjZSIsImNvbnRlbnRQcmV2aWV3Iiwibm90ZVRpdGxlIiwib25FZGl0b3JDaGFuZ2UiLCJnZXRWYWx1ZSIsImpzb25Db250ZW50Iiwib25UaXRsZUNoYW5nZSIsInVwZGF0ZWRfYXQiLCJEYXRlIiwidG9JU09TdHJpbmciLCJjYXRjaCIsInYiLCJjb21wYXJlV2l0aFNuYXBzaG90IiwidHJpbSIsImhhc0NoYW5nZXMiLCJnZXRFZGl0b3JTdGF0ZSIsImN1cnJlbnRDb250ZW50Iiwic25hcHNob3QiLCJpc05ld05vdGUiLCJjbGVhckFsbFNuYXBzaG90cyIsInNldFRpbWVvdXQiLCJzYXZlQ3VycmVudENvbnRlbnQiLCJ0aXRsZUlucHV0IiwiaXNEYWlseU5vdGUiLCJkb2N1bWVudCIsInF1ZXJ5U2VsZWN0b3IiLCJMZXhpY2FsRWRpdG9yU3RhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./libs/web/state/lexical-editor.ts\n"));

/***/ })

});