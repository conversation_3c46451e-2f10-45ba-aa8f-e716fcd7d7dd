{"version": 3, "names": ["version", "split", "reduce", "v", "x", "version<PERSON>ey", "createRegExpFeaturePlugin", "name", "feature", "options", "manipulateOptions", "pre", "file", "features", "get", "<PERSON><PERSON><PERSON>", "newFeatures", "enableFeature", "FEATURES", "useUnicodeFlag", "runtime", "unicodeFlag", "set", "undefined", "has", "runtimeKey", "hasFeature", "duplicateNamedCaptureGroups", "Error", "visitor", "RegExpLiteral", "path", "node", "regexpuOptions", "generateRegexpuOptions", "pattern", "canSkipRegexpu", "namedCaptureGroups", "__proto__", "namedGroups", "onNamedGroup", "index", "prev", "Array", "isArray", "push", "rewritePattern", "flags", "Object", "keys", "length", "isRegExpTest", "call", "t", "callExpression", "addHelper", "valueToNode", "annotateAsPure", "replaceWith", "transformFlags", "parentPath", "isMemberExpression", "object", "computed", "isIdentifier"], "sources": ["../src/index.ts"], "sourcesContent": ["import rewritePattern from \"regexpu-core\";\nimport {\n  features<PERSON>ey,\n  FEATURES,\n  enableFeature,\n  runtimeKey,\n  hasFeature,\n} from \"./features\";\nimport { generateRegexpuOptions, canSkipRegexpu, transformFlags } from \"./util\";\nimport type { NodePath } from \"@babel/traverse\";\n\nimport { types as t } from \"@babel/core\";\nimport type { PluginObject } from \"@babel/core\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\ndeclare const PACKAGE_JSON: { name: string; version: string };\n\n// Note: Versions are represented as an integer. e.g. 7.1.5 is represented\n//       as 70000100005. This method is easier than using a semver-parsing\n//       package, but it breaks if we release x.y.z where x, y or z are\n//       greater than 99_999.\nconst version = PACKAGE_JSON.version\n  .split(\".\")\n  .reduce((v, x) => v * 1e5 + +x, 0);\nconst versionKey = \"@babel/plugin-regexp-features/version\";\n\nexport interface Options {\n  name: string;\n  feature: keyof typeof FEATURES;\n  options?: {\n    useUnicodeFlag?: boolean;\n    runtime?: boolean;\n  };\n  manipulateOptions?: PluginObject[\"manipulateOptions\"];\n}\n\nexport function createRegExpFeaturePlugin({\n  name,\n  feature,\n  options = {},\n  manipulateOptions = () => {},\n}: Options): PluginObject {\n  return {\n    name,\n\n    manipulateOptions,\n\n    pre() {\n      const { file } = this;\n      const features = file.get(featuresKey) ?? 0;\n      let newFeatures = enableFeature(features, FEATURES[feature]);\n\n      const { useUnicodeFlag, runtime } = options;\n      if (useUnicodeFlag === false) {\n        newFeatures = enableFeature(newFeatures, FEATURES.unicodeFlag);\n      }\n      if (newFeatures !== features) {\n        file.set(featuresKey, newFeatures);\n      }\n\n      if (runtime !== undefined) {\n        if (\n          file.has(runtimeKey) &&\n          file.get(runtimeKey) !== runtime &&\n          // TODO(Babel 8): Remove this check. It's necessary because in Babel 7\n          // we allow multiple copies of transform-named-capturing-groups-regex\n          // with conflicting 'runtime' options.\n          hasFeature(newFeatures, FEATURES.duplicateNamedCaptureGroups)\n        ) {\n          throw new Error(\n            `The 'runtime' option must be the same for ` +\n              `'@babel/plugin-transform-named-capturing-groups-regex' and ` +\n              `'@babel/plugin-proposal-duplicate-named-capturing-groups-regex'.`,\n          );\n        }\n        // TODO(Babel 8): Remove this check and always set it.\n        // It's necessary because in Babel 7 we allow multiple copies of\n        // transform-named-capturing-groups-regex with conflicting 'runtime' options.\n        if (feature === \"namedCaptureGroups\") {\n          if (!runtime || !file.has(runtimeKey)) file.set(runtimeKey, runtime);\n        } else {\n          file.set(runtimeKey, runtime);\n        }\n      }\n\n      if (!file.has(versionKey) || file.get(versionKey) < version) {\n        file.set(versionKey, version);\n      }\n    },\n\n    visitor: {\n      RegExpLiteral(path) {\n        const { node } = path;\n        const { file } = this;\n        const features = file.get(featuresKey);\n        const runtime = file.get(runtimeKey) ?? true;\n\n        const regexpuOptions = generateRegexpuOptions(node.pattern, features);\n        if (canSkipRegexpu(node, regexpuOptions)) {\n          return;\n        }\n\n        const namedCaptureGroups: Record<string, number | number[]> = {\n          __proto__: null,\n        };\n        if (regexpuOptions.namedGroups === \"transform\") {\n          regexpuOptions.onNamedGroup = (name, index) => {\n            const prev = namedCaptureGroups[name];\n            if (typeof prev === \"number\") {\n              namedCaptureGroups[name] = [prev, index];\n            } else if (Array.isArray(prev)) {\n              prev.push(index);\n            } else {\n              namedCaptureGroups[name] = index;\n            }\n          };\n        }\n\n        node.pattern = rewritePattern(node.pattern, node.flags, regexpuOptions);\n\n        if (\n          regexpuOptions.namedGroups === \"transform\" &&\n          Object.keys(namedCaptureGroups).length > 0 &&\n          runtime &&\n          !isRegExpTest(path)\n        ) {\n          const call = t.callExpression(this.addHelper(\"wrapRegExp\"), [\n            node,\n            t.valueToNode(namedCaptureGroups),\n          ]);\n          annotateAsPure(call);\n\n          path.replaceWith(call);\n        }\n\n        node.flags = transformFlags(regexpuOptions, node.flags);\n      },\n    },\n  };\n}\n\nfunction isRegExpTest(path: NodePath<t.RegExpLiteral>) {\n  return (\n    path.parentPath.isMemberExpression({\n      object: path.node,\n      computed: false,\n    }) && path.parentPath.get(\"property\").isIdentifier({ name: \"test\" })\n  );\n}\n"], "mappings": ";;;;;;;AAAA;;AACA;;AAOA;;AAGA;;AAEA;;AAQA,MAAMA,OAAO,GAAG,SACbC,KADa,CACP,GADO,EAEbC,MAFa,CAEN,CAACC,CAAD,EAAIC,CAAJ,KAAUD,CAAC,GAAG,GAAJ,GAAU,CAACC,CAFf,EAEkB,CAFlB,CAAhB;AAGA,MAAMC,UAAU,GAAG,uCAAnB;;AAYO,SAASC,yBAAT,CAAmC;EACxCC,IADwC;EAExCC,OAFwC;EAGxCC,OAAO,GAAG,EAH8B;EAIxCC,iBAAiB,GAAG,MAAM,CAAE;AAJY,CAAnC,EAKmB;EACxB,OAAO;IACLH,IADK;IAGLG,iBAHK;;IAKLC,GAAG,GAAG;MAAA;;MACJ,MAAM;QAAEC;MAAF,IAAW,IAAjB;MACA,MAAMC,QAAQ,gBAAGD,IAAI,CAACE,GAAL,CAASC,qBAAT,CAAH,wBAA4B,CAA1C;MACA,IAAIC,WAAW,GAAG,IAAAC,uBAAA,EAAcJ,QAAd,EAAwBK,kBAAA,CAASV,OAAT,CAAxB,CAAlB;MAEA,MAAM;QAAEW,cAAF;QAAkBC;MAAlB,IAA8BX,OAApC;;MACA,IAAIU,cAAc,KAAK,KAAvB,EAA8B;QAC5BH,WAAW,GAAG,IAAAC,uBAAA,EAAcD,WAAd,EAA2BE,kBAAA,CAASG,WAApC,CAAd;MACD;;MACD,IAAIL,WAAW,KAAKH,QAApB,EAA8B;QAC5BD,IAAI,CAACU,GAAL,CAASP,qBAAT,EAAsBC,WAAtB;MACD;;MAED,IAAII,OAAO,KAAKG,SAAhB,EAA2B;QACzB,IACEX,IAAI,CAACY,GAAL,CAASC,oBAAT,KACAb,IAAI,CAACE,GAAL,CAASW,oBAAT,MAAyBL,OADzB,IAKA,IAAAM,oBAAA,EAAWV,WAAX,EAAwBE,kBAAA,CAASS,2BAAjC,CANF,EAOE;UACA,MAAM,IAAIC,KAAJ,CACH,4CAAD,GACG,6DADH,GAEG,kEAHC,CAAN;QAKD;;QAID,IAAIpB,OAAO,KAAK,oBAAhB,EAAsC;UACpC,IAAI,CAACY,OAAD,IAAY,CAACR,IAAI,CAACY,GAAL,CAASC,oBAAT,CAAjB,EAAuCb,IAAI,CAACU,GAAL,CAASG,oBAAT,EAAqBL,OAArB;QACxC,CAFD,MAEO;UACLR,IAAI,CAACU,GAAL,CAASG,oBAAT,EAAqBL,OAArB;QACD;MACF;;MAED,IAAI,CAACR,IAAI,CAACY,GAAL,CAASnB,UAAT,CAAD,IAAyBO,IAAI,CAACE,GAAL,CAAST,UAAT,IAAuBL,OAApD,EAA6D;QAC3DY,IAAI,CAACU,GAAL,CAASjB,UAAT,EAAqBL,OAArB;MACD;IACF,CA9CI;;IAgDL6B,OAAO,EAAE;MACPC,aAAa,CAACC,IAAD,EAAO;QAAA;;QAClB,MAAM;UAAEC;QAAF,IAAWD,IAAjB;QACA,MAAM;UAAEnB;QAAF,IAAW,IAAjB;QACA,MAAMC,QAAQ,GAAGD,IAAI,CAACE,GAAL,CAASC,qBAAT,CAAjB;QACA,MAAMK,OAAO,iBAAGR,IAAI,CAACE,GAAL,CAASW,oBAAT,CAAH,yBAA2B,IAAxC;QAEA,MAAMQ,cAAc,GAAG,IAAAC,4BAAA,EAAuBF,IAAI,CAACG,OAA5B,EAAqCtB,QAArC,CAAvB;;QACA,IAAI,IAAAuB,oBAAA,EAAeJ,IAAf,EAAqBC,cAArB,CAAJ,EAA0C;UACxC;QACD;;QAED,MAAMI,kBAAqD,GAAG;UAC5DC,SAAS,EAAE;QADiD,CAA9D;;QAGA,IAAIL,cAAc,CAACM,WAAf,KAA+B,WAAnC,EAAgD;UAC9CN,cAAc,CAACO,YAAf,GAA8B,CAACjC,IAAD,EAAOkC,KAAP,KAAiB;YAC7C,MAAMC,IAAI,GAAGL,kBAAkB,CAAC9B,IAAD,CAA/B;;YACA,IAAI,OAAOmC,IAAP,KAAgB,QAApB,EAA8B;cAC5BL,kBAAkB,CAAC9B,IAAD,CAAlB,GAA2B,CAACmC,IAAD,EAAOD,KAAP,CAA3B;YACD,CAFD,MAEO,IAAIE,KAAK,CAACC,OAAN,CAAcF,IAAd,CAAJ,EAAyB;cAC9BA,IAAI,CAACG,IAAL,CAAUJ,KAAV;YACD,CAFM,MAEA;cACLJ,kBAAkB,CAAC9B,IAAD,CAAlB,GAA2BkC,KAA3B;YACD;UACF,CATD;QAUD;;QAEDT,IAAI,CAACG,OAAL,GAAeW,YAAc,CAACd,IAAI,CAACG,OAAN,EAAeH,IAAI,CAACe,KAApB,EAA2Bd,cAA3B,CAA7B;;QAEA,IACEA,cAAc,CAACM,WAAf,KAA+B,WAA/B,IACAS,MAAM,CAACC,IAAP,CAAYZ,kBAAZ,EAAgCa,MAAhC,GAAyC,CADzC,IAEA9B,OAFA,IAGA,CAAC+B,YAAY,CAACpB,IAAD,CAJf,EAKE;UACA,MAAMqB,IAAI,GAAGC,WAAA,CAAEC,cAAF,CAAiB,KAAKC,SAAL,CAAe,YAAf,CAAjB,EAA+C,CAC1DvB,IAD0D,EAE1DqB,WAAA,CAAEG,WAAF,CAAcnB,kBAAd,CAF0D,CAA/C,CAAb;;UAIA,IAAAoB,6BAAA,EAAeL,IAAf;UAEArB,IAAI,CAAC2B,WAAL,CAAiBN,IAAjB;QACD;;QAEDpB,IAAI,CAACe,KAAL,GAAa,IAAAY,oBAAA,EAAe1B,cAAf,EAA+BD,IAAI,CAACe,KAApC,CAAb;MACD;;IA9CM;EAhDJ,CAAP;AAiGD;;AAED,SAASI,YAAT,CAAsBpB,IAAtB,EAAuD;EACrD,OACEA,IAAI,CAAC6B,UAAL,CAAgBC,kBAAhB,CAAmC;IACjCC,MAAM,EAAE/B,IAAI,CAACC,IADoB;IAEjC+B,QAAQ,EAAE;EAFuB,CAAnC,KAGMhC,IAAI,CAAC6B,UAAL,CAAgB9C,GAAhB,CAAoB,UAApB,EAAgCkD,YAAhC,CAA6C;IAAEzD,IAAI,EAAE;EAAR,CAA7C,CAJR;AAMD"}