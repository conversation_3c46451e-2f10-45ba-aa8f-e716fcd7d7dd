# motea Environment Configuration - Neon PostgreSQL
# Based on the open-source project Notea, originally created by qingwei-li<<EMAIL>>
# Modified and maintained by waycaan, 2025.

# ===========================================
# Application Configuration
# ===========================================
NODE_ENV=production
PORT=3000

# ===========================================
# Database Configuration - Neon PostgreSQL
# ===========================================
# Neon is a serverless PostgreSQL platform optimized for modern applications
# Recommended for Vercel deployments due to excellent performance and scaling

# Primary database connection (REQUIRED)
DATABASE_URL=postgres://username:<EMAIL>/dbname?sslmode=require

# Explicit provider setting (OPTIONAL - auto-detected from URL)
DB_PROVIDER=neon

# Database table prefix (OPTIONAL)
# STORE_PREFIX=motea_

# ===========================================
# Neon-specific Configuration Tips
# ===========================================
# 1. Use US East region (us-east-1) for best Vercel performance
# 2. SSL is required for Neon connections
# 3. Connection pooling is handled by Neon
# 4. Supports branching for development/staging environments

# ===========================================
# Authentication Configuration
# ===========================================
# Option 1: Set a password for the application
PASSWORD=your_secure_password_here

# Option 2: Disable password authentication (uncomment below)
# DISABLE_PASSWORD=true

# ===========================================
# Performance Configuration
# ===========================================
# Number of notes to preload (recommended: 10-20 for Neon)
PRELOAD_NOTES_COUNT=15

# Logging level
LOG_LEVEL=info

# ===========================================
# Session Configuration
# ===========================================
# Session secret for secure cookies
SESSION_SECRET=your_session_secret_here

# Use secure cookies in production
COOKIE_SECURE=true

# ===========================================
# Optional Configuration
# ===========================================
# Base URL for the application (useful for reverse proxies)
# BASE_URL=https://your-domain.com

# Disable Next.js telemetry
NEXT_TELEMETRY_DISABLED=1

# ===========================================
# Neon Database Setup Instructions
# ===========================================
# 1. Sign up at https://neon.tech
# 2. Create a new project
# 3. Choose "US East (N. Virginia)" region for Vercel deployments
# 4. Copy the connection string from the dashboard
# 5. Replace the DATABASE_URL above with your connection string
# 6. The connection string format is:
#    postgres://username:<EMAIL>/dbname?sslmode=require

# ===========================================
# Deployment Notes
# ===========================================
# - Neon automatically handles connection pooling
# - SSL is always required and enabled by default
# - Supports up to 100 concurrent connections on free tier
# - Database automatically scales to zero when not in use
# - Excellent for serverless deployments like Vercel
