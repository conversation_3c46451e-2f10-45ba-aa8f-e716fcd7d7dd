{"version": 3, "names": ["has", "Function", "call", "bind", "Object", "hasOwnProperty", "addProposalSyntaxPlugins", "items", "proposalSyntaxPlugins", "for<PERSON>ach", "plugin", "add", "removeUnnecessaryItems", "overlapping", "item", "name", "delete", "removeUnsupportedItems", "babelVersion", "minVersions", "semver", "lt"], "sources": ["../src/filter-items.ts"], "sourcesContent": ["import semver from \"semver\";\nimport { minVersions } from \"./available-plugins\";\n\nconst has = Function.call.bind(Object.hasOwnProperty);\n\nexport function addProposalSyntaxPlugins(\n  items: Set<string>,\n  proposalSyntaxPlugins: readonly string[],\n) {\n  proposalSyntaxPlugins.forEach(plugin => {\n    items.add(plugin);\n  });\n}\nexport function removeUnnecessaryItems(\n  items: Set<string>,\n  overlapping: { [name: string]: string[] },\n) {\n  items.forEach(item => {\n    overlapping[item]?.forEach(name => items.delete(name));\n  });\n}\nexport function removeUnsupportedItems(\n  items: Set<string>,\n  babelVersion: string,\n) {\n  items.forEach(item => {\n    if (\n      has(minVersions, item) &&\n      semver.lt(\n        babelVersion,\n        // @ts-expect-error we have checked minVersions[item] in has call\n        minVersions[item],\n      )\n    ) {\n      items.delete(item);\n    }\n  });\n}\n"], "mappings": ";;;;;;;;;AAAA;;AACA;;AAEA,MAAMA,GAAG,GAAGC,QAAQ,CAACC,IAAT,CAAcC,IAAd,CAAmBC,MAAM,CAACC,cAA1B,CAAZ;;AAEO,SAASC,wBAAT,CACLC,KADK,EAELC,qBAFK,EAGL;EACAA,qBAAqB,CAACC,OAAtB,CAA8BC,MAAM,IAAI;IACtCH,KAAK,CAACI,GAAN,CAAUD,MAAV;EACD,CAFD;AAGD;;AACM,SAASE,sBAAT,CACLL,KADK,EAELM,WAFK,EAGL;EACAN,KAAK,CAACE,OAAN,CAAcK,IAAI,IAAI;IAAA;;IACpB,qBAAAD,WAAW,CAACC,IAAD,CAAX,uCAAmBL,OAAnB,CAA2BM,IAAI,IAAIR,KAAK,CAACS,MAAN,CAAaD,IAAb,CAAnC;EACD,CAFD;AAGD;;AACM,SAASE,sBAAT,CACLV,KADK,EAELW,YAFK,EAGL;EACAX,KAAK,CAACE,OAAN,CAAcK,IAAI,IAAI;IACpB,IACEd,GAAG,CAACmB,6BAAD,EAAcL,IAAd,CAAH,IACAM,OAAM,CAACC,EAAP,CACEH,YADF,EAGEC,6BAAA,CAAYL,IAAZ,CAHF,CAFF,EAOE;MACAP,KAAK,CAACS,MAAN,CAAaF,IAAb;IACD;EACF,CAXD;AAYD"}