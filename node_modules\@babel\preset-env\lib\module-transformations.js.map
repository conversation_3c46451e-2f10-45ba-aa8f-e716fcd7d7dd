{"version": 3, "names": ["auto", "amd", "commonjs", "cjs", "systemjs", "umd"], "sources": ["../src/module-transformations.ts"], "sourcesContent": ["type AvailablePlugins = typeof import(\"./available-plugins\").default;\n\nexport default {\n  auto: \"transform-modules-commonjs\",\n  amd: \"transform-modules-amd\",\n  commonjs: \"transform-modules-commonjs\",\n  cjs: \"transform-modules-commonjs\",\n  systemjs: \"transform-modules-systemjs\",\n  umd: \"transform-modules-umd\",\n} as { [transform: string]: keyof AvailablePlugins };\n"], "mappings": ";;;;;;eAEe;EACbA,IAAI,EAAE,4BADO;EAEbC,GAAG,EAAE,uBAFQ;EAGbC,QAAQ,EAAE,4BAHG;EAIbC,GAAG,EAAE,4BAJQ;EAKbC,QAAQ,EAAE,4BALG;EAMbC,GAAG,EAAE;AANQ,C"}